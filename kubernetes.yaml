apiVersion: apps/v1
kind: Deployment
metadata:
  name: shwethe-api-n
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      k8s-app: shwethe-api-n
  template:
    metadata:
      labels:
        k8s-app: shwethe-api-n
    spec:
      volumes:
        - name: timezone
          hostPath:
            path: /usr/share/zoneinfo/Asia/Bangkok
            type: ''
      containers:
        - name: shwethe-api-n
          image: 'siess223/shwethe_api_n:latest'
          resources:
            requests:
              cpu: '1'
              memory: 500Mi
          volumeMounts:
            - name: timezone
              mountPath: /etc/localtime
      imagePullSecrets:
        - name: regcred

---
apiVersion: v1
kind: Service
metadata:
  name: shwethe-api-n-service
spec:
  selector:
    k8s-app: shwethe-api-n
  ports:
    - protocol: TCP
      port: 8000
      targetPort: 8000

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: minimal-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              serviceName: my-service2
              servicePort: 5000
          - path: /api/
            pathType: Prefix
            backend:
              serviceName: shwethe-api-n-service
              servicePort: 8000