
from src.shwethe_miniapp_carItemChange.src.views import view as shwethe_miniapp_carItemChange_info
# from src.shwethe_miniapp_carItemChange.src2.views import view as shwethe_miniapp_carItemChange_info_2
from src.shwethe_miniapp_carItemChange.check_stock.views import view as shwethe_miniapp_carItemChange_check_stock

from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    shwethe_miniapp_carItemChange_info.router,
    prefix="/shwethe_miniapp_carItemChange/api/v1/form",
    tags=["shwethe_miniapp_carItemChange"],
    responses={404: {"message": "Not found"}},
)

# app.include_router(
#     shwethe_miniapp_carItemChange_info_2.router,
#     prefix="/shwethe_miniapp_carItemChange_2/api/v2/form",
#     tags=["shwethe_miniapp_carItemChange_2"],
#     responses={404: {"message": "Not found"}},
# )

app.include_router(
    shwethe_miniapp_carItemChange_check_stock.router,
    prefix="/shwethe_miniapp_carItemChange/api/v1/check_stock",
    tags=["shwethe_miniapp_carItemChange"],
    responses={404: {"message": "Not found"}},
)

