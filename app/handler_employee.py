
from src.shwethe_petrol.views import view as shwethe_petrol_info
from src.service.employee_data.api.v1 import main as employee_data

from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    employee_data.router,
    prefix="/shwethe_employee/api/v1",
    tags=["shwethe_employee"],
    responses={404: {"message": "Not found"}},
)

