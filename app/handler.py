from src.order_s_insert import main as order_s_insert
from src.order_s_insert.table_product_form import main as table_product_form
from src.order_d_insert import main as order_d_insert
from src.order_d_insert.order_d_list import main as order_d_list
from src.order_d_insert.order_d_list_page import main as order_d_list_page

from src.order_search import main as order_search
from src.order_s_insert.table_form import main as table_form
from src.order_s_list import main as order_s_list
from src.order_s_list import main2 as order_s_list2
from src.order_in_judy import main as order_in_judy
from src.to_table import main as to_table
from src.orderGoods import main as orderGoods
from src.info import main as info

from src.order_s_profit import main as order_s_profit
# Remove this line from here: import handler_guiling

from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title = "FastAPI Video Store",
              description = "Description and technical detail of APIs, Live on Medium | Author : <PERSON><PERSON><PERSON>",
              version = "0.0.1")

origins = [
    "*",
    "https://localhost.tiangolo.com",
    "http://localhost",
    "http://************:8000",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


app.include_router(
    orderGoods.router,
    prefix="/shwethe_order_in_fast_api/api/v1/orderGoods",
    tags=["orderGoods"],
    responses={404: {"message": "Not found"}},
)
app.include_router(
    table_form.router,
    prefix="/shwethe_order_in_fast_api/api/v1/order_s_insert/in_order",
    tags=["in_order"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    table_product_form.router,
    prefix="/shwethe_order_in_fast_api/api/v1/order_s_insert/in_order/table_product_form",
    tags=["table_product_form"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    order_s_insert.router,
    prefix="/shwethe_order_in_fast_api/api/v1/order_s_insert",
    tags=["in order api"],
    responses={404: {"message": "Not found"}},
)
app.include_router(
    order_d_insert.router,
    prefix="/shwethe_order_in_fast_api/api/v1/order_d_insert",
    tags=["d_in order api"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    order_d_list.router,
    prefix="/shwethe_order_in_fast_api/api/v1/order_d_list/order_d_list",
    tags=["order_d_list"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    order_d_list_page.router,
    prefix="/shwethe_order_in_fast_api/api/v1/order_d_list/order_d_list_page",
    tags=["order_d_list_page"],
    responses={404: {"message": "Not found"}},
)


app.include_router(
    order_search.router,
    prefix="/shwethe_order_in_fast_api/api/v1/order_search",
    tags=["order_search"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    order_search.router,
    prefix="/shwethe_order_in_fast_api/api/v1/order_search",
    tags=["order_search"],
    responses={404: {"message": "Not found"}},
)


app.include_router(
    order_s_list.router,
    prefix="/shwethe_order_in_fast_api/api/v1/order_s_list",
    tags=["order_s_list"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    order_s_list2.router,
    prefix="/shwethe_order_in_fast_api/api/v2/order_s_list",
    tags=["order_s_list2"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    order_in_judy.router,
    prefix="/shwethe_order_in_fast_api/api/v1/order_in_judy",
    tags=["order_in_judy"],
    responses={404: {"message": "Not found"}},
)
app.include_router(
    order_s_profit.router,
    prefix="/shwethe_order_in_fast_api/api/v1/order_s_profit",
    tags=["order_s_profit"],
    responses={404: {"message": "Not found"}},
)


app.include_router(
    to_table.router,
    prefix="/shwethe_order_in_fast_api/api/v1/to_table",
    tags=["to_table"],
    responses={404: {"message": "Not found"}},
)


app.include_router(
    info.router,
    prefix="/shwethe_order_in_fast_api/api/v1/info",
    tags=["info"],
    responses={404: {"message": "Not found"}},
)

# Import handler_guiling at the end after app is defined
import handler_guiling
