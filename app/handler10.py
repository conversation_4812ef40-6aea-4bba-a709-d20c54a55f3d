
from src.shwethe_car_garage.src.views import view as shwethe_car_garage_router

from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    shwethe_car_garage_router.router,
    prefix="/shwethe_car_garage/api/v1/src",
    tags=["shwethe_car_garage"],
    responses={404: {"message": "Not found"}},
)
