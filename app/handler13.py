
from src.shwethe_personal.form_personal.views import view as shwethe_personal_info

from fastapi import FastAPI, Depends, <PERSON>er, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    shwethe_personal_info.router,
    prefix="/shwethe_personal/api/v1/personal",
    tags=["shwethe_personal"],
    responses={404: {"message": "Not found"}},
)


