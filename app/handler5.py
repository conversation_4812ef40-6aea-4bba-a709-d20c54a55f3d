
from src.shwethe_d_check import main as api_main
from src.shwethe_d_check.o2o import main as o2o_api_main


from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    api_main.router,
    prefix="/shwethe_d_check/api/v1/d_check",
    tags=["d_check"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    o2o_api_main.router,
    prefix="/shwethe_d_check/api/v1/o2o",
    tags=["o2o"],
    responses={404: {"message": "Not found"}},
)







