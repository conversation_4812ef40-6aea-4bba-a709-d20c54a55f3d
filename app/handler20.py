
from src.test_nern.src.views import view as test_nern_info
# from src.test_nern.src_carActive.views import view as test_nern_info_2

from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    test_nern_info.router,
    prefix="/test_nern/api/v1/form",
    tags=["test_nern"],
    responses={404: {"message": "Not found"}},
)

# app.include_router(
#     test_nern_info_2.router,
#     prefix="/test_nern_carActive/api/v1/form",
#     tags=["test_nern_carActive"],
#     responses={404: {"message": "Not found"}},
# )
