
from src.form_application.form_personal.views import view as form_application_info

from fastapi import FastAPI, <PERSON>pen<PERSON>, <PERSON>er, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    form_application_info.router,
    prefix="/form_application/api/v1/form",
    tags=["form_application"],
    responses={404: {"message": "Not found"}},
)


