
from src.shwethe_goods.cliend.goods_category import main as goods_category
from src.shwethe_goods.cliend.goods_category import main2 as goods_category2
from src.shwethe_goods.cliend.goods_category import main3 as goods_category3


from src.shwethe_goods.cliend.goods_category.view import view as goods_category_view


from src.shwethe_goods.cliend.goods_attr_selector import main as goods_attr_selector

from src.shwethe_goods.cliend.list_data import main as list_data
from src.shwethe_goods.cliend.brand_data import main as brand_data

from src.shwethe_goods.goodsList.views import view as goodsList





from src.search import main as search

from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app

app.include_router(
    goods_category.router,
    prefix="/shwethe_goods/api/v1/cliend/goods_category",
    tags=["cliend_goods_category"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    goods_category_view.router,
    prefix="/shwethe_goods/api/v1/cliend/goods_category_new",
    tags=["cliend_goods_category_new"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    goods_category2.router,
    prefix="/shwethe_goods/api/v2/cliend/goods_category",
    tags=["cliend_goods_category2"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    goods_category3.router,
    prefix="/shwethe_goods/api/v3/cliend/goods_category",
    tags=["cliend_goods_category3"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    search.router,
    prefix="/shwethe_goods/api/v1/cliend/search",
    tags=["search_goods"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    list_data.router,
    prefix="/shwethe_goods/api/v1/cliend/list_data",
    tags=["list_data_goods"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    brand_data.router,
    prefix="/shwethe_goods/api/v1/cliend/brand_data",
    tags=["brand_data"],
    responses={404: {"message": "Not found"}},
)



app.include_router(
    goods_attr_selector.router,
    prefix="/shwethe_goods/api/v1/cliend/goods_attr_selector",
    tags=["goods_attr_selector"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    goodsList.router,
    prefix="/shwethe_goods/api/v1/goodsList",
    tags=["goodsList"],
    responses={404: {"message": "Not found"}},
)
