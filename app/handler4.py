
from src.search import main as search
from src.shwethe_delivery.cliend import main as shwethe_delivery_cliend
from src.shwethe_delivery.cliend import main2 as shwethe_delivery_cliend2
from src.shwethe_delivery.cliend import main3 as shwethe_delivery_cliend3
from src.shwethe_delivery.recipient import main as shwethe_delivery_recipient
from src.shwethe_delivery.manager import main as shwethe_delivery_manager
from src.shwethe_delivery.deliveryGoods.views import view as shwethe_deliveryGoods
from src.shwethe_delivery.cliend_orm.views import view as shwethe_deliveryGoods_cliend_orm







from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    search.router,
    prefix="/shwethe_delivery/api/v1/cliend/search",
    tags=["shwethe_delivery_search_goods"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    shwethe_delivery_cliend.router,
    prefix="/shwethe_delivery/api/v1/cliend/shwethe_delivery_cliend",
    tags=["shwethe_delivery_shwethe_delivery_cliend"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    shwethe_delivery_cliend2.router,
    prefix="/shwethe_delivery/api/v2/cliend/shwethe_delivery_cliend",
    tags=["shwethe_delivery_shwethe_delivery_cliend"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    shwethe_delivery_cliend3.router,
    prefix="/shwethe_delivery/api/v3/cliend/shwethe_delivery_cliend",
    tags=["shwethe_delivery_shwethe_delivery_cliend"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    shwethe_delivery_recipient.router,
    prefix="/shwethe_delivery/api/v1/cliend/shwethe_delivery_recipient",
    tags=["shwethe_delivery_shwethe_delivery_recipient"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    shwethe_delivery_manager.router,
    prefix="/shwethe_delivery/api/v1/cliend/shwethe_delivery_manager",
    tags=["shwethe_delivery_manager"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    shwethe_deliveryGoods.router,
    prefix="/shwethe_delivery/api/v1/cliend/shwethe_deliveryGoods",
    tags=["shwethe_deliveryGoods"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    shwethe_deliveryGoods_cliend_orm.router,
    prefix="/shwethe_delivery/api/v1/cliend/shwethe_deliveryGoods_cliend_orm",
    tags=["shwethe_deliveryGoods"],
    responses={404: {"message": "Not found"}},
)





