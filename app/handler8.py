
from src.order_in_weight.product_weight import main as product_weight_v1
from src.order_in_weight.product_weight import main2 as product_weight_v2

from src.order_in_weight.truck_load_weight import main as truck_load_weight_v1


from src.order_in_tax_refund.tax_refund_product import main as tax_refund_product_v1


from src.order_in_cargo_category.cargo_info_s import main as cargo_info_s_v1


from src.order_in_price.o_price import main as o_price_v1


from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    product_weight_v1.router,
    prefix="/order_in_weight/api/v1/product_weight",
    tags=["order_in_weight"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    product_weight_v2.router,
    prefix="/order_in_weight/api/v2/product_weight",
    tags=["order_in_weight"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    truck_load_weight_v1.router,
    prefix="/order_in_weight/api/v1/truck_load_weight",
    tags=["order_in_weight"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    tax_refund_product_v1.router,
    prefix="/order_in_tax_refund/api/v1/tax_refund_product",
    tags=["order_in_tax_refund"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    cargo_info_s_v1.router,
    prefix="/order_in_cargo_category/api/v1/cargo_info_s",
    tags=["order_in_cargo_category"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    o_price_v1.router,
    prefix="/order_in_price/api/v1/o_price",
    tags=["order_in_price"],    
    responses={404: {"message": "Not found"}},
)
