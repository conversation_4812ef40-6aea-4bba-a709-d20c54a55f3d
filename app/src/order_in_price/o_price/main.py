from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie,generate_datetime_selie_v1
from typing import List, Optional
import json
import requests
import pandas as pd
import time

router = APIRouter()


# 创建类 POST传参
class Item(BaseModel):
    product_in_tb_id: int
    product_price_y: str

@router.post("/input_o_price_today", status_code = 200)
def input_o_price_today(item: Item):
    print("POST")
    print(item.product_in_tb_id)

    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    try:

        print(item)

        import datetime
        import pytz

        insert_data_s_cursor = psycopg2_conn_insert_data_s.cursor()

        def update_data():
            
            sql_update  = " UPDATE product_in SET product_price_y = " + str(item.product_price_y) + " " \
                          " WHERE auto_id = " + str(item.product_in_tb_id) + " "
            insert_data_s_cursor.execute(sql_update)
            psycopg2_conn_insert_data_s.commit()
            print("update success")

        # insert data function
        update_data()

        return "Success!"

    finally:
        psycopg2_conn_insert_data_s.close()
        print("close")



