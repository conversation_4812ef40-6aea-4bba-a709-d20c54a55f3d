from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie,generate_datetime_selie_v1
from typing import List, Optional
import json
import requests
import pandas as pd
import time

router = APIRouter()


@router.get("/product_weight/{text}/", status_code = 200)
def order_s_insert_list(text: str):
    import src.Connect.postgresql_connect_shwethedb as psycopg2_conn_db_shwethedb
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_db_shwethedb)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect_shwethedb import psycopg2_conn_db_shwethedb
    from src.Connect.https_connect import mongodb_data_api

    try:

        params = {'text':text}

        product_search_text = requests.get(mongodb_data_api + '/api/v2/search/product_search_text',params=params)
        product_search_text_api = pd.DataFrame(product_search_text.json())

        data_list = product_search_text_api['product_id'].to_list()
        tuples = tuple(data_list)

        # 换成缅甸时间
        product_weight_info_tb = pd.read_sql("SELECT ((datetime AT TIME ZONE 'UTC') AT TIME ZONE 'MMT') AS datetime,product_id,product_weight from product_weight_info where product_id in %s", psycopg2_conn_db_shwethedb,params=(tuples,))
        
        # 排序：同一商品同一天中最后输入的数据
        product_weight_info_tb1_1 = product_weight_info_tb.sort_values(by=['product_id','datetime'],ascending=[False,False])

        # 删除数据
        product_weight_info_tb1_2 = product_weight_info_tb1_1.drop_duplicates(subset=['product_id'], keep='first')
        
        # convert datetime64[ns] to str
        product_weight_info_tb1_2['datetime'] = product_weight_info_tb1_2['datetime'].astype('str')

        data = product_weight_info_tb1_2[['product_id']].to_json(orient='records')
        data = json.loads(data)

        product_list = requests.get(mongodb_data_api + '/api/v2/search/product_list_id',json={"data_api": data})
        product_list_api = pd.DataFrame(product_list.json())

        product_weight_info_tb2 = pd.merge(product_list_api, product_weight_info_tb1_2, how='right', on=['product_id'], 
                                   left_index=False,right_index=False, sort=True)

        A1001 = product_weight_info_tb2.to_json(orient='records')

        return json.loads(A1001)

    # except:
    #     print("输入错误！没有该数据！")
    #     return "Error"

    finally:
        psycopg2_conn_db_shwethedb.close()



