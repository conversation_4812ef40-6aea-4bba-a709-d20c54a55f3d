from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie,generate_datetime_selie_v1
from typing import List, Optional
import json
import requests
import pandas as pd
import time

router = APIRouter()


@router.get("/product_weight/{text}/", status_code = 200)
def order_s_insert_list(text: str):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api

    try:

        params = {'text':text}

        product_search_text = requests.get(mongodb_data_api + '/api/v2/search/product_search_text',params=params)
        product_search_text_api = pd.DataFrame(product_search_text.json())

        data_list = product_search_text_api['product_id'].to_list()
        tuples = tuple(data_list)

        # 换成缅甸时间
        product_weight_info_tb = pd.read_sql("SELECT ((shu_ri_qi AT TIME ZONE 'UTC') AT TIME ZONE 'MMT') AS shu_ri_qi,product_id,product_weight,ri_qi,jin_huo_bian from product_weight_info where product_id in %s", psycopg2_conn_insert_data_s,params=(tuples,))
        
        # 排序：同一商品同一天中最后输入的数据
        product_weight_info_tb1_1 = product_weight_info_tb.sort_values(by=['product_id','shu_ri_qi'],ascending=[False,False]).drop_duplicates(
                                    subset=['product_id'], keep='first')
        
        # convert datetime64[ns] to str
        product_weight_info_tb1_1['shu_ri_qi'] = product_weight_info_tb1_1['shu_ri_qi'].astype('str')
        product_weight_info_tb1_1['ri_qi'] = product_weight_info_tb1_1['ri_qi'].astype('str')

        data = product_weight_info_tb1_1[['product_id']].to_json(orient='records')
        data = json.loads(data)

        product_list = requests.get(mongodb_data_api + '/api/v2/search/product_list_id',json={"data_api": data})
        product_list_api = pd.DataFrame(product_list.json())

        product_weight_info_tb2 = pd.merge(product_list_api, product_weight_info_tb1_1, how='right', on=['product_id'], 
                                   left_index=False,right_index=False, sort=True)

        product_list = requests.get(mongodb_data_api + '/api/v2/search/product_list_id',json={"data_api": data})
        product_list_api = pd.DataFrame(product_list.json())

        A1001 = product_weight_info_tb2.to_json(orient='records')

        return json.loads(A1001)

    # except:
    #     print("输入错误！没有该数据！")
    #     return "Error"

    finally:
        psycopg2_conn_insert_data_s.close()

# 创建类 POST传参
class Item(BaseModel):
    id: int
    # idname: str
    # mm_name: str
    # d_name: str
    weight: str
    ri_qi: str
    jin_huo_bian: int

@router.post("/product_weight", status_code = 200)
def product_weight(item: Item):
    print("POST")
    print(item.id)

    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    try:

        print(item)

        import datetime
        import pytz

        insert_data_s_cursor = psycopg2_conn_insert_data_s.cursor()

        # def insert_data(data):
            
        #     tuples = [tuple(x) for x in data.values]
            
        #     sql_insert  = """ insert into product_weight_info(shu_ri_qi,product_id,product_weight,ri_qi,jin_huo_bian) values (%s,%s,%s,%s,%s)  """
        #     insert_data_s_cursor.executemany(sql_insert,tuples)
        #     psycopg2_conn_insert_data_s.commit()
        #     print("insert success")

        def insert_data():
        
            tz = pytz.timezone('UTC')
            dt = datetime.datetime.now(tz=tz).strftime('%Y-%m-%d %H:%M:%S')
            print(dt)
            
            sql_insert  = " insert into product_weight_info(shu_ri_qi,product_id,product_weight,ri_qi,jin_huo_bian) values ( " \
                          " '" + str(dt) + "','" + str(item.id) + "','" + str(item.weight) + "','" + str(item.ri_qi) + "','" + str(item.jin_huo_bian) + "') "
            insert_data_s_cursor.execute(sql_insert)
            psycopg2_conn_insert_data_s.commit()
            print("insert success")

        # insert data function
        insert_data()

        return "Success!"

    finally:
        psycopg2_conn_insert_data_s.close()
        print("close")



