from typing import Dict
from xmlrpc.client import DateTime

from click import option
from pydantic.types import Optional, List
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel, Column, TEXT, text, JSON
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id



class item_insert_test_base(SQLModel):
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'carItemChange'::text || lpad(nextval('item_insert_auto_id_seq'::regclass)::text, 8, '0'::text)")))
    datetime: Optional[datetime]
    warehouse_id: Optional[int]
    che_liang_id: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    product_id: Optional[int]
    qty: Optional[float]
    price: Optional[int]
    bi_zhi: Optional[int]
    group_id: Optional[str]
    status: Optional[str]
    used_type: Optional[str]
    old_qty: Optional[float]
    amount_in_de: Optional[float]

class item_insert_test(item_insert_test_base, table=True):
    __tablename__ = "item_insert_test"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'carItemChange'::text || lpad(nextval('item_insert_auto_id_seq'::regclass)::text, 8, '0'::text)")))
    datetime: Optional[datetime]
    warehouse_id: Optional[int]
    che_liang_id: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    product_id: Optional[int]
    qty: Optional[float]
    price: Optional[int]
    bi_zhi: Optional[int]
    group_id: Optional[str]
    status: Optional[str]
    used_type: Optional[str]
    old_qty: Optional[float]
    amount_in_de: Optional[float]


class item_insert_test_read(BaseModel):
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'CIC'::text || lpad(nextval('item_insert_auto_id_seq'::regclass)::text, 8, '0'::text)")))
    datetime: Optional[datetime]
    warehouse_id: Optional[int]
    che_liang_id: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    product_id: Optional[int]
    qty: Optional[float]
    price: Optional[int]
    bi_zhi: Optional[int]
    group_id: Optional[str]
    status: Optional[str]
    used_type: Optional[str]
    old_qty: Optional[float]
    amount_in_de: Optional[float]

class item_insert_test_post(BaseModel):
    datetime: Optional[datetime]
    warehouse_id: Optional[int]
    che_liang_id: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    product_id: Optional[int]
    qty: Optional[float]
    price: Optional[int]
    bi_zhi: Optional[int]
    group_id: Optional[str]
    status: Optional[str]
    used_type: Optional[str]
    old_qty: Optional[float]
    amount_in_de: Optional[float]


