from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.test_nern.database import get_session
from src.test_nern.src.crud.crud import (
    getCarAtive100,
    update_item_insert100
)
from src.test_nern.src.models.models import item_insert_test, item_insert_test_post
 
from fastapi import APIRouter, Depends, Query, Body

router = APIRouter()


@router.get("/getCarAtive")
def getCarAtive(db: Session = Depends(get_session)):
    return getCarAtive100(db=db)


@router.put("/updateRealtimeQty")
def updateRealtimeQty(db: Session = Depends(get_session)):
    return update_item_insert100(db=db)



