{"cells": [{"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-01-05 06:54:08,896 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-01-05 06:54:08,930 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.datetime > %(datetime_1)s ORDER BY item_insert_test.datetime DESC\n", "2024-01-05 06:54:08,944 INFO sqlalchemy.engine.Engine [cached since 5584s ago] {'datetime_1': datetime.date(2023, 12, 24)}\n", "2024-01-05 06:54:09,042 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>auto_id</th>\n", "      <th>datetime</th>\n", "      <th>product_id</th>\n", "      <th>qty</th>\n", "      <th>old_qty</th>\n", "      <th>amount_in_de</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>579</td>\n", "      <td>2024-01-02 16:33:20.182779+06:30</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>578</td>\n", "      <td>2024-01-02 16:32:27.639376+06:30</td>\n", "      <td>801</td>\n", "      <td>5.0</td>\n", "      <td>10.0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>577</td>\n", "      <td>2023-12-31 16:37:34.180036+06:30</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>10.0</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>576</td>\n", "      <td>2023-12-30 16:50:55.144481+06:30</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>5.0</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>575</td>\n", "      <td>2023-12-29 16:38:32.252846+06:30</td>\n", "      <td>9463</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>574</td>\n", "      <td>2023-12-27 16:42:44.078889+06:30</td>\n", "      <td>53776</td>\n", "      <td>10.0</td>\n", "      <td>36.0</td>\n", "      <td>26.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>573</td>\n", "      <td>2023-12-27 16:41:45.494288+06:30</td>\n", "      <td>53776</td>\n", "      <td>3.0</td>\n", "      <td>36.0</td>\n", "      <td>33.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>572</td>\n", "      <td>2023-12-27 16:39:44.281248+06:30</td>\n", "      <td>53776</td>\n", "      <td>6.0</td>\n", "      <td>36.0</td>\n", "      <td>30.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>571</td>\n", "      <td>2023-12-26 16:28:34.962427+06:30</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>570</td>\n", "      <td>2023-12-25 16:11:26.320870+06:30</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>569</td>\n", "      <td>2023-12-25 16:10:32.111632+06:30</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>7.0</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>568</td>\n", "      <td>2023-12-24 15:37:41.156083+06:30</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>12.0</td>\n", "      <td>11.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>567</td>\n", "      <td>2023-12-24 15:36:51.887096+06:30</td>\n", "      <td>46805</td>\n", "      <td>2.0</td>\n", "      <td>10.0</td>\n", "      <td>8.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    auto_id                         datetime  product_id   qty  old_qty   \n", "0       579 2024-01-02 16:33:20.182779+06:30         801   1.0     10.0  \\\n", "1       578 2024-01-02 16:32:27.639376+06:30         801   5.0     10.0   \n", "2       577 2023-12-31 16:37:34.180036+06:30         801   1.0     10.0   \n", "3       576 2023-12-30 16:50:55.144481+06:30       61350   1.0      5.0   \n", "4       575 2023-12-29 16:38:32.252846+06:30        9463   1.0      1.0   \n", "5       574 2023-12-27 16:42:44.078889+06:30       53776  10.0     36.0   \n", "6       573 2023-12-27 16:41:45.494288+06:30       53776   3.0     36.0   \n", "7       572 2023-12-27 16:39:44.281248+06:30       53776   6.0     36.0   \n", "8       571 2023-12-26 16:28:34.962427+06:30       61350   1.0      6.0   \n", "9       570 2023-12-25 16:11:26.320870+06:30         801   1.0     11.0   \n", "10      569 2023-12-25 16:10:32.111632+06:30       61350   1.0      7.0   \n", "11      568 2023-12-24 15:37:41.156083+06:30         801   1.0     12.0   \n", "12      567 2023-12-24 15:36:51.887096+06:30       46805   2.0     10.0   \n", "\n", "    amount_in_de  \n", "0            9.0  \n", "1            5.0  \n", "2            9.0  \n", "3            4.0  \n", "4            0.0  \n", "5           26.0  \n", "6           33.0  \n", "7           30.0  \n", "8            5.0  \n", "9           10.0  \n", "10           6.0  \n", "11          11.0  \n", "12           8.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2024-01-05 06:54:09,089 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-01-05 06:54:09,093 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.datetime > %(datetime_1)s ORDER BY item_insert_test.datetime DESC\n", "2024-01-05 06:54:09,095 INFO sqlalchemy.engine.Engine [cached since 5584s ago] {'datetime_1': datetime.date(2023, 12, 24)}\n", "2024-01-05 06:54:09,119 INFO sqlalchemy.engine.Engine ROLLBACK\n", "2024-01-05 06:54:09,161 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-01-05 06:54:09,167 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,170 INFO sqlalchemy.engine.Engine [cached since 5094s ago] {'auto_id_1': 568}\n", "2024-01-05 06:54:09,184 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,189 INFO sqlalchemy.engine.Engine [cached since 5094s ago] {'auto_id_1': 570}\n", "2024-01-05 06:54:09,198 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,201 INFO sqlalchemy.engine.Engine [cached since 5094s ago] {'auto_id_1': 577}\n", "2024-01-05 06:54:09,210 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,213 INFO sqlalchemy.engine.Engine [cached since 5094s ago] {'auto_id_1': 578}\n", "2024-01-05 06:54:09,221 INFO sqlalchemy.engine.Engine UPDATE item_insert_test SET old_qty=%(old_qty)s, amount_in_de=%(amount_in_de)s WHERE item_insert_test.auto_id = %(item_insert_test_auto_id)s\n", "2024-01-05 06:54:09,229 INFO sqlalchemy.engine.Engine [cached since 5094s ago] {'old_qty': 9.0, 'amount_in_de': 4.0, 'item_insert_test_auto_id': 578}\n", "2024-01-05 06:54:09,261 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,263 INFO sqlalchemy.engine.Engine [cached since 5094s ago] {'auto_id_1': 579}\n", "2024-01-05 06:54:09,279 INFO sqlalchemy.engine.Engine UPDATE item_insert_test SET old_qty=%(old_qty)s, amount_in_de=%(amount_in_de)s WHERE item_insert_test.auto_id = %(item_insert_test_auto_id)s\n", "2024-01-05 06:54:09,283 INFO sqlalchemy.engine.Engine [cached since 5094s ago] {'old_qty': 4.0, 'amount_in_de': 3.0, 'item_insert_test_auto_id': 579}\n", "2024-01-05 06:54:09,294 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,300 INFO sqlalchemy.engine.Engine [cached since 5094s ago] {'auto_id_1': 575}\n", "2024-01-05 06:54:09,329 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,337 INFO sqlalchemy.engine.Engine [cached since 5094s ago] {'auto_id_1': 567}\n", "2024-01-05 06:54:09,361 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,367 INFO sqlalchemy.engine.Engine [cached since 5094s ago] {'auto_id_1': 572}\n", "2024-01-05 06:54:09,408 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,411 INFO sqlalchemy.engine.Engine [cached since 5095s ago] {'auto_id_1': 573}\n", "2024-01-05 06:54:09,425 INFO sqlalchemy.engine.Engine UPDATE item_insert_test SET old_qty=%(old_qty)s, amount_in_de=%(amount_in_de)s WHERE item_insert_test.auto_id = %(item_insert_test_auto_id)s\n", "2024-01-05 06:54:09,428 INFO sqlalchemy.engine.Engine [cached since 5095s ago] {'old_qty': 30.0, 'amount_in_de': 27.0, 'item_insert_test_auto_id': 573}\n", "2024-01-05 06:54:09,432 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,434 INFO sqlalchemy.engine.Engine [cached since 5095s ago] {'auto_id_1': 574}\n", "2024-01-05 06:54:09,443 INFO sqlalchemy.engine.Engine UPDATE item_insert_test SET old_qty=%(old_qty)s, amount_in_de=%(amount_in_de)s WHERE item_insert_test.auto_id = %(item_insert_test_auto_id)s\n", "2024-01-05 06:54:09,448 INFO sqlalchemy.engine.Engine [cached since 5095s ago] {'old_qty': 27.0, 'amount_in_de': 17.0, 'item_insert_test_auto_id': 574}\n", "2024-01-05 06:54:09,451 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,453 INFO sqlalchemy.engine.Engine [cached since 5095s ago] {'auto_id_1': 569}\n", "2024-01-05 06:54:09,461 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,470 INFO sqlalchemy.engine.Engine [cached since 5095s ago] {'auto_id_1': 571}\n", "2024-01-05 06:54:09,474 INFO sqlalchemy.engine.Engine SELECT item_insert_test.record_id, item_insert_test.datetime, item_insert_test.warehouse_id, item_insert_test.che_liang_id, item_insert_test.jia_yi_fang_a, item_insert_test.jia_yi_fang_b, item_insert_test.lei_a, item_insert_test.lei_b, item_insert_test.product_id, item_insert_test.qty, item_insert_test.price, item_insert_test.bi_zhi, item_insert_test.group_id, item_insert_test.status, item_insert_test.used_type, item_insert_test.old_qty, item_insert_test.amount_in_de, item_insert_test.auto_id \n", "FROM item_insert_test \n", "WHERE item_insert_test.auto_id = %(auto_id_1)s\n", "2024-01-05 06:54:09,476 INFO sqlalchemy.engine.Engine [cached since 5095s ago] {'auto_id_1': 576}\n", "2024-01-05 06:54:09,482 INFO sqlalchemy.engine.Engine COMMIT\n"]}], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "from src.test_nern.database import get_session\n", "from src.test_nern.src.models.models import item_insert_test\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "\n", "\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def dataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "def get_item_insert():\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        # GET TODAY DATA\n", "        week_ago = today - DT.<PERSON><PERSON><PERSON>(days=12)\n", "        heroesPersonal = db.exec(select(item_insert_test).where(item_insert_test.datetime > week_ago).order_by(item_insert_test.datetime.desc())).all()\n", "        mergeDF = dataframe(heroesPersonal, to_dict=False)\n", "        mergeDF = mergeDF[['auto_id', 'datetime', 'product_id', 'qty', 'old_qty', 'amount_in_de']]\n", "    return mergeDF\n", "\n", "\n", "\n", "# def update_item_insert():\n", "#     # Get the data from the item_insert table\n", "#     data = get_item_insert()\n", "\n", "#     # Group the data by product_id to process each product separately\n", "#     grouped_data = data.groupby(\"product_id\")\n", "\n", "#     with get_session_dependency() as db:\n", "#         for product_id, group in grouped_data:\n", "#             # Sort the group by datetime in ascending order\n", "#             sorted_group = group.sort_values(by=\"datetime\", ascending=True)\n", "\n", "#             # Initialize the initial values for old_qty and amount_in_de\n", "#             old_qty = sorted_group.iloc[0][\"old_qty\"]\n", "#             amount_in_de = old_qty\n", "\n", "#             # Variables to track the total quantity added after each row's datetime\n", "#             total_qty_added = 0\n", "\n", "#             for _, row in sorted_group.iterrows():\n", "#                 qty = row[\"qty\"]\n", "#                 datetime = row[\"datetime\"]\n", "\n", "#                 # Calculate the time difference between the current row and the last row\n", "#                 time_diff = (datetime - datetime_previous).total_seconds() / 3600  # Convert to hours\n", "\n", "#                 # Update old_qty and amount_in_de based on the total quantity added after each row's datetime\n", "#                 old_qty = old_qty + total_qty_added - qty\n", "#                 amount_in_de = old_qty\n", "\n", "#                 # Update the row with the new values\n", "#                 row[\"old_qty\"] = old_qty\n", "#                 row[\"amount_in_de\"] = amount_in_de\n", "\n", "#                 # Update the total quantity added after each row's datetime\n", "#                 total_qty_added += qty\n", "\n", "#                 # Get the auto_id and update the row in the database\n", "#                 auto_id = row[\"auto_id\"]\n", "#                 statement_item_insert = select(item_insert_test).where(item_insert_test.auto_id == auto_id)\n", "#                 result_item_insert = db.exec(statement_item_insert).first()\n", "#                 result_item_insert.old_qty = old_qty\n", "#                 result_item_insert.amount_in_de = amount_in_de\n", "#                 db.add(result_item_insert)\n", "\n", "#                 # Update the datetime_previous for the next iteration\n", "#                 datetime_previous = datetime\n", "\n", "#         # Commit the changes to the database\n", "#         db.commit()\n", "\n", "# update_item_insert()\n", "\n", "\n", "def update_item_insert():\n", "    # Get the data from the item_insert table\n", "    data = get_item_insert()\n", "\n", "    # Group the data by product_id to process each product separately\n", "    grouped_data = data.groupby(\"product_id\")\n", "\n", "    with get_session_dependency() as db:\n", "        for product_id, group in grouped_data:\n", "            # Sort the group by datetime in ascending order\n", "            sorted_group = group.sort_values(by=\"datetime\", ascending=True)\n", "\n", "            # Initialize the initial values for old_qty and amount_in_de\n", "            old_qty = None\n", "            amount_in_de = None\n", "\n", "            for _, row in sorted_group.iterrows():\n", "                qty = row[\"qty\"]\n", "\n", "                # Check if old_qty and amount_in_de need to be updated\n", "                if old_qty is not None and amount_in_de is not None:\n", "                    old_qty = amount_in_de\n", "                    amount_in_de = old_qty - qty\n", "                else:\n", "                    old_qty = row[\"old_qty\"]\n", "                    amount_in_de = old_qty - qty\n", "\n", "                # Update the row with the new values\n", "                row[\"old_qty\"] = old_qty\n", "                row[\"amount_in_de\"] = amount_in_de\n", "\n", "                # Get the auto_id and update the row in the database\n", "                auto_id = row[\"auto_id\"]\n", "                statement_item_insert = select(item_insert_test).where(item_insert_test.auto_id == auto_id)\n", "                result_item_insert = db.exec(statement_item_insert).first()\n", "                result_item_insert.old_qty = old_qty\n", "                result_item_insert.amount_in_de = amount_in_de\n", "                db.add(result_item_insert)\n", "\n", "        # Commit the changes to the database\n", "        db.commit()\n", "\n", "display(get_item_insert())\n", "update_item_insert()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}