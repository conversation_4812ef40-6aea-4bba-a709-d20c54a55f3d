from sqlite3 import dbapi2
import requests
from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,and_
from typing import List, Optional

from datetime import datetime, timedelta
from src.test_nern.database import get_session
from helper import generate_datetime_id
from src.test_nern.src.models.models import item_insert_test
import json
import pandas as pd
import calendar
from src.time_zone.time_zone_function import get_datetime
from helper import generate_id
from sqlalchemy import and_, or_, not_, func, literal_column, desc
from fastapi import APIRouter, Depends, Query, Body
# from sqlmodel.sql.expression import literal_column
from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api

pd.set_option('display.max_columns', None)

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

def dataframe(sqlModel, to_dict=False):
    records = [i.dict() for i in sqlModel]
    mergeDF = pd.DataFrame.from_records(records).fillna(0)
    if to_dict:
        mergeDF = mergeDF.to_dict("records")
    return mergeDF

def getCarAtive100(db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(item_insert_test)).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records).fillna(0)

        mergeDF = df.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def get_item_insert100(db: Session = Depends(get_session)):

    import datetime as DT
    today = DT.date.today()
    # GET TODAY DATA
    week_ago = today - DT.timedelta(days=12)
    heroesPersonal = db.exec(select(item_insert_test).where(item_insert_test.datetime > week_ago)).all()
    mergeDF = dataframe(heroesPersonal, to_dict=False)
    # print(mergeDF)
    mergeDF = mergeDF[['auto_id', 'datetime', 'product_id', 'qty', 'old_qty', 'amount_in_de']]
    return mergeDF


def update_item_insert100(db: Session = Depends(get_session)):
    # Get the data from the item_insert table
    data = get_item_insert100(db=db)

    # Group the data by product_id to process each product separately
    grouped_data = data.groupby("product_id")

    for product_id, group in grouped_data:
        # Sort the group by datetime in ascending order
        sorted_group = group.sort_values(by="datetime", ascending=True)

        # Initialize the initial values for old_qty and amount_in_de
        old_qty = None
        amount_in_de = None

        for _, row in sorted_group.iterrows():
            qty = row["qty"]

            # Check if old_qty and amount_in_de need to be updated
            if old_qty is not None and amount_in_de is not None:
                old_qty = amount_in_de
                amount_in_de = old_qty - qty
            else:
                old_qty = row["old_qty"]
                amount_in_de = old_qty - qty

            # Update the row with the new values
            row["old_qty"] = old_qty
            row["amount_in_de"] = amount_in_de

            # Get the auto_id and update the row in the database
            auto_id = row["auto_id"]
            statement_item_insert = select(item_insert_test).where(item_insert_test.auto_id == auto_id)
            result_item_insert = db.exec(statement_item_insert).first()
            result_item_insert.old_qty = old_qty
            result_item_insert.amount_in_de = amount_in_de
            db.add(result_item_insert)

    # Commit the changes to the database
    db.commit()

    return "data"