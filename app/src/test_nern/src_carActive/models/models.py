from typing import Dict
from xmlrpc.client import DateTime

from click import option
from pydantic.types import Optional, List
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel, Column, JSON
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id


class vehicle_base(SQLModel):
    jia_yi_id: Optional[int]
    type: Optional[str]
    fen_dian_id: Optional[int]

class vehicle(vehicle_base, table=True):
    __tablename__ = "vehicle"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    jia_yi_id: Optional[int]
    type: Optional[str]
    fen_dian_id: Optional[int]

class vehicle_read(BaseModel):
    auto_id: Optional[int]
    jia_yi_id: Optional[int]
    type: Optional[str]
    fen_dian_id: Optional[int]

class vehicle_post(BaseModel):
    jia_yi_id: Optional[int]
    type: Optional[str]
    fen_dian_id: Optional[int]

# ------------------------------------

class vehicle_insert_base(SQLModel):
    jia_yi_id_vehicle: Optional[int]
    jia_yi_id_driver: Optional[int]
    image: Optional[str]
    datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    status: Optional[str]
    where: Optional[str]
    latitude: Optional[float]
    longitude: Optional[float]
    image_victus: Optional[str]
    image_victus_back: Optional[str]
    details: dict = Field(sa_column=Column(JSONB), default={})

class vehicle_insert(vehicle_insert_base, table=True):
    __tablename__ = "vehicle_insert"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    jia_yi_id_vehicle: Optional[int]
    jia_yi_id_driver: Optional[int]
    image: Optional[str]
    datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    status: Optional[str]
    where: Optional[str]
    latitude: Optional[float]
    longitude: Optional[float]
    image_victus: Optional[str]
    image_victus_back: Optional[str]
    details: dict = Field(sa_column=Column(JSONB), default={})

class vehicle_insert_read(BaseModel):
    auto_id: Optional[int]
    jia_yi_id_vehicle: Optional[int]
    jia_yi_id_driver: Optional[int]
    image: Optional[str]
    datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    status: Optional[str]
    where: Optional[str]
    latitude: Optional[float]
    longitude: Optional[float]
    image_victus: Optional[str]
    image_victus_back: Optional[str]
    details: dict = Field(sa_column=Column(JSONB), default={})

class vehicle_insert_post(BaseModel):
    jia_yi_id_vehicle: Optional[int]
    jia_yi_id_driver: Optional[int]
    image: Optional[str]
    datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    status: Optional[str]
    where: Optional[str]
    latitude: Optional[float]
    longitude: Optional[float]
    image_victus: Optional[str]
    image_victus_back: Optional[str]
    details: dict = Field(sa_column=Column(JSONB), default={})


# ------------------------------------

class expirevitus_base(SQLModel):
    type: Optional[str]
    detail: dict = Field(sa_column=Column(JSONB), default=[])
    datetime: Optional[datetime]
    period: Optional[int]

class expirevitus(expirevitus_base, table=True):
    __tablename__ = "expirevitus"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    type: Optional[str]
    detail: dict = Field(sa_column=Column(JSONB), default=[])
    datetime: Optional[datetime]
    period: Optional[int]

# class insertDepart_base(SQLModel):
#     shu_riqi_datetime: Optional[datetime]
#     fen_dian_id: Optional[int]
#     jia_yi_id: Optional[int]
#     department_id: Optional[int]

# class insertDepart(insertDepart_base, table=True):
#     __tablename__ = "insertDepart"
#     auto_id: Optional[int] = Field(default=None, primary_key=True)
#     shu_riqi_datetime: Optional[datetime]
#     fen_dian_id: Optional[int]
#     jia_yi_id: Optional[int]
#     department_id: Optional[int]

# class insertDepart_read(BaseModel):
#     auto_id: Optional[int]
#     shu_riqi_datetime: Optional[datetime]
#     fen_dian_id: Optional[int]
#     jia_yi_id: Optional[int]
#     department_id: Optional[int]

# class insertDepart_post(BaseModel):
#     shu_riqi_datetime: Optional[datetime]
#     fen_dian_id: Optional[int]
#     jia_yi_id: Optional[int]
#     department_id: Optional[int]



# 1 车辆类型表（VehicleTypes）

class vehicle_types_base(SQLModel):
    type_name: Optional[str]

class vehicle_types(vehicle_types_base, table=True):
    __tablename__ = "vehicle_types"
    type_id: Optional[int] = Field(default=None, primary_key=True)
    type_name: Optional[str]

class vehicle_types_post(BaseModel):
    type_name: Optional[str]

class create_vehicle_types_model(BaseModel):
    jia_yi_id : Optional[int]

# 2 车辆类型表（VehicleTypes）

class vehicles_base(SQLModel):
    che_liang_id: Optional[int]
    license_plate: Optional[str]
    type_id: Optional[int]
    brand: Optional[str]
    model: Optional[str]
    year: Optional[str]
    color: Optional[str]
    owner_id: Optional[int]
    images: Optional[dict]

class vehicles(vehicles_base, table=True):
    __tablename__ = "vehicles"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    che_liang_id: Optional[int]
    license_plate: Optional[str]
    type_id: Optional[int]
    brand: Optional[str]
    model: Optional[str]
    year: Optional[str]
    color: Optional[str]
    owner_id: Optional[int]
    images: dict = Field(sa_column=Column(JSONB), default={})


class vehicles_post(BaseModel):
    che_liang_id: Optional[int]
    license_plate: Optional[str]
    type_id: Optional[int]
    brand: Optional[str]
    model: Optional[str]
    year: Optional[str]
    color: Optional[str]
    owner_id: Optional[int]
    images: Optional[dict]


# 3 文件类型表（DocumentTypes）

class document_types_base(SQLModel):
    document_types_name: Optional[str]
    document_types_description: Optional[str]

class document_types(document_types_base, table=True):
    __tablename__ = "document_types"
    document_types_id: Optional[int] = Field(default=None, primary_key=True)
    document_types_name: Optional[str]
    document_types_description: Optional[str]

class document_types_post(BaseModel):
    document_types_name: str
    document_types_description: str


# 4 文件类型适用车辆类型关联表（DocumentTypeVehicleTypeRelations）

class document_type_vehicle_type_relations_base(SQLModel):
    document_type_id: Optional[int]
    applicable_vehicle_type_id: Optional[int]

class document_type_vehicle_type_relations(document_type_vehicle_type_relations_base, table=True):
    __tablename__ = "document_type_vehicle_type_relations"
    relations_id: Optional[int] = Field(default=None, primary_key=True)
    document_type_id: Optional[int]
    applicable_vehicle_type_id: Optional[int]




class document_type_vehicle_type_relations_post(BaseModel):
    document_type_id: int
    applicable_vehicle_type_id: int

# 5 车辆文件表（VehicleDocuments）

class vehicle_documents_base(SQLModel):
    che_liang_id: Optional[int]
    document_types_id: Optional[int]
    document_number: Optional[str]
    start_date: Optional[datetime]
    expiration_date: Optional[datetime]

class vehicle_documents(vehicle_documents_base, table=True):
    __tablename__ = "vehicle_documents"
    document_id: Optional[int] = Field(default=None, primary_key=True)
    che_liang_id: Optional[int]
    document_types_id: Optional[int]
    document_number: Optional[str]
    start_date: Optional[datetime]
    expiration_date: Optional[datetime]
    images: dict = Field(sa_column=Column(JSONB), default={})

class vehicle_documents_post(BaseModel):
    che_liang_id: int
    document_types_id: int
    document_number: str
    start_date: datetime
    expiration_date: datetime
    images: dict


# 6 车辆主人信息表（VehicleOwners）

class vehicle_owners_base(SQLModel):
    jia_yi_id: Optional[int]
    contact : Optional[str]
    address : Optional[str]

class vehicle_owners(vehicle_owners_base, table=True):
    __tablename__ = "vehicle_owners"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    jia_yi_id: Optional[int]
    contact : Optional[str]
    address : Optional[str]


class vehicle_owners_post(BaseModel):
    jia_yi_id: int
    contact : Optional[str]
    address : Optional[str]


# 7 车辆主人变更记录表（VehicleOwnershipChanges）


class vehicle_ownership_changes_base(SQLModel):
    vehicle_id: Optional[int]
    newOwner_id: Optional[int]
    oldOwner_id: Optional[int]
    change_date: Optional[datetime]



class vehicle_ownership_changes(vehicle_ownership_changes_base, table=True):
    __tablename__ = "vehicle_ownership_changes"
    change_id: Optional[int] = Field(default=None, primary_key=True)
    vehicle_id: Optional[int]
    newOwner_id: Optional[int]
    oldOwner_id: Optional[int]
    change_date: Optional[datetime]


# 8 车辆主人变更证明文件表（VehicleOwnershipChangeProofs）

class vehicle_ownership_change_proofs_base(SQLModel):
    change_id: Optional[int]
    file_url: Optional[int]

class vehicle_ownership_change_proofs(vehicle_ownership_change_proofs_base, table=True):
    __tablename__ = "vehicle_ownership_change_proofs"
    proof_id: Optional[int] = Field(default=None, primary_key=True)
    change_id: Optional[int]
    file_url: Optional[int]




# 文件类型适用车辆类型关联表（DocumentTypeVehicleTypeRelations）

class document_typeVehicle_type_relations_base(SQLModel):
    document_type_id: Optional[int]
    applicable_vehicle_type_id: Optional[str]

class document_typeVehicle_type_relations(document_typeVehicle_type_relations_base, table=True):
    __tablename__ = "document_typeVehicle_type_relations"
    relation_id: Optional[int] = Field(default=None, primary_key=True)
    document_type_id: Optional[int]
    applicable_vehicle_type_id: Optional[str]


# # 车辆主人变更记录表（VehicleOwnershipChanges）

# class vehicle_ownership_changes_base(SQLModel):
#     vehicle_id: Optional[int]
#     new_owner_id: Optional[int]
#     old_owner_id: Optional[int]
#     change_date: Optional[datetime]
#     proof_document_image: Optional[str]

# class vehicle_ownership_changes(vehicle_ownership_changes_base, table=True):
#     __tablename__ = "vehicle_ownership_changes"
#     change_id: Optional[int] = Field(default=None, primary_key=True)
#     vehicle_id: Optional[int]
#     new_owner_id: Optional[int]
#     old_owner_id: Optional[int]
#     change_date: Optional[datetime]
#     proof_document_image: Optional[str]
