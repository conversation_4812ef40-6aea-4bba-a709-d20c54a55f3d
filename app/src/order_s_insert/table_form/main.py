from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie,generate_datetime_selie_v1
from typing import List, Optional
import json
import requests
import pandas as pd
import time

router = APIRouter()


class pre_order_n_get_(BaseModel):
    order_s_insert_bill_id : str
    fen_dian_id : int
    jin_huo_bian : int
    car_round : int

@router.put("/car_round", status_code = 200)
def car_round(req_body : pre_order_n_get_ = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    datetime = str(generate_datetime())
    import json

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    order_s_insert_bill_id = FFF['order_s_insert_bill_id']
    fen_dian_id = FFF['fen_dian_id']
    jin_huo_bian = FFF['jin_huo_bian']
    car_round = FFF['car_round']

    def put_data(json_dums,order_s_insert_bill_id,jin_huo_bian,fen_dian_id):

        sql_insert = """
                        UPDATE order_s_insert
                        SET data_sub=data_sub  || %s ::jsonb
                        where order_s_insert_bill_id = %s and data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s' 
                        and data_sub ->> 'status' = 'wait_sucess' ;
                        """
        cur.execute(sql_insert, (json_dums,order_s_insert_bill_id,jin_huo_bian,fen_dian_id))

    def check_data(order_s_insert_bill_id):
        sql_insert = """
                    SELECT *
                    FROM order_s_insert 
                    where jia_fang = %s
                    """ % (order_s_insert_bill_id)

        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001

        if FF100001.empty:
            FF100001 = True
        else:
            FF100001 = False

        return FF100001

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()
    FD10001 = check_data(order_s_insert_bill_id)

    json_dums = json.dumps({'car_round' : car_round })

    if FD10001 == True:
        put_data(json_dums,order_s_insert_bill_id,jin_huo_bian,fen_dian_id)

    psycopg2_conn_insert_data_s.close()
    
    return "sucess"


@router.get("/order_s_list", status_code = 200)
def order_s_insert_list():
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api

    def check_api_data():
        sql_insert = """SELECT *  FROM order_s_insert where order_s_insert_bill_id is not null 
        and  data_sub ->> 'status' = 'wait_sucess' and  data_sub ->> 'row_type' = 'for_product' and datetime > current_date -30  """ 
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        GG100001 = pd.DataFrame(FF100001)
        # print("GG100001")
        # print(GG100001)
        GG100001['new_date_column'] = GG100001['datetime'].dt.date
        SD100001=GG100001.groupby(['new_date_column'])['order_s_insert_id'].max().reset_index()
        SD100001 = SD100001.sort_values(['order_s_insert_id'], ascending=[False]).head(2)
        # print("SD100001")
        # print(SD100001)
        GG100001 = GG100001[GG100001['new_date_column'].isin(SD100001['new_date_column'].to_list())]
        GG100002 = GG100001.to_dict('records')
        GG100003 = pd.json_normalize(GG100002)
        GG100003 = GG100003.rename(columns={"data_sub.car_round": "car_round","data_sub.fen_dian_id": "fen_dian_id","data_sub.jin_huo_bian": "jin_huo_bian"})

        return GG100003
    FF100001 = check_api_data()
    GG100001 = pd.DataFrame(FF100001)
    # GG100001 = pd.DataFrame(FF100001['data_detail'][0])

    # GG100001['product_id'] = GG100001['product_id'].astype(int)
    H1000001 = GG100001[['che_liang']]
    H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
    H3000001 = GG100001[['jia_fang']]
    H3000001 = H3000001.rename(columns={'jia_fang': 'jia_yi_id'})

    res_1 = json.loads(H1000001.to_json(orient='records'))

    res_2 = json.loads(H3000001.to_json(orient='records'))


    r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                     json={"data_api": res_1})

    r2 = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                     json={"data_api": res_2})


    FF100002 = FF100001.to_json(orient='records')

    GF10001 = pd.DataFrame(json.loads(r.json()))

    GF20001 = pd.DataFrame(json.loads(r2.json()))


    GG100009 = GF10001.merge(GG100001, left_on=['jia_yi_id'], right_on=['che_liang'], how='inner')
    GG100009 = GG100009.drop(columns=['che_liang'])
    GG100009

    GG100010 = GF20001.merge(GG100009, left_on=['jia_yi_id'], right_on=['jia_fang'], how='inner')
    GG100010 = GG100010.drop(columns=['jia_fang'])
    GG100010
    GG100010 = GG100010.rename(columns={'jia_yi_id_x': 'parner_id','jia_yi_idname_x': 'parner_idname','jia_yi_mm_name_x': 'parner_mmname'
    ,'jia_yi_id_y': 'car_id','jia_yi_idname_y': 'car_idname','car_mm_name_y': 'parner_mmname'})
    print(GG100010)
    GG100010['datetime'] = GG100010['datetime'].astype(str)
    GG100010 = GG100010.sort_values(['datetime'], ascending=[False])
    GG100010 = GG100010.to_json(orient='records')

    psycopg2_conn_insert_data_s.close()

    return GG100010

class che_liang_(BaseModel):
    parner_id : int
    car_id : int
    fen_dian_id : int
    jin_huo_bian : int
    comment : str


@router.post("/order_s_insert", status_code=200)
def order_s_insert(req_body : che_liang_ = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    import json
    ganarate_id_1 = str(generate_datetime_selie_v1())
    ganarate_id_2 = ganarate_id_1 + str(2)
    # print(ganarate_id)
    datetime = str(generate_datetime())

    def check_data(jia_fang,che_liang,fen_dian_id,jin_huo_bian):
        sql_insert = """
                    SELECT *
                    FROM order_s_insert 
                    where jia_fang = '%s' and che_liang = '%s' and  data_sub ->> 'fen_dian_id' = '%s' 
                    and  data_sub ->> 'jin_huo_bian' = '%s' 
                    """ % (jia_fang, che_liang,fen_dian_id,jin_huo_bian)
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001

        if FF100001.empty:
            FF100001 = True
        else:
            FF100001 = False

        return FF100001



    def insert_data(valuess,valuess2,datetime_,ganarate_id,data_sub):
        sql_insert = """ insert into order_s_insert(jia_fang,che_liang,datetime,order_s_insert_bill_id,data_sub) values (%s,%s,%s,%s,%s)  """
        cur.execute(sql_insert, (valuess,valuess2,datetime_,ganarate_id,data_sub))

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()
    G100001 = check_data(str(req_body.parner_id),str(req_body.car_id),req_body.fen_dian_id,req_body.jin_huo_bian)
    print(G100001)
    if G100001 == True:
        insert_data(str(req_body.parner_id),str(req_body.car_id), str(datetime),ganarate_id_1,json.dumps({"row_type": "for_product","status": "wait_sucess","fen_dian_id":req_body.fen_dian_id,"jin_huo_bian":req_body.jin_huo_bian,"comment":req_body.comment}))
        insert_data(str(req_body.parner_id),str(req_body.car_id), str(datetime),ganarate_id_2,json.dumps({"row_type": "for_car_cost","status": "wait_sucess","fen_dian_id":req_body.fen_dian_id,"jin_huo_bian":req_body.jin_huo_bian,"comment":req_body.comment}))

    psycopg2_conn_insert_data_s.close()

    return "ganarate_id"



