from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time

router = APIRouter()


class Foo_post(BaseModel):
    s_bill_id:str
    product_id: int
    product_qty: float
    product_price: float
    lei_type_id: int
    bi_zhi : int

class order_s_insert_post(BaseModel):
    order_s_insert_bill_id : str
    data_jsonb : Foo_post

@router.post("/order_s_insert", status_code = 200)
def order_s_insert_post(req_body : order_s_insert_post = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    datetime = str(generate_datetime())
    import json

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    bill_id = FFF['order_s_insert_bill_id']
    bill_id

    product_id = FFF['data_jsonb']['product_id']
    product_id

    product_qty = FFF['data_jsonb']['product_qty']
    lei_type_id = FFF['data_jsonb']['lei_type_id']
    product_price = FFF['data_jsonb']['product_price']
    s_bill_id = FFF['data_jsonb']['s_bill_id']




    def update_data(s_bill_id,product_id,bill_id,FFF,lei_type_id,product_price):
        sql_insert = """   WITH f AS
                        (
                        SELECT ('{'||index-1||'}')::text[] as path
                        FROM order_s_insert 
                        ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                        where contact ->> 's_bill_id' = %s  and contact ->> 'product_id' = '%s'  and  contact ->> 'lei_type_id' = '%s' and  contact ->> 'product_price' = '%s' 
                        and order_s_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess' ) 

                        UPDATE order_s_insert
                        SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                        FROM f where order_s_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess'   ;

                        """
        cur.execute(sql_insert, (s_bill_id,product_id,lei_type_id,product_price, bill_id, FFF, bill_id))

    def put_data(product_id,bill_id,FFF):

        sql_insert = """
                        UPDATE order_s_insert
                        SET data_detail=data_detail  || %s ::jsonb
                        where order_s_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess' ;
                        """
        cur.execute(sql_insert, ( FFF, bill_id))


    sql_insert = """
                    SELECT *
                    FROM order_s_insert 
                    ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                    where contact ->> 's_bill_id' = '%s' and  contact ->> 'product_id' = '%s' and contact ->> 'lei_type_id' = '%s' and contact ->> 'product_price' = '%s'
                    and order_s_insert_bill_id = '%s' and data_sub ->> 'status' = 'wait_sucess'
                    """ % (s_bill_id,product_id,lei_type_id,product_price, bill_id)

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
    FF100001

    try:
        FF100001.contact[0]['product_qty'] = float(FF100001.contact[0]['product_qty']) + float(product_qty)
        print(FF100001.contact[0])
    except:
        pass

    def check_type_list(auto_id):
        HG100001 = pd.read_sql('select lei_a,lei_b from lei_type_list where auto_id = '+ str(auto_id) +' ',psycopg2_conn_insert_data_s)
        
        return HG100001

    T10001 = check_type_list(FFF['data_jsonb']['lei_type_id'])
    print(T10001)

    FFF['data_jsonb']['lei_a'] = T10001['lei_a'][0]
    FFF['data_jsonb']['lei_b'] = T10001['lei_b'][0]


    import json
    PPO10001 = pd.DataFrame([FFF['data_jsonb']])
    # PPO10001 = PPO10001.drop(columns=['lei_type_id'])
    T100009 = PPO10001.to_dict(orient='records')[0]
    FFF

    if not FF100001.empty:
        print("1")
        print(FF100001.contact[0])
        FFF2 = json.dumps(FF100001.contact[0])
        update_data(s_bill_id,product_id,bill_id,FFF2,lei_type_id,product_price)

    else:
        print("2")
        PPO10001['r_id'] = generate_datetime_id()
        T100003 = PPO10001.to_dict(orient='records')
        FFF = json.dumps(T100003)
        put_data(product_id, bill_id, FFF)

    psycopg2_conn_insert_data_s.close()

    return "sucess"



@router.get("/order_s_insert", status_code = 200)
def order_s_insert_list(list_id : str):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    sql_insert = """
                        SELECT * 
                        FROM order_s_insert  where order_s_insert_bill_id = '%s'
                        """ % (list_id)
    FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
    FF100001
    GG100001 = pd.DataFrame(FF100001['data_detail'][0])
    if not GG100001.empty:
        BV100001 = GG100001.loc[GG100001['lei_type_id'] == 2]
    if not GG100001.empty:
        FF100001 = GG100001.loc[GG100001['lei_type_id'] != 2]
        FF100001['delete'] = 1
    if not GG100001.empty:
        GG900001 = BV100001.merge(FF100001, on=['product_id'], how='outer', indicator=True)
        GG900001 = GG900001[GG900001['_merge'] == 'both']
        if not GG900001.empty:
            BV100001 = GG100001[GG100001['r_id'].isin (GG900001['r_id_x'].to_list())]
            BV100001['delete'] = 0
        else:
            BV100001['delete'] = 1

    FF100001['delete'] = 1

    print(GG100001)
    if not GG100001.empty:
        GG100001 = pd.concat([BV100001,FF100001])
    print(GG100001)
    if not GG100001.empty:
        GG100001['product_id'] = GG100001['product_id'].astype(int)
        H1000001 = GG100001[['product_id']]

        res = json.loads(H1000001.to_json(orient='records'))
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res})
        FF100002 = FF100001.to_json(orient='records')
        GF10001 = pd.DataFrame(json.loads(r.json()))
    else:
        GF10001 = pd.DataFrame()
    if not GF10001.empty:
        GG100009 = GF10001.merge(GG100001, left_on=['product_id'], right_on=['product_id'], how='inner')
        GG100009
    else:
        GG100009 = pd.DataFrame()

    GG100010 = GG100009.to_json(orient='records')

    psycopg2_conn_insert_data_s.close()
    return GG100010




class pre_order_n_get_Foo_delete(BaseModel):
    r_id : str
    product_id: int
    before_delete_qty : float



class pre_PPPP_delete(BaseModel):
    order_s_insert_bill_id : str
    data_jsonb : pre_order_n_get_Foo_delete

@router.delete("/order_s_insert", status_code = 200)  
def order_s_insert_delete(req_body : pre_PPPP_delete = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    datetime = str(generate_datetime())
    import json

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    order_s_insert_bill_id = FFF['order_s_insert_bill_id']
    order_s_insert_bill_id
    product_id = FFF['data_jsonb']['product_id']
    before_delete_qty = FFF['data_jsonb']['before_delete_qty']
    r_id = FFF['data_jsonb']['r_id']

    def update_data(r_id,order_s_insert_bill_id,FFF,before_delete_qty):
        sql_insert = """   WITH f AS
                        (
                        SELECT ('{'||index-1||'}')::text[] as path
                        FROM order_s_insert 
                        ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                        where contact ->> 'r_id' = %s and contact ->> 'product_qty' = '%s'
                        and order_s_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess' ) 

                        UPDATE order_s_insert
                        SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                        FROM f where order_s_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess'   ;

                        """
        cur.execute(sql_insert, (r_id,before_delete_qty,order_s_insert_bill_id, FFF, order_s_insert_bill_id))
    def delete_data(product_id,order_s_insert_bill_id):
        sql_insert = """   UPDATE order_s_insert set data_detail = t.js_new from (
            select jsonb_agg ((data_detail ->> (idx-1)::int)::jsonb) as js_new
            from order_s_insert cross join jsonb_array_elements(data_detail) with 
            ordinality arr(j,idx) where  j ->> 'product_id' not in ('%s')  and order_s_insert_bill_id = %s
        )t where order_s_insert_bill_id = %s;
                        """
        cur.execute(sql_insert, (product_id,order_s_insert_bill_id,order_s_insert_bill_id))


    def check_data(r_id,order_s_insert_bill_id):
        sql_insert = """
                    SELECT *
                    FROM order_s_insert 
                    ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                    where contact ->> 'r_id' = '%s'
                    and order_s_insert_bill_id = '%s' and data_sub ->> 'status' = 'wait_sucess'
                    """ % (r_id, order_s_insert_bill_id)

        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001  

        return  FF100001
    
    def check_data_empty(order_s_insert_bill_id):
        sql_insert = """
                    SELECT data_detail
                    FROM order_s_insert 
                    where order_s_insert_bill_id = '%s' and data_sub ->> 'status' = 'wait_sucess'
                    """ % (order_s_insert_bill_id)

        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001  

        return  FF100001

    def update_json(order_s_insert_bill_id):
            sql_insert = """ UPDATE order_s_insert SET data_detail= '[]' ::jsonb WHERE order_s_insert_bill_id = %s ; """
            cur.execute(sql_insert, (order_s_insert_bill_id,))


    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    # print(r_id)
    FF100001 = check_data(r_id,order_s_insert_bill_id)
    print(FF100001.contact)
    # print(FF100001.contact[0]['product_id'])
    # print(FF100001)

    # import json
    # PPO10001 = pd.DataFrame([FFF['data_jsonb']])
    # # PPO10001 = PPO10001.drop(columns=['parner_id'])
    # T100009 = PPO10001.to_dict(orient='records')[0]
    # # print(FFF)
    # FFF
    # print("1")
    # print(FF100001.contact[0])
    try:
        FF100001.contact[0]['product_id'] = 0 
        FFF2 = json.dumps(FF100001.contact[0])
        update_data(r_id,order_s_insert_bill_id,FFF2,before_delete_qty)
    except:
        pass
    delete_data(0,order_s_insert_bill_id)

    F10001 = check_data_empty(order_s_insert_bill_id)
    print(F10001)
    if F10001['data_detail'][0] == None:
        print('yes')
        update_json(order_s_insert_bill_id)
    psycopg2_conn_insert_data_s.close()
    return "sucess"

class pre_order_n_get_Foo_(BaseModel):
    r_id : str
    product_id: int
    before_cheange_qty : float
    product_qty: float
    product_price: float



class pre_PPPP(BaseModel):
    order_s_insert_bill_id : str
    data_jsonb : pre_order_n_get_Foo_

@router.put("/order_s_insert", status_code = 200)  
def order_s_insert(req_body : pre_PPPP = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    datetime = str(generate_datetime())
    import json

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    order_s_insert_bill_id = FFF['order_s_insert_bill_id']
    order_s_insert_bill_id
    product_id = FFF['data_jsonb']['product_id']
    product_qty = FFF['data_jsonb']['product_qty']
    before_cheange_qty = FFF['data_jsonb']['before_cheange_qty']
    product_price = FFF['data_jsonb']['product_price']
    r_id = FFF['data_jsonb']['r_id']




    def update_data(r_id,order_s_insert_bill_id,FFF,before_cheange_qty):
        sql_insert = """   WITH f AS
                        (
                        SELECT ('{'||index-1||'}')::text[] as path
                        FROM order_s_insert 
                        ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                        where contact ->> 'r_id' = %s and contact ->> 'product_qty' = '%s'
                        and order_s_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess' ) 

                        UPDATE order_s_insert
                        SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                        FROM f where order_s_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess'   ;

                        """
        cur.execute(sql_insert, (r_id, before_cheange_qty,order_s_insert_bill_id, FFF, order_s_insert_bill_id))


    def check_data(r_id,order_s_insert_bill_id):
        sql_insert = """
                    SELECT *
                    FROM order_s_insert 
                    ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                    where contact ->> 'r_id' = '%s'
                    and order_s_insert_bill_id = '%s' and data_sub ->> 'status' = 'wait_sucess'
                    """ % (r_id, order_s_insert_bill_id)

        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001

        return  FF100001

    

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    FF100001 = check_data(r_id,order_s_insert_bill_id)

    FF100001.contact[0]['product_qty'] = product_qty


    import json
    PPO10001 = pd.DataFrame([FFF['data_jsonb']])
    T100009 = PPO10001.to_dict(orient='records')[0]
    FFF

    # print(FF100001)
    if not FF100001.empty:
        print("1")
        print(FF100001.contact[0])
        FFF2 = json.dumps(FF100001.contact[0])
        update_data(r_id,order_s_insert_bill_id,FFF2,before_cheange_qty)

    else:
        print("2")
        PPO10001['r_id'] = generate_datetime_id()
        T100003 = PPO10001.to_dict(orient='records')
        FFF = json.dumps(T100003)
        put_data(product_id, bill_id, FFF)

    psycopg2_conn_insert_data_s.close()

    return "sucess"


@router.get("/list_type", status_code = 200)
def list_type(row_type : str):

    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    sql_insert = """SELECT auto_id,lei_name  FROM lei_type_list where data_sub ->> 'row_type' = '%s'  """  % row_type
    FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
    FF100001 = FF100001.rename(columns={'auto_id': 'lei_type_id' ,'lei_name' : 'lei_type_name'})
    FF100001

    GG100010 = FF100001.to_json(orient='records')
    psycopg2_conn_insert_data_s.close()

    return GG100010


class che_liang_ssss(BaseModel):
    order_s_insert_bill_id : int
    jin_huo_bian: int


@router.post("/order_s_insert_bill_id", status_code=200)
def order_s_insert_bill_id(req_body : che_liang_ssss = Body(...)):

    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    def create_SEQUENCE():
        sql_exe_2 = """ CREATE SEQUENCE if not exists bill_id_s_insert_id_id \
        INCREMENT 1 \
        START 1 \
        MINVALUE 1 """
        cur.execute(sql_exe_2)

    # %%

    def create_table():
        sql_exe = """  CREATE TABLE if not exists bill_id_s_insert (
                        auto_id serial PRIMARY KEY,
                        order_in_s_bill_id TEXT DEFAULT 'bill_id_s_insert'||lpad(NEXTVAL('bill_id_s_insert_id_id'::regclass)::text,10, '0' ), 
                        datetime timestamp NOT NULL 
        ); """
        cur.execute(sql_exe)

    def alter_insert_id():
        # sql_exe = """  ALTER TABLE bill_id_s_insert ALTER COLUMN order_in_s_bill_id SET DEFAULT  's'::text || lpad(nextval('bill_id_s_insert_id_id'::regclass)::text, 10, '0'::text);
        # ; """
        sql_exe = """  ALTER TABLE bill_id_s_insert ADD COLUMN IF NOT EXISTS data_sub JSONB NOT NULL  DEFAULT '{}'
        ; """
        cur.execute(sql_exe)
    # %%
    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()
    create_SEQUENCE();
    create_table();
    # alter_insert_id();
    def insert_data(valuess,data_sub):
        sql_insert = """ insert into bill_id_s_insert(order_s_insert_bill_id,data_sub) values ('%s','%s')  """ % (str(valuess),data_sub)
        cur.execute(sql_insert)

    G100001 = insert_data(int(req_body.order_s_insert_bill_id),json.dumps({'jin_huo_bian' : req_body.jin_huo_bian  }))
    print(G100001)    

    psycopg2_conn_insert_data_s.close()
    return "ganarate_id"


@router.get("/order_s_insert_bill_id", status_code=200)
def order_s_insert_bill_id(jin_huo_bian :int ):

    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    def check_data(valuess):
        sql = """  select  order_s_insert_bill_id,order_in_s_bill_id from bill_id_s_insert where data_sub ->> 'jin_huo_bian'  = '%s' """ % valuess
        TR10001 = pd.read_sql(sql,psycopg2_conn_insert_data_s)
        TR10001 = TR10001.sort_values(['order_in_s_bill_id'], ascending=[False])
        return TR10001
    G100001 = check_data(jin_huo_bian)
    print(G100001)    
    G100001 = G100001.to_json(orient='records')
    psycopg2_conn_insert_data_s.close()
    return json.loads(G100001)




class Foo_post__(BaseModel):
    s_bill_id:str
    product_id: int
    product_qty: float
    product_price: float
    lei_type_id: int
    jia_yi_name_a: int
    jia_yi_name_b: int
    bi_zhi : int

class order_s_insert_post_(BaseModel):
    order_s_insert_bill_id : str
    data_jsonb : Foo_post__

@router.post("/order_s_insert_car_cost", status_code = 200)
def order_s_insert_post_(req_body : order_s_insert_post_ = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    datetime = str(generate_datetime())
    import json

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    bill_id = FFF['order_s_insert_bill_id']
    bill_id


    product_id = FFF['data_jsonb']['product_id']
    product_id

    product_qty = FFF['data_jsonb']['product_qty']
    lei_type_id = FFF['data_jsonb']['lei_type_id']
    product_price = FFF['data_jsonb']['product_price']
    s_bill_id = FFF['data_jsonb']['s_bill_id']




    def update_data(s_bill_id,product_id,bill_id,FFF,lei_type_id,product_price):
        sql_insert = """   WITH f AS
                        (
                        SELECT ('{'||index-1||'}')::text[] as path
                        FROM order_s_insert 
                        ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                        where contact ->> 's_bill_id' = %s  and contact ->> 'product_id' = '%s'  and  contact ->> 'lei_type_id' = '%s' and  contact ->> 'product_price' = '%s' 
                        and order_s_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess' ) 

                        UPDATE order_s_insert
                        SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                        FROM f where order_s_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess'   ;

                        """
        cur.execute(sql_insert, (s_bill_id,product_id,lei_type_id,product_price, bill_id, FFF, bill_id))

    def put_data(product_id,bill_id,FFF):

        sql_insert = """
                        UPDATE order_s_insert
                        SET data_detail=data_detail  || %s ::jsonb
                        where order_s_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess' ;
                        """
        cur.execute(sql_insert, ( FFF, bill_id))


    sql_insert = """
                    SELECT *
                    FROM order_s_insert 
                    ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                    where contact ->> 's_bill_id' = '%s' and  contact ->> 'product_id' = '%s' and contact ->> 'lei_type_id' = '%s' and contact ->> 'product_price' = '%s'
                    and order_s_insert_bill_id = '%s' and data_sub ->> 'status' = 'wait_sucess'
                    """ % (s_bill_id,product_id,lei_type_id,product_price, bill_id)

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
    FF100001

    try:
        FF100001.contact[0]['product_qty'] = float(FF100001.contact[0]['product_qty']) + float(product_qty)
        print(FF100001.contact[0])
    except:
        pass

    def check_type_list(auto_id):
        HG100001 = pd.read_sql('select lei_a,lei_b from lei_type_list where auto_id = '+ str(auto_id) +' ',psycopg2_conn_insert_data_s)

        return HG100001

    T10001 = check_type_list(FFF['data_jsonb']['lei_type_id'])
    print(T10001)

    FFF['data_jsonb']['lei_a'] = T10001['lei_a'][0]
    FFF['data_jsonb']['lei_b'] = T10001['lei_b'][0]

    import json
    PPO10001 = pd.DataFrame([FFF['data_jsonb']])
    # PPO10001 = PPO10001.drop(columns=['lei_type_id'])
    T100009 = PPO10001.to_dict(orient='records')[0]
    FFF

    if not FF100001.empty:
        print("1")
        print(FF100001.contact[0])
        FFF2 = json.dumps(FF100001.contact[0])
        update_data(s_bill_id,product_id,bill_id,FFF2,lei_type_id,product_price)

    else:
        print("2")
        PPO10001['r_id'] = generate_datetime_id()
        T100003 = PPO10001.to_dict(orient='records')
        FFF = json.dumps(T100003)
        put_data(product_id, bill_id, FFF)
    psycopg2_conn_insert_data_s.close()

    return "sucess"


@router.get("/product_check", status_code = 200)
def product_check(product_id : int,jia_fang:int):

    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api

    params = {'product_id':product_id}

    def check_data(product_id,jia_fang):
        FD100001 = """  select jia_fang,data_detail,datetime from order_s_insert where jia_fang = '%s' and datetime > CURRENT_DATE and data_detail @>  '[{"product_id": %s,"lei_a": 22}]' """ % (jia_fang,product_id)
        FD100001 = pd.read_sql(FD100001,psycopg2_conn_insert_data_s)
        FD100002 = FD100001.to_dict('records')
        FD100003 = pd.json_normalize(FD100002,'data_detail',['jia_fang','datetime'])
        if FD100003.empty:
            FD100004 = pd.DataFrame()
        else:
            FD100003 = FD100003.loc[FD100003['product_id'] == product_id]
            FD100004 = FD100003[['product_id','product_qty','datetime','jia_fang']]
            FD100004['product_qty'] =  (FD100004['product_qty'] * -1 )
        return FD100004 
    r = requests.get(mongodb_data_api + '/api/v1/data/zuotian_maimai/zuotian_maimai_qty_by',
                     params=params)

    GF10001 = pd.DataFrame(r.json())
    r = requests.get(mongodb_data_api + '/api/v1/data/o_ku_zou/o_ku_zuo_by',
                     params=params)
    GF20001 = pd.DataFrame(r.json())
    GF30001 = check_data(product_id,jia_fang)

    JH10001 = pd.concat([GF10001,GF20001,GF30001])
    print(JH10001)
    if not JH10001.empty:
        JH10003=JH10001.groupby(['product_id','jia_fang'])['product_qty'].sum().reset_index()
        JH10003 = JH10003[JH10003['jia_fang'].isin ([jia_fang])]
        # try:
        #     JH10003 = JH10003.loc[JH10003['jia_fang'] == jia_fang]
        # except:
        #     JH10003 = pd.DataFrame([{'product_id':jia_fang,'product_qty':0}])
        print(jia_fang)
        print(JH10003)
        if JH10003.empty:
            JH10003 = pd.DataFrame([{'product_id':jia_fang,'product_qty':0}])
        else:
            pass

    else:
        JH10003 = pd.DataFrame([{'product_id':jia_fang,'product_qty':0}])
    # print(JH10003)
    JH10004 = JH10003.to_json(orient='records')
    psycopg2_conn_insert_data_s.close()

    return json.loads(JH10004)