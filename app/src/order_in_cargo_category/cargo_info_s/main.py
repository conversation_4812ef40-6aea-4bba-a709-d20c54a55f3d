from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie,generate_datetime_selie_v1
from typing import List, Optional
import json
import requests
import pandas as pd
import time

router = APIRouter()


# 创建类 POST传参
class Item(BaseModel):
    id: int
    # idname: str
    # mm_name: str
    # d_name: str
    product_category_id: int
    # ri_qi: str
    # jin_huo_bian: int

@router.post("/cargo_info_s", status_code = 200)
def cargo_info_s(item: Item):
    print("POST")
    print(item.id)

    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    try:

        print(item)

        import datetime
        import pytz

        insert_data_s_cursor = psycopg2_conn_insert_data_s.cursor()

        # def insert_data(data):
            
        #     tuples = [tuple(x) for x in data.values]
            
        #     sql_insert  = """ insert into product_weight_info(shu_ri_qi,product_id,product_weight,ri_qi,jin_huo_bian) values (%s,%s,%s,%s,%s)  """
        #     insert_data_s_cursor.executemany(sql_insert,tuples)
        #     psycopg2_conn_insert_data_s.commit()
        #     print("insert success")

        def insert_data():
        
            # tz = pytz.timezone('UTC')
            # dt = datetime.datetime.now(tz=tz).strftime('%Y-%m-%d %H:%M:%S')
            # print(dt)
            
            sql_insert  = " insert into cargo_info_s(product_id,product_category_id) values ( " \
                          " '" + str(item.id) + "','" + str(item.product_category_id) + "') "
            insert_data_s_cursor.execute(sql_insert)
            psycopg2_conn_insert_data_s.commit()
            print("insert success")

        # insert data function
        insert_data()

        return "Success!"

    finally:
        psycopg2_conn_insert_data_s.close()
        print("close")



