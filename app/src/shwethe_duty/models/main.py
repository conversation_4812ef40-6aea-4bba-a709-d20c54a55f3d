from datetime import datetime
from typing import Optional
from sqlmodel import SQLModel, <PERSON>
from sqlalchemy import Column
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import TIMESTAMP, JSONB
import pytz

class duty_join_product(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    product_id: Optional[int] = Field(
        default=None, title="bu_bian_ji_id")
    duty_id: int = Field(default=None, title="bu_bian")
    shu_riqi: str = Field(default=func.now(), title="shu_riqi")
