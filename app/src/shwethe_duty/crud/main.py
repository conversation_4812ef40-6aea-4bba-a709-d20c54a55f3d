from sqlmodel import Session, select
from typing import List
from sqlalchemy import and_
from sqlalchemy import and_,desc,or_,cast, String

from src.shwethe_duty.schemas.main import (
    duty_join_product_post
)
from src.shwethe_duty.models.main import (
    duty_join_product
)
from src.common.http.mongodbApi.main import (
    get_post_product,
    get_product_total_sale
)
import pandas as pd

def duty_join_product_post_fun(db: Session,duty_join_product_post:duty_join_product_post):

    new_jia_yi_fang = duty_join_product(**duty_join_product_post.dict(exclude_unset=True))
    db.add(new_jia_yi_fang)
    db.commit()
    db.refresh(new_jia_yi_fang)  
    
    return new_jia_yi_fang


def duty_join_product_get_fun(db: Session):
    
    rows = db.query(duty_join_product).all()
    
    # 转换查询结果为字典列表
    data_dict = [{k: v for k, v in obj.__dict__.items() if k != '_sa_instance_state'} for obj in rows]
    
    df_db = pd.DataFrame(data_dict)
    df_db = df_db.sort_values(['shu_riqi'], ascending=[False])
    df_db = df_db.drop_duplicates(subset=['product_id'], keep='first')
    
    # 从API获取产品信息
    duty_ids = df_db['duty_id'].tolist()
    df_duty_api = get_post_product(duty_ids)
    product_ids = df_db['product_id'].tolist()
    df_product_api = get_post_product(product_ids)
    
    # 重命名列以区分duty和product的信息 
    duty_rename_dict = {
        'product_id': 'duty_id',
        'product_idname': 'duty_idname',
        'product_mm_name': 'duty_mm_name',
        'product_d_name': 'duty_d_name'
    }
    product_rename_dict = {
        'product_id': 'product_id',
        'product_idname': 'product_idname',
        'product_mm_name': 'product_mm_name',
        'product_d_name': 'product_d_name'
    }
    
    df_duty_api = df_duty_api.rename(columns=duty_rename_dict)
    df_product_api = df_product_api.rename(columns=product_rename_dict)
    
    # 合并数据
    merged_df = df_db.merge(df_duty_api, on=['duty_id'], how='inner').merge(df_product_api, on=['product_id'], how='inner')
    
    return merged_df.to_dict("records")


def duty_join_product_get_fun_complie(db: Session):
    
    rows = db.query(duty_join_product).all()
    
    # 转换查询结果为字典列表
    data_dict = [{k: v for k, v in obj.__dict__.items() if k != '_sa_instance_state'} for obj in rows]
    df_db = pd.DataFrame(data_dict)

    # 初始化一个空的DataFrame用于保存API的数据
    df_api = pd.DataFrame()

    # 获取第一页的数据并确定总页数
    first_page_response = get_product_total_sale(page=1, limit=1000)
    total_pages = int(first_page_response["total_pages"])
    df_api = pd.concat([df_api, pd.DataFrame(first_page_response["data"])], ignore_index=True)

    # 从第二页开始，获取剩余的数据
    for page in range(2, total_pages + 1):
        current_response = get_product_total_sale(page=page, limit=1000)
        df_api = pd.concat([df_api, pd.DataFrame(current_response["data"])], ignore_index=True)

        
        
    QA1004 = df_api[~df_api['product_id'].isin (df_db['product_id'])]
    
    
    product_ids = QA1004['product_id'].tolist()
    df_product_api = get_post_product(product_ids)
    
    product_rename_dict = {
        'product_id': 'product_id',
        'product_idname': 'product_idname',
        'product_mm_name': 'product_mm_name',
        'product_d_name': 'product_d_name'
    }
    
    df_product_api = df_product_api.rename(columns=product_rename_dict)
    
    merged_df = df_api.merge(df_product_api, on=['product_id'], how='inner')
    
    merged_df = merged_df.head(1000)

    return merged_df.to_dict("records")

    
