from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from src.shwethe_duty.schemas.main import (
    duty_join_product_post
)
from src.shwethe_duty.crud.main import (
    duty_join_product_post_fun,duty_join_product_get_fun,duty_join_product_get_fun_complie)
from src.config.duty.database import get_db

router = APIRouter()

@router.post("/duty_join_product")
def price_tb(duty_join_product_post: duty_join_product_post,db: Session = Depends(get_db)):
    return duty_join_product_post_fun(duty_join_product_post=duty_join_product_post,db=db)

@router.get("/duty_join_product")
def price_tb(db: Session = Depends(get_db)):
    return duty_join_product_get_fun(db=db)

@router.get("/duty_join_product_get_fun_complie")
def price_tb(db: Session = Depends(get_db)):
    return duty_join_product_get_fun_complie(db=db)