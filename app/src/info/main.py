from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time

router = APIRouter()


class che_liang_(BaseModel):
    product_id : int
    data_jsonb_1 : dict = Body(...)


@router.post("/info_data", status_code=200)
def order_in_judy(req_body : che_liang_ = Body(...)):

    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s



    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)



    def data_insert(product_id,data_json):
        sql_insert = """ insert into product_info_s(product_id,data_sub) values (%s,%s)  """
        cur.execute(sql_insert, (product_id,data_json)) 



    try:
        data_insert(FFF['product_id'],json.dumps(FFF['data_jsonb_1']))
        return "AAAAA"

    finally:

        psycopg2_conn_insert_data_s.close()




    