from sqlmodel import Session, select
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import and_,desc,or_,cast, String
from sqlalchemy import join
from sqlalchemy import String, Text,func
from src.service.data.schemas.v1.main import (
    account_money_post,
    account_money_company_name_put
)
from src.service.data.models.v1.main import (
    account_money
)
from src.time_zone.time_zone_function import get_datetime
from src.config.data.database import get_db as get_session



def create_account_money(hero: account_money_post, db: Session = Depends(get_session)):
    """
    If the account_money with the given account_id already exists, update it.
    Otherwise, create a new one.

    Args:
        hero (account_money_post): The account_money data to be created or updated.
        db (Session, optional): The database session. Defaults to Depends(get_session).

    Returns:
        account_money: The created or updated account_money
    """
    # Query whether the account_id already exists
    existing_account = db.query(account_money).filter(account_money.account_id == hero.account_id).first()

    if existing_account:
        # If it exists, update the existing record
        existing_account.account_type = hero.account_type
        existing_account.create_datetime = str(get_datetime())
        db.add(existing_account)
        db.commit()
        db.refresh(existing_account)
        return existing_account
    else:
        # If it doesn't exist, insert a new record
        hero.create_datetime = str(get_datetime())
        hero_to_db = account_money.from_orm(hero)
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        return hero_to_db
    
    
def put_company_name(hero: account_money_company_name_put, db: Session = Depends(get_session)):
    """
    If the account_money with the given account_id already exists, update it.
    Otherwise, create a new one.

    Args:
        hero (account_money_post): The account_money data to be created or updated.
        db (Session, optional): The database session. Defaults to Depends(get_session).

    Returns:
        account_money: The created or updated account_money
    """
    # Query whether the account_id already exists
    existing_account = db.query(account_money).filter(account_money.account_id == hero.account_id).first()

    if existing_account:
        # If it exists, update the existing record
        existing_account.company_id = hero.compayny_id
        db.add(existing_account)
        db.commit()
        db.refresh(existing_account)
        return existing_account
    else:
        # If it doesn't exist, insert a new record
        hero.create_datetime = str(get_datetime())
        hero_to_db = account_money.from_orm(hero)
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        return hero_to_db