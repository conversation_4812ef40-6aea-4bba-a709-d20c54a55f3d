from xmlrpc.client import DateTime
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id



class account_money(SQLModel, table=True):
    __tablename__ = "account_money"
    __table_args__ = {'extend_existing': True}
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    account_id: Optional[int]
    account_type: Optional[int]
    company_id: Optional[int]
    create_datetime : datetime = Field(default_factory=get_datetime, index=True)