from sqlmodel import create_engine, Session, select
from sqlmodel import <PERSON><PERSON><PERSON>, Integer, String, SQLModel
from sqlmodel import Field, Relationship, SQLModel,Column
from pydantic import BaseModel
from sqlalchemy.dialects.postgresql import JSONB
from pydantic.types import Optional
from datetime import datetime


class account_money_post(SQLModel):
    account_id: Optional[int]
    account_type: Optional[int]
    create_datetime : Optional[datetime]
    
class account_money_company_name_put(SQLModel):
    account_id: Optional[int]
    compayny_id: Optional[int]