from sqlalchemy.exc import IntegrityError
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from src.config.data.database import get_db
from src.service.data.crud.v1.main import (
    create_account_money,
    put_company_name
)
from src.service.data.schemas.v1.main import (
    account_money_post,
    account_money_company_name_put
)



router = APIRouter()
@router.post("/account_money")
def price_tb(hero: account_money_post,db: Session = Depends(get_db) ):
    return create_account_money(hero=hero,db=db)


@router.put("/account_money/company_name")
def price_tb(hero: account_money_company_name_put,db: Session = Depends(get_db) ):
    return put_company_name(hero=hero,db=db)

# @router.get("/account_money/{fen_dian_id}")
# def price_tb(fen_dian_id: int=1,db: Session = Depends(get_db) ):
#     fen_dian_id = 1
#     return get_address_info(fen_dian_id=fen_dian_id,db=db)
