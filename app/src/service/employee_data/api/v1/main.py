from sqlalchemy.exc import IntegrityError
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from src.config.employee_data.database import get_db
from src.service.employee_data.crud.v1.main import (
    employee_table_put_salary_type_crud,
    department_table_put_crud,
    position_table_put_crud,
    create_employee_position_table,
    salary_detail_table_post_salary_type_crud,
    salary_benefit_for_employee_get_salary_type_crud,
    salary_detail_table_get,
    employee_table_put_lunch_break_crud,
    employee_table_put_fen_dian_crud,
    employee_table_put_work_active_crud,
    salary_detail_table_social_security_post_salary_type_crud
)
from src.service.employee_data.schemas.v1.main import (
   employee_table_put_salary_type,
   department_table_put,
   position_table_put,
   employee_position_table_post,
   salary_detail_table_post,
   employee_table_put_lunch_break,
   employee_table_put_fen_dian,
   employee_table_put_work_active,
   salary_detail_table_social_security_post
)



router = APIRouter()
@router.put("/employee/salary_type")
def price_tb(hero: employee_table_put_salary_type,db: Session = Depends(get_db) ):
    return employee_table_put_salary_type_crud(hero=hero,db=db)


@router.put("/employee/lunch_break")
def price_tb(hero: employee_table_put_lunch_break,db: Session = Depends(get_db) ):
    return employee_table_put_lunch_break_crud(hero=hero,db=db)

@router.put("/employee/fen_dian")
def price_tb(hero: employee_table_put_fen_dian,db: Session = Depends(get_db) ):
    return employee_table_put_fen_dian_crud(hero=hero,db=db)

@router.put("/employee/work_active")
def price_tb(hero: employee_table_put_work_active,db: Session = Depends(get_db) ):
    return employee_table_put_work_active_crud(hero=hero,db=db)

@router.post("/department_table")
def price_tb(hero: department_table_put,db: Session = Depends(get_db) ):
    return department_table_put_crud(hero=hero,db=db)

@router.post("/position_table")
def price_tb(hero: position_table_put,db: Session = Depends(get_db) ):
    return position_table_put_crud(hero=hero,db=db)

@router.post("/employee_position_table")
def price_tb(hero: employee_position_table_post,db: Session = Depends(get_db) ):
    return create_employee_position_table(hero=hero,db=db)


@router.get("/salary_detail_table")
def price_tb(employee_id: int,db: Session = Depends(get_db) ):
    return salary_detail_table_get(employee_id=employee_id,db=db)


@router.post("/salary_detail_table")
def price_tb(hero: salary_detail_table_post,db: Session = Depends(get_db) ):
    return salary_detail_table_post_salary_type_crud(hero=hero,db=db)


@router.get("/salary_benefit_for_employee_get_salary_type_crud")
def price_tb(salary_type_id: int,db: Session = Depends(get_db) ):
    return salary_benefit_for_employee_get_salary_type_crud(salary_type_id=salary_type_id,db=db)


@router.post("/salary_detail_table_social_security")
def price_tb(hero: salary_detail_table_social_security_post,db: Session = Depends(get_db) ):
    return salary_detail_table_social_security_post_salary_type_crud(hero=hero,db=db)