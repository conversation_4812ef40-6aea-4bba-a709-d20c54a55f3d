from xmlrpc.client import DateTime
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id



class employee_table(SQLModel, table=True):
    __tablename__ = "employee_table"
    __table_args__ = {'extend_existing': True}
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    employee_id: Optional[int]
    employee_status: Optional[int]
    salary_type : Optional[int]
    lunch_break : Optional[int]
    fen_dian_id : Optional[int]
    work_active : Optional[bool]
    create_datetime : datetime = Field(default_factory=get_datetime, index=True)

class salary_master_table(SQLModel, table=True):
    __tablename__ = "salary_master_table"
    __table_args__ = {'extend_existing': True}
    salary_master_id: Optional[int] = Field(default=None, primary_key=True)
    employee_id: Optional[int]
    total_salary: Optional[float]
    create_datetime : datetime = Field(default_factory=get_datetime, index=True)

class salary_detail_table(SQLModel, table=True):
    __tablename__ = "salary_detail_table"
    __table_args__ = {'extend_existing': True}
    salary_detail_id: Optional[int] = Field(default=None, primary_key=True)
    salary_goods_id: Optional[int]
    employee_id : Optional[int]
    amount: Optional[int]
    create_datetime : datetime = Field(default_factory=get_datetime, index=True)
    
class department_table(SQLModel, table=True):
    __tablename__ = "department_table"
    __table_args__ = {'extend_existing': True}
    department_id: Optional[int] = Field(default=None, primary_key=True)
    department_name: Optional[str]
    
class position_table(SQLModel, table=True):
    __tablename__ = "position_table"
    __table_args__ = {'extend_existing': True}
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    position_id: Optional[int]
    position_name: Optional[str]
    department_id: Optional[int]
    job_description: Optional[str]
    
    
class employee_position_table(SQLModel, table=True):
    __tablename__ = "employee_position_table"
    __table_args__ = {'extend_existing': True}
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    employee_id: Optional[int]
    position_id: Optional[int]
    assignment_date: Optional[datetime]
    end_date: Optional[datetime]
    
    
class salary_benefit_for_employee(SQLModel, table=True):
    __tablename__ = "salary_benefit_for_employee"
    __table_args__ = {'extend_existing': True}
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    benefit_id: Optional[int]
    salary_type_id: Optional[int]
    
    
class salary_detail_table_social_security(SQLModel, table=True):
    __tablename__ = "salary_detail_table_social_security"
    __table_args__ = {'extend_existing': True}
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    employee_id: Optional[int]
    amount: Optional[float]
    create_datetime : datetime = Field(default_factory=get_datetime, index=True)
    goods_id: Optional[int]
    is_active: Optional[bool]
    
    