from sqlmodel import create_engine, Session, select
from sqlmodel import <PERSON>um<PERSON>, Integer, String, SQLModel
from sqlmodel import Field, Relationship, SQLModel,Column
from pydantic import BaseModel
from sqlalchemy.dialects.postgresql import JSONB
from pydantic.types import Optional
from datetime import datetime


class employee_table_put_salary_type(SQLModel):
    employee_id: Optional[int]
    salary_type: Optional[int]
    create_datetime : Optional[datetime]
    
    
class employee_table_put_lunch_break(SQLModel):
    employee_id: Optional[int]
    lunch_break: Optional[int]
    create_datetime : Optional[datetime]
    
class employee_table_put_fen_dian(SQLModel):
    employee_id: Optional[int]
    fen_dian_id: Optional[int]
    create_datetime : Optional[datetime]
    
    
class employee_table_put_work_active(SQLModel):
    employee_id: Optional[int]
    work_active: Optional[bool]
    
class department_table_put(SQLModel):
    department_name: Optional[str]
    department_id: Optional[int] = 0
    
    
class position_table_put(SQLModel):
    position_name: Optional[str]
    position_id: Optional[int]
    department_id: Optional[int]
    
    
class employee_position_table_post(SQLModel):
    employee_id: Optional[int]
    position_id: Optional[int]
    assignment_date: Optional[datetime]
    end_date: Optional[datetime]
    
    
class salary_detail_table_post(SQLModel):
    employee_id: Optional[int]
    amount: Optional[float]
    salary_goods_id: Optional[int]
    create_datetime: Optional[datetime]
    

class salary_detail_table_social_security_post(SQLModel):
    employee_id: Optional[int]
    amount: Optional[float]
    goods_id: Optional[int]
    is_active: Optional[bool]
    create_datetime: Optional[datetime]
    
    
