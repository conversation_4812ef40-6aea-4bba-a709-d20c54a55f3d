from sqlmodel import Session, select
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import and_,desc,or_,cast, String
from sqlalchemy import join
from sqlalchemy import String, Text,func
from src.service.employee_data.schemas.v1.main import (
    salary_detail_table_post,
    employee_table_put_salary_type,
    department_table_put,
    employee_position_table_post,
    position_table_put,
    employee_table_put_lunch_break,
    employee_table_put_fen_dian,
    employee_table_put_work_active,
    salary_detail_table_social_security_post
)
from src.service.employee_data.models.v1.main import (
    salary_master_table,
    employee_table,
    department_table,
    position_table,
    employee_position_table,
    salary_detail_table,
    salary_benefit_for_employee,
    salary_detail_table_social_security
)
from src.time_zone.time_zone_function import get_datetime
from src.config.employee_data.database import get_db as get_session
from src.common.function.apiFunction import merge_dataframe_on_keyword_jia_yi_fang,merge_dataframe_on_keyword_product
from src.common.function.function import sqmodel_to_df
import json



# def create_salary_master_table(hero: salary_detail_table_post, db: Session = Depends(get_session)):
#     """
#     If the account_money with the given account_id already exists, update it.
#     Otherwise, create a new one.

#     Args:
#         hero (account_money_post): The account_money data to be created or updated.
#         db (Session, optional): The database session. Defaults to Depends(get_session).

#     Returns:
#         account_money: The created or updated account_money
#     """
#     # Query whether the account_id already exists
#     existing_account = db.query(salary_master_table).filter(salary_master_table.employee_id == hero.employee_id).first()

#     if existing_account:
#         # If it exists, update the existing record
#         existing_account.create_datetime = str(get_datetime())
#         db.add(existing_account)
#         db.commit()
#         db.refresh(existing_account)
#         return existing_account
#     else:
#         # If it doesn't exist, insert a new record
#         hero.create_datetime = str(get_datetime())
#         hero_to_db = salary_master_table.from_orm(hero)
#         db.add(hero_to_db)
#         db.commit()
#         db.refresh(hero_to_db)
#         return hero_to_db
    
    
    
def employee_table_put_salary_type_crud(hero: employee_table_put_salary_type, db: Session = Depends(get_session)):
    """
    If the account_money with the given account_id already exists, update it.
    Otherwise, create a new one.

    Args:
        hero (account_money_post): The account_money data to be created or updated.
        db (Session, optional): The database session. Defaults to Depends(get_session).

    Returns:
        account_money: The created or updated account_money
    """
    # Query whether the account_id already exists
    existing_account = db.query(employee_table).filter(employee_table.employee_id == hero.employee_id).first()
    
    
    if existing_account:
        # Update the existing record
        if hero.salary_type is not None:
            existing_account.salary_type = hero.salary_type
        # existing_employee.update_datetime = str(get_datetime())  # Update the timestamp for modification
        db.commit()
        db.refresh(existing_account)
        return existing_account
    
    # If it doesn't exist, insert a new record
    hero.create_datetime = str(get_datetime())
    hero_to_db = employee_table.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    return hero_to_db



def employee_table_put_lunch_break_crud(hero: employee_table_put_lunch_break, db: Session = Depends(get_session)):
    """
    Update or create a record in the employee_table with the lunch_break value.
    Args:
        hero (employee_table_put_lunch_break): The input data with employee_id, lunch_break, and create_datetime.
        db (Session, optional): The database session. Defaults to Depends(get_session).
    Returns:
        employee_table: The created or updated record.
    """
    # Check if the record with the given employee_id exists
    existing_employee = db.query(employee_table).filter(employee_table.employee_id == hero.employee_id).first()

    if existing_employee:
        # Update the existing record
        if hero.lunch_break is not None:
            existing_employee.lunch_break = hero.lunch_break
        # existing_employee.update_datetime = str(get_datetime())  # Update the timestamp for modification
        db.commit()
        db.refresh(existing_employee)
        return existing_employee
    
    
    
def employee_table_put_fen_dian_crud(hero: employee_table_put_fen_dian, db: Session = Depends(get_session)):
    """
    Update or create a record in the employee_table with the lunch_break value.
    Args:
        hero (employee_table_put_lunch_break): The input data with employee_id, lunch_break, and create_datetime.
        db (Session, optional): The database session. Defaults to Depends(get_session).
    Returns:
        employee_table: The created or updated record.
    """
    # Check if the record with the given employee_id exists
    existing_employee = db.query(employee_table).filter(employee_table.employee_id == hero.employee_id).first()

    if existing_employee:
        # Update the existing record
        if hero.fen_dian_id is not None:
            existing_employee.fen_dian_id = hero.fen_dian_id
        # existing_employee.update_datetime = str(get_datetime())  # Update the timestamp for modification
        db.commit()
        db.refresh(existing_employee)
        return existing_employee
    
    
def employee_table_put_work_active_crud(hero: employee_table_put_work_active, db: Session = Depends(get_session)):
    """
    Update or create a record in the employee_table with the lunch_break value.
    Args:
        hero (employee_table_put_lunch_break): The input data with employee_id, lunch_break, and create_datetime.
        db (Session, optional): The database session. Defaults to Depends(get_session).
    Returns:
        employee_table: The created or updated record.
    """
    # Check if the record with the given employee_id exists
    existing_employee = db.query(employee_table).filter(employee_table.employee_id == hero.employee_id).first()

    if existing_employee:
        # Update the existing record
        existing_employee.work_active = hero.work_active
        # existing_employee.update_datetime = str(get_datetime())  # Update the timestamp for modification
        db.commit()
        db.refresh(existing_employee)
        
        return existing_employee
    

def department_table_put_crud(hero: department_table_put, db: Session = Depends(get_session)):
    """
    If the account_money with the given account_id already exists, update it.
    Otherwise, create a new one.

    Args:
        hero (account_money_post): The account_money data to be created or updated.
        db (Session, optional): The database session. Defaults to Depends(get_session).

    Returns:
        account_money: The created or updated account_money
    """
    # Query whether the account_id already exists
    existing_account = db.query(department_table).filter(department_table.department_name == hero.department_name).first()

    if existing_account:
        pass
    else:
        # If it doesn't exist, insert a new record
        # hero.create_datetime = str(get_datetime())
        hero_to_db = department_table.from_orm(hero)
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        return hero_to_db
    
    
def position_table_put_crud(hero: position_table_put, db: Session = Depends(get_session)):
    """
    If the account_money with the given account_id already exists, update it.
    Otherwise, create a new one.

    Args:
        hero (account_money_post): The account_money data to be created or updated.
        db (Session, optional): The database session. Defaults to Depends(get_session).

    Returns:
        account_money: The created or updated account_money
    """
    # Query whether the account_id already exists
    existing_account = db.query(position_table).filter(position_table.position_name == hero.position_name).first()

    if existing_account:
        pass
    else:
        # If it doesn't exist, insert a new record
        # hero.create_datetime = str(get_datetime())
        hero_to_db = position_table.from_orm(hero)
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        return hero_to_db
    

    
def create_employee_position_table(hero: employee_position_table_post, db: Session = Depends(get_session)):
    """
    If the account_money with the given account_id already exists, update it.
    Otherwise, create a new one.

    Args:
        hero (account_money_post): The account_money data to be created or updated.
        db (Session, optional): The database session. Defaults to Depends(get_session).

    Returns:
        account_money: The created or updated account_money
    """
    # Query whether the account_id already exists
    existing_account = db.query(employee_position_table).filter(employee_position_table.employee_id == hero.employee_id).first()

    if existing_account:
        # If it exists, update the existing record
        existing_account.assignment_date = str(get_datetime())
        existing_account.end_date = str(get_datetime())
        existing_account.position_id = hero.position_id
        
        db.add(existing_account)
        db.commit()
        db.refresh(existing_account)
        return existing_account
    else:
        # If it doesn't exist, insert a new record
        hero.assignment_date = str(get_datetime())
        hero.end_date = str(get_datetime())
        hero_to_db = employee_position_table.from_orm(hero)
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        return hero_to_db
    
    
def edit_employee_position_table(hero: employee_position_table_post, db: Session = Depends(get_session)):
    """
    If the account_money with the given account_id already exists, update it.
    Otherwise, create a new one.

    Args:
        hero (account_money_post): The account_money data to be created or updated.
        db (Session, optional): The database session. Defaults to Depends(get_session).

    Returns:
        account_money: The created or updated account_money
    """
    # Query whether the account_id already exists
    existing_account = db.query(employee_position_table).filter(employee_position_table.employee_id == hero.employee_id).first()

    if existing_account:
        # If it exists, update the existing record
        existing_account.assignment_date = str(get_datetime())
        existing_account.end_date = str(get_datetime())
        existing_account.position_id = hero.position_id
        
        db.add(existing_account)
        db.commit()
        db.refresh(existing_account)
        return existing_account
    else:
        # If it doesn't exist, insert a new record
        hero.assignment_date = str(get_datetime())
        hero.end_date = str(get_datetime())
        hero_to_db = employee_position_table.from_orm(hero)
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        return hero_to_db
    
    
    
def salary_detail_table_post_salary_type_crud(hero: salary_detail_table_post, db: Session = Depends(get_session)):
    """
    If the account_money with the given account_id already exists, update it.
    Otherwise, create a new one.

    Args:
        hero (account_money_post): The account_money data to be created or updated.
        db (Session, optional): The database session. Defaults to Depends(get_session).

    Returns:
        account_money: The created or updated account_money
    """
    # Query whether the account_id already exists
    # existing_account = db.query(salary_detail_table).filter(salary_detail_table.employee_id == hero.employee_id).first()

    # if existing_account:
        
        # If it exists, update the existing record
    # existing_account.create_datetime = str(get_datetime())
    # existing_account.salary_type = hero.salary_type
    # db.add(existing_account)
    # db.commit()
    # db.refresh(existing_account)
        
    # return existing_account
    
    # else:
    #     # If it doesn't exist, insert a new record
    hero.create_datetime = str(get_datetime())
    hero_to_db = salary_detail_table.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
        
    return hero_to_db
    
    
def salary_benefit_for_employee_get_salary_type_crud(salary_type_id: int, db: Session = Depends(get_session)):
    """
    If the account_money with the given account_id already exists, update it.
    Otherwise, create a new one.

    Args:
        hero (account_money_post): The account_money data to be created or updated.
        db (Session, optional): The database session. Defaults to Depends(get_session).

    Returns:
        account_money: The created or updated account_money
    """
    # Query whether the account_id already exists


    
    import pandas as pd
    
    # Query whether the salary_type_id already exists
    existing_account = db.query(salary_benefit_for_employee).filter(
        salary_benefit_for_employee.salary_type_id == str(salary_type_id)
    ).all()

    if existing_account:
        # If it exists, convert it to a DataFrame
        df_existing_account = sqmodel_to_df(existing_account)
        
        print(df_existing_account)
        
        # Use merge_dataframe_on_keyword_jia_yi_fang to merge the DataFrame
        updated_account_df = merge_dataframe_on_keyword_jia_yi_fang(df=df_existing_account, keyword="salary_type_id")
        
        updated_account_df = updated_account_df.to_json(orient='records')
        
        return json.loads(updated_account_df)

    return existing_account




from sqlalchemy import func, select, asc
import json
import pandas as pd
def salary_detail_table_get(employee_id: int, db: Session):
    """
    Fetch and group salary details by employee_id and salary_goods_id, returning the most recent entry.

    Args:
        employee_id (int): The employee ID to query.
        db (Session): The database session.

    Returns:
        JSON: The processed salary details.
    """
    
    # Adjusted query to return only the most recent entry per employee_id and salary_goods_id using window function
    query_ = """
    
    WITH RankedSalaryDetails AS (
        
        SELECT
            salary_detail_table.salary_detail_id,
            salary_detail_table.employee_id,
            salary_detail_table.salary_goods_id,
            salary_detail_table.amount,
            salary_detail_table.create_datetime,
            ROW_NUMBER() OVER (
                PARTITION BY salary_detail_table.employee_id, salary_detail_table.salary_goods_id
                ORDER BY salary_detail_table.create_datetime DESC  -- Order by create_datetime in descending order
            ) AS rank
        FROM
            salary_detail_table
        WHERE
            salary_detail_table.employee_id = :employee_id
    )
    
    SELECT
        salary_detail_id,
        employee_id,
        salary_goods_id,
        amount,
        create_datetime
    FROM
        RankedSalaryDetails
    WHERE
        rank = 1
    ORDER BY
        create_datetime DESC;
        
    """
    def get_data_1():
    
        sql_data_1 = """ SELECT p.position_id,e.employee_id, e.salary_type, p.salary_type_id,p.goods_id
        FROM employee_table e
        INNER JOIN position_table p
            ON e.salary_type = p.salary_type_id
        WHERE e.employee_id = :employee_id and p.goods_id is not null;
        """
        
        
        result_proxy_1 = db.execute(sql_data_1, {'employee_id': employee_id})
        results_1 = result_proxy_1.fetchall()
        column_names = result_proxy_1.keys()
        df_1 = pd.DataFrame(results_1, columns=column_names)
        
        df_1 = df_1[['goods_id']]
        df_1 = df_1.rename(columns = {'goods_id':'goods_id'})
        
        return df_1
    
    
    def get_data_2():
    
        sql_data_1 = """ SELECT p.benefit_id,e.employee_id, e.salary_type, p.salary_type_id
        FROM employee_table e
        INNER JOIN salary_benefit_for_employee p
            ON e.salary_type = p.salary_type_id
        WHERE e.employee_id = :employee_id ;
        """
        
        
        result_proxy_1 = db.execute(sql_data_1, {'employee_id': employee_id})
        results_1 = result_proxy_1.fetchall()
        column_names = result_proxy_1.keys()
        df_1 = pd.DataFrame(results_1, columns=column_names)
        
        df_1 = df_1[['benefit_id']]
        df_1 = df_1.rename(columns = {'benefit_id':'goods_id'})
        
        return df_1
    
    
    A10001 = get_data_1()
    
    
    A10002 = get_data_2()
    
    T10001 = pd.concat([A10001, A10002])
    
    
    print(T10001)
    
    # Execute the query and fetch all results
    result_proxy = db.execute(query_, {'employee_id': employee_id})
    results = result_proxy.fetchall()

    if results:
        # Extract column names from the query result
        column_names = result_proxy.keys()

        # Convert the list of tuples into a Pandas DataFrame
        df_existing_account = pd.DataFrame(results, columns=column_names)
        
        # Assuming merge_dataframe_on_keyword_product is a function that processes the dataframe
        df_existing_account = merge_dataframe_on_keyword_product(df=df_existing_account, keyword="salary_goods_id")
        
        df_existing_account = df_existing_account[df_existing_account['salary_goods_id'].isin (T10001['goods_id'])]
        
        # Convert DataFrame to JSON and return the result
        updated_account_df = df_existing_account.to_json(orient='records')
        return json.loads(updated_account_df)

    return []



def salary_detail_table_social_security_post_salary_type_crud(hero: salary_detail_table_social_security_post, db: Session = Depends(get_session)):
    """
    If the account_money with the given account_id already exists, update it.
    Otherwise, create a new one.

    Args:
        hero (account_money_post): The account_money data to be created or updated.
        db (Session, optional): The database session. Defaults to Depends(get_session).

    Returns:
        account_money: The created or updated account_money
    """
    
    hero.create_datetime = str(get_datetime())
    hero_to_db = salary_detail_table_social_security.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    return hero_to_db