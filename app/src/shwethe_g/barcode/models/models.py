from xmlrpc.client import DateTime
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id


# 资料表名称 pre_order_product 和框架
class barcode_location_base(SQLModel):
    product_id: Optional[int]
    fen_dian: Optional[int]
    shu_ri_qi: Optional[datetime]
    status_code: Optional[int]
    data_sub: dict = Field(sa_column=Column(JSONB), default={})
    barcode_status_code: Optional[int]
    

class barcode_location(barcode_location_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    product_id: Optional[int]
    fen_dian: Optional[int]
    shu_ri_qi: Optional[datetime]
    status_code: Optional[int]
    data_sub: dict = Field(sa_column=Column(JSONB), default={})
    barcode_status_code: Optional[int]
    product_price: Optional[float]


class barcode_location_post(BaseModel):
    product_id: Optional[int]
    shu_ri_qi: Optional[datetime]
    data_sub: Optional[dict] 
    fen_dian : Optional[int] = 3
    status_code : Optional[int] = 60002
    product_price : Optional[float] 

class barcode_location_put(BaseModel):
    auto_id: Optional[int] 
    status_code : Optional[int] = 60001
    
    
class barcode_location_stop(BaseModel):
    # auto_id: Optional[int] 
    barcode_status_code : Optional[int] = 60003
    grop : Optional[str] 
    list : Optional[str]
    rack : Optional[str] 
    fen_dian_id : Optional[str]
    status_code_id : Optional[int]