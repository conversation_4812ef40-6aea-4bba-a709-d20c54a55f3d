from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.config.insert_data.database import get_session as get_session_insert_data


from src.shwethe_g.barcode.crud.crud import (
    barcode_location_post_crud,
    barcode_location_put_crud,
    product_barcode_not,
    barcode_location_stop_crud,
    get_barcodes
)
from src.shwethe_g.barcode.models.models import (
    barcode_location_post,  
    barcode_location_put,
    barcode_location_stop
)

router = APIRouter()

@router.get("/barcode")
async def getbarcode(barcode_status_code : int = None,page: int = 1, limit: int = 10 ,db: Session = Depends(get_session_insert_data)):
    
    return get_barcodes(barcode_status_code=barcode_status_code,page=page,limit=limit, db=db)

@router.post("/barcode")
def postExchangeGoods(hero: barcode_location_post , db: Session = Depends(get_session_insert_data)):
    
    return barcode_location_post_crud(hero=hero, db=db)

@router.put("/barcode")
def postExchangeGoods(hero: barcode_location_put , db: Session = Depends(get_session_insert_data)):
    
    return barcode_location_put_crud(hero=hero, db=db)


@router.get("/barcodeList")
def barcodeList(barcodeId:str , db: Session = Depends(get_session_insert_data)):
    
    return product_barcode_not(barcodeId=barcodeId, db=db)





@router.put("/barcode/stop_barcode")
def postExchangeGoods(hero: barcode_location_stop , db: Session = Depends(get_session_insert_data)):
    
    return barcode_location_stop_crud(hero=hero, db=db)