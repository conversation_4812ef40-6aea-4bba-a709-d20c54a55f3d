from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,or_,and_
from sqlalchemy import create_engine, func,text,desc,Float
from sqlalchemy import select, cast, Integer
from typing import List, Optional
from src.config.insert_data.database import get_session as get_session_insert_data
from src.common.product_name import product_name_package
import json
from src.shwethe_g.barcode.models.models import (
    barcode_location_post,
    barcode_location,
    barcode_location_stop,
    barcode_location_put,
    
)
from src.time_zone import time_zone_function
import numpy as np
import pandas as pd 
from src.time_zone.time_zone_function import get_datetime

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def barcode_location_post_crud(hero:barcode_location_post,db: Session = Depends(get_session_insert_data)):
    
    # 创建查询语句以检查相同的数据是否已存在
    statement = select(barcode_location).where(
        barcode_location.data_sub['grop'].astext == hero.data_sub['grop'],
        cast(barcode_location.data_sub['list'].astext, Float) == hero.data_sub['list'],
        cast(barcode_location.data_sub['rack'].astext, Float) == hero.data_sub['rack'],
        barcode_location.barcode_status_code != 60003
    )

    # 执行查询并获取结果
    existing_data = db.execute(statement).fetchone()

    # 如果找到现有数据，则返回提示
    if existing_data:
        return {'code': 400,'message': 'Data already exists'}

    # 如果不存在相同数据，则继续添加新数据
    hero.shu_ri_qi = get_datetime()
    hero_to_db = barcode_location.from_orm(hero)

    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    
    return {'code': 200,'message': 'Data added successfully','data':hero_to_db}

def barcode_location_put_crud(hero:barcode_location_put,db: Session = Depends(get_session_insert_data)):
    
    print(hero)
    
    statement = select(barcode_location).where(barcode_location.auto_id == hero.auto_id)
    results = db.execute(statement)
    try:
        resultsone = results.scalar_one()
    except NoResultFound:
        raise HTTPException(status_code=404, detail="Item not found")
    
    resultsone.status_code = hero.status_code  # 动态设置状态码
    
    db.add(resultsone)
    db.commit()
    db.refresh(resultsone)
    
    return resultsone

def barcode_location_stop_crud(hero: barcode_location_stop, db: Session = Depends(get_session_insert_data)):

    # 使用正确的类型转换和条件比较来构建查询语句
    statement = select(barcode_location).where(
        barcode_location.data_sub['grop'].astext == hero.grop,
        cast(barcode_location.data_sub['list'].astext, Float) == int(hero.list),
        cast(barcode_location.data_sub['rack'].astext, Float) == int(hero.rack),
        barcode_location.fen_dian == int(hero.fen_dian_id)
    )
    
    # 执行查询并获取所有结果
    results = db.execute(statement).scalars().all()

    # 遍历查询结果并更新每个条目
    for result in results:
        result.barcode_status_code = hero.barcode_status_code

    # 提交事务以保存更改
    db.commit()

    # 更新后的记录可能需要重新加载以反映最新的数据库状态
    for result in results:
        db.refresh(result)
    
    return results


def product_barcode_not(barcodeId=str ,db: Session = Depends(get_session_insert_data)):
    
    from src.common.http.mongodbApi.main import get_product_barcode_not

    A100001 = get_product_barcode_not(barcodeId)

    A100001 = json.loads(A100001.to_json(orient='records'))
    
    return A100001

    
def get_barcodes(
    barcode_status_code: int = None,
    page: int = 1,
    limit: int = 10,
    db: Session = Depends(get_session_insert_data)
) -> dict:
    """
    Get a page of barcodes from the database.

    :param barcode_status_code: Status code to filter the barcodes
    :param page: Current page to fetch
    :param limit: Maximum number of entries to return per page
    :param db: Dependency that provides the database session
    :return: A dictionary containing the current page data and pagination info
    """
    if page < 1:
        raise HTTPException(status_code=400, detail="Page must be greater than 0")

    # 计算需要跳过的条目数
    skip = (page - 1) * limit
    
    # 构建查询基础
    query_base = select(barcode_location)
    if barcode_status_code is not None:
        

        query_base = query_base.where(barcode_location.barcode_status_code == barcode_status_code)
        
        if barcode_status_code == 0:
            
            query_base = query_base.where(barcode_location.barcode_status_code.is_(None))
    
    # 查询所有条形码的数量以计算总页数
    total_count_stmt = (
        select([func.count()])
        .select_from(barcode_location)
        
    )
    if barcode_status_code is not None:
        total_count_stmt = total_count_stmt.where(barcode_location.barcode_status_code == barcode_status_code)
        
        if barcode_status_code == 0:
            total_count_stmt = total_count_stmt.where(barcode_location.barcode_status_code.is_(None))
            
    total_count = db.execute(total_count_stmt).scalar()
    
    # 计算总页数
    total_pages = (total_count + limit - 1) // limit
    
    # 获取数据
    stmt = (
        query_base
        .order_by(desc(barcode_location.auto_id))
        .offset(skip)
        .limit(limit)
    )
    results = db.execute(stmt).scalars().all()
    
    # 返回数据和分页信息
    return {
        "data": results,
        "pagination": {
            "total_count": total_count,
            "total_pages": total_pages,
            "current_page": page,
            "per_page": limit
        }
    }