from fastapi import FastAPI, APIRouter, Body, Response,BackgroundTasks,Header
from pydantic import BaseModel
from helper import generate_id,generate_datetime
from typing import List,Optional
import pandas as pd
import requests
import datetime
import json
import time
router = APIRouter()



@router.get("/work_list", status_code = 200)
def delivery():
    from src.Connect.https_connect import mongodb_data_api
    def get_data_api():
        r = requests.get(mongodb_data_api + '/api/v1/work_list/work_list')
        GF10001 = pd.DataFrame(r.json())
        return GF10001
        
    CX10001 = get_data_api()
    # CX90001 = CX10001.sort_values(['year-month','day'], ascending=[False,False]).head(1)
    CX90001 = CX10001.sort_values(['year-month','day'], ascending=[False,False])
    CX10001 = CX10001[CX10001.set_index(['year-month','day']).index.isin(CX90001.set_index(['year-month','day']).index)]
    CX10001 = CX10001.sort_values(['count'], ascending=[False])
    CX10002 = CX10001.to_json(orient='records')

    return json.loads(CX10002)