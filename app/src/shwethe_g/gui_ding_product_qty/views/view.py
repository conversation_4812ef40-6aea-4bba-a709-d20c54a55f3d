from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
# from src.time_zone.time_zone_function import get_datetime
# from src.config.insert_data.database import get_session as get_session_insert_data
from src.shwethe_g.config import get_session_shwethe_g

from src.shwethe_g.gui_ding_product_qty.crud.crud import (
    gui_ding_product_qty_post_function,
    gui_ding_product_qty_get_function
)
from src.shwethe_g.gui_ding_product_qty.models.models import (
    gui_ding_product_qty_post,
    gui_ding_product_qty
)
router = APIRouter()


@router.post("/product_fix_qty")
def postExchangeGoods(hero: gui_ding_product_qty_post , db: Session = Depends(get_session_shwethe_g)):
    
    return gui_ding_product_qty_post_function(hero=hero, db=db)


@router.get("/product_fix_qty")
def postExchangeGoods(db: Session = Depends(get_session_shwethe_g)):
    
    return gui_ding_product_qty_get_function( db=db)