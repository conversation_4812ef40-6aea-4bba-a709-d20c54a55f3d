from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,or_,and_
from sqlalchemy import text
from typing import List, Optional
from src.shwethe_g.config import get_session_shwethe_g
from src.common.product_name import product_name_package
import json
from src.shwethe_g.gui_ding_product_qty.models.models import (
    gui_ding_product_qty_post,
    gui_ding_product_qty
)
from src.time_zone import time_zone_function
import numpy as np
import pandas as pd 
from src.time_zone.time_zone_function import get_datetime

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def gui_ding_product_qty_post_function(hero:gui_ding_product_qty_post,db: Session = Depends(get_session_shwethe_g)):
    hero.shu_riqi_datetime = get_datetime()
    hero_to_db = gui_ding_product_qty.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    
    return hero_to_db


def gui_ding_product_qty_get_function(db: Session = Depends(get_session_shwethe_g)):

    from src.common.product_name import product_name_package

    product_name_package_10001 = product_name_package()


    engine = db.get_bind()

    with engine.connect() as con:

        A10001 = pd.read_sql(text('select * from gui_ding_product_qty where shu_riqi_datetime > current_date - 30'),con)

    VC10001 = product_name_package_10001.select_product_list_id_with_http('product',A10001[['product_id']]).merge(A10001, left_on=['product_id'], right_on=['product_id'], how='inner')

    A100001 = VC10001.to_json(orient='records')

    return json.loads(A100001)


