from xmlrpc.client import DateTime
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id


# 资料表名称 pre_order_product 和框架
class gui_ding_product_qty_base(SQLModel):
    product_id: Optional[int]
    product_gui_ding_qty: Optional[int]
    gui_ding_lei_id: Optional[int]
    fen_dian_id: Optional[int]
    shu_riqi_datetime: Optional[datetime]

class gui_ding_product_qty(gui_ding_product_qty_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    product_id: Optional[int]
    product_gui_ding_qty: Optional[int]
    gui_ding_lei_id: Optional[int]
    fen_dian_id: Optional[int]
    shu_riqi_datetime: Optional[datetime]

class gui_ding_product_qty_post(BaseModel):
    product_id: int
    product_gui_ding_qty: int
    gui_ding_lei_id: Optional[int]
    fen_dian_id: int
    shu_riqi_datetime: Optional[datetime]


