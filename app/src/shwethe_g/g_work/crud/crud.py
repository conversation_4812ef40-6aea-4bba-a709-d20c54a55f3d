from fastapi import WebSocketDisconnect,Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,or_,and_
from sqlalchemy import text
from typing import List, Optional
from src.shwethe_g.config import get_session_shwethe_g
from src.common.product_name import product_name_package
import json
from src.common.http.mongodbApi.main import (
  get_h_list_v2,  get_gu_list_v_2,get_oa_delete,get_h_list_all,get_gu_list,get_h_list,get_qty,get_qty_h,get_gu_delete,get_gu_delete_v_2,get_price,get_h_delete_v_2
)

from src.shwethe_g.g_work.models.models import (
    gu_tb,
    gu_tb_post,
    h_tb_post,
    h_tb,
    bill_for_g_seller,
    bill_for_g_seller_post,
    h_tb_put,
    oa_tb_post,
    oa_tb,
    gu_tb_post_v1
)


from src.time_zone import time_zone_function
import numpy as np
import pandas as pd 
from src.time_zone.time_zone_function import get_datetime

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def gu_list_post(hero:gu_tb_post_v1,db: Session = Depends(get_session_shwethe_g)):
    
    from src.shwethe_g.g_work.crud.crud import getQty
    
    
    HG1001 = getQty(hero.product_id,hero.fen_dian_id)
    HG1001 = pd.DataFrame(HG1001)
    
    if HG1001.empty:
        
        hero.product_p_qty = 0
        
        
    if not HG1001.empty:
        
        
        HG2001 = HG1001.loc[HG1001['type'] == 1].reset_index()
        
        
    
        
        HG1001 = HG1001.loc[HG1001['type'] == 2].reset_index()
        
        if not HG1001.empty:
            hero.product_p_qty = HG1001['product_qty'][0]
        else:
            hero.product_p_qty = 0
            
        
        if not HG2001.empty:
            hero.product_qty_have_gu = HG2001['product_qty'][0]
        else:
            hero.product_qty_have_gu = 0
            

    B100001 = get_gu_delete_v_2(hero.mongodbId)
    
    if B100001.status_code != 200:
        raise HTTPException(status_code=B100001.status_code, detail=json.loads(B100001.text)['detail'])

    if B100001.status_code == 200:
    
        hero.shu_riqi_datetime = get_datetime()
        hero.riqi_datetime = get_datetime()
        hero_to_db = gu_tb.from_orm(hero)

        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)

        return hero_to_db
    
def oa_list_post(hero:oa_tb_post,db: Session = Depends(get_session_shwethe_g)):
    
    B100001 = get_oa_delete(hero.mongodbId)
    
    if B100001.status_code != 200:
            raise HTTPException(status_code=B100001.status_code, detail=json.loads(B100001.text)['detail'])

    if B100001.status_code == 200:
        A10001 = get_qty(hero.product_id)
        
        if not A10001.empty:
            A10001 = A10001.loc[A10001['fen'] == hero.fen_dian_id]
            A10001 = A10001.loc[A10001['type'] == 2]
        
        if A10001.empty:
            hero.product_p_qty = 0
            
        if not A10001.empty:
            A10001 = A10001.reset_index()
            hero.product_p_qty = A10001['product_qty'][0]
        
        # A10001 = A10001[A10001['fen'].isin ([1,2,3])]
        
        
        # B100001 = get_gu_delete_v_2(hero.mongodbId)
        
        # if B100001.status_code != 200:
            
        #     raise HTTPException(status_code=B100001.status_code, detail=json.loads(B100001.text)['detail'])
        
        # if B100001.status_code == 200:
            
        hero.shu_riqi = get_datetime()
        hero.riqi = get_datetime()
        
        hero_to_db = oa_tb.from_orm(hero)
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        
        return hero_to_db
    
def gu_list_post_minual(hero:gu_tb_post,db: Session = Depends(get_session_shwethe_g)):

    hero.shu_riqi_datetime = get_datetime()
    hero.riqi_datetime = get_datetime()
    hero_to_db = gu_tb.from_orm(hero)

    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)

    return hero_to_db


def gu_list_get(fen:int, db: Session = Depends(get_session_shwethe_g)):
    A100001 = get_gu_list(fen)
    
    if A100001.empty:
        return []

    # 确保 GROP 列存在
    if 'GROP' not in A100001.columns:
        A100001['GROP'] = ''  # 或根据业务逻辑设置默认值

    A100001['GROP'] = A100001['GROP'].astype(str)
    A100001['GROP'] = A100001['GROP'].replace('0', 'ZZZZZZ')
    A100001 = A100001.sort_values(['GROP'], ascending=[True])

    HG10001 = A100001.loc[A100001['qty_x'] > 0]
    HG10002 = A100001.loc[A100001['qty_x'] <= 0]

    TT10001 = pd.concat([HG10001, HG10002]).round(3).head(50)

    A100001 = TT10001.to_json(orient='records')

    return json.loads(A100001)


async def h_list_get_v1(fen:int,db: Session = Depends(get_session_shwethe_g)):

    A100001 = await get_h_list_v2(fen)
    
    A100001 = A100001.to_json(orient='records')

    return json.loads(A100001)

from fastapi import WebSocket

class ConnectionManager:
    def __init__(self):
        self.active_connections: list[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            await connection.send_text(message)


manager = ConnectionManager()


# @app.websocket("/ws/gu_list")
async def h_list_ws(websocket: WebSocket, fen: int, db: Session = Depends(get_session_shwethe_g)):
    await manager.connect(websocket)
    try:
        while True:
            
            data = await websocket.receive_text()
            
            print(f"Received data: {data}")  # 打印接收到的数据
            
            if data == 'get_data':
                result = await h_list_get_v1(fen, db)  # Assume this function fetches the required data
                await websocket.send_text(json.dumps(result))  # Send the result as a JSON string
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        await manager.broadcast(f"Client #{fen} left the chat")
    
    # await websocket.accept()  # 接受 WebSocket 连接
    # while True:  # 保持连接打开状态，直到某个条件触发关闭
    #     data = await websocket.receive_text()  # 假设客户端会发送一些文本数据来触发或更新数据获取
    #     if data == 'get_data':
    #         # db = get_session_shwethe_g()  # 假设这是获取数据库会话的函数
    #         # result = gu_list_get(fen, db)  # 获取数据
    #         await websocket.send_text("json.dumps(result)")  # 将结果发送回客户端
    #     elif data == 'close':
    #         await websocket.close()  # 客户端请求关闭连接
    #         break


def h_list_get(fen:int,db: Session = Depends(get_session_shwethe_g)):
    
    A100001 = get_h_list(fen).head(30)

    A100001 = A100001.to_json(orient='records')

    return json.loads(A100001)


def h_list_get_statics(db: Session = Depends(get_session_shwethe_g)):
    
    # """"  example value [  {
    #         "id": 73060,
    #         "idname": "GW5194",
    #         "mm_name": "ဖြတ်ဝိုင်းပြား",
    #         "d_name": "Pumpkin 4'' ( 4AB 23110 ) PK",
    #         "Idname": 73060,
    #         "fen": 3,
    #         "qty": 49,
    #         "type": "H",
    #         "U": 3,
    #         "barcode_auto_id": 1000107009,
    #         "barcode_id": 73060,
    #         "GROP": "D11",
    #         "list": 4,
    #         "rack": 2,
    #         "barcodeLocationId": 1000107009,
    #         "p_qty": 49
    #     }
    # ] """
    
    A100001 = get_h_list_all()
    
    A100002 = A100001.rename(columns={'type': 'type_spec','idname': 'count'})
    
    A100003 = A100002.groupby(['type_spec','fen'])['count'].count().reset_index()

    A100001 = A100003.to_json(orient='records')

    return json.loads(A100001)


def h_list_work_get(fen:int=None,db: Session = Depends(get_session_shwethe_g)):
    
    from src.common.DataFrameTimeZone import DataFrameTimeZone
    Function = product_name_package()
    DataFrameTimeZone10001 = DataFrameTimeZone()
    
    
    
    engine = db.get_bind()
    with engine.connect() as con:
        if fen == None:
            A900001 = pd.read_sql(text("select * from h_tb where shu_riqi_datetime > current_date"),con)
            
            if A900001.empty:
                return []
            B900001 = Function.select_product_list_id_with_http(nameStr='product',df=A900001[['product_id']])
            A900001 = A900001.merge(B900001, on=['product_id'], how='right')
            A900001 = DataFrameTimeZone10001.convert_timezone(A900001)
            A900001['shu_riqi_datetime'] = A900001['shu_riqi_datetime'].astype(str)
            A900001 = A900001.sort_values(['shu_riqi_datetime'], ascending=[False])
            A900001['jia_yi_id'] = A900001['user_id']
            
            from src.common.jia_yi_name2 import jia_yi_name_package
            A100001 = jia_yi_name_package()
            
            A100002 = A100001.select_data_list_id_with_http_v2(A900001[['jia_yi_id']],'jia_yi')
            
            A100002 = A100002.rename(columns={'jia_yi_id': 'user_id','jia_yi_idname': 'user_idname','jia_yi_mm_name': 'user_mm_name'})
            A900001 = A900001.drop(columns=['jia_yi_id'])
            BV10001 = A900001.merge(A100002, on=['user_id'], how='inner')
            
            # X1001 = A100002.merge(FD10001, on=['jia_yi_id'], how='inner')
            A900002 = BV10001.to_json(orient='records')
            return json.loads(A900002)
        
        
def h_list_work_put(h_tb_put:h_tb_put,db: Session = Depends(get_session_shwethe_g)):
    
    # 我这个 function 我想 做 update 的功能 但是 我不知道 怎么做 h_tb table 的 update
    
    result = db.exec(select(h_tb).where(h_tb.auto_id == h_tb_put.auto_id)).first()
    result.status_code_id = h_tb_put.status_code_id
    db.add(result)
    db.commit()
    db.refresh(result)
    
    
    return result
    
    # from src.common.DataFrameTimeZone import DataFrameTimeZone
    # Function = product_name_package()
    # DataFrameTimeZone10001 = DataFrameTimeZone()
    
    # engine = db.get_bind()
    # with engine.connect() as con:
    #     if fen == None:
    #         A900001 = pd.read_sql(text("select * from h_tb where shu_riqi_datetime > current_date"),con)
    #         B900001 = Function.select_product_list_id_with_http(nameStr='product',df=A900001[['product_id']])
    #         A900001 = A900001.merge(B900001, on=['product_id'], how='right')
    #         A900001 = DataFrameTimeZone10001.convert_timezone(A900001)
    #         A900001['shu_riqi_datetime'] = A900001['shu_riqi_datetime'].astype(str)
    #         A900002 = A900001.to_json(orient='records')
    #         return json.loads(A900002)

def getQty(productId:int ,fen:int, db: Session = Depends(get_session_shwethe_g)):
    
    A10001 = get_qty(productId)

    A10001 = A10001.loc[A10001['fen'] == fen]
    A10001 = A10001[A10001['fen'].isin ([1,2,3])]

    if A10001.empty:
        return []

    A10002 = A10001.to_json(orient='records')

    return json.loads(A10002)

def getQtyH(productId:int ,fen:int, db: Session = Depends(get_session_shwethe_g)):
    
    A10001 = get_qty_h(productId)
    
    if A10001.empty:
        
        return []
    
    A10001 = A10001.loc[A10001['fen'] == fen]
    A10001 = A10001[A10001['fen'].isin ([1,2,3])]

    if A10001.empty:
        return []

    A10002 = A10001.to_json(orient='records')

    return json.loads(A10002)

def h_list_post(hero:h_tb_post,db: Session = Depends(get_session_shwethe_g)):
    

    
    
    # 获取商品库存数量
    getqty = getQtyH(hero.product_id,hero.fen_dian_id)
    T10001 = pd.DataFrame(getqty)
    # 获取商品价格
    
    product_price = get_price(hero.product_id)
    hero.product_price = product_price
    
    # 如果商品价格为 False，将价格设置为0
    if product_price == False:
        product_price = 0
        
        
    # 判断获取到的库存信息是否为空
    if T10001.empty:
        # 处理 GU 类型商品的库存
        product_qty_p_Gu = 0
        product_qty_p_Gu_ans = hero.product_qty_gu - product_qty_p_Gu
        # 处理 G 类型商品的库存
        product_qty_p_G = 0
        product_qty_p_G_ans = hero.product_qty_g - product_qty_p_G 
        # 计算商品库存差值总和，及其对应的总价值
        product_qty_p_G_ans_toTal = product_qty_p_Gu_ans + product_qty_p_G_ans
        product_qty_p_G_ans_toTal_price = product_qty_p_G_ans_toTal * product_price
        
    # 判断获取到的库存信息是否为空
    if not T10001.empty:
        
        # 处理 GU 类型商品的库存
        T20001 = T10001.loc[T10001['type'] == 1].reset_index()
        if not T20001.empty:
            product_qty_p_Gu = T20001['product_qty'][0]
        else:
            product_qty_p_Gu = 0
        product_qty_p_Gu_ans = hero.product_qty_gu - product_qty_p_Gu
        
        # 处理 G 类型商品的库存
        T30001 = T10001.loc[T10001['type'] == 2].reset_index()
        if not T30001.empty:
            product_qty_p_G = T30001['product_qty'][0]
        else:
            product_qty_p_G = 0
        product_qty_p_G_ans = hero.product_qty_g - product_qty_p_G 
        
        # 计算商品库存差值总和，及其对应的总价值
        product_qty_p_G_ans_toTal = product_qty_p_Gu_ans + product_qty_p_G_ans
        product_qty_p_G_ans_toTal_price = product_qty_p_G_ans_toTal * product_price
        print(abs(product_qty_p_G_ans_toTal_price))
        print("product_qty_p_G_ans_toTal_price")
    
    # 更新 hero 对象的 data_sub 属性
    hero.data_sub = {'product_qty_n_G':hero.product_qty_g,'product_qty_n_Gu':hero.product_qty_gu,'product_qty_p_G':product_qty_p_G,'product_qty_p_Gu':product_qty_p_Gu}
    
    # 更新 hero 对象的 product_qty_g 和 product_qty_gu 属性
    hero.product_qty_g = product_qty_p_G_ans
    hero.product_qty_gu = product_qty_p_Gu_ans
    
    # 更新 hero 对象的 shu_riqi_datetime 和 riqi_datetime 属性
    hero.shu_riqi_datetime = get_datetime()
    hero.riqi_datetime = get_datetime()

    # 将 hero 对象转换为数据库模型对象
    
    # 当价格变动在 -500 到 500 之间时，状态码设置为 200
    
    LK10001 = abs(product_qty_p_G_ans_toTal_price)
    LK10002 = abs(product_qty_p_G_ans_toTal)
    LK10003 = LK10001 - LK10002
    
    print(LK10003)
    print("LK10003")
    
    
    
    if abs(product_qty_p_G_ans_toTal_price) == 0 :
        hero.status_code_id = 200
        B100001 = get_h_delete_v_2(hero.mongodbId)
        if B100001.text != str(200):
            raise HTTPException(status_code=404, detail="this data not have in database")
            
            
    hero_to_db = h_tb.from_orm(hero)
    # 将 hero_to_db 对象添加到数据库会话，并进行提交和刷新操作
    db.add(hero_to_db)
    db.commit()
    
    db.refresh(hero_to_db)
    
    # 打印 product_qty_p_G_ans_toTal_price，用于调试
    print(product_qty_p_G_ans_toTal_price)
    print("product_qty_p_G_ans_toTal_price")
    
    # 如果 product_qty_p_G_ans_toTal_price 大于 500 或者小于 500，抛出 HTTP 异常
    
    if abs(product_qty_p_G_ans_toTal_price) > 0 :
        
        raise HTTPException(status_code=404, detail="max price")
    

    return hero_to_db


def workList(fen:int, db: Session = Depends(get_session_shwethe_g)):

    
    from src.common.jia_yi_name import jia_yi_name_package

    engine = db.get_bind()

    with engine.connect() as con:

        A10001 = pd.read_sql(text("select * from gu_tb where shu_riqi_datetime > current_date and fen_dian_id = '%s' "%(fen)),con)
        A10001['type'] = 'gu'
        
        # A20001 = pd.read_sql(text("select * from gu_tb where shu_riqi_datetime > current_date and fen_dian_id = '%s' "%(fen)),con)
        A20001 = pd.read_sql(text("select * from h_tb where shu_riqi_datetime > current_date and fen_dian_id = '%s'  and status_code_id = 200 "%(fen)),con)
        A20001['type'] = 'h'
        
        A10001 = pd.concat([A10001,A20001])

        # A10001 = A10001.groupby(['user_id'])['auto_id'].count().reset_index()
        A10001 = A10001.groupby(['user_id','type'])['auto_id'].count().reset_index()
        # 接下来的 你可以 帮我 改吗
        
        A10001['jia_yi_id'] = A10001['user_id']

        A10001 = A10001.sort_values(['auto_id'], ascending=[False])

        T10001 = jia_yi_name_package()

        T10002 = T10001.select_data_list_id_with_http(A10001,'parner')

        A10001 = A10001.merge(T10002, on=['jia_yi_id'], how='inner')

        A10001 = A10001.to_json(orient='records')


    return json.loads(A10001)



def workListPost(hero:bill_for_g_seller_post, db: Session = Depends(get_session_shwethe_g)):
    
    
    hero_to_db = bill_for_g_seller.from_orm(hero)

    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)


    return 200

def workListGet(userId:int, db: Session = Depends(get_session_shwethe_g)):
    
    
    engine = db.get_bind()

    with engine.connect() as con:

        A10001 = pd.read_sql(text("select * from bill_for_g_seller where shu_riqi_datetime > current_date and seller_id = '%s' "%(userId)),con)

    A10001 = A10001.to_json(orient='records')

    return json.loads(A10001)

