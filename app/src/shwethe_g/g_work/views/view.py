from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session
from fastapi import WebSocket


from helper import generate_datetime_id
# from src.time_zone.time_zone_function import get_datetime
# from src.config.insert_data.database import get_session as get_session_insert_data
from src.shwethe_g.config import get_session_shwethe_g

from src.shwethe_g.g_work.crud.crud import (
    gu_list_get,
    gu_list_post,h_list_get,
    h_list_post,
    getQty,
    workList,
    workListPost,
    workListGet,
    gu_list_post_minual,
    h_list_work_get,
    h_list_work_put,
    h_list_get_statics,
    oa_list_post,
    h_list_ws
)
from src.shwethe_g.g_work.models.models import (
    gu_tb,
    gu_tb_post,
    h_tb_post,
    bill_for_g_seller,
    bill_for_g_seller_post,
    h_tb_put,
    oa_tb_post,
    gu_tb_post_v1
)
router = APIRouter()

from fastapi import WebSocket

@router.websocket("/ws/h_list")
async def websocket_endpoint(websocket: WebSocket, fen: int, db: Session = Depends(get_session_shwethe_g)):
    
    return await h_list_ws(websocket=websocket,fen=fen, db=db)
    
@router.post("/gu_list")
def postExchangeGoods(hero: gu_tb_post_v1 , db: Session = Depends(get_session_shwethe_g)):
    return gu_list_post(hero=hero, db=db)

@router.post("/gu_list/minual/")
def postExchangeGoods(hero: gu_tb_post , db: Session = Depends(get_session_shwethe_g)):
    return gu_list_post_minual(hero=hero, db=db)


@router.get("/gu_list")
def postExchangeGoods(fen:int,db: Session = Depends(get_session_shwethe_g)):
    return gu_list_get( fen=fen,db=db)


@router.get("/h_list")
def postExchangeGoods(fen:int,db: Session = Depends(get_session_shwethe_g)):
    return h_list_get( fen=fen,db=db)

@router.post("/h_list")
def postExchangeGoods(hero: h_tb_post , db: Session = Depends(get_session_shwethe_g)):
    return h_list_post(hero=hero, db=db)

@router.get("/h_list_work")
def postExchangeGoods(fen:int=None,db: Session = Depends(get_session_shwethe_g)):
    return h_list_work_get(fen=fen,db=db)

@router.put("/h_list_work/statuscode")
def postExchangeGoods(h_tb_put:h_tb_put=None,db: Session = Depends(get_session_shwethe_g)):
    return h_list_work_put(h_tb_put=h_tb_put,db=db)

@router.get("/qty")
def postExchangeGoods(productId:int,fen:int, db: Session = Depends(get_session_shwethe_g)):
    return getQty(productId=productId,fen=fen,db=db)

@router.get("/workList/gu")
def postExchangeGoods(fen:int, db: Session = Depends(get_session_shwethe_g)):
    return workList(fen=fen,db=db)

@router.post("/billForG")
def postExchangeGoods(hero:bill_for_g_seller_post, db: Session = Depends(get_session_shwethe_g)):

    return workListPost(hero=hero,db=db)

@router.get("/billForG")
def postExchangeGoods(userId:int, db: Session = Depends(get_session_shwethe_g)):

    return workListGet(userId=userId,db=db)

@router.get("/statics/workList")
def staticsList(db: Session = Depends(get_session_shwethe_g)):
    return h_list_get_statics(db=db)

@router.post("/oa_list")
def postExchangeGoods(hero:oa_tb_post, db: Session = Depends(get_session_shwethe_g)):
    return oa_list_post(hero=hero,db=db)