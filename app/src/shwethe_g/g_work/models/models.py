from xmlrpc.client import DateTime
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id


# 资料表名称 pre_order_product 和框架
class gu_tb_base(SQLModel):
    product_id: Optional[int]
    product_qty: Optional[float]
    fen_dian_id: Optional[int]
    user_id: Optional[int]
    product_p_qty: Optional[float]
    shu_riqi_datetime: Optional[datetime]
    riqi_datetime: Optional[datetime]

class gu_tb(gu_tb_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    product_id: Optional[int]
    product_qty: Optional[float]
    product_want_qty: Optional[float]
    product_qty_have_gu: Optional[float]
    fen_dian_id: Optional[int]
    user_id: Optional[int]
    product_p_qty: Optional[float]
    shu_riqi_datetime: Optional[datetime]
    riqi_datetime: Optional[datetime]
    data_sub: dict = Field(sa_column=Column(JSONB), default={})
    data_sub_name: dict = Field(sa_column=Column(JSONB), default={})
    image: dict = Field(sa_column=Column(JSONB), default={})



class gu_tb_post(BaseModel):
    product_id: int
    product_qty: float
    fen_dian_id: int
    user_id: int
    product_p_qty: float
    shu_riqi_datetime: Optional[datetime]
    riqi_datetime: Optional[datetime]
    data_sub: dict = {}
    data_sub_name: dict = {}
    image: dict = {}
    mongodbId: str
    
class gu_tb_post_v1(BaseModel):
    product_id: int
    product_qty: float
    product_want_qty: float
    product_qty_have_gu: Optional[float] = 0
    fen_dian_id: int
    user_id: int
    product_p_qty: float
    shu_riqi_datetime: Optional[datetime]
    riqi_datetime: Optional[datetime]
    data_sub: dict = {}
    data_sub_name: dict = {}
    image: dict = {}
    mongodbId: str


class h_tb_base(SQLModel):
    product_id: Optional[int]
    fen_dian_id: Optional[int]
    product_price: Optional[float]
    user_id: Optional[int]
    product_qty_gu: Optional[float]
    product_qty_g: Optional[float]
    shu_riqi_datetime: Optional[datetime]
    riqi_datetime: Optional[datetime]
    status_code_id : Optional[int]
    data_sub: dict = Field(sa_column=Column(JSONB), default={})
    data_sub_name: dict = Field(sa_column=Column(JSONB), default={})
    image: dict = Field(sa_column=Column(JSONB), default={})

class h_tb(h_tb_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    product_id: Optional[int]
    product_price: Optional[float]
    fen_dian_id: Optional[int]
    user_id: Optional[int]
    product_qty_gu: Optional[float]
    product_qty_g: Optional[float]
    shu_riqi_datetime: Optional[datetime]
    riqi_datetime: Optional[datetime]
    status_code_id : Optional[int]
    data_sub: dict = Field(sa_column=Column(JSONB), default={})
    data_sub_name: dict = Field(sa_column=Column(JSONB), default={})
    image: dict = Field(sa_column=Column(JSONB), default={})

class h_tb_post(BaseModel):
    product_id: int
    product_price: Optional[float]
    product_qty_g: float
    product_qty_gu: float
    fen_dian_id: int
    user_id: int
    status_code_id : Optional[int] = 300
    shu_riqi_datetime: Optional[datetime]
    riqi_datetime: Optional[datetime]
    data_sub: dict = {}
    data_sub_name: dict = {}
    image: dict = {}
    mongodbId: str



# 资料表名称 pre_order_product 和框架
class bill_for_g_seller_base(SQLModel):
    bill_id: Optional[str]
    seller_id: Optional[int]

class bill_for_g_seller(bill_for_g_seller_base, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    bill_id: Optional[str]
    seller_id: Optional[int]


class bill_for_g_seller_post(BaseModel):
    bill_id: str
    seller_id: int
    
    
class h_tb_put(BaseModel):
    auto_id: int
    status_code_id : int
    
    
# 资料表名称 pre_order_product 和框架
# class bill_for_g_seller_base(SQLModel):
#     bill_id: Optional[str]
#     seller_id: Optional[int]

# class bill_for_g_seller(bill_for_g_seller_base, table=True):
#     auto_id: Optional[int] = Field(default=None, primary_key=True)
#     bill_id: Optional[str]
#     seller_id: Optional[int]

# 资料表名称 pre_order_product 和框架
class oa_tb_base(SQLModel):
    product_id: Optional[int]
    product_qty: Optional[float]
    product_price: Optional[float]
    bi_zhi: Optional[int]
    status_code_id: Optional[int]
    fen_dian_id: Optional[int]
    product_p_qty: Optional[float]
    user_id: Optional[int]
    image: dict = {}
    
    

class oa_tb(oa_tb_base, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    product_id: Optional[int]
    product_qty: Optional[float]
    product_price: Optional[float]
    bi_zhi: Optional[int]
    user_id: Optional[int]
    status_code_id: Optional[int]
    fen_dian_id: Optional[int]
    product_p_qty: Optional[float]
    shu_riqi: Optional[datetime]
    riqi: Optional[datetime]
    image: dict = Field(sa_column=Column(JSONB), default={})   
    
    
class oa_tb_post(BaseModel):
    product_id: Optional[int]
    product_qty: Optional[float]
    product_price: Optional[float] = 0
    bi_zhi: Optional[int] = 138
    status_code_id: Optional[int]
    fen_dian_id: Optional[int]
    product_p_qty: Optional[float]
    shu_riqi: Optional[datetime]
    riqi: Optional[datetime]
    user_id: Optional[int]
    image: Optional[dict]
    mongodbId: str
    
    
    