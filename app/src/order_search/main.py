from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time

router = APIRouter()


@router.get("/product_search", status_code = 200)
def product_search(idname : str):

    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(https_connect)
    from src.Connect.https_connect import mongodb_data_api
    params = {'ID':idname}
    try:
        r = requests.get(mongodb_data_api + '/api/v1/search/product',params=params
                            )

        print(r.json())
        GF10001 = pd.DataFrame(json.loads(r.json()))
        GF10001 = GF10001.to_json(orient='records',lines=True)
    except:
        GF10001 = False
    
    return GF10001


@router.get("/product_price", status_code = 200)
def product_search(product_id : int):

    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(https_connect)
    from src.Connect.https_connect import mongodb_data_api
    params = {'product_id':product_id}
    r = requests.get(mongodb_data_api + '/api/v1/price/price',params=params
                        )
    # print(r.json())
    GF10001 = pd.DataFrame(r.json())
    print("GF10001")
    print(GF10001)
    if not GF10001.empty:
        GF10001=GF10001.groupby(['id'])['price'].max().reset_index()
        GF10001 = GF10001.to_json(orient='records',lines=True)
        return json.loads(GF10001)
    else:
        GF10001 = {}
        return json.loads(json.dumps(GF10001))
    

@router.get("/jia_yi_search", status_code = 200)
def product_search(idname : str):

    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(https_connect)
    from src.Connect.https_connect import mongodb_data_api
    params = {'ID':idname}
    try:
        r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name',params=params
                            )

        print(r.json())
        GF10001 = pd.DataFrame(json.loads(r.json()))
        GF10001 = GF10001.to_json(orient='records',lines=True)
    except:
        GF10001 = False
    
    return GF10001

@router.get("/fen_dian_list", status_code = 200)
def fen_dian_list():

    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    def check_type_list():
        HG100001 = pd.read_sql('select fen_dian_id,fen_dian_name from fen_dian  ',psycopg2_conn_insert_data_s)
        
        return HG100001
    GF10001 = check_type_list()

    psycopg2_conn_insert_data_s.close()

    GF10001 = GF10001.to_json(orient='records')
    
    return GF10001

@router.get("/parner_name_list", status_code = 200)
def parner_name_list():
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api

    def check_type_list():
        HG100001 = pd.read_sql('select parner_id from parner_name_list  ',psycopg2_conn_insert_data_s)
        
        return HG100001
    GF10001 = check_type_list()

    def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                     json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'jia_yi_id': str+'_id','jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mmname'})
        print(GF10001)

        return GF10001

    H3000001 = GF10001[['parner_id']]
    H3000001 = H3000001.rename(columns={'parner_id': 'jia_yi_id'})
    print(H3000001)
    H3000001 = get_data_api(H3000001,"parner")
    GG100009 = H3000001.merge(GF10001, on=['parner_id'], how='inner')
    # GG100009 = GG100009.drop(columns=['che_liang'])

    psycopg2_conn_insert_data_s.close()

    GF10001 = GG100009.to_json(orient='records')
    
    return json.loads(GF10001)