from src.Connect.postgresql_connect_db_insert_data_v2 import psycopg2_conn_insert_data_engine
from src.Connect.postgresql_connect_db_insert_data_s_v2 import psycopg2_conn_insert_data_s_engine
from src.connection.db_connection import get_connection
from src.Connect.mongo_connect import client
from sqlalchemy import create_engine
from sqlalchemy.sql import text
from datetime import datetime
import json
import time
import random
import requests
from src.Connect.https_connect import mongodb_data_api
import pandas as pd
from typing import List, Optional
from sqlmodel import Session, select,SQLModel,or_,and_




def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def get_shwethe_in_goods(product_id):
    with psycopg2_conn_insert_data_s_engine.connect() as connection:
        # 获取今天的日期
        today_date = datetime.today().date()
        
        # 使用参数化查询来提高安全性
        sql = """
        SELECT order_body_in.jia_yi_fang as jia_fang,product_in.product_id as product_id,product_in.product_qty as product_qty
        FROM order_body_in 
        INNER JOIN product_in 
        ON order_body_in.auto_id = product_in.order_che_ci_body_in_id 
        WHERE product_in.product_id = %s AND product_in.shu_ri_qi::date = %s 
        """
        
        # 执行查询
        V1001 = pd.read_sql(sql, connection, params=(product_id, today_date))
        
        if not V1001.empty:
            V1001['product_qty'] = V1001['product_qty'] * -1
        
        return V1001
    
    

def get_shwethe_in_goods_parner(product_id):
    with psycopg2_conn_insert_data_s_engine.connect() as connection:
        # 获取今天的日期
        today_date = datetime.today().date()
        
        # 使用参数化查询来提高安全性
        sql = """
        SELECT order_body_in.jia_yi_fang as jia_fang,product_in.product_id as product_id,product_in.product_qty as product_qty
        FROM order_body_in 
        INNER JOIN product_in 
        ON order_body_in.auto_id = product_in.order_che_ci_body_in_id 
        WHERE product_in.product_id = %s AND product_in.shu_ri_qi::date = %s and product_in.lei_type_list_id = 5003
        """
        
        # 执行查询
        V1001 = pd.read_sql(sql, connection, params=(product_id, today_date))
        
        if not V1001.empty:
            V1001['product_qty'] = V1001['product_qty'] * -1
        
        return V1001
    

def delete_insert_order_from_postgsql(auto_id):
    with psycopg2_conn_insert_data_engine.connect() as connection:
        # 使用参数化查询来提高安全性
        auto_id_str = str(auto_id)
        sql = text("update insert_order set is_show = false  WHERE id = :auto_id")
        connection.execute(sql, auto_id=auto_id_str)
        
        
    with psycopg2_conn_insert_data_engine.connect() as connection:
        # Use parameterized queries for security
        auto_id_str = str(auto_id)
        
        # Use the JSONB operators and array functions to find the specific id inside the JSONB array
        sql = text("""
            UPDATE insert_order
            SET is_show = false
            WHERE EXISTS (
                SELECT 1
                FROM jsonb_array_elements(data) AS elem
                WHERE elem->>'id' = :auto_id
            )
        """)
        
        connection.execute(sql, {'auto_id': auto_id_str})
        
        
        
def delete_insert_order_from_mongo(auto_id):

    view = client['view']
    order_list = view['order_list']
    
    # 使用 $pull 操作符删除特定 iid 的条目
    # 获取今天的日期字符串
    today_date_str = datetime.now().strftime("%Y-%m-%d")
    
    # 使用 $pull 操作符删除特定 iid 的条目，基于今天的日期
    result = order_list.update_one(
        {'date': today_date_str},  # 使用 date 字段和今天的日期来定位文档
        {'$pull': {'data_order': {'iid': str(auto_id)}}}  # 从 data_order 数组中移除匹配的条目
    )
    
    # 打印更新结果
    print(f'Modified {result.modified_count} document(s)')
    
    
    
def delete_insert_order(auto_id):
    
    delete_insert_order_from_postgsql(auto_id)
    delete_insert_order_from_mongo(auto_id)
    
    
    
    
def generate_id() -> str:
    x = time.strftime("%Y%m%d%H%M")
    B = random.randint(1, 100000000000000000)
    return f"{x}{B}"


def insert_order_follow_ithem(data):
    data_dict = vars(data)
    for item in data_dict['data']:
        item['uuid'] = generate_id()
        item['id'] = data_dict['id']
        item['user_id'] = data_dict['user_id']

    values = (
        data_dict['datetime'],
        data_dict['date'],
        data_dict['idname'],
        data_dict['sname'],
        data_dict['price'],
        data_dict['id'],
        json.dumps(data_dict['data']),
        data_dict['user_id']
    )
    
    SHWETHE_INSERT_DATA_N_PB_DATACENTER_DB_CON = get_connection('SHWETHE_DB_INSERTDATA_DATACENTER_DB')
    sql_insert = """INSERT INTO insert_order (datetime, date, idname, sname, price, id, data, input_user_id) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)"""
    
    with SHWETHE_INSERT_DATA_N_PB_DATACENTER_DB_CON.connect() as connection:
        connection.execute(sql_insert, values)
        print("Inserted data successfully.")
    
    
def get_post_data_from_api(api_url, ids, timeout=45):
    """
    Retrieves data from an API endpoint for the specified IDs and returns it as a pandas DataFrame.
    
    Parameters:
    api_url (str): URL of the API endpoint
    ids (set): set of IDs for which to retrieve data
    timeout (int): maximum number of seconds to wait for the server's response (default: 45 seconds)
    
    Returns:
    pandas.DataFrame: DataFrame containing the retrieved data
    """
    # Convert the set of IDs to a list (or keep it as a set if the API accepts it)
    id_list = list(ids)
    
    # Send the API request using POST method and get the response
    response = requests.post(api_url, json={'ids': id_list}, timeout=timeout)
    
    # Check if the request was successful
    if response.status_code != 200:
        raise ValueError(f"Request failed with status code {response.status_code}")
    
    # Parse the response as JSON and convert it to a pandas DataFrame
    data = pd.DataFrame(response.json())
    
    return data
    
def get_post_product(data):
    # A10001 = mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' '
    A10001 = mongodb_data_api+'/api/v1/search/product/in'
    # r = requests.get(mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' ', verify=False, timeout=10) # 10 seconds
    return get_post_data_from_api(A10001, data, 45)



def get_tb_goods_sku_tb_pb_v2():
    
    con = get_connection('SHWETHE_DB_INFO_DATACENTER_DB')
    query1 = f""" SELECT tb_goods_sku.spg_id,tb_goods_sku.images,tb_goods_sku.goods_id,tb_goods_category.mm_name FROM tb_goods_sku INNER JOIN tb_goods_category on  tb_goods_sku.spg_id = tb_goods_category.category_id """
    H80001 = pd.read_sql(query1, con)
    
    return H80001


def get_tb_goods_weight():
    
    con = get_connection('SHWETHE_DB_INSERTDATAS_DATACENTER_DB')
    query1 = """
        WITH RankedProducts AS (
            SELECT 
                _id,
                product_id,
                product_weight,
                ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY ri_qi DESC) AS rn
            FROM 
                product_weight_info
        )
        SELECT 
            _id,
            product_id,
            product_weight
        FROM 
            RankedProducts
        WHERE 
            rn = 1;
        """
    query1 = pd.read_sql(query1, con)
    # query1 = query1.sort_values(['_id'], ascending=[False])
    # query1 = query1.drop_duplicates(subset=['product_id'], keep='first')
    
    return query1
    
def merge_dataframe_on_keyword_product(df, keyword, images=None, weight_data=None):
    """
    Merges the provided DataFrame with product data based on the specified keyword.
    
    Args:
    df (DataFrame): The base DataFrame to be merged.
    keyword (str): The keyword column to join on.
    images (bool, optional): If True, joins additional SKU data. Defaults to None.
    weight_data (bool, optional): If True, joins weight data. Defaults to None.
    
    Returns:
    DataFrame: The merged DataFrame.
    """
    # Remove '_id' from the keyword to get the base keyword
    base_keyword = keyword.replace('_id', '')
    
    # Get the list of IDs from the keyword column
    ids = df[keyword].tolist()
    
    # Fetch product data from the API
    df_api = get_post_product(ids)
    
    print(df_api)
    print("df_api")
    
    
    # Rename columns for better understanding
    df_api_renamed = df_api.rename(columns={
        'product_id': keyword,
        'product_idname': f"{keyword}name",
        'product_mm_name': f"{base_keyword}_mm_name",
        'product_d_name': f"{base_keyword}_d_name",
        'product_th_name': f"{base_keyword}_th_name"
    })
    
    # Merge the DataFrame with the API data
    merged_df = df.merge(df_api_renamed, on=[keyword], how='inner')
    
    # Determine the order of columns
    columns_order = list(df.columns) + [f"{keyword}name", f"{base_keyword}_mm_name", f"{base_keyword}_d_name", f"{base_keyword}_th_name"]
    
    # If images is True, join the SKU data
    if images:
        sku_data = get_tb_goods_sku_tb_pb_v2()
        merged_df = merged_df.merge(sku_data, left_on=keyword, right_on='goods_id', how='left')
        columns_order += ['images', 'goods_id', 'mm_name']
    
    # If weight_data is True, join the weight data
    if weight_data:
        weight_df = get_tb_goods_weight()
        merged_df = merged_df.merge(weight_df, left_on=keyword, right_on='product_id', how='left')
        columns_order += ['product_weight']
    
    # Return the merged DataFrame with the desired column order
    return merged_df[columns_order]



def updateWithKey(database,collection,df, df_keys, db_keys, columnList):
    # Import necessary libraries
    import pandas as pd
    
    from src.Connect.mongo_connect_v2 import  get_mongo_client

    with get_mongo_client() as mongo_client:
        # Access the specific collection
        data = mongo_client[database]
        qty = data[collection]

        # Iterate over the DataFrame rows
        for index, row in df.iterrows():
            # Construct the query using multiple keys
            query = {db_key: row[df_key] for db_key, df_key in zip(db_keys, df_keys)}
            # Construct the new values to be updated
            new_values = {'$set': {col: row[col] for col in columnList if col in row}}
            # Update the document in MongoDB
            qty.update_one(query, new_values)
    
    print("Update completed.")
