from src.common.api.apiclass.api import API
import requests
import requests
import pandas as pd
# from connect.https_connect import mongodb_data_api
from src.Connect.https_connect import mongodb_data_api


def get_api_employee_info(api_key=None, timeout=None):
    base_url = "http://192.168.1.11:8021/shwethe_personal/api/v1/personal/getPersonalNameShow"

    response  = API(base_url, api_key=api_key, timeout=timeout)

    return response.make_request()


def get_data_from_api(api_url, ids):
    """
    Retrieves data from an API endpoint for the specified IDs and returns it as a pandas DataFrame.

    Parameters:
    api_url (str): URL of the API endpoint
    ids (set): set of IDs for which to retrieve data

    Returns:
    pandas.DataFrame: DataFrame containing the retrieved data
    """
    # Convert the set of IDs to a comma-separated string
    id_str = ",".join(map(str, ids))
    #     print(id_str)

    # Construct the URL for the API request by appending the ID string to the endpoint URL
    request_url = f"{api_url}?id={id_str}"
    #     print(request_url)

    # Send the API request and get the response
    response = requests.get(request_url)

    # Check if the request was successful
    if response.status_code != 200:
        raise ValueError(f"Request failed with status code {response.status_code}")

    # Parse the response as JSON and convert it to a pandas DataFrame
    data = pd.DataFrame(response.json())

    return data


def getProductListBathSize(df):

    products_api_url = "http://192.168.1.11:8200/mongodb_data_api/api/v1/search/product/in"
    products_df = get_data_from_api(products_api_url, df)
    # products_df_all = products_df_all.append(products_df)

    return products_df



def get_post_data_from_api(api_url, ids, timeout=45):
    """
    Retrieves data from an API endpoint for the specified IDs and returns it as a pandas DataFrame.
    
    Parameters:
    api_url (str): URL of the API endpoint
    ids (set): set of IDs for which to retrieve data
    timeout (int): maximum number of seconds to wait for the server's response (default: 45 seconds)
    
    Returns:
    pandas.DataFrame: DataFrame containing the retrieved data
    """
    # Convert the set of IDs to a list (or keep it as a set if the API accepts it)
    id_list = list(ids)
    
    # Send the API request using POST method and get the response
    response = requests.post(api_url, json={'ids': id_list}, timeout=timeout)
    
    # Check if the request was successful
    if response.status_code != 200:
        raise ValueError(f"Request failed with status code {response.status_code}")
    
    # Parse the response as JSON and convert it to a pandas DataFrame
    data = pd.DataFrame()
    json_data = response.json()
    
    
    if json_data:
        data = pd.DataFrame(json_data)
    # data = pd.DataFrame(response.json())
    
    return data

def get_post_jia_yi_fang(data):
    
    # A10001 = mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' '
    A10001 = mongodb_data_api+'/api/v1/search/jia_yi_name/in'
    # r = requests.get(mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' ', verify=False, timeout=10) # 10 seconds
    
    return get_post_data_from_api(A10001, data, 45)



def merge_dataframe_on_keyword_jia_yi_fang(df, keyword,inner='inner'):
    """根据指定的关键词合并数据框"""
    base_keyword = keyword.replace('_id', '')  # 移除_id
    ids = df[keyword].tolist()
    df_api = get_post_jia_yi_fang(ids)
    df_api_renamed = df_api.rename(columns={
        'jia_yi_id': keyword,
        'jia_yi_idname': f"{keyword}name",
        'jia_yi_mmname': f"{base_keyword}_mm_name"
    })
    
    # 使用df的顺序合并数据，然后添加新列
    merged_df = df.merge(df_api_renamed, on=[keyword], how=inner)
    
    
    # 组织期望的列顺序
    columns_order = list(df.columns) + [f"{keyword}name", f"{base_keyword}_mm_name"]
    
    return merged_df[columns_order]


def merge_dataframe_on_keyword_product(df, keyword,inner='inner'):
    """根据指定的关键词合并数据框"""
    base_keyword = keyword.replace('_id', '')  # 移除_id
    ids = df[keyword].tolist()
    df_api = get_post_product(ids)
    df_api_renamed = df_api.rename(columns={
        'product_id': keyword,
        'product_idname': f"{keyword}name",
        'product_mm_name': f"{base_keyword}_mm_name",
        'product_d_name': f"{base_keyword}_d_name"
        
    })
    
    # 使用df的顺序合并数据，然后添加新列
    merged_df = df.merge(df_api_renamed, on=[keyword], how=inner)
    
    # 组织期望的列顺序
    columns_order = list(df.columns) + [f"{keyword}name", f"{base_keyword}_mm_name", f"{base_keyword}_d_name"]
    
    return merged_df[columns_order]


def get_post_product(data):
    # A10001 = mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' '
    A10001 = mongodb_data_api+'/api/v1/search/product/in'
    # r = requests.get(mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' ', verify=False, timeout=10) # 10 seconds
    return get_post_data_from_api(A10001, data, 45)


def get_customer_credit_info():
    url = "http://pv-api.shwethe.com/sql2000/api/v1/info_data/customer_credit_info"

    try:
        response = requests.get(url)
        response.raise_for_status()  # Raise an HTTPError if the HTTP request returned an unsuccessful status code
        
        data = response.json()  # If the response content is JSON format
        
        # Handle your data if needed, e.g., print it
        print(data)
        
        return pd.DataFrame(data)

    except requests.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
    except Exception as err:
        print(f"Other error occurred: {err}")
    
    return None  # Return None or an empty dataframe if there's an error

