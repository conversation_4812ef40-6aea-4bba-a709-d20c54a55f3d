import requests
import json 
from src.Connect.https_connect import mongodb_data_api
import pandas as pd

timeout = 30

def delivery_not_have():

    r = requests.get(mongodb_data_api+'/api/v1/delivery/delivery_not_have', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()
    df2 = pd.DataFrame(df2)

    return df2


def delivery_for_sell():

    r = requests.get(mongodb_data_api+'/api/v1/delivery/delivery_for_sell', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()
    df2 = pd.DataFrame(df2)

    return df2


def get_gu_list(fen):

    r = requests.get(mongodb_data_api+'/api/v1/statistics/statistics/gu_list?fen='+str(fen)+'', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()
    df2 = pd.DataFrame(df2)

    return df2


async def get_gu_list_v_2(fen):
    
    r = requests.get(mongodb_data_api+'/api/v1/statistics/statistics/gu_list?fen='+str(fen)+'', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()
    df2 = pd.DataFrame(df2)

    return df2

def get_gu_delete(_id):
    
    r = requests.delete(mongodb_data_api+'/api/v1/statistics/statistics/gu_list?_id='+str(_id)+'', verify=False, timeout=timeout) # 10 seconds

    return r.status_code

def get_gu_delete_v_2(_id):
    
    r = requests.delete(mongodb_data_api+'/api/v1/statistics/statistics/gu_list?_id='+str(_id)+'', verify=False, timeout=timeout) # 10 seconds

    return r

def get_oa_delete(_id):
    
    r = requests.delete(mongodb_data_api+'/api/v1/statistics/statistics/oa_list?_id='+str(_id)+'', verify=False, timeout=timeout) # 10 seconds

    return r

def get_h_delete_v_2(_id):
    print(mongodb_data_api+'/api/v1/statistics/statistics/h_list?_id='+str(_id)+'')
    r = requests.delete(mongodb_data_api+'/api/v1/statistics/statistics/h_list?_id='+str(_id)+'', verify=False, timeout=timeout) # 10 seconds

    return r

def get_h_list(fen):
    
    r = requests.get(mongodb_data_api+'/api/v1/statistics/statistics/h_list?fen='+str(fen)+'', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()
    df2 = pd.DataFrame(df2)

    return df2


async def get_h_list_v2(fen):
    
    r = requests.get(mongodb_data_api+'/api/v1/statistics/statistics/h_list?fen='+str(fen)+'', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()
    df2 = pd.DataFrame(df2)

    return df2

def get_h_list_all():
    
    r = requests.get(mongodb_data_api+'/api/v1/statistics/statistics/h_list', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()
    df2 = pd.DataFrame(df2)

    return df2

def get_product_barcode_not(idname):
    
    r = requests.get(mongodb_data_api+'/api/v1/search/product_barcode_not?barcodeId='+str(idname)+'', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()
    df2 = pd.DataFrame(df2)

    return df2


def get_qty(idname):
    
    r = requests.get(mongodb_data_api+'/api/v1/search/qty/'+str(idname)+'', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()
    df2 = pd.DataFrame(df2)

    return df2

def get_qty_h(idname):
    
    r = requests.get(mongodb_data_api+'/api/v1/search/qty_h/'+str(idname)+'', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()
    df2 = pd.DataFrame(df2)

    return df2

def get_price(idname):
    
    r = requests.get(mongodb_data_api+'/api/v2/search/price?ID='+str(idname)+'', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()

    return df2


def get_everyMonth_for_deleverly():
    
    r = requests.get(mongodb_data_api+'/api/v1/statistics/statistics/logistics_every_month', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()
    
    df2 = pd.DataFrame(df2)

    return df2

def get_everyMonth_for_deleverly_by(fen:int,product_id:int):
    
    r = requests.get(mongodb_data_api+'/api/v1/statistics/statistics/logistics_every_month?fen='+fen+'&product_id='+product_id+'', verify=False, timeout=timeout) # 10 seconds
    df2 = r.json()
    df2 = pd.DataFrame(df2)

    return df2


def get_d_to_gu(jia_yi_fang_b,product_id):
    
    fenDian = 0
    
    if jia_yi_fang_b== 25943:
        
        fenDian = 1
    
    if jia_yi_fang_b== 28100:
            
        fenDian = 2
        
    if jia_yi_fang_b== 42956:
            
        fenDian = 3
    
    r = requests.get(mongodb_data_api+'/api/v1/g_deparment/d_to_gu/d_to_gu?fen_dian_id='+str(fenDian)+'', verify=False, timeout=timeout) # 10 seconds
    
    df2 = r.json()
    df2 = pd.DataFrame(df2)
    
    print(df2)
    
    df2 = df2.loc[df2['product_id'] == product_id]

    return df2


    # http://192.168.1.11:8200/mongodb_data_api/api/v1/g_deparment/d_to_gu/d_to_gu?fen_dian_id=3


def get_data_from_api(api_url, ids, timeout=45):
    """
    Retrieves data from an API endpoint for the specified IDs and returns it as a pandas DataFrame.
    
    Parameters:
    api_url (str): URL of the API endpoint
    ids (set): set of IDs for which to retrieve data
    timeout (int): maximum number of seconds to wait for the server's response (default: 10 seconds)
    
    Returns:
    pandas.DataFrame: DataFrame containing the retrieved data
    """
    # Convert the set of IDs to a comma-separated string
    id_str = ",".join(map(str, ids))
    
    # Construct the URL for the API request by appending the ID string to the endpoint URL
    request_url = f"{api_url}?id={id_str}"
    
    # Send the API request and get the response
    response = requests.get(request_url, timeout=timeout)
    
    # Check if the request was successful
    if response.status_code != 200:
        raise ValueError(f"Request failed with status code {response.status_code}")
    
    # Parse the response as JSON and convert it to a pandas DataFrame
    data = pd.DataFrame(response.json())
    
    return data

def get_jia_yi_fang(data):
    
    # A10001 = mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' '
    A10001 = mongodb_data_api+'/api/v1/search/jia_yi_name/in'
    # r = requests.get(mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' ', verify=False, timeout=10) # 10 seconds
    
    return get_data_from_api(A10001, data, 45)


def get_product(data):
    # A10001 = mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' '
    A10001 = mongodb_data_api+'/api/v1/search/product/in'
    # r = requests.get(mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' ', verify=False, timeout=10) # 10 seconds
    return get_data_from_api(A10001, data, 45)


def get_post_data_from_api(api_url, ids, timeout=45):
    """
    Retrieves data from an API endpoint for the specified IDs and returns it as a pandas DataFrame.
    
    Parameters:
    api_url (str): URL of the API endpoint
    ids (set): set of IDs for which to retrieve data
    timeout (int): maximum number of seconds to wait for the server's response (default: 45 seconds)
    
    Returns:
    pandas.DataFrame: DataFrame containing the retrieved data
    """
    # Convert the set of IDs to a list (or keep it as a set if the API accepts it)
    id_list = list(ids)
    
    # Send the API request using POST method and get the response
    response = requests.post(api_url, json={'ids': id_list}, timeout=timeout)
    
    # Check if the request was successful
    if response.status_code != 200:
        raise ValueError(f"Request failed with status code {response.status_code}")
    
    # Parse the response as JSON and convert it to a pandas DataFrame
    data = pd.DataFrame(response.json())
    
    return data

def get_post_jia_yi_fang(data):
    
    # A10001 = mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' '
    A10001 = mongodb_data_api+'/api/v1/search/jia_yi_name/in'
    # r = requests.get(mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' ', verify=False, timeout=10) # 10 seconds
    
    return get_post_data_from_api(A10001, data, 45)


def get_post_product(data):
    # A10001 = mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' '
    A10001 = mongodb_data_api+'/api/v1/search/product/in'
    # r = requests.get(mongodb_data_api+'/api/v1/search/jia_yi_name/in?id='+str(data)+' ', verify=False, timeout=10) # 10 seconds
    return get_post_data_from_api(A10001, data, 45)


def get_product_total_sale(page: int = 1, limit: int = 1000) -> dict:
    
    timeout = 10  # 设置超时为10秒

    # 使用传入的参数构建URL
    url = f"{mongodb_data_api}/api/v1/views/product_total_sale?page={page}&limit={limit}"
    
    # 发起请求
    r = requests.get(url, verify=False, timeout=timeout)
    
    # 检查响应状态，如果请求失败则抛出异常
    r.raise_for_status()

    # 返回JSON响应
    return r.json()