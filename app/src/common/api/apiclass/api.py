import requests


class API:
    def __init__(self, base_url, api_key=None, timeout=None):
        self.base_url = base_url
        self.api_key = api_key
        self.timeout = timeout

    def make_request(self, endpoint=None, method="GET", data=None, headers=None, params=None):
        url = f"{self.base_url}"
        if endpoint:
            url = f"{self.base_url}/{endpoint}"
        if self.api_key:
            headers = headers or {}
            headers["Authorization"] = f"Bearer {self.api_key}"
        response = requests.request(method, url, data=data, headers=headers, params=params, timeout=self.timeout)
        response.raise_for_status()
        return response.json()