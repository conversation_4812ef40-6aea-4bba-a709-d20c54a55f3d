# 引用

# import common.mongo_db_package as mongo_db_package
# import importlib
# importlib.reload(mongo_db_package)
# from common.mongo_db_package import mongo_db_package

# with mongo_db_package('data','ji10001') as ji10001_class:
#     ji10001_tb = ji10001_class.select_data()

# jin_huo_ri_qi_product_class = mongo_db_package('view','jin_huo_ri_qi_product')
# jin_huo_ri_qi_product_tb = jin_huo_ri_qi_product_class.select_data()

from src.Connect.https_connect import mongodb_data_api
import requests
import json
import pandas as pd


# Class
class jia_yi_name_package():

    def __init__(self):
        pass

        # import src.Connect.mongo_connect as client
        # import importlib
        # importlib.reload(client)
        # from src.Connect.mongo_connect import client

        # self.db_name = db_name
        # self.tb_name = tb_name
        # self.client = client
        # self.var_db_name = self.client[db_name]
        # self.var_tb_name = self.var_db_name[tb_name]

    def __enter__(self):
        return self

    def select_data_list_with_http(self,nameStr,paramsData):
        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'

        # {
        # "data_api": [
        #     {
        #     "jia_yi_id": 25730
        #     }
        # ]
        # }

        # df = df.to_json(orient='records')
        # df = json.loads(df)

        url = mongodb_data_api + '/api/v1/search/jia_yi_search_text_v3'
        # body_raw = {"data_api": [{"jia_yi_id": 22904}]}
        # body_raw = {"text": df}

        payload = paramsData


        df2 = requests.get(url=url, params=payload)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)


        # df2 = df2.rename(columns={'jia_yi_idname': nameStr+'_idname','jia_yi_mm_name': nameStr+'_mmname'})
        df2 = df2.rename(columns={'jiaYiId':nameStr + '_id','jiaYiIdname': nameStr+'_idname','jiaYiMmName': nameStr+'_mm_name'})


        print(df2)

        return df2


    def select_data_list_id_with_http(self,df,nameStr):
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'

        # {
        # "data_api": [
        #     {
        #     "jia_yi_id": 25730
        #     }
        # ]
        # }

        df = df.to_dict(orient='records')
        # df = json.loads(df)

        # url = mongodb_data_api + '/api/v1/search/jia_yi_search_text_v3'
        # body_raw = {"data_api": [{"jia_yi_id": 22904}]}
        body_raw = {"data_api": df}

        payload = body_raw


        df2 = requests.get(url=url, json=payload)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)


        print(df2)


        df2 = df2.rename(columns={'jia_yi_idname': nameStr+'_idname','jia_yi_mm_name': nameStr+'_mmname'})
        # df2 = df2.rename(columns={'jiaYiId':nameStr + '_id','jiaYiIdname': nameStr+'_idname','jiaYiMmName': nameStr+'_mm_name'})


        print(df2)

        return df2


    def select_data_list_id_with_http_v2(self,df,nameStr):
        url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'

        # {
        # "data_api": [
        #     {
        #     "jia_yi_id": 25730
        #     }
        # ]
        # }

        df = df.to_dict(orient='records')
        # df = json.loads(df)

        # url = mongodb_data_api + '/api/v1/search/jia_yi_search_text_v3'
        # body_raw = {"data_api": [{"jia_yi_id": 22904}]}
        body_raw = {"data_api": df}

        payload = body_raw


        df2 = requests.get(url=url, json=payload)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)


        print(df2)


        df2 = df2.rename(columns={'jia_yi_idname': nameStr+'_idname','jia_yi_mm_name': nameStr+'_mmname','jia_yi_id': nameStr+'_id'})
        # df2 = df2.rename(columns={'jiaYiId':nameStr + '_id','jiaYiIdname': nameStr+'_idname','jiaYiMmName': nameStr+'_mm_name'})


        print(df2)

        return df2

    


    def __exit__(self, *args, **kwargs):
        # self.client.close()
        print("close mongodb")