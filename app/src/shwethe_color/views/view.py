from typing import Any, List, Dict

import requests
from pydantic import BaseModel
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from src.config.shwethe_color.database import get_session
import os
import time
from src.shwethe_color.crud.crud import (
    getCarAtive100,
    get_type_prefix100,
    get_base_color100,
    get_machine_color100,
    get_result_color100,
    insert_mix_color100,
    check_cai_liao100,
    get_cai_liao100,
    insert_color_insert_tb100,
    check_color_insert_tb100,
    get_all_color_cai_liao100,
    update_status_color_cai_liao100,
    get_customer100,
    get_all_color_insert100,
    get_color_not_sell100,
    send_big_table100,
    get_warehouse_name100,
    insert_color_insert_oa_tb100,
    send_big_table_oa100,
    get_price100,
    get_all_color_oa_insert100,
    update_status_color_insert100,
    update_status_color_oa_insert100,
    search_color_cai_liao100,
    search_base_color100
)
# from src.shwethe_color.models import name_storage_tb
from src.shwethe_color.schemas.schemas import (
    color_cai_liao_tb_post,
    color_insert_tb_post
)
from sqlalchemy.exc import IntegrityError
router = APIRouter()


# @router.post("/product")
# def create_product_handler(nameStorageTbPostDatag: nameStorageTbForProductPost, db: Session = Depends(get_session)):
#     A100001 = create_name_storage_tb(db=db, nameStorageTbPostData=nameStorageTbPostDatag)
#     nameStorageTbPostDatag.product_id = A100001.id
#     return create_product(db=db, nameStorageTbPostData=nameStorageTbPostDatag)


# @router.post("/getCarAtive")
# def getCarAtive(db: Session = Depends(get_session)):
#     return getCarAtive100(db=db)
#     # return "aaaaaaaaaaaaaa"

# Global variable to store data
cached_data = None
@router.post("/getCarAtive")
async def get_car_active(data: List[Dict[str, Any]]):
    try:
        # Process the received data
        global cached_data
        cached_data = data
        print(cached_data)
        for car in data:
            print(f"Received car data: {car}")
        return {"status": "Data received successfully", "data": data}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

# Define NiFi details
NIFI_HOSTNAME = "*************"  # Change this to your NiFi hostname
NIFI_PORT = "8080"  # Change this to your NiFi port
NIFI_PG_ID = "f27a0696-0190-1000-8724-19ba0ae39bd5"  # Change this to your NiFi process group ID

def get_nifi_process_group_status(pg_id):
    url = f"http://{NIFI_HOSTNAME}:{NIFI_PORT}/nifi-api/flow/process-groups/{NIFI_PG_ID}/status"
    response = requests.get(url, headers={'Content-Type': 'application/json'})
    response.raise_for_status()
    return response.json()

@router.post("/start_and_stop_nifi_process")
def start_and_stop_nifi_process():
    start_nifi_url = f"http://{NIFI_HOSTNAME}:{NIFI_PORT}/nifi-api/flow/process-groups/{NIFI_PG_ID}"
    stop_nifi_url = f"http://{NIFI_HOSTNAME}:{NIFI_PORT}/nifi-api/flow/process-groups/{NIFI_PG_ID}"
    
    payload_start = {"id": NIFI_PG_ID, "state": "RUNNING"}
    payload_stop = {"id": NIFI_PG_ID, "state": "STOPPED"}
    
    try:
        # Start the NiFi process
        start_response = requests.put(start_nifi_url, json=payload_start, headers={'Content-Type': 'application/json'})
        start_response.raise_for_status()  # Check if the request was successful
        
        # Continuously check the process status until it completes
        while True:
            status_response = get_nifi_process_group_status(NIFI_PG_ID)
            if status_response["processGroupStatus"]["aggregateSnapshot"]["activeThreadCount"] == 0:
                break
            time.sleep(1)  # Check every second
        
        # Stop the NiFi process
        stop_response = requests.put(stop_nifi_url, json=payload_stop, headers={'Content-Type': 'application/json'})
        stop_response.raise_for_status()  # Check if the request was successful
        
        return {"message": "NiFi process started and stopped successfully"}
    except requests.exceptions.RequestException as e:
        raise HTTPException(status_code=500, detail=f"Error starting or stopping NiFi process: {e}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {e}")





@router.get("/get_type_prefix")
def get_type_prefix(ID: int, db: Session = Depends(get_session)):
    return get_type_prefix100(ID=ID, db=db)

@router.get("/get_base_color")
def get_base_color(brand_id: int, db: Session = Depends(get_session)):
    return get_base_color100(brand_id=brand_id, db=db)

@router.get("/search_base_color")
def search_base_color(brand_id: int, base_idname: str, db: Session = Depends(get_session)):
    return search_base_color100(brand_id=brand_id, base_idname=base_idname, db=db)

@router.get("/get_machine_color")
def get_machine_color(brand_id: int, db: Session = Depends(get_session)):
    return get_machine_color100(brand_id=brand_id, db=db)

@router.post("/get_price")
def get_price(list_data: List[Dict], db: Session = Depends(get_session)):
    return get_price100(list_data=list_data, db=db)

@router.get("/get_result_color")
def get_result_color(product_idname: str, db: Session = Depends(get_session)):
    return get_result_color100(product_idname=product_idname, db=db)

@router.post("/insert_mix_color")
def insert_mix_color(list_data: Dict, db: Session = Depends(get_session)):
    return insert_mix_color100(list_data=list_data, db=db)


@router.get("/check_cai_liao")
def check_cai_liao(group_id: str, cai_liao_id: int, db: Session = Depends(get_session)):
    return check_cai_liao100(group_id=group_id, cai_liao_id=cai_liao_id, db=db)

@router.get("/get_cai_liao")
def get_cai_liao(product_idname: str, base_value: int, db: Session = Depends(get_session)):
    return get_cai_liao100(product_idname=product_idname, base_value=base_value, db=db)


@router.post("/check_color_insert_tb")
def check_color_insert_tb(list_data: List[Dict], group_id: str, db: Session = Depends(get_session)):
    return check_color_insert_tb100(list_data=list_data, group_id=group_id, db=db)

@router.post("/insert_color_insert_tb")
def insert_color_insert_tb(list_data: List[Dict], db: Session = Depends(get_session)):
    return insert_color_insert_tb100(list_data=list_data, db=db)

@router.get("/get_all_color_cai_liao")
def get_all_color_cai_liao(days_ago: int = 2, page: int = 1, per_page: int = 1, db: Session = Depends(get_session)):
    return get_all_color_cai_liao100(days_ago=days_ago, page=page, per_page=per_page, db=db)

@router.get("/search_color_cai_liao")
def search_color_cai_liao(search_term: str, db: Session = Depends(get_session)):
    return search_color_cai_liao100(search_term=search_term, db=db)

@router.put("/update_status_color_cai_liao")
def update_status_color_cai_liao(auto_id: int, status: str, db: Session = Depends(get_session)):
    return update_status_color_cai_liao100(auto_id=auto_id, status=status, db=db)

@router.get("/get_customer")
def get_customer(name: str, db: Session = Depends(get_session)):
    return get_customer100(name=name, db=db)

@router.get("/get_all_color_insert")
def get_all_color_insert(fen: int, days_ago: int = 2, page: int = 1, per_page: int = 1, db: Session = Depends(get_session)):
    return get_all_color_insert100(fen=fen, days_ago=days_ago, page=page, per_page=per_page, db=db)

@router.get("/get_color_not_sell")
def get_color_not_sell(days_ago: int = 2, db: Session = Depends(get_session)):
    return get_color_not_sell100(days_ago=days_ago, db=db)

@router.get("/get_all_color_oa_insert")
def get_all_color_oa_insert(fen: int, days_ago: int = 2, page: int = 1, per_page: int = 1, db: Session = Depends(get_session)):
    return get_all_color_oa_insert100(fen=fen, days_ago=days_ago, page=page, per_page=per_page, db=db)

@router.get("/get_warehouse_name")
def get_warehouse_name(name: int, db: Session = Depends(get_session)):
    return get_warehouse_name100(name=name, db=db)

@router.post("/insert_color_insert_oa_tb")
def insert_color_insert_oa_tb(list_data: List[Dict], db: Session = Depends(get_session)):
    return insert_color_insert_oa_tb100(list_data=list_data, db=db)

@router.put("/update_status_color_insert")
def update_status_color_insert(record_id: str, status: str, db: Session = Depends(get_session)):
    return update_status_color_insert100(record_id=record_id, status=status, db=db)

@router.put("/update_status_color_oa_insert")
def update_status_color_oa_insert(record_id: str, status: str, db: Session = Depends(get_session)):
    return update_status_color_oa_insert100(record_id=record_id, status=status, db=db)

@router.post("/send_big_table")
def send_big_table(db: Session = Depends(get_session)):
    return send_big_table100(db=db)

@router.post("/send_big_table_oa")
def send_big_table_oa(db: Session = Depends(get_session)):
    return send_big_table_oa100(db=db)
