from datetime import datetime
from typing import Optional, Dict
from sqlmodel import SQLModel, Field
from sqlalchemy import Column, Integer, JSON
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import TIMESTAMP, JSONB
from sqlalchemy import TEXT
from sqlalchemy.sql.expression import table, text


class da_ka_tb(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    jia_yi_id : int = Field(default=..., title="jia_yi_id")
    da_ka_datetime: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="da_ka_datetime"
    )  
    da_ka_lei : int = Field(default=..., title="da_ka_lei")

class color_device_tb(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    device_id: int = Field(default=..., title="device_id")
    device_fen_dian: int = Field(default=..., title="device_fen_dian")
    type_device_id: str = Field(default=..., title="type_device_id")

class type_device_tb(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    name: str = Field(default=..., title="name")
    brand_id: int = Field(default=..., title="brand_id")

class color_brand_tb(SQLModel, table=True):
    color_brand_id: Optional[int] = Field(default=None, primary_key=True, title="color_brand_id")
    brand_name: str = Field(default=..., title="name")
    
class color_prefix_idname(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    brand_id: int = Field(default=..., title="brand_id")
    prefix_idname: str = Field(default=..., title="prefix_idname")
    prefix_details: str = Field(default=..., title="prefix_details")

class color_base_tb(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    base_id: int = Field(default=..., title="base_id")
    brand_id: int = Field(default=..., title="brand_id")

class pei_liao_oa_tb(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    product_id_a: int = Field(default=..., title="product_id_a")
    product_id_b: int = Field(default=..., title="product_id_b")
    product_qty_a: float = Field(default=..., title="product_qty_a")
    product_qty_b: float = Field(default=..., title="product_qty_b")
    type_device_id: int = Field(default=..., title="type_device_id")
    number: int = Field(default=..., title="number")

class color_cai_liao_tb(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    cai_liao_id: Optional[int] = Field(default=..., title="cai_liao_id")
    cai_liao_qty: Optional[float] = Field(default=..., title="cai_liao_qty")
    qty_lei: Optional[str] = Field(default=..., title="qty_lei")
    color_base_id: Optional[int] = Field(default=..., title="color_base_id")
    detail_json: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
    status: Optional[str] = Field(default=..., title="status")
    group_id: Optional[str] = Field(default=..., title="group_id")
    datetime: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="datetime"
    )  
    check_json: dict = Field(sa_column=Column(JSONB), default={})

class color_insert_tb(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    # datetime: Optional[int] = Field(default=..., title="cai_liao_id")
    datetime: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="datetime"
    )  
    # record_id: Optional[str]
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'COLOR'::text || lpad(nextval('color_insert_sequence'::regclass)::text, 8, '0'::text)")))
    group_id: Optional[str] = Field(default=..., title="group_id")
    jia_yi_fang_a: Optional[int] = Field(default=..., title="jia_yi_fang_a")
    jia_yi_fang_b: Optional[int] = Field(default=..., title="jia_yi_fang_b")
    lei_a: Optional[int] = Field(default=..., title="lei_a")
    lei_b: Optional[int] = Field(default=..., title="lei_b")
    device_id: Optional[int] = Field(default=..., title="device_id")
    product_id: Optional[int] = Field(default=..., title="product_id")
    product_qty: Optional[float] = Field(default=..., title="product_qty")
    color_price: Optional[float] = Field(default=..., title="color_price")
    unit_price: Optional[float] = Field(default=..., title="unit_price")
    bi_zhi: Optional[int] = Field(default=..., title="bi_zhi")
    fen: Optional[int] = Field(default=..., title="fen")
    sum_price: Optional[int] = Field(default=..., title="sum_price")
    detail_json: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
    status: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
    product_qty_sum: Optional[float] = Field(default=..., title="product_qty_sum")
    unit_price_sum: Optional[float] = Field(default=..., title="unit_price_sum")


class color_insert_oa_tb(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    # datetime: Optional[int] = Field(default=..., title="cai_liao_id")
    datetime: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="datetime"
    )  
    # record_id: Optional[str]
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'COLOROA'::text || lpad(nextval('color_insertoa_sequence'::regclass)::text, 8, '0'::text)")))
    group_id: Optional[str] = Field(default=..., title="group_id")
    jia_yi_fang_a: Optional[int] = Field(default=..., title="jia_yi_fang_a")
    jia_yi_fang_b: Optional[int] = Field(default=..., title="jia_yi_fang_b")
    lei_a: Optional[int] = Field(default=..., title="lei_a")
    lei_b: Optional[int] = Field(default=..., title="lei_b")
    device_id: Optional[int] = Field(default=..., title="device_id")
    product_id: Optional[int] = Field(default=..., title="product_id")
    product_qty: Optional[float] = Field(default=..., title="product_qty")
    fen: Optional[int] = Field(default=..., title="fen")
    detail_json: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
    status: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
    color_price: Optional[float] = Field(default=..., title="color_price")
    unit_price: Optional[float] = Field(default=..., title="unit_price")
    

class code_color_tb(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    color_base_id: Optional[int] = Field(default=..., title="color_base_id")
    brand_id: Optional[int] = Field(default=..., title="brand_id")

    
    
    