{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-02-05 02:50:06,792 INFO sqlalchemy.engine.Engine select pg_catalog.version()\n", "2024-02-05 02:50:06,794 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-02-05 02:50:06,817 INFO sqlalchemy.engine.Engine select current_schema()\n", "2024-02-05 02:50:06,819 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-02-05 02:50:06,828 INFO sqlalchemy.engine.Engine show standard_conforming_strings\n", "2024-02-05 02:50:06,830 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-02-05 02:50:06,848 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-02-05 02:50:06,877 INFO sqlalchemy.engine.Engine SELECT color_insert_tb.record_id, color_insert_tb.detail_json, color_insert_tb.status, color_insert_tb.auto_id, color_insert_tb.datetime, color_insert_tb.group_id, color_insert_tb.jia_yi_fang_a, color_insert_tb.jia_yi_fang_b, color_insert_tb.lei_a, color_insert_tb.lei_b, color_insert_tb.device_id, color_insert_tb.product_id, color_insert_tb.product_qty, color_insert_tb.color_price, color_insert_tb.unit_price, color_insert_tb.bi_zhi, color_insert_tb.fen, color_insert_tb.sum_price, color_insert_tb.product_qty_sum, color_insert_tb.unit_price_sum \n", "FROM color_insert_tb \n", "WHERE color_insert_tb.group_id IN (SELECT anon_1.group_id \n", "FROM (SELECT color_insert_tb.group_id AS group_id \n", "FROM color_insert_tb \n", "WHERE CAST((color_insert_tb.status ->> %(status_1)s) AS VARCHAR) = %(param_1)s) AS anon_1) AND color_insert_tb.datetime >= %(datetime_1)s ORDER BY color_insert_tb.datetime DESC\n", "2024-02-05 02:50:06,879 INFO sqlalchemy.engine.Engine [generated in 0.00217s] {'status_1': 'status_big_table', 'param_1': 'success', 'datetime_1': datetime.date(2023, 11, 7)}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_17822/2598543404.py:54: SAWarning: Coercing Subquery object into a select() for use in IN(); please pass a select() construct explicitly\n", "  color_insert_tb.group_id.in_(success_groups_subquery),\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024-02-05 02:50:07,483 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>jia_yi_fang_a</th>\n", "      <th>jia_yi_fang_b</th>\n", "      <th>lei_a</th>\n", "      <th>fen</th>\n", "      <th>group_id</th>\n", "      <th>device_id</th>\n", "      <th>product_id</th>\n", "      <th>product_qty_sum</th>\n", "      <th>type_lei</th>\n", "      <th>all_proQty_use</th>\n", "      <th>proQty_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>616</th>\n", "      <td>2024-01-16 09:28:44.199233+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>62be5dda</td>\n", "      <td>49019</td>\n", "      <td>57494</td>\n", "      <td>4.0</td>\n", "      <td>base</td>\n", "      <td>96.0</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>562</th>\n", "      <td>2024-01-18 15:12:33.516703+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>2131618d</td>\n", "      <td>49019</td>\n", "      <td>57491</td>\n", "      <td>1.0</td>\n", "      <td>base</td>\n", "      <td>91.0</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>520</th>\n", "      <td>2024-01-19 14:09:37.090800+06:30</td>\n", "      <td>33447</td>\n", "      <td>33447</td>\n", "      <td>22</td>\n", "      <td>2</td>\n", "      <td>e63eb8fc</td>\n", "      <td>49020</td>\n", "      <td>57492</td>\n", "      <td>9.0</td>\n", "      <td>base</td>\n", "      <td>56.0</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>324</th>\n", "      <td>2024-01-27 09:56:49.790650+06:30</td>\n", "      <td>33447</td>\n", "      <td>33447</td>\n", "      <td>22</td>\n", "      <td>2</td>\n", "      <td>bdff7515</td>\n", "      <td>49020</td>\n", "      <td>57496</td>\n", "      <td>1.0</td>\n", "      <td>base</td>\n", "      <td>53.0</td>\n", "      <td>28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>282</th>\n", "      <td>2024-01-29 15:48:39.318035+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>5038847e</td>\n", "      <td>49023</td>\n", "      <td>78380</td>\n", "      <td>12.0</td>\n", "      <td>base</td>\n", "      <td>40.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>219</th>\n", "      <td>2024-01-30 16:24:59.851883+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>3adf615c</td>\n", "      <td>49023</td>\n", "      <td>50748</td>\n", "      <td>20.0</td>\n", "      <td>code</td>\n", "      <td>32.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>599</th>\n", "      <td>2024-01-18 10:17:02.433430+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>c6398a57</td>\n", "      <td>49023</td>\n", "      <td>51659</td>\n", "      <td>6.0</td>\n", "      <td>code</td>\n", "      <td>30.0</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-02-04 14:01:01.461933+06:30</td>\n", "      <td>34233</td>\n", "      <td>34233</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>409d9eac</td>\n", "      <td>49019</td>\n", "      <td>61260</td>\n", "      <td>17.0</td>\n", "      <td>code</td>\n", "      <td>27.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>697</th>\n", "      <td>2024-01-11 16:12:05.931332+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>4b3caabe</td>\n", "      <td>49019</td>\n", "      <td>61318</td>\n", "      <td>5.0</td>\n", "      <td>code</td>\n", "      <td>26.0</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>689</th>\n", "      <td>2024-01-12 11:12:34.950000+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>185f997b</td>\n", "      <td>49019</td>\n", "      <td>76908</td>\n", "      <td>4.0</td>\n", "      <td>code</td>\n", "      <td>25.0</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>429</th>\n", "      <td>2024-01-24 10:32:29.966954+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>5eaed327</td>\n", "      <td>49023</td>\n", "      <td>37162</td>\n", "      <td>4.0</td>\n", "      <td>base</td>\n", "      <td>24.0</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>230</th>\n", "      <td>2024-01-30 15:17:12.709559+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>42c75690</td>\n", "      <td>49023</td>\n", "      <td>37161</td>\n", "      <td>6.0</td>\n", "      <td>base</td>\n", "      <td>21.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>797</th>\n", "      <td>2024-01-06 16:34:43.031412+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>22ffec0a</td>\n", "      <td>49019</td>\n", "      <td>77957</td>\n", "      <td>7.0</td>\n", "      <td>code</td>\n", "      <td>17.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>311</th>\n", "      <td>2024-01-27 14:06:15.817377+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>075d47ca</td>\n", "      <td>49019</td>\n", "      <td>71847</td>\n", "      <td>10.0</td>\n", "      <td>code</td>\n", "      <td>16.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>417</th>\n", "      <td>2024-01-25 10:08:29.799467+06:30</td>\n", "      <td>33447</td>\n", "      <td>33447</td>\n", "      <td>22</td>\n", "      <td>2</td>\n", "      <td>f144e18a</td>\n", "      <td>49020</td>\n", "      <td>61174</td>\n", "      <td>1.0</td>\n", "      <td>base</td>\n", "      <td>15.0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2024-02-04 09:29:11.656822+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>935c097a</td>\n", "      <td>49019</td>\n", "      <td>62924</td>\n", "      <td>3.0</td>\n", "      <td>base</td>\n", "      <td>14.0</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>802</th>\n", "      <td>2024-01-06 16:33:07.639092+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>856835bc</td>\n", "      <td>49019</td>\n", "      <td>62917</td>\n", "      <td>1.0</td>\n", "      <td>base</td>\n", "      <td>13.0</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>2024-01-31 16:56:20.382323+06:30</td>\n", "      <td>46117</td>\n", "      <td>46117</td>\n", "      <td>31</td>\n", "      <td>3</td>\n", "      <td>e2e89e6d</td>\n", "      <td>49021</td>\n", "      <td>61689</td>\n", "      <td>1.0</td>\n", "      <td>code</td>\n", "      <td>13.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>702</th>\n", "      <td>2024-01-11 16:11:08.284076+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>91d2554e</td>\n", "      <td>49019</td>\n", "      <td>61769</td>\n", "      <td>5.0</td>\n", "      <td>code</td>\n", "      <td>12.0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>680</th>\n", "      <td>2024-01-12 13:27:38.365587+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>fe465193</td>\n", "      <td>49023</td>\n", "      <td>43990</td>\n", "      <td>10.0</td>\n", "      <td>code</td>\n", "      <td>10.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>762</th>\n", "      <td>2024-01-08 13:26:58.834640+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>cd792d45</td>\n", "      <td>49019</td>\n", "      <td>78071</td>\n", "      <td>6.0</td>\n", "      <td>code</td>\n", "      <td>10.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>519</th>\n", "      <td>2024-01-19 14:09:37.091089+06:30</td>\n", "      <td>33447</td>\n", "      <td>33447</td>\n", "      <td>31</td>\n", "      <td>2</td>\n", "      <td>e63eb8fc</td>\n", "      <td>49020</td>\n", "      <td>78375</td>\n", "      <td>9.0</td>\n", "      <td>code</td>\n", "      <td>10.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>2024-01-31 16:11:22.165394+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>2d354494</td>\n", "      <td>49023</td>\n", "      <td>78382</td>\n", "      <td>10.0</td>\n", "      <td>base</td>\n", "      <td>10.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>2024-01-31 16:53:23.572218+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>b3d6a718</td>\n", "      <td>49023</td>\n", "      <td>78383</td>\n", "      <td>10.0</td>\n", "      <td>base</td>\n", "      <td>10.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>2024-01-31 16:53:23.572425+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>b3d6a718</td>\n", "      <td>49023</td>\n", "      <td>53784</td>\n", "      <td>10.0</td>\n", "      <td>code</td>\n", "      <td>10.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>358</th>\n", "      <td>2024-01-26 10:58:13.051232+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>34f068c8</td>\n", "      <td>49019</td>\n", "      <td>70928</td>\n", "      <td>1.0</td>\n", "      <td>code</td>\n", "      <td>9.0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>2024-02-03 10:25:45.990987+06:30</td>\n", "      <td>46117</td>\n", "      <td>46117</td>\n", "      <td>31</td>\n", "      <td>3</td>\n", "      <td>8affe59a</td>\n", "      <td>49021</td>\n", "      <td>59342</td>\n", "      <td>3.0</td>\n", "      <td>code</td>\n", "      <td>8.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>275</th>\n", "      <td>2024-01-29 15:50:23.448779+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>f483f3f1</td>\n", "      <td>49023</td>\n", "      <td>76382</td>\n", "      <td>8.0</td>\n", "      <td>code</td>\n", "      <td>8.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>710</th>\n", "      <td>2024-01-10 09:46:38.430000+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>61e0a039</td>\n", "      <td>49019</td>\n", "      <td>62922</td>\n", "      <td>5.0</td>\n", "      <td>base</td>\n", "      <td>7.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>817</th>\n", "      <td>2024-01-05 16:09:21.542972+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>1e0de847</td>\n", "      <td>49019</td>\n", "      <td>78293</td>\n", "      <td>7.0</td>\n", "      <td>code</td>\n", "      <td>7.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>224</th>\n", "      <td>2024-01-30 15:19:41.106349+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>97a8d505</td>\n", "      <td>49019</td>\n", "      <td>63745</td>\n", "      <td>7.0</td>\n", "      <td>code</td>\n", "      <td>7.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133</th>\n", "      <td>2024-02-02 13:41:01.758005+06:30</td>\n", "      <td>46117</td>\n", "      <td>46117</td>\n", "      <td>31</td>\n", "      <td>3</td>\n", "      <td>d21af065</td>\n", "      <td>49021</td>\n", "      <td>67006</td>\n", "      <td>1.0</td>\n", "      <td>code</td>\n", "      <td>7.0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>691</th>\n", "      <td>2024-01-11 16:13:04.165470+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>cc759f01</td>\n", "      <td>49019</td>\n", "      <td>62141</td>\n", "      <td>1.0</td>\n", "      <td>code</td>\n", "      <td>6.0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>782</th>\n", "      <td>2024-01-07 16:06:10.430071+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>41bcd2fa</td>\n", "      <td>49019</td>\n", "      <td>78303</td>\n", "      <td>1.0</td>\n", "      <td>code</td>\n", "      <td>6.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>2024-02-01 15:44:52.113438+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>22beec07</td>\n", "      <td>49019</td>\n", "      <td>62928</td>\n", "      <td>1.0</td>\n", "      <td>base</td>\n", "      <td>6.0</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>406</th>\n", "      <td>2024-01-25 10:39:20.032278+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>d89ce21b</td>\n", "      <td>49019</td>\n", "      <td>64958</td>\n", "      <td>1.0</td>\n", "      <td>code</td>\n", "      <td>6.0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>789</th>\n", "      <td>2024-01-07 08:59:17.750868+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>ce8c9157</td>\n", "      <td>49019</td>\n", "      <td>61176</td>\n", "      <td>1.0</td>\n", "      <td>base</td>\n", "      <td>6.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148</th>\n", "      <td>2024-02-02 10:14:24.677588+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>d3e5d28f</td>\n", "      <td>49019</td>\n", "      <td>61178</td>\n", "      <td>3.0</td>\n", "      <td>base</td>\n", "      <td>6.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>2024-02-02 15:34:55.003189+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>998bdae8</td>\n", "      <td>49019</td>\n", "      <td>62501</td>\n", "      <td>6.0</td>\n", "      <td>code</td>\n", "      <td>6.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>475</th>\n", "      <td>2024-01-22 16:40:51.756719+06:30</td>\n", "      <td>33447</td>\n", "      <td>33447</td>\n", "      <td>31</td>\n", "      <td>2</td>\n", "      <td>41d70319</td>\n", "      <td>49020</td>\n", "      <td>73371</td>\n", "      <td>6.0</td>\n", "      <td>code</td>\n", "      <td>6.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>229</th>\n", "      <td>2024-01-30 15:17:12.709771+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>42c75690</td>\n", "      <td>49023</td>\n", "      <td>78560</td>\n", "      <td>6.0</td>\n", "      <td>code</td>\n", "      <td>6.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>416</th>\n", "      <td>2024-01-25 10:08:29.799693+06:30</td>\n", "      <td>33447</td>\n", "      <td>33447</td>\n", "      <td>31</td>\n", "      <td>2</td>\n", "      <td>f144e18a</td>\n", "      <td>49020</td>\n", "      <td>78431</td>\n", "      <td>1.0</td>\n", "      <td>code</td>\n", "      <td>5.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>2024-01-30 17:22:25.227126+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>77488b11</td>\n", "      <td>49023</td>\n", "      <td>43970</td>\n", "      <td>5.0</td>\n", "      <td>code</td>\n", "      <td>5.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>729</th>\n", "      <td>2024-01-09 15:13:28.941576+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>fde33113</td>\n", "      <td>49019</td>\n", "      <td>75435</td>\n", "      <td>4.0</td>\n", "      <td>code</td>\n", "      <td>5.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>503</th>\n", "      <td>2024-01-20 15:45:16.502025+06:30</td>\n", "      <td>33447</td>\n", "      <td>33447</td>\n", "      <td>31</td>\n", "      <td>2</td>\n", "      <td>339b04d6</td>\n", "      <td>49020</td>\n", "      <td>73887</td>\n", "      <td>3.0</td>\n", "      <td>code</td>\n", "      <td>5.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>669</th>\n", "      <td>2024-01-13 10:15:53.797000+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>68e2353e</td>\n", "      <td>49019</td>\n", "      <td>73234</td>\n", "      <td>1.0</td>\n", "      <td>code</td>\n", "      <td>5.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>746</th>\n", "      <td>2024-01-08 13:29:46.712145+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>6caf87db</td>\n", "      <td>49019</td>\n", "      <td>63144</td>\n", "      <td>1.0</td>\n", "      <td>code</td>\n", "      <td>5.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>332</th>\n", "      <td>2024-01-26 16:38:33.113944+06:30</td>\n", "      <td>46117</td>\n", "      <td>46117</td>\n", "      <td>31</td>\n", "      <td>3</td>\n", "      <td>115e85bd</td>\n", "      <td>49021</td>\n", "      <td>66816</td>\n", "      <td>5.0</td>\n", "      <td>code</td>\n", "      <td>5.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>327</th>\n", "      <td>2024-01-26 17:11:18.425932+06:30</td>\n", "      <td>46117</td>\n", "      <td>46117</td>\n", "      <td>31</td>\n", "      <td>3</td>\n", "      <td>4e574a9e</td>\n", "      <td>49021</td>\n", "      <td>78455</td>\n", "      <td>5.0</td>\n", "      <td>code</td>\n", "      <td>5.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>708</th>\n", "      <td>2024-01-10 09:46:38.430000+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>61e0a039</td>\n", "      <td>49019</td>\n", "      <td>63735</td>\n", "      <td>5.0</td>\n", "      <td>code</td>\n", "      <td>5.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>777</th>\n", "      <td>2024-01-07 16:07:33.373915+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>3691465a</td>\n", "      <td>49019</td>\n", "      <td>61284</td>\n", "      <td>5.0</td>\n", "      <td>code</td>\n", "      <td>5.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>2024-02-02 15:32:12.142298+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>799bc8d6</td>\n", "      <td>49019</td>\n", "      <td>62925</td>\n", "      <td>1.0</td>\n", "      <td>base</td>\n", "      <td>4.0</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>464</th>\n", "      <td>2024-01-22 16:46:17.406162+06:30</td>\n", "      <td>33447</td>\n", "      <td>33447</td>\n", "      <td>31</td>\n", "      <td>2</td>\n", "      <td>1a4aa651</td>\n", "      <td>49020</td>\n", "      <td>78441</td>\n", "      <td>4.0</td>\n", "      <td>code</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>382</th>\n", "      <td>2024-01-26 09:42:28.250285+06:30</td>\n", "      <td>46117</td>\n", "      <td>46117</td>\n", "      <td>31</td>\n", "      <td>3</td>\n", "      <td>7af05e65</td>\n", "      <td>49021</td>\n", "      <td>61537</td>\n", "      <td>1.0</td>\n", "      <td>code</td>\n", "      <td>4.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104</th>\n", "      <td>2024-02-02 15:49:44.934842+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>215f9826</td>\n", "      <td>49019</td>\n", "      <td>62623</td>\n", "      <td>4.0</td>\n", "      <td>code</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>433</th>\n", "      <td>2024-01-24 10:31:30.921165+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>fc2566f6</td>\n", "      <td>49023</td>\n", "      <td>78444</td>\n", "      <td>4.0</td>\n", "      <td>code</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>434</th>\n", "      <td>2024-01-24 10:31:30.920943+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>fc2566f6</td>\n", "      <td>49023</td>\n", "      <td>37163</td>\n", "      <td>4.0</td>\n", "      <td>base</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>240</th>\n", "      <td>2024-01-30 13:19:08.340905+06:30</td>\n", "      <td>33447</td>\n", "      <td>33447</td>\n", "      <td>31</td>\n", "      <td>2</td>\n", "      <td>fbf1a28c</td>\n", "      <td>49020</td>\n", "      <td>72963</td>\n", "      <td>1.0</td>\n", "      <td>code</td>\n", "      <td>4.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>525</th>\n", "      <td>2024-01-19 13:29:35.362192+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>1e68d98c</td>\n", "      <td>49019</td>\n", "      <td>75462</td>\n", "      <td>4.0</td>\n", "      <td>code</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>459</th>\n", "      <td>2024-01-22 16:56:13.306037+06:30</td>\n", "      <td>26100</td>\n", "      <td>26100</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>ea5697d5</td>\n", "      <td>49019</td>\n", "      <td>77897</td>\n", "      <td>4.0</td>\n", "      <td>code</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                            datetime  jia_yi_fang_a  jia_yi_fang_b  lei_a   \n", "616 2024-01-16 09:28:44.199233+06:30          26100          26100     22  \\\n", "562 2024-01-18 15:12:33.516703+06:30          26100          26100     22   \n", "520 2024-01-19 14:09:37.090800+06:30          33447          33447     22   \n", "324 2024-01-27 09:56:49.790650+06:30          33447          33447     22   \n", "282 2024-01-29 15:48:39.318035+06:30          26100          26100     22   \n", "219 2024-01-30 16:24:59.851883+06:30          26100          26100     31   \n", "599 2024-01-18 10:17:02.433430+06:30          26100          26100     31   \n", "17  2024-02-04 14:01:01.461933+06:30          34233          34233     31   \n", "697 2024-01-11 16:12:05.931332+06:30          26100          26100     31   \n", "689 2024-01-12 11:12:34.950000+06:30          26100          26100     31   \n", "429 2024-01-24 10:32:29.966954+06:30          26100          26100     22   \n", "230 2024-01-30 15:17:12.709559+06:30          26100          26100     22   \n", "797 2024-01-06 16:34:43.031412+06:30          26100          26100     31   \n", "311 2024-01-27 14:06:15.817377+06:30          26100          26100     31   \n", "417 2024-01-25 10:08:29.799467+06:30          33447          33447     22   \n", "27  2024-02-04 09:29:11.656822+06:30          26100          26100     22   \n", "802 2024-01-06 16:33:07.639092+06:30          26100          26100     22   \n", "178 2024-01-31 16:56:20.382323+06:30          46117          46117     31   \n", "702 2024-01-11 16:11:08.284076+06:30          26100          26100     31   \n", "680 2024-01-12 13:27:38.365587+06:30          26100          26100     31   \n", "762 2024-01-08 13:26:58.834640+06:30          26100          26100     31   \n", "519 2024-01-19 14:09:37.091089+06:30          33447          33447     31   \n", "195 2024-01-31 16:11:22.165394+06:30          26100          26100     22   \n", "189 2024-01-31 16:53:23.572218+06:30          26100          26100     22   \n", "188 2024-01-31 16:53:23.572425+06:30          26100          26100     31   \n", "358 2024-01-26 10:58:13.051232+06:30          26100          26100     31   \n", "68  2024-02-03 10:25:45.990987+06:30          46117          46117     31   \n", "275 2024-01-29 15:50:23.448779+06:30          26100          26100     31   \n", "710 2024-01-10 09:46:38.430000+06:30          26100          26100     22   \n", "817 2024-01-05 16:09:21.542972+06:30          26100          26100     31   \n", "224 2024-01-30 15:19:41.106349+06:30          26100          26100     31   \n", "133 2024-02-02 13:41:01.758005+06:30          46117          46117     31   \n", "691 2024-01-11 16:13:04.165470+06:30          26100          26100     31   \n", "782 2024-01-07 16:06:10.430071+06:30          26100          26100     31   \n", "164 2024-02-01 15:44:52.113438+06:30          26100          26100     22   \n", "406 2024-01-25 10:39:20.032278+06:30          26100          26100     31   \n", "789 2024-01-07 08:59:17.750868+06:30          26100          26100     22   \n", "148 2024-02-02 10:14:24.677588+06:30          26100          26100     22   \n", "108 2024-02-02 15:34:55.003189+06:30          26100          26100     31   \n", "475 2024-01-22 16:40:51.756719+06:30          33447          33447     31   \n", "229 2024-01-30 15:17:12.709771+06:30          26100          26100     31   \n", "416 2024-01-25 10:08:29.799693+06:30          33447          33447     31   \n", "214 2024-01-30 17:22:25.227126+06:30          26100          26100     31   \n", "729 2024-01-09 15:13:28.941576+06:30          26100          26100     31   \n", "503 2024-01-20 15:45:16.502025+06:30          33447          33447     31   \n", "669 2024-01-13 10:15:53.797000+06:30          26100          26100     31   \n", "746 2024-01-08 13:29:46.712145+06:30          26100          26100     31   \n", "332 2024-01-26 16:38:33.113944+06:30          46117          46117     31   \n", "327 2024-01-26 17:11:18.425932+06:30          46117          46117     31   \n", "708 2024-01-10 09:46:38.430000+06:30          26100          26100     31   \n", "777 2024-01-07 16:07:33.373915+06:30          26100          26100     31   \n", "114 2024-02-02 15:32:12.142298+06:30          26100          26100     22   \n", "464 2024-01-22 16:46:17.406162+06:30          33447          33447     31   \n", "382 2024-01-26 09:42:28.250285+06:30          46117          46117     31   \n", "104 2024-02-02 15:49:44.934842+06:30          26100          26100     31   \n", "433 2024-01-24 10:31:30.921165+06:30          26100          26100     31   \n", "434 2024-01-24 10:31:30.920943+06:30          26100          26100     22   \n", "240 2024-01-30 13:19:08.340905+06:30          33447          33447     31   \n", "525 2024-01-19 13:29:35.362192+06:30          26100          26100     31   \n", "459 2024-01-22 16:56:13.306037+06:30          26100          26100     31   \n", "\n", "     fen  group_id  device_id  product_id  product_qty_sum type_lei   \n", "616    1  62be5dda      49019       57494              4.0     base  \\\n", "562    1  2131618d      49019       57491              1.0     base   \n", "520    2  e63eb8fc      49020       57492              9.0     base   \n", "324    2  bdff7515      49020       57496              1.0     base   \n", "282    1  5038847e      49023       78380             12.0     base   \n", "219    1  3adf615c      49023       50748             20.0     code   \n", "599    1  c6398a57      49023       51659              6.0     code   \n", "17     1  409d9eac      49019       61260             17.0     code   \n", "697    1  4b3caabe      49019       61318              5.0     code   \n", "689    1  185f997b      49019       76908              4.0     code   \n", "429    1  5eaed327      49023       37162              4.0     base   \n", "230    1  42c75690      49023       37161              6.0     base   \n", "797    1  22ffec0a      49019       77957              7.0     code   \n", "311    1  075d47ca      49019       71847             10.0     code   \n", "417    2  f144e18a      49020       61174              1.0     base   \n", "27     1  935c097a      49019       62924              3.0     base   \n", "802    1  856835bc      49019       62917              1.0     base   \n", "178    3  e2e89e6d      49021       61689              1.0     code   \n", "702    1  91d2554e      49019       61769              5.0     code   \n", "680    1  fe465193      49023       43990             10.0     code   \n", "762    1  cd792d45      49019       78071              6.0     code   \n", "519    2  e63eb8fc      49020       78375              9.0     code   \n", "195    1  2d354494      49023       78382             10.0     base   \n", "189    1  b3d6a718      49023       78383             10.0     base   \n", "188    1  b3d6a718      49023       53784             10.0     code   \n", "358    1  34f068c8      49019       70928              1.0     code   \n", "68     3  8affe59a      49021       59342              3.0     code   \n", "275    1  f483f3f1      49023       76382              8.0     code   \n", "710    1  61e0a039      49019       62922              5.0     base   \n", "817    1  1e0de847      49019       78293              7.0     code   \n", "224    1  97a8d505      49019       63745              7.0     code   \n", "133    3  d21af065      49021       67006              1.0     code   \n", "691    1  cc759f01      49019       62141              1.0     code   \n", "782    1  41bcd2fa      49019       78303              1.0     code   \n", "164    1  22beec07      49019       62928              1.0     base   \n", "406    1  d89ce21b      49019       64958              1.0     code   \n", "789    1  ce8c9157      49019       61176              1.0     base   \n", "148    1  d3e5d28f      49019       61178              3.0     base   \n", "108    1  998bdae8      49019       62501              6.0     code   \n", "475    2  41d70319      49020       73371              6.0     code   \n", "229    1  42c75690      49023       78560              6.0     code   \n", "416    2  f144e18a      49020       78431              1.0     code   \n", "214    1  77488b11      49023       43970              5.0     code   \n", "729    1  fde33113      49019       75435              4.0     code   \n", "503    2  339b04d6      49020       73887              3.0     code   \n", "669    1  68e2353e      49019       73234              1.0     code   \n", "746    1  6caf87db      49019       63144              1.0     code   \n", "332    3  115e85bd      49021       66816              5.0     code   \n", "327    3  4e574a9e      49021       78455              5.0     code   \n", "708    1  61e0a039      49019       63735              5.0     code   \n", "777    1  3691465a      49019       61284              5.0     code   \n", "114    1  799bc8d6      49019       62925              1.0     base   \n", "464    2  1a4aa651      49020       78441              4.0     code   \n", "382    3  7af05e65      49021       61537              1.0     code   \n", "104    1  215f9826      49019       62623              4.0     code   \n", "433    1  fc2566f6      49023       78444              4.0     code   \n", "434    1  fc2566f6      49023       37163              4.0     base   \n", "240    2  fbf1a28c      49020       72963              1.0     code   \n", "525    1  1e68d98c      49019       75462              4.0     code   \n", "459    1  ea5697d5      49019       77897              4.0     code   \n", "\n", "     all_proQty_use  proQty_count  \n", "616            96.0            30  \n", "562            91.0            22  \n", "520            56.0            22  \n", "324            53.0            28  \n", "282            40.0             3  \n", "219            32.0             2  \n", "599            30.0             4  \n", "17             27.0             2  \n", "697            26.0             4  \n", "689            25.0             4  \n", "429            24.0             4  \n", "230            21.0             3  \n", "797            17.0             2  \n", "311            16.0             2  \n", "417            15.0             5  \n", "27             14.0             8  \n", "802            13.0             7  \n", "178            13.0             3  \n", "702            12.0             5  \n", "680            10.0             1  \n", "762            10.0             3  \n", "519            10.0             2  \n", "195            10.0             1  \n", "189            10.0             1  \n", "188            10.0             1  \n", "358             9.0             5  \n", "68              8.0             2  \n", "275             8.0             1  \n", "710             7.0             3  \n", "817             7.0             1  \n", "224             7.0             1  \n", "133             7.0             5  \n", "691             6.0             5  \n", "782             6.0             3  \n", "164             6.0             6  \n", "406             6.0             5  \n", "789             6.0             2  \n", "148             6.0             3  \n", "108             6.0             1  \n", "475             6.0             1  \n", "229             6.0             1  \n", "416             5.0             2  \n", "214             5.0             1  \n", "729             5.0             2  \n", "503             5.0             3  \n", "669             5.0             3  \n", "746             5.0             2  \n", "332             5.0             1  \n", "327             5.0             1  \n", "708             5.0             1  \n", "777             5.0             1  \n", "114             4.0             4  \n", "464             4.0             1  \n", "382             4.0             3  \n", "104             4.0             1  \n", "433             4.0             1  \n", "434             4.0             1  \n", "240             4.0             2  \n", "525             4.0             1  \n", "459             4.0             1  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sys\n", "# sys.path.append('../../../../../app/')\n", "sys.path.append('../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "# from src.shwethe_color.database import get_session\n", "from src.config.shwethe_color.database import get_session\n", "from src.shwethe_color.models.model import color_insert_tb, color_insert_oa_tb\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "\n", "\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def dataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "def get_color_insert_tb():\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        # GET TODAY DATA\n", "        week_ago = today - DT.<PERSON><PERSON><PERSON>(days=90)\n", "\n", "        # heroesPersonal = db.exec(select(color_insert_tb).where(color_insert_tb.datetime > week_ago, color_insert_tb.lei_a == 31).order_by(color_insert_tb.datetime.desc())).all()\n", "\n", "        # First, create a subquery for SuccessGroups\n", "        success_groups_subquery = select(color_insert_tb.group_id).where(\n", "            color_insert_tb.status[(\"status_big_table\")].as_string() == \"success\"\n", "        ).subquery()\n", "        # Now, create the main query\n", "        query = select(color_insert_tb).where(\n", "            color_insert_tb.group_id.in_(success_groups_subquery),\n", "            color_insert_tb.datetime >= week_ago\n", "        ).order_by(color_insert_tb.datetime.desc())\n", "        # Execute the query\n", "        heroesPersonal = db.exec(query).all()\n", "\n", "        mergeDF = dataframe(heroesPersonal, to_dict=False)\n", "        mergeDF = mergeDF[['datetime', 'jia_yi_fang_a', 'jia_yi_fang_b', 'lei_a', 'fen', 'group_id', 'device_id', 'product_id', 'product_qty_sum']]\n", "        # mergeDF = mergeDF.head(60)\n", "        # mergeDF = mergeDF[60:120]\n", "    return mergeDF\n", "\n", "\n", "def calOne():\n", "    df = get_color_insert_tb()\n", "\n", "    # Create a combination key of jia_yi_fang_a and group_id where lei_a is 31\n", "    code_combination = df[df['lei_a'] == 31][['jia_yi_fang_a', 'group_id']]\n", "    code_combination['key'] = code_combination['jia_yi_fang_a'].astype(str) + '-' + code_combination['group_id']\n", "    code_set = set(code_combination['key'])\n", "    # Assign 'type_lei' based on conditions\n", "    df['type_lei'] = 'oa'  # Default value\n", "    df.loc[df['lei_a'] == 31, 'type_lei'] = 'code'  # Set 'code' where lei_a is 31\n", "    # Set 'base' where combination of jia_yi_fang_a and group_id matches the code_set and lei_a is not 31\n", "    df['combination_key'] = df['jia_yi_fang_a'].astype(str) + '-' + df['group_id']\n", "    df.loc[df['combination_key'].isin(code_set) & (df['lei_a'] != 31), 'type_lei'] = 'base'\n", "    # Drop the temporary combination_key column\n", "    df.drop('combination_key', axis=1, inplace=True)\n", "\n", "    # Calculate sum of product_qty_sum for each product_id and assign it to a new column\n", "    df['all_proQty_use'] = df.groupby('product_id')['product_qty_sum'].transform('sum')\n", "    df['proQty_count'] = df.groupby('product_id')['product_qty_sum'].transform('count')\n", "\n", "    # Filter rows where 'type_lei' is either 'code' or 'base'\n", "    df = df[df['type_lei'].isin(['code', 'base'])]\n", "\n", "    # df = df.sort_values(by=['group_id'], ascending=[False])\n", "    df = df.sort_values(by=['all_proQty_use'], ascending=[False])\n", "    # Assuming 'result' is your DataFrame after merging and renaming columns\n", "    # df = df['product_id'].unique()\n", "    df = df.drop_duplicates(subset='product_id')\n", "    df = df.head(60)\n", " \n", "    return df\n", "# def calOne():\n", "#     df = get_color_insert_tb()\n", "\n", "#     # Create a combination key of jia_yi_fang_a and group_id where lei_a is 31\n", "#     code_combination = df[df['lei_a'] == 31][['jia_yi_fang_a', 'group_id']]\n", "#     code_combination['key'] = code_combination['jia_yi_fang_a'].astype(str) + '-' + code_combination['group_id']\n", "#     code_set = set(code_combination['key'])\n", "#     # Assign 'type_lei' based on conditions\n", "#     df['type_lei'] = 'oa'  # Default value\n", "#     df.loc[df['lei_a'] == 31, 'type_lei'] = 'code'  # Set 'code' where lei_a is 31\n", "#     # Set 'base' where combination of jia_yi_fang_a and group_id matches the code_set and lei_a is not 31\n", "#     df['combination_key'] = df['jia_yi_fang_a'].astype(str) + '-' + df['group_id']\n", "#     df.loc[df['combination_key'].isin(code_set) & (df['lei_a'] != 31), 'type_lei'] = 'base'\n", "#     # Drop the temporary combination_key column\n", "#     df.drop('combination_key', axis=1, inplace=True)\n", "\n", "#     # Filter rows where 'type_lei' is either 'code' or 'base'\n", "#     df = df[df['type_lei'].isin(['code', 'base'])]\n", "\n", "#     # Group by 'group_id' and 'product_id' and sum 'product_qty_sum'\n", "#     grouped = df.groupby(['group_id', 'product_id'])['product_qty_sum'].sum().reset_index()\n", "#     # Merge this result back to the original dataframe\n", "#     result = df.merge(grouped, on=['group_id', 'product_id'])\n", "#     # Rename columns for clarity\n", "#     result.rename(columns={'product_qty_sum_x': 'product_qty_sum', 'product_qty_sum_y': 'all_proQty_use'}, inplace=True)\n", "#     # Group by 'group_id' only and sum 'product_qty_sum'\n", "#     grouped = df.groupby('group_id')['product_qty_sum'].sum().reset_index()\n", "#     # Merge this result back to the original dataframe\n", "#     result = df.merge(grouped, on='group_id')\n", "#     # Rename columns for clarity\n", "#     df.rename(columns={'product_qty_sum_x': 'product_qty_sum', 'product_qty_sum_y': 'all_proQty_use'}, inplace=True)\n", "\n", "#     df = df.sort_values(by=['group_id'], ascending=[False])\n", "#     df = df.drop_duplicates(subset='product_id')\n", "#     df = df.head(60)\n", "    \n", "#     return df\n", "\n", "\n", "display(calOne())\n", "# display(get_color_insert_tb())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}