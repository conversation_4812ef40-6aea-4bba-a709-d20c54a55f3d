{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_fang_a</th>\n", "      <th>jia_yi_fang_b</th>\n", "      <th>lei_a</th>\n", "      <th>lei_b</th>\n", "      <th>product_id</th>\n", "      <th>qty</th>\n", "      <th>price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>50115</td>\n", "      <td>50115</td>\n", "      <td>22</td>\n", "      <td>50</td>\n", "      <td>76280</td>\n", "      <td>1</td>\n", "      <td>920.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>49021</td>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>50</td>\n", "      <td>57507</td>\n", "      <td>-1000</td>\n", "      <td>0.92</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50115</td>\n", "      <td>50115</td>\n", "      <td>22</td>\n", "      <td>22</td>\n", "      <td>37400</td>\n", "      <td>1</td>\n", "      <td>920.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   jia_yi_fang_a  jia_yi_fang_b  lei_a  lei_b  product_id   qty   price\n", "0          50115          50115     22     50       76280     1  920.00\n", "1          49021          49021     22     50       57507 -1000    0.92\n", "2          50115          50115     22     22       37400     1  920.00"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "# sys.path.append('../../../../../app/')\n", "sys.path.append('../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "# from src.shwethe_color.database import get_session\n", "from src.config.shwethe_color.database import get_session\n", "from src.shwethe_color.models.model import color_insert_tb, color_insert_oa_tb\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "\n", "\n", "data = {\n", "  \"jia_yi_fang_a\": [50115, 49021, 50115],\n", "  \"jia_yi_fang_b\": [50115, 49021, 50115],\n", "  \"lei_a\": [22, 22, 22],\n", "  \"lei_b\": [50, 50, 22],\n", "  \"product_id\": [76280, 57507, 37400],\n", "  \"qty\": [1, -1000, 1],\n", "  \"price\": [920, 0.92, 920]\n", "}\n", "\n", "#load data into a DataFrame object:\n", "df = pd.DataFrame(data)\n", "\n", "df"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_10237/3494567378.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df1['qty'] = (df1['qty'] * -1)\n", "/tmp/ipykernel_10237/3494567378.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df1['price'] = (df1['price'] * df1['qty'])\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_fang_a</th>\n", "      <th>lei_a</th>\n", "      <th>price</th>\n", "      <th>qty</th>\n", "      <th>product_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>50115</td>\n", "      <td>22</td>\n", "      <td>-920.0</td>\n", "      <td>-1</td>\n", "      <td>76280</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>920.0</td>\n", "      <td>1000</td>\n", "      <td>57507</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50115</td>\n", "      <td>22</td>\n", "      <td>-920.0</td>\n", "      <td>-1</td>\n", "      <td>37400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   jia_yi_fang_a  lei_a  price   qty  product_id\n", "0          50115     22 -920.0    -1       76280\n", "1          49021     22  920.0  1000       57507\n", "2          50115     22 -920.0    -1       37400"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df1 = df[['jia_yi_fang_a', 'lei_a', 'price', 'qty', 'product_id']]\n", "df1['qty'] = (df1['qty'] * -1)\n", "df1['price'] = (df1['price'] * df1['qty'])\n", "df1"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_10237/2535003620.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df2['price'] = (df2['price'] * df2['qty'])\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_fang_b</th>\n", "      <th>lei_b</th>\n", "      <th>price</th>\n", "      <th>qty</th>\n", "      <th>product_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>50115</td>\n", "      <td>50</td>\n", "      <td>920.0</td>\n", "      <td>1</td>\n", "      <td>76280</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>49021</td>\n", "      <td>50</td>\n", "      <td>-920.0</td>\n", "      <td>-1000</td>\n", "      <td>57507</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50115</td>\n", "      <td>22</td>\n", "      <td>920.0</td>\n", "      <td>1</td>\n", "      <td>37400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   jia_yi_fang_b  lei_b  price   qty  product_id\n", "0          50115     50  920.0     1       76280\n", "1          49021     50 -920.0 -1000       57507\n", "2          50115     22  920.0     1       37400"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df2 = df[['jia_yi_fang_b', 'lei_b', 'price', 'qty', 'product_id']]\n", "df2['price'] = (df2['price'] * df2['qty'])\n", "df2"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_fang</th>\n", "      <th>lei</th>\n", "      <th>product_id</th>\n", "      <th>qty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>57507</td>\n", "      <td>1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>50115</td>\n", "      <td>22</td>\n", "      <td>37400</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50115</td>\n", "      <td>22</td>\n", "      <td>76280</td>\n", "      <td>-1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   jia_yi_fang  lei  product_id   qty\n", "0        49021   22       57507  1000\n", "1        50115   22       37400     0\n", "2        50115   22       76280    -1"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Assuming df1 and df2 are already defined DataFrames\n", "df11 = df1.rename(columns={'jia_yi_fang_a': 'jia_yi_fang', 'lei_a': 'lei'})\n", "df21 = df2.rename(columns={'jia_yi_fang_b': 'jia_yi_fang', 'lei_b': 'lei'})\n", "# Concatenate along the columns (axis=1)\n", "df3 = pd.concat([df11, df21])\n", "# filter lei 50\n", "df3 = df3[df3['lei'] == 22]\n", "# Display the resulting DataFrame\n", "df3.groupby(['jia_yi_fang', 'lei', 'product_id'])['qty'].sum().reset_index()\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_fang</th>\n", "      <th>lei</th>\n", "      <th>product_id</th>\n", "      <th>qty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>57507</td>\n", "      <td>1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>50115</td>\n", "      <td>22</td>\n", "      <td>37400</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50115</td>\n", "      <td>22</td>\n", "      <td>76280</td>\n", "      <td>-1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   jia_yi_fang  lei  product_id   qty\n", "0        49021   22       57507  1000\n", "1        50115   22       37400     0\n", "2        50115   22       76280    -1"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df3.groupby(['jia_yi_fang', 'lei', 'product_id'])['qty'].sum().reset_index()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}