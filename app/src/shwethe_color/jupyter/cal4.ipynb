{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["18afc9b6\n"]}], "source": ["import uuid\n", "random_group_id = str(uuid.uuid4().hex)[0:8]\n", "print(random_group_id)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["202405180009199\n"]}], "source": ["import datetime\n", "import random\n", "\n", "# Get today's date in YYYYMMDD format\n", "today_date = datetime.datetime.now().strftime(\"%Y%m%d\")\n", "\n", "# Generate a random 6-digit number\n", "random_number = random.randint(1000, 9999)\n", "\n", "# Combine the date, dead number, and random number\n", "result = f\"{today_date}000{random_number}\"\n", "\n", "print(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}