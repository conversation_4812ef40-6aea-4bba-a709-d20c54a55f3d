{"cells": [{"cell_type": "code", "execution_count": 117, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_fang_a</th>\n", "      <th>jia_yi_fang_b</th>\n", "      <th>lei_a</th>\n", "      <th>lei_b</th>\n", "      <th>product_id</th>\n", "      <th>qty</th>\n", "      <th>price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>49021</td>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>50</td>\n", "      <td>76280</td>\n", "      <td>1</td>\n", "      <td>920.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>49021</td>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>50</td>\n", "      <td>57507</td>\n", "      <td>-1000</td>\n", "      <td>0.92</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50115</td>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>22</td>\n", "      <td>76280</td>\n", "      <td>1</td>\n", "      <td>920.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   jia_yi_fang_a  jia_yi_fang_b  lei_a  lei_b  product_id   qty   price\n", "0          49021          49021     22     50       76280     1  920.00\n", "1          49021          49021     22     50       57507 -1000    0.92\n", "2          50115          49021     22     22       76280     1  920.00"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "# sys.path.append('../../../../../app/')\n", "sys.path.append('../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "# from src.shwethe_color.database import get_session\n", "from src.config.shwethe_color.database import get_session\n", "from src.shwethe_color.models.model import color_insert_tb, color_insert_oa_tb\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "\n", "\n", "data = {\n", "  \"jia_yi_fang_a\": [49021, 49021, 50115],\n", "  \"jia_yi_fang_b\": [49021, 49021, 49021],\n", "  \"lei_a\": [22, 22, 22],\n", "  \"lei_b\": [50, 50, 22],\n", "  \"product_id\": [76280, 57507, 76280],\n", "  \"qty\": [1, -1000, 1],\n", "  \"price\": [920, 0.92, 920]\n", "}\n", "\n", "#load data into a DataFrame object:\n", "df = pd.DataFrame(data)\n", "\n", "df"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_10076/3494567378.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df1['qty'] = (df1['qty'] * -1)\n", "/tmp/ipykernel_10076/3494567378.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df1['price'] = (df1['price'] * df1['qty'])\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_fang_a</th>\n", "      <th>lei_a</th>\n", "      <th>price</th>\n", "      <th>qty</th>\n", "      <th>product_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>-920.0</td>\n", "      <td>-1</td>\n", "      <td>76280</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>920.0</td>\n", "      <td>1000</td>\n", "      <td>57507</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50115</td>\n", "      <td>22</td>\n", "      <td>-920.0</td>\n", "      <td>-1</td>\n", "      <td>76280</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   jia_yi_fang_a  lei_a  price   qty  product_id\n", "0          49021     22 -920.0    -1       76280\n", "1          49021     22  920.0  1000       57507\n", "2          50115     22 -920.0    -1       76280"]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}], "source": ["df1 = df[['jia_yi_fang_a', 'lei_a', 'price', 'qty', 'product_id']]\n", "df1['qty'] = (df1['qty'] * -1)\n", "df1['price'] = (df1['price'] * df1['qty'])\n", "df1"]}, {"cell_type": "code", "execution_count": 119, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_10076/2535003620.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df2['price'] = (df2['price'] * df2['qty'])\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_fang_b</th>\n", "      <th>lei_b</th>\n", "      <th>price</th>\n", "      <th>qty</th>\n", "      <th>product_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>49021</td>\n", "      <td>50</td>\n", "      <td>920.0</td>\n", "      <td>1</td>\n", "      <td>76280</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>49021</td>\n", "      <td>50</td>\n", "      <td>-920.0</td>\n", "      <td>-1000</td>\n", "      <td>57507</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>920.0</td>\n", "      <td>1</td>\n", "      <td>76280</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   jia_yi_fang_b  lei_b  price   qty  product_id\n", "0          49021     50  920.0     1       76280\n", "1          49021     50 -920.0 -1000       57507\n", "2          49021     22  920.0     1       76280"]}, "execution_count": 119, "metadata": {}, "output_type": "execute_result"}], "source": ["df2 = df[['jia_yi_fang_b', 'lei_b', 'price', 'qty', 'product_id']]\n", "df2['price'] = (df2['price'] * df2['qty'])\n", "df2"]}, {"cell_type": "code", "execution_count": 122, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_fang</th>\n", "      <th>lei</th>\n", "      <th>product_id</th>\n", "      <th>qty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>57507</td>\n", "      <td>1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>76280</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50115</td>\n", "      <td>22</td>\n", "      <td>76280</td>\n", "      <td>-1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   jia_yi_fang  lei  product_id   qty\n", "0        49021   22       57507  1000\n", "1        49021   22       76280     0\n", "2        50115   22       76280    -1"]}, "execution_count": 122, "metadata": {}, "output_type": "execute_result"}], "source": ["# Assuming df1 and df2 are already defined DataFrames\n", "df11 = df1.rename(columns={'jia_yi_fang_a': 'jia_yi_fang', 'lei_a': 'lei'})\n", "df21 = df2.rename(columns={'jia_yi_fang_b': 'jia_yi_fang', 'lei_b': 'lei'})\n", "# Concatenate along the columns (axis=1)\n", "df3 = pd.concat([df11, df21])\n", "# filter lei 50\n", "df3 = df3[df3['lei'] == 22]\n", "# Display the resulting DataFrame\n", "df3.groupby(['jia_yi_fang', 'lei', 'product_id'])['qty'].sum().reset_index()\n", "# df3.groupby(['jia_yi_fang', 'lei'])['price'].sum().reset_index()\n"]}, {"cell_type": "code", "execution_count": 123, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_fang</th>\n", "      <th>lei</th>\n", "      <th>product_id</th>\n", "      <th>qty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>57507</td>\n", "      <td>1000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>49021</td>\n", "      <td>22</td>\n", "      <td>76280</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50115</td>\n", "      <td>22</td>\n", "      <td>76280</td>\n", "      <td>-1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   jia_yi_fang  lei  product_id   qty\n", "0        49021   22       57507  1000\n", "1        49021   22       76280     0\n", "2        50115   22       76280    -1"]}, "execution_count": 123, "metadata": {}, "output_type": "execute_result"}], "source": ["df3.groupby(['jia_yi_fang', 'lei', 'product_id'])['qty'].sum().reset_index()\n", "# df3.groupby(['jia_yi_fang', 'lei'])['price'].sum().reset_index()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'<!DOCTYPE html><html lang=\"en-US\"><head><title>Just a moment...</title><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\"><meta name=\"robots\" content=\"noindex,nofollow\"><meta name=\"viewport\" content=\"width=device-width,initial-scale=1\"><style>*{box-sizing:border-box;margin:0;padding:0}html{line-height:1.15;-webkit-text-size-adjust:100%;color:#313131;font-family:system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}body{display:flex;flex-direction:column;height:100vh;min-height:100vh}.main-content{margin:8rem auto;max-width:60rem;padding-left:1.5rem}@media (width <= 720px){.main-content{margin-top:4rem}}.h2{font-size:1.5rem;font-weight:500;line-height:2.25rem}@media (width <= 720px){.h2{font-size:1.25rem;line-height:1.5rem}}#challenge-error-text{background-image:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSI+PHBhdGggZmlsbD0iI0IyMEYwMyIgZD0iTTE2IDNhMTMgMTMgMCAxIDAgMTMgMTNBMTMuMDE1IDEzLjAxNSAwIDAgMCAxNiAzbTAgMjRhMTEgMTEgMCAxIDEgMTEtMTEgMTEuMDEgMTEuMDEgMCAwIDEtMTEgMTEiLz48cGF0aCBmaWxsPSIjQjIwRjAzIiBkPSJNMTcuMDM4IDE4LjYxNUgxNC44N0wxNC41NjMgOS41aDIuNzgzem0tMS4wODQgMS40MjdxLjY2IDAgMS4wNTcuMzg4LjQwNy4zODkuNDA3Ljk5NCAwIC41OTYtLjQwNy45ODQtLjM5Ny4zOS0xLjA1Ny4zODktLjY1IDAtMS4wNTYtLjM4OS0uMzk4LS4zODktLjM5OC0uOTg0IDAtLjU5Ny4zOTgtLjk4NS40MDYtLjM5NyAxLjA1Ni0uMzk3Ii8+PC9zdmc+);background-repeat:no-repeat;background-size:contain;padding-left:34px}@media (prefers-color-scheme:dark){body{background-color:#222;color:#d9d9d9}}</style><meta http-equiv=\"refresh\" content=\"390\"></head><body class=\"no-js\"><div class=\"main-wrapper\" role=\"main\"><div class=\"main-content\"><noscript><div class=\"h2\"><span id=\"challenge-error-text\">Enable JavaScript and cookies to continue</span></div></noscript></div></div><script>(function(){window._cf_chl_opt={cvId: \\'3\\',cZone: \"api.investing.com\",cType: \\'managed\\',cRay: \\'8f0269ab2c0ff90c\\',cH: \\'my6lYZyIenK1Sxx1WY3IfPLslASbc98efawhkmn1nUk-1733888001-*******-juMyOvwYB7dBPi9oHYO_04.lRvGie46zlLPs2v5uzbtpj4hi1o_cFL3NnHTSyr6U\\',cUPMDTk: \"\\\\/api\\\\/financialdata\\\\/assets\\\\/equitiesByCountry\\\\/default?fields-list=id%252Cname%252Csymbol%252CisCFD%252Chigh%252Clow%252Clast%252ClastPairDecimal%252Cchange%252CchangePercent%252Cvolume%252Ctime%252CisOpen%252Curl%252Cflag%252CcountryNameTranslated%252CexchangeId%252CperformanceDay%252CperformanceWeek%252CperformanceMonth%252CperformanceYtd%252CperformanceYear%252Cperformance3Year%252CtechnicalHour%252CtechnicalDay%252CtechnicalWeek%252CtechnicalMonth%252CavgVolume%252CfundamentalMarketCap%252CfundamentalRevenue%252CfundamentalRatio%252CfundamentalBeta%252CpairType&country-id=41&filter-domain=&page=0&page-size=1&limit=0&include-additional-indices=false&include-major-indices=false&include-other-indices=false&include-primary-sectors=false&include-market-overview=false&__cf_chl_tk=dI7ZRM8DbVLJ_Pzl.g26W8Z5fDVDiYC45akcHjmC4UE-1733888001-*******-HhZvZmAdTnLFIuTCPoKEmbHUCk7hEnAvetsPp1u_08o\",cFPWv: \\'g\\',cITimeS: \\'1733888001\\',cTTimeMs: \\'1000\\',cMTimeMs: \\'390000\\',cTplC: 0,cTplV: 5,cTplB: \\'cf\\',cK: \"\",fa: \"\\\\/api\\\\/financialdata\\\\/assets\\\\/equitiesByCountry\\\\/default?fields-list=id%252Cname%252Csymbol%252CisCFD%252Chigh%252Clow%252Clast%252ClastPairDecimal%252Cchange%252CchangePercent%252Cvolume%252Ctime%252CisOpen%252Curl%252Cflag%252CcountryNameTranslated%252CexchangeId%252CperformanceDay%252CperformanceWeek%252CperformanceMonth%252CperformanceYtd%252CperformanceYear%252Cperformance3Year%252CtechnicalHour%252CtechnicalDay%252CtechnicalWeek%252CtechnicalMonth%252CavgVolume%252CfundamentalMarketCap%252CfundamentalRevenue%252CfundamentalRatio%252CfundamentalBeta%252CpairType&country-id=41&filter-domain=&page=0&page-size=1&limit=0&include-additional-indices=false&include-major-indices=false&include-other-indices=false&include-primary-sectors=false&include-market-overview=false&__cf_chl_f_tk=dI7ZRM8DbVLJ_Pzl.g26W8Z5fDVDiYC45akcHjmC4UE-1733888001-*******-HhZvZmAdTnLFIuTCPoKEmbHUCk7hEnAvetsPp1u_08o\",md: \"Wlp6LpJckxtJEcZh17WVEN7eJ9JLCK6RZ_ugWsqZogo-1733888001-*******-e0WgIrboJFo1Ql1ORWqXRG0D4HbUkr5R530PV3PswsUSCcEmHA2C.QYNZJ.iBWkClpp22OLdVjpVR3TDhfblxmyc64U3rY_pR2CEVu5Yl3MQ3tHJQ3kgekxD0bEp3ZjjfwMjMK37dh0O.7.3wPDHHff8DKKOShndrgpCPSK5iwhTHZuqu0HdhEb63clWVvM1lRIr6kmw6vnwvlOG5fTOaU_DIZ6rPBnKGB_WwvZfuXYVwc3uUtc9Zz6L4aYecRn8kpm.qsXGcYiGOiS_QvrdgOvL5SDJliVH2ROaFi9NxaHCpXquaCECmZBEp_l3K4aiwBfg04ssS5eH6T7azWAr3dYS2..hSh3RrB19sFqvM6PwxfItwh2AumfLg5E.owTdM8tEaSgZWDggK7_vAcfIRcLNzckK.Jl_CpGzk.oHrRcQdX.r_z.8v5SmCUdy2.dGYhZ3VNfbymnUPLgLJcuGoKLAUATOEyLKoipdmcVrBC1UMb.TWrO_rN4MbGmDtDjJYWX_Hg0K1yGk.r7jEwXDmt4PUw_7RLG0hjLywLuMP06tQDjNPGClUT.9k7cIIyb3KJwhoTjqG1Is7VJqlqRx5xT9VmcqE6lUTl0riKCrsgXtwYcNW_OBJapxWqmpWvHXPdlkXp7st5QL_pOqEHdFXgu0aaMfakghckaUu1P78t0oceG7kmT2M5.H9jcb54TnBouLxo18ZRT15dsgIXPViBI6_6k7.9WlC0XgMHy3D9GcrMA_fyH7DubR7s4EJgZzygTWcXvlwDT5O2ylofCdA412PRMsfwl6gjyfuhvoib6_d2KC7fUrL79iaCOlHEJsciUVYJ5MZSbpE0FqBXwEdl87yqVWcv_HRNJvBdGE321cFag7gtjL216SmrflidNn_JN3ZsxzW.myIq_fQqkq2frPKJjP3u9zerObvhHkIPzRIwvEdLsuAb3yDY1OuiQ13XaueM_WELfVc2ZW12wc1DurJmVnyzuU4VbDKs5i4nIXm5QAkGfU7wwZfmTaqLIxsRc4S5Uz5MTntZy181g3gE03ad7TKZRm7kzbonXrVoKBdCF.eb.um7x94VzWlgAYJUWTvUrKq9ViHII69KzDbvy2AiVYESHCTvaZtAErGBQdZOU7VKRRe.tu0_CV380GlEON.DtQOa1l_9CeWVR8R.bkbSDRGB8GBiMwmu7FpVvm6jTGGoT6iwKXe3BiBFnqZDUcCXVLCKaG4_b3MPaiEo5QKcADc3WPMleb2flFn3aqyGexvZGNDBrG0XFNkxpkuEgwFLBZU1IlCPjsNeQhfoApJDUimYfq6XgChtS1CNZCt9EEI_coyUDJZgcL012U5SuJnKVKPNgJLSa_nuvJIH8tWu8xIkrhuC9aX1SrQuTOj7khCf9O7Tdc7cIJvqg4I.Mx3j36NkyO1fihKZwZGUzgGiDED_CQ1ueQLDpy_BZ.f2SHbigOT3Sd21qZAaMQkXZCwLBLxBSZhNM9R1vSxHOHZL37OMwMxDavB.IckqTuvBxqdQTiRxUrmAO9182GuHOuxgUZmLHu1ACSHSAv_n2j4ppZAHqgCZQEdh3ekhwzR7KxERCem8GzI6XsqcAICQBIoOn5NrDtQFpFJ56qFOElQdVKQ8zVRcx52I.xRUgWaEzrPTw34mKO9oJt7II0Ca1gj14gi0DNqM_ncS09O8a294M67EhMwslq4EzigFijSWbRm3TyhW0hOQLulfNCK5o7MKmZtK5hEIIICe2or0iw2TrkhtuIjBRWObAZJ_V3eHXZTLZRLY6KD1E7UBlczdFqTqgHj1rttqccqUg.X1LJdJNkMM2_UsLW7gfhlFz7fldn77TM_jZ8XjIf85SksEEe1DAkF3wzymkVVeYGXvNLe7osPp23TrNRp2UXBDahqqFrsAp27rzQYsxYVgMLRwpQ3vK8F5vfuocjmNBpy.WWNjv_meuRS8jHTUYGdSPMPfHdsuYlSabzcBjyzTHX0A6wSNLMWS153Yga6WYiP4LI7iTzsh5rfWG5vD7OT0p2AqTYFELQ.3JxKx312UqvZF8nlaebj8eYXfb6076Bpl9BVlua3ZmVh99nFoishoihz1pQ3fGTV8cjjIAALUUugq62a3_x9bgay0raLBXJ2y83tWQycBKP9AiSYQxqy.g.dE2.pftmeW7u6M27qVwkX_G.QaZ6cwJtqsZDmMjEG8RZcC0AjU9l8EPKYz1.RsqTVW.gcY1VFTQ1eQjXs1wmt6CMNEmVuH7GmCbuQBv2ri9jyc9Tuil0OSqU07nY68UiaxdssS7NsSnhibfaiZUEh80zcgTUUqSK5oGOSh8axoKNZt05Lou_6FHnhiXh.adBrR3Rhhw9YPk5Psuk_B1w15lKqptjaBsH86Yd3NYlob.FciAfrjZK6trUixtwOUmiuDRYACMg2HfdS.S9_ht0wx6jfi1bJoX8C1epf1n.4VRqzpcOVHk6kClQMek6iAGvn8nM2jv1GUb7O6E0IQU0Yg47GQlUKhKCysIIhl9Gf4YDmTHDBRBp6bsC1AsKi9PR4wekB1wUmdG67p90ZhdifLcDn.k0lGumgZN9lQnVZhJNPCMR5JPnJ_wjyg.3sPrfZMrDGKv3XGWcOReGaKGiv8Winqk4LLz.29TVeBqA0nClSqfvhACVmfu_yB3lmMWh0m57IsuffFPx1_P_54cL1Dju.FhdiX1I_NTOKYgXi.sDcPGYFYVXqtwqhocx2WA8Py5N5XPwO1C2qC1fh.N4XfJf1BrQ078d54mLeqFrF0qN2NEsWMhMUpuYOiWDPYuSo8r7mLyifRKZsrKrhAAbR_3lFv2_I0uu377xxfd5AK8s4j.kSvbcDnEwU.YQMnNTHRrnR3nDMoO8W1lLMUFW2M5q9SmnxgQOYjcC.V3KboC005voZ7IwXyTWUPb0RfZu3hd16YYxgxMh_gVpCG8YKS1vCyYa8hrBmWV_jKVya2M_TlZBXsI4JcH6TI0ElfZvILGCeG29etSE5QqCw7BWN0DHvDLNpdTtvDQfJvGrMoeR5kajKkyDhZVc7EMYh4qasqY0YtbnkuJxhwciBDg19ijq4MKUVFxv78I7n8U1eXuGOkjFgEq6gU3ZL1nOCj2SQZmAhUZCl5avw1sMQ4KXWtygKeIR.ZToJoOwyjlkQdCFXRHbT.3_eh3hRTOAA05CRlECi76hqOAQV9haXBRhcMRpab4oDvMZSj1AewibPqZPJG_18J2h751aWM1TAXdgWJjaRG_J_X4VWIY_xSvUEdWbsm8g8CUCPXzTg9XEBOfjK_cCu4cDBjhe4nfAV2.fn8AjmATN2WHdLymut0tjWIcy5bS8PgG63CQ6XfrfFBAHQjXmp__ybm8XXfZys2TSsEKDZ3PMtv489X8rK7CwcYOVdPQJ9DCGOZYh2Eyo6okp2R0MZKyr.Ph3rAN0qk4yFrC3XU0v1QsFuF5nWvpQNlUG28VuS93vtFWqn1JRoQ7EP5HrG35IgDeau3rM4odzvE5HcfVxTQ9NZSkz90yGrrWVWASbQ5V0KBGuerhNZ3L5FSFYkvzs.dGfnVK2y_N2oIn0NnDEv7uAClvghxn_Pggl3_vGOupbJp_rMLRwXcStV_CNFUB42TwPydtjbxMvq1zGaEeXgZCJLGJ9lnFvKEYTN1O.KUxaA0D3H3brk0la_TeQLUSVgbA_8IN.4jBOofWLSo.KBEGHO_2ucWcEwMuaEqShqb_dV17QFu2FxLtHeZcKBldf7HkLvicse5PEd_AC4n0CC_daFR6_pllD0xX3XwGJhI_88zh3HCNzXtAzI6MxOXP9bbRd9T8ciIrjD7fJdSKRUL8AC6I7i0NjBzi7N7GDKJEu140EJzUFByh0VQxMGc6uzTwGK6tpG.qTRMvYBOiQglRVfAQlMTA25.XW6ksbi003Fshf.8FcLr1LuSdf9a5r4eBk1uq1ohgrcm8L.CeJhG2A__czRDPwzslZFhC3noUU.8B4fwP6MPfO0ZM5WqThaLvSssRSD74gBuvNRdWoh3hEhL9Ae9GQkKOPWM3kqAwvssF0C0BYYLwO2FAcjCkBkg0Z7VEcsTzDNfYuhcvfzDKxRtYm0gtegmn3L.Oen67HHu5LYyk7XOUrxQAxwh_Q6HeO6.Zxamv0jdMu7bfq0m6bq8EKzR2kJ7TiLcF2djuXvid.uUDFjCVl4VJuuZqSBTVQFGTOik.qKslGXgaOUft65WlPsWOFH_9l8JGXEcal3IWiD2Tnsrxp628049yaVL5dLjF.UMc\",mdrd: \"6tW9NHhee8bB_LXtHABmx5tu50_I4.3P6V.leqCuPf0-1733888001-*******-sk6Vq87PHMi6hCGrmAttn3CgjipHrv6ZaDPx07oz_jNtZExLc88z4vQGIgHAykiGFONvOGFoqWVnV65eCI630wbqk4CNrKrDJgf2wnNnVVyANONVAThMQ.GEcjXXHwy2.scDpu3e8Tj_VVsEdFRms0HIXJcc1kX6omSjgkT5RllRkwa83XCKxZ2t6PIhUb6BwqWpazuayHdDv7qCwS9F2SXVrKwHszjGuTXbtCNH_ZSTMLLAGKLNk3KopSq.iIJlaqPzi0sTIjI3Rj_mK91O7qHBVkLgPgeT91aSsbjxLJIRYwMFW2mWhogMwXc4pxU3ryerh3cJf_7prLyDbjxp_FyGi5plEJx7xryvjjIbqO0kzr.OxtAlME7UvSrxe244gYi1zdpsdwt9pTWwSP.A5QSczPpbzTUrNid35I8LenfFgDm3vd4yMYe2xcmdAe9FlPmXSthiPz4G57WL0GZrB4zt_iiQrbeZHehcc38GjmBYLtKP5oYxX8HyMZud3e7w4ojTfqJU8MmMkSzItT6KnlrdEqaNMXrq2BVLdxRI4BNT2gXod64RG03AuuRXgfcIVofmAHTnD_rUpewMxiLWqC0VA4UGX0PHZ.OK5onK.MRR09z2lghIdMKp3xdb12Tth4xwxR3FuD7y.kSuF6aSXFWgVrir7xPgUjoI_Uxw5DtQKbOxXWkkrst2_ql8PfckYKG9g6jQbVW26ab0Qnj_r27UxIWY4ndS6LctQW4ETAm_sRXYmNjX3M._q_TKdhh2hbQXWLFt6ePcqiESuUVj1AVKgYzmPuBCZX9B1Iav90ZsSlATJ1MOLTtZ6R3xFZ2nCODsTxV4y7cUqzzt6SGZVwYfXjgJRipGj8gKjzJRxmR2kOJZVPPMwM2.ewybO3MshKVJJ4QK0vczzYckiEZNhIuHUnT7w0d3w._AcbYZPAVcWjiHrTUebpxHVqkxclBFjJHfZCmqxZvlnFNESC5fT.6YATzZrTa62c7evfOdsB0hR6vW.97N5zl.l18Ju9r5cuO5zjJxDb5hTNjovR2nlOsGN0ZWwVz6GByeYRor43bEpXsdBhk7L9zTEHQX.RYYMNB6X1Q2Ku.bjIX09r_Nnj0BCnc1HNEUCQSIzq9wo1.WyUQHPHNx9_YRIw6.WhS.0ZoWqh37OZsDGN48cPJ43VNYi3qxhP1wp757wa5Ijzf9pGuMeKQ5CQwBSIhAg2ycU9EBJxkjZZpB3hiMNaT.UGaUzrYM__4_mHNtiKyrEmc9MG7ZzR5VpuajRZoj1j9xJ.0QYA34b3RvKK6oGxlVMNqopzkKvNIrfbkYSjtxPa54eX1Z.D7G_VrySol_Z1HeWRL8mAxxy0GMvefNDalNk.374yaIEuNAFfaS6xP17CX1.LPa.iHbkxYIVYC7vtI4IDG4q8LHY2YCGHULBoHpWg8HmngBiR2tNC368Hd7CaI3QzqAZ4iRum90iXjgX04yvGilPAqXLuD3ylaXrqq6tiDD._t..nUJr1ZhgCd3rce2CY6FuINRXWsimyTWehJL2aW7LCunJYVmB5qJJDkFolfluMsQsqcVnONrW07Sp3rJw.JiM812ehzftAUHvq3_EoDHNdMWWvFVOuDciH.1tpiNGtHD4hxD1h2SJhr5bBa5HgapHzmLitRCZeiWv_bN_GADyXIhBYtkNf3H.2eUYzi6c3ORJ3XnUM1mkjZMzPW6jOYlzC0B6yB6Mhvb7cMMY4GtFENAWzPb53W5SKt4wRcvlVpAvKIQZbDZMFWm6PPL3Qcw_vD4_kb37wwXnKxhM73XFwcEyGteTfIhWPnKHR1.Id5hb3j26okSJQI0wlx1WvOB64JeAdAhNOhkkBct25lL3GU.IW7AHTUIjhhcKOeEzVeyJN2kDasXRki9TlCN4oyZQqdYiCv4coI6nMCxBIhaoXihHxSRtFzXq3Law70pOdyiiXF9PjjEITlMDD3hVSvYoBxuQD5SSldUdJZZaElNJgEiyrmpt7yGVKbv2SKjhjLgKEa1.TjWwNFinizqoioVAukwJ6KKaFFKCHjA6VTrDFUdjm_N.Spl6BQ2FwzDPCuW66ZzYciOAItANor97XgRnYYvsXVHkf3D1cjzoK.RE4GicaeUJh4iDQ7uQhgRHx5GXeKXXfS5nBEb79ixOhlR1tvVfdyeXlQtWyBeSAq.O7oo7HkZEd2bNweVSQZTKM3DdgTe8brsqqzR_E461_xbpWS0rJADTGaND890taAM2P_HNIURTWAERQuMxn_1AfqAAu41t0VJJWo4Hpcod1MLJaKcIMP961Ab4NzRiXGDdSTA1kMaIoPmQDvDpNPp3aEEyIsQfTrIkPB8Dg5bzr9vHa1klkT6FLU2764B5kUpyQV40MVZyEGQQQTnO3NqKpSdXWKwRrF1mpqhiK60.vVwe71T8k8KC2XAtGUd\"};var cpo = document.createElement(\\'script\\');cpo.src = \\'/cdn-cgi/challenge-platform/h/g/orchestrate/chl_page/v1?ray=8f0269ab2c0ff90c\\';window._cf_chl_opt.cOgUHash = location.hash === \\'\\' && location.href.indexOf(\\'#\\') !== -1 ? \\'#\\' : location.hash;window._cf_chl_opt.cOgUQuery = location.search === \\'\\' && location.href.slice(0, location.href.length - window._cf_chl_opt.cOgUHash.length).indexOf(\\'?\\') !== -1 ? \\'?\\' : location.search;if (window.history && window.history.replaceState) {var ogU = location.pathname + window._cf_chl_opt.cOgUQuery + window._cf_chl_opt.cOgUHash;history.replaceState(null, null, \"\\\\/api\\\\/financialdata\\\\/assets\\\\/equitiesByCountry\\\\/default?fields-list=id%252Cname%252Csymbol%252CisCFD%252Chigh%252Clow%252Clast%252ClastPairDecimal%252Cchange%252CchangePercent%252Cvolume%252Ctime%252CisOpen%252Curl%252Cflag%252CcountryNameTranslated%252CexchangeId%252CperformanceDay%252CperformanceWeek%252CperformanceMonth%252CperformanceYtd%252CperformanceYear%252Cperformance3Year%252CtechnicalHour%252CtechnicalDay%252CtechnicalWeek%252CtechnicalMonth%252CavgVolume%252CfundamentalMarketCap%252CfundamentalRevenue%252CfundamentalRatio%252CfundamentalBeta%252CpairType&country-id=41&filter-domain=&page=0&page-size=1&limit=0&include-additional-indices=false&include-major-indices=false&include-other-indices=false&include-primary-sectors=false&include-market-overview=false&__cf_chl_rt_tk=dI7ZRM8DbVLJ_Pzl.g26W8Z5fDVDiYC45akcHjmC4UE-1733888001-*******-HhZvZmAdTnLFIuTCPoKEmbHUCk7hEnAvetsPp1u_08o\" + window._cf_chl_opt.cOgUHash);cpo.onload = function() {history.replaceState(null, null, ogU);}}document.getElementsByTagName(\\'head\\')[0].appendChild(cpo);}());</script></body></html>'\n"]}], "source": ["import http.client\n", "\n", "conn = http.client.HTTPSConnection(\"api.investing.com\")\n", "payload = ''\n", "headers = {\n", "  'Cookie': '__cf_bm=oNfjkWzRXQsM22U1AeEVqc1nqadciNxULEk67zjw5cI-1733887479-*******-HMFE9A9VdRiwxUtBfMVdEKPlfe41IBCm0bsMrf3zVFxTWOybvvkb2izzGVvJpFADGdBPLxH_La3ZjB9gu0.6nL81IZLjLQPvsbsFeuQU.TQ; __cflb=02DiuEaBtsFfH7bEbN4qQwLpwTUxNYEGyp7Yt9NK8eFQY'\n", "}\n", "conn.request(\"GET\", \"/api/financialdata/assets/equitiesByCountry/default?fields-list=id%252Cname%252Csymbol%252CisCFD%252Chigh%252Clow%252Clast%252ClastPairDecimal%252Cchange%252CchangePercent%252Cvolume%252Ctime%252CisOpen%252Curl%252Cflag%252CcountryNameTranslated%252CexchangeId%252CperformanceDay%252CperformanceWeek%252CperformanceMonth%252CperformanceYtd%252CperformanceYear%252Cperformance3Year%252CtechnicalHour%252CtechnicalDay%252CtechnicalWeek%252CtechnicalMonth%252CavgVolume%252CfundamentalMarketCap%252CfundamentalRevenue%252CfundamentalRatio%252CfundamentalBeta%252CpairType&country-id=41&filter-domain=&page=0&page-size=1&limit=0&include-additional-indices=false&include-major-indices=false&include-other-indices=false&include-primary-sectors=false&include-market-overview=false\", payload, headers)\n", "res = conn.getresponse()\n", "data = res.read()\n", "print(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}