{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-01-18 08:24:29,491 INFO sqlalchemy.engine.Engine select pg_catalog.version()\n", "2024-01-18 08:24:29,493 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-01-18 08:24:29,501 INFO sqlalchemy.engine.Engine select current_schema()\n", "2024-01-18 08:24:29,502 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-01-18 08:24:29,508 INFO sqlalchemy.engine.Engine show standard_conforming_strings\n", "2024-01-18 08:24:29,510 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-01-18 08:24:29,516 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-01-18 08:24:29,534 INFO sqlalchemy.engine.Engine SELECT color_insert_tb.record_id, color_insert_tb.detail_json, color_insert_tb.status, color_insert_tb.auto_id, color_insert_tb.datetime, color_insert_tb.group_id, color_insert_tb.jia_yi_fang_a, color_insert_tb.jia_yi_fang_b, color_insert_tb.lei_a, color_insert_tb.lei_b, color_insert_tb.device_id, color_insert_tb.product_id, color_insert_tb.product_qty, color_insert_tb.color_price, color_insert_tb.unit_price, color_insert_tb.bi_zhi, color_insert_tb.fen, color_insert_tb.sum_price, color_insert_tb.product_qty_sum, color_insert_tb.unit_price_sum \n", "FROM color_insert_tb \n", "WHERE color_insert_tb.group_id IN (SELECT anon_1.group_id \n", "FROM (SELECT color_insert_tb.group_id AS group_id \n", "FROM color_insert_tb \n", "WHERE CAST((color_insert_tb.status ->> %(status_1)s) AS VARCHAR) = %(param_1)s) AS anon_1) AND color_insert_tb.datetime >= %(datetime_1)s ORDER BY color_insert_tb.datetime DESC\n", "2024-01-18 08:24:29,545 INFO sqlalchemy.engine.Engine [generated in 0.01194s] {'status_1': 'status_big_table', 'param_1': 'success', 'datetime_1': datetime.date(2023, 12, 19)}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_4581/1005741452.py:53: SAWarning: Coercing Subquery object into a select() for use in IN(); please pass a select() construct explicitly\n", "  color_insert_tb.group_id.in_(success_groups_subquery),\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024-01-18 08:24:29,685 INFO sqlalchemy.engine.Engine ROLLBACK\n", "2024-01-18 08:24:29,717 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-01-18 08:24:29,722 INFO sqlalchemy.engine.Engine SELECT color_insert_oa_tb.record_id, color_insert_oa_tb.detail_json, color_insert_oa_tb.status, color_insert_oa_tb.auto_id, color_insert_oa_tb.datetime, color_insert_oa_tb.group_id, color_insert_oa_tb.jia_yi_fang_a, color_insert_oa_tb.jia_yi_fang_b, color_insert_oa_tb.lei_a, color_insert_oa_tb.lei_b, color_insert_oa_tb.device_id, color_insert_oa_tb.product_id, color_insert_oa_tb.product_qty, color_insert_oa_tb.fen, color_insert_oa_tb.color_price, color_insert_oa_tb.unit_price \n", "FROM color_insert_oa_tb \n", "WHERE color_insert_oa_tb.group_id IN (SELECT anon_1.group_id \n", "FROM (SELECT color_insert_oa_tb.group_id AS group_id \n", "FROM color_insert_oa_tb \n", "WHERE CAST((color_insert_oa_tb.status ->> %(status_1)s) AS VARCHAR) = %(param_1)s) AS anon_1) AND color_insert_oa_tb.product_qty = %(product_qty_1)s AND color_insert_oa_tb.datetime >= %(datetime_1)s ORDER BY color_insert_oa_tb.datetime DESC\n", "2024-01-18 08:24:29,723 INFO sqlalchemy.engine.Engine [generated in 0.00176s] {'status_1': 'status_big_table', 'param_1': 'success', 'product_qty_1': -1000, 'datetime_1': datetime.date(2023, 12, 19)}\n", "2024-01-18 08:24:29,742 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_4581/1005741452.py:80: SAWarning: Coercing Subquery object into a select() for use in IN(); please pass a select() construct explicitly\n", "  color_insert_oa_tb.group_id.in_(success_groups_subquery),\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>lei_a</th>\n", "      <th>fen</th>\n", "      <th>group_id</th>\n", "      <th>device_id</th>\n", "      <th>product_id</th>\n", "      <th>product_qty</th>\n", "      <th>product_qty_group</th>\n", "      <th>sum_qty_per_product</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-18 14:23:42.641499+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>78303</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-01-18 14:23:42.641090+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>256.5</td>\n", "      <td>513.0</td>\n", "      <td>515.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-01-18 14:23:42.640848+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>76278</td>\n", "      <td>18.0</td>\n", "      <td>36.0</td>\n", "      <td>36.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01-18 14:23:42.640560+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>76277</td>\n", "      <td>49.5</td>\n", "      <td>99.0</td>\n", "      <td>99.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-01-18 14:23:42.640035+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>14.0</td>\n", "      <td>28.0</td>\n", "      <td>178.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-01-18 14:07:11.433701+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>62141</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-01-18 14:07:11.432947+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>279.0</td>\n", "      <td>279.0</td>\n", "      <td>496.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-01-18 14:07:11.432569+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>42.3</td>\n", "      <td>42.3</td>\n", "      <td>54.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-01-18 14:07:11.432105+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>76271</td>\n", "      <td>12.2</td>\n", "      <td>12.2</td>\n", "      <td>12.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-01-18 14:07:11.431397+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>150.0</td>\n", "      <td>150.0</td>\n", "      <td>178.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-01-18 14:03:22.841320+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>7dd20b32</td>\n", "      <td>49019</td>\n", "      <td>61769</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-01-18 14:03:22.840553+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>7dd20b32</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>2.7</td>\n", "      <td>2.7</td>\n", "      <td>515.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-01-18 14:03:22.840182+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>7dd20b32</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>213.0</td>\n", "      <td>213.0</td>\n", "      <td>496.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-01-18 14:03:22.839523+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>7dd20b32</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>54.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-01-18 13:40:32.521602+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>db95861f</td>\n", "      <td>49019</td>\n", "      <td>76908</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-01-18 13:40:32.520901+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>db95861f</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>0.7</td>\n", "      <td>4.2</td>\n", "      <td>496.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-01-18 13:59:20.591260+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>3c965ac1</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-01-17 08:54:56.743347+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>3f6f9373</td>\n", "      <td>49019</td>\n", "      <td>76276</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2024-01-17 08:54:56.741623+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>3f6f9373</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-01-16 09:22:59.400000+06:30</td>\n", "      <td>22</td>\n", "      <td>3</td>\n", "      <td>01786cc4</td>\n", "      <td>49021</td>\n", "      <td>76272</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2024-01-16 08:58:20.076308+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>65bf0026</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2024-01-12 11:22:51.346067+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>97643c3e</td>\n", "      <td>49023</td>\n", "      <td>77985</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2024-01-12 11:22:51.344858+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>97643c3e</td>\n", "      <td>49023</td>\n", "      <td>77983</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2024-01-12 11:22:51.343865+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>97643c3e</td>\n", "      <td>49023</td>\n", "      <td>77981</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2024-01-12 11:22:51.342529+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>97643c3e</td>\n", "      <td>49023</td>\n", "      <td>77980</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2024-01-11 13:32:09.561581+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>beb630da</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2024-01-11 13:06:54.493296+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>47d0b457</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2024-01-11 13:06:54.492621+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>47d0b457</td>\n", "      <td>49019</td>\n", "      <td>76279</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2024-01-08 14:01:38.626857+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>66807ed4</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2024-01-08 13:18:37.009366+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>41acfc61</td>\n", "      <td>49019</td>\n", "      <td>76277</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>2024-01-08 13:18:37.008507+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>41acfc61</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>2024-01-08 13:18:37.007576+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>41acfc61</td>\n", "      <td>49019</td>\n", "      <td>76273</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>2024-01-08 13:18:37.006841+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>41acfc61</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2024-01-07 15:45:23.247352+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>149e2ed4</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime  lei_a  fen  group_id  device_id   \n", "0  2024-01-18 14:23:42.641499+06:30     31    1  e634095c      49019  \\\n", "1  2024-01-18 14:23:42.641090+06:30     22    1  e634095c      49019   \n", "2  2024-01-18 14:23:42.640848+06:30     22    1  e634095c      49019   \n", "3  2024-01-18 14:23:42.640560+06:30     22    1  e634095c      49019   \n", "4  2024-01-18 14:23:42.640035+06:30     22    1  e634095c      49019   \n", "5  2024-01-18 14:07:11.433701+06:30     31    1  10127a38      49019   \n", "6  2024-01-18 14:07:11.432947+06:30     22    1  10127a38      49019   \n", "7  2024-01-18 14:07:11.432569+06:30     22    1  10127a38      49019   \n", "8  2024-01-18 14:07:11.432105+06:30     22    1  10127a38      49019   \n", "9  2024-01-18 14:07:11.431397+06:30     22    1  10127a38      49019   \n", "10 2024-01-18 14:03:22.841320+06:30     31    1  7dd20b32      49019   \n", "11 2024-01-18 14:03:22.840553+06:30     22    1  7dd20b32      49019   \n", "12 2024-01-18 14:03:22.840182+06:30     22    1  7dd20b32      49019   \n", "13 2024-01-18 14:03:22.839523+06:30     22    1  7dd20b32      49019   \n", "14 2024-01-18 13:40:32.521602+06:30     31    1  db95861f      49019   \n", "15 2024-01-18 13:40:32.520901+06:30     22    1  db95861f      49019   \n", "16 2024-01-18 13:59:20.591260+06:30     22    1  3c965ac1      49019   \n", "17 2024-01-17 08:54:56.743347+06:30     22    1  3f6f9373      49019   \n", "18 2024-01-17 08:54:56.741623+06:30     22    1  3f6f9373      49019   \n", "19 2024-01-16 09:22:59.400000+06:30     22    3  01786cc4      49021   \n", "20 2024-01-16 08:58:20.076308+06:30     22    1  65bf0026      49019   \n", "21 2024-01-12 11:22:51.346067+06:30     22    1  97643c3e      49023   \n", "22 2024-01-12 11:22:51.344858+06:30     22    1  97643c3e      49023   \n", "23 2024-01-12 11:22:51.343865+06:30     22    1  97643c3e      49023   \n", "24 2024-01-12 11:22:51.342529+06:30     22    1  97643c3e      49023   \n", "25 2024-01-11 13:32:09.561581+06:30     22    1  beb630da      49019   \n", "26 2024-01-11 13:06:54.493296+06:30     22    1  47d0b457      49019   \n", "27 2024-01-11 13:06:54.492621+06:30     22    1  47d0b457      49019   \n", "28 2024-01-08 14:01:38.626857+06:30     22    1  66807ed4      49019   \n", "29 2024-01-08 13:18:37.009366+06:30     22    1  41acfc61      49019   \n", "30 2024-01-08 13:18:37.008507+06:30     22    1  41acfc61      49019   \n", "31 2024-01-08 13:18:37.007576+06:30     22    1  41acfc61      49019   \n", "32 2024-01-08 13:18:37.006841+06:30     22    1  41acfc61      49019   \n", "33 2024-01-07 15:45:23.247352+06:30     22    1  149e2ed4      49019   \n", "\n", "    product_id  product_qty  product_qty_group  sum_qty_per_product  \n", "0        78303          2.0                2.0                  2.0  \n", "1        76281        256.5              513.0                515.7  \n", "2        76278         18.0               36.0                 36.0  \n", "3        76277         49.5               99.0                 99.0  \n", "4        76270         14.0               28.0                178.0  \n", "5        62141          1.0                1.0                  1.0  \n", "6        76280        279.0              279.0                496.2  \n", "7        76274         42.3               42.3                 54.3  \n", "8        76271         12.2               12.2                 12.2  \n", "9        76270        150.0              150.0                178.0  \n", "10       61769          1.0                1.0                  1.0  \n", "11       76281          2.7                2.7                515.7  \n", "12       76280        213.0              213.0                496.2  \n", "13       76274         12.0               12.0                 54.3  \n", "14       76908          6.0                6.0                  6.0  \n", "15       76280          0.7                4.2                496.2  \n", "16       76280      -1000.0                NaN                  NaN  \n", "17       76276      -1000.0                NaN                  NaN  \n", "18       76270      -1000.0                NaN                  NaN  \n", "19       76272      -1000.0                NaN                  NaN  \n", "20       76280      -1000.0                NaN                  NaN  \n", "21       77985      -1000.0                NaN                  NaN  \n", "22       77983      -1000.0                NaN                  NaN  \n", "23       77981      -1000.0                NaN                  NaN  \n", "24       77980      -1000.0                NaN                  NaN  \n", "25       76280      -1000.0                NaN                  NaN  \n", "26       76280      -1000.0                NaN                  NaN  \n", "27       76279      -1000.0                NaN                  NaN  \n", "28       76280      -1000.0                NaN                  NaN  \n", "29       76277      -1000.0                NaN                  NaN  \n", "30       76274      -1000.0                NaN                  NaN  \n", "31       76273      -1000.0                NaN                  NaN  \n", "32       76270      -1000.0                NaN                  NaN  \n", "33       76281      -1000.0                NaN                  NaN  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2024-01-18 08:24:29,778 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-01-18 08:24:29,780 INFO sqlalchemy.engine.Engine SELECT color_insert_tb.record_id, color_insert_tb.detail_json, color_insert_tb.status, color_insert_tb.auto_id, color_insert_tb.datetime, color_insert_tb.group_id, color_insert_tb.jia_yi_fang_a, color_insert_tb.jia_yi_fang_b, color_insert_tb.lei_a, color_insert_tb.lei_b, color_insert_tb.device_id, color_insert_tb.product_id, color_insert_tb.product_qty, color_insert_tb.color_price, color_insert_tb.unit_price, color_insert_tb.bi_zhi, color_insert_tb.fen, color_insert_tb.sum_price, color_insert_tb.product_qty_sum, color_insert_tb.unit_price_sum \n", "FROM color_insert_tb \n", "WHERE color_insert_tb.group_id IN (SELECT anon_1.group_id \n", "FROM (SELECT color_insert_tb.group_id AS group_id \n", "FROM color_insert_tb \n", "WHERE CAST((color_insert_tb.status ->> %(status_1)s) AS VARCHAR) = %(param_1)s) AS anon_1) AND color_insert_tb.datetime >= %(datetime_1)s ORDER BY color_insert_tb.datetime DESC\n", "2024-01-18 08:24:29,781 INFO sqlalchemy.engine.Engine [cached since 0.2481s ago] {'status_1': 'status_big_table', 'param_1': 'success', 'datetime_1': datetime.date(2023, 12, 19)}\n", "2024-01-18 08:24:29,875 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_4581/1005741452.py:53: SAWarning: Coercing Subquery object into a select() for use in IN(); please pass a select() construct explicitly\n", "  color_insert_tb.group_id.in_(success_groups_subquery),\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>lei_a</th>\n", "      <th>fen</th>\n", "      <th>group_id</th>\n", "      <th>device_id</th>\n", "      <th>product_id</th>\n", "      <th>product_qty</th>\n", "      <th>product_qty_group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-18 14:23:42.641499+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>78303</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-01-18 14:23:42.641090+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>256.5</td>\n", "      <td>513.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01-18 14:23:42.640848+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>76278</td>\n", "      <td>18.0</td>\n", "      <td>36.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-01-18 14:23:42.640560+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>76277</td>\n", "      <td>49.5</td>\n", "      <td>99.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-01-18 14:23:42.640035+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>14.0</td>\n", "      <td>28.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-01-18 14:07:11.433701+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>62141</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-01-18 14:07:11.432947+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>279.0</td>\n", "      <td>279.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-01-18 14:07:11.432569+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>42.3</td>\n", "      <td>42.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-01-18 14:07:11.432105+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>76271</td>\n", "      <td>12.2</td>\n", "      <td>12.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-01-18 14:07:11.431397+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>150.0</td>\n", "      <td>150.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-01-18 14:03:22.841320+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>7dd20b32</td>\n", "      <td>49019</td>\n", "      <td>61769</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-01-18 14:03:22.840553+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>7dd20b32</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>2.7</td>\n", "      <td>2.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-01-18 14:03:22.840182+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>7dd20b32</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>213.0</td>\n", "      <td>213.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-01-18 14:03:22.839523+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>7dd20b32</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-01-18 13:40:32.521602+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>db95861f</td>\n", "      <td>49019</td>\n", "      <td>76908</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-01-18 13:40:32.520901+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>db95861f</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>0.7</td>\n", "      <td>4.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime  lei_a  fen  group_id  device_id   \n", "0  2024-01-18 14:23:42.641499+06:30     31    1  e634095c      49019  \\\n", "2  2024-01-18 14:23:42.641090+06:30     22    1  e634095c      49019   \n", "3  2024-01-18 14:23:42.640848+06:30     22    1  e634095c      49019   \n", "4  2024-01-18 14:23:42.640560+06:30     22    1  e634095c      49019   \n", "5  2024-01-18 14:23:42.640035+06:30     22    1  e634095c      49019   \n", "6  2024-01-18 14:07:11.433701+06:30     31    1  10127a38      49019   \n", "8  2024-01-18 14:07:11.432947+06:30     22    1  10127a38      49019   \n", "9  2024-01-18 14:07:11.432569+06:30     22    1  10127a38      49019   \n", "10 2024-01-18 14:07:11.432105+06:30     22    1  10127a38      49019   \n", "11 2024-01-18 14:07:11.431397+06:30     22    1  10127a38      49019   \n", "12 2024-01-18 14:03:22.841320+06:30     31    1  7dd20b32      49019   \n", "14 2024-01-18 14:03:22.840553+06:30     22    1  7dd20b32      49019   \n", "15 2024-01-18 14:03:22.840182+06:30     22    1  7dd20b32      49019   \n", "16 2024-01-18 14:03:22.839523+06:30     22    1  7dd20b32      49019   \n", "17 2024-01-18 13:40:32.521602+06:30     31    1  db95861f      49019   \n", "19 2024-01-18 13:40:32.520901+06:30     22    1  db95861f      49019   \n", "\n", "    product_id  product_qty  product_qty_group  \n", "0        78303          2.0                2.0  \n", "2        76281        256.5              513.0  \n", "3        76278         18.0               36.0  \n", "4        76277         49.5               99.0  \n", "5        76270         14.0               28.0  \n", "6        62141          1.0                1.0  \n", "8        76280        279.0              279.0  \n", "9        76274         42.3               42.3  \n", "10       76271         12.2               12.2  \n", "11       76270        150.0              150.0  \n", "12       61769          1.0                1.0  \n", "14       76281          2.7                2.7  \n", "15       76280        213.0              213.0  \n", "16       76274         12.0               12.0  \n", "17       76908          6.0                6.0  \n", "19       76280          0.7                4.2  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2024-01-18 08:24:29,902 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-01-18 08:24:29,905 INFO sqlalchemy.engine.Engine SELECT color_insert_tb.record_id, color_insert_tb.detail_json, color_insert_tb.status, color_insert_tb.auto_id, color_insert_tb.datetime, color_insert_tb.group_id, color_insert_tb.jia_yi_fang_a, color_insert_tb.jia_yi_fang_b, color_insert_tb.lei_a, color_insert_tb.lei_b, color_insert_tb.device_id, color_insert_tb.product_id, color_insert_tb.product_qty, color_insert_tb.color_price, color_insert_tb.unit_price, color_insert_tb.bi_zhi, color_insert_tb.fen, color_insert_tb.sum_price, color_insert_tb.product_qty_sum, color_insert_tb.unit_price_sum \n", "FROM color_insert_tb \n", "WHERE color_insert_tb.group_id IN (SELECT anon_1.group_id \n", "FROM (SELECT color_insert_tb.group_id AS group_id \n", "FROM color_insert_tb \n", "WHERE CAST((color_insert_tb.status ->> %(status_1)s) AS VARCHAR) = %(param_1)s) AS anon_1) AND color_insert_tb.datetime >= %(datetime_1)s ORDER BY color_insert_tb.datetime DESC\n", "2024-01-18 08:24:29,906 INFO sqlalchemy.engine.Engine [cached since 0.373s ago] {'status_1': 'status_big_table', 'param_1': 'success', 'datetime_1': datetime.date(2023, 12, 19)}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_4581/1005741452.py:53: SAWarning: Coercing Subquery object into a select() for use in IN(); please pass a select() construct explicitly\n", "  color_insert_tb.group_id.in_(success_groups_subquery),\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024-01-18 08:24:30,016 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>lei_a</th>\n", "      <th>fen</th>\n", "      <th>group_id</th>\n", "      <th>device_id</th>\n", "      <th>product_id</th>\n", "      <th>product_qty</th>\n", "      <th>product_qty_group</th>\n", "      <th>sum_qty_per_product</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-18 14:23:42.641499+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>78303</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-01-18 14:23:42.641090+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>256.5</td>\n", "      <td>513.0</td>\n", "      <td>515.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01-18 14:23:42.640848+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>76278</td>\n", "      <td>18.0</td>\n", "      <td>36.0</td>\n", "      <td>36.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-01-18 14:23:42.640560+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>76277</td>\n", "      <td>49.5</td>\n", "      <td>99.0</td>\n", "      <td>99.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-01-18 14:23:42.640035+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>e634095c</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>14.0</td>\n", "      <td>28.0</td>\n", "      <td>178.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-01-18 14:07:11.433701+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>62141</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-01-18 14:07:11.432947+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>279.0</td>\n", "      <td>279.0</td>\n", "      <td>496.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-01-18 14:07:11.432569+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>42.3</td>\n", "      <td>42.3</td>\n", "      <td>54.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-01-18 14:07:11.432105+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>76271</td>\n", "      <td>12.2</td>\n", "      <td>12.2</td>\n", "      <td>12.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-01-18 14:07:11.431397+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>10127a38</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>150.0</td>\n", "      <td>150.0</td>\n", "      <td>178.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-01-18 14:03:22.841320+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>7dd20b32</td>\n", "      <td>49019</td>\n", "      <td>61769</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-01-18 14:03:22.840553+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>7dd20b32</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>2.7</td>\n", "      <td>2.7</td>\n", "      <td>515.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-01-18 14:03:22.840182+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>7dd20b32</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>213.0</td>\n", "      <td>213.0</td>\n", "      <td>496.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-01-18 14:03:22.839523+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>7dd20b32</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>54.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-01-18 13:40:32.521602+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>db95861f</td>\n", "      <td>49019</td>\n", "      <td>76908</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-01-18 13:40:32.520901+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>db95861f</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>0.7</td>\n", "      <td>4.2</td>\n", "      <td>496.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime  lei_a  fen  group_id  device_id   \n", "0  2024-01-18 14:23:42.641499+06:30     31    1  e634095c      49019  \\\n", "2  2024-01-18 14:23:42.641090+06:30     22    1  e634095c      49019   \n", "3  2024-01-18 14:23:42.640848+06:30     22    1  e634095c      49019   \n", "4  2024-01-18 14:23:42.640560+06:30     22    1  e634095c      49019   \n", "5  2024-01-18 14:23:42.640035+06:30     22    1  e634095c      49019   \n", "6  2024-01-18 14:07:11.433701+06:30     31    1  10127a38      49019   \n", "8  2024-01-18 14:07:11.432947+06:30     22    1  10127a38      49019   \n", "9  2024-01-18 14:07:11.432569+06:30     22    1  10127a38      49019   \n", "10 2024-01-18 14:07:11.432105+06:30     22    1  10127a38      49019   \n", "11 2024-01-18 14:07:11.431397+06:30     22    1  10127a38      49019   \n", "12 2024-01-18 14:03:22.841320+06:30     31    1  7dd20b32      49019   \n", "14 2024-01-18 14:03:22.840553+06:30     22    1  7dd20b32      49019   \n", "15 2024-01-18 14:03:22.840182+06:30     22    1  7dd20b32      49019   \n", "16 2024-01-18 14:03:22.839523+06:30     22    1  7dd20b32      49019   \n", "17 2024-01-18 13:40:32.521602+06:30     31    1  db95861f      49019   \n", "19 2024-01-18 13:40:32.520901+06:30     22    1  db95861f      49019   \n", "\n", "    product_id  product_qty  product_qty_group  sum_qty_per_product  \n", "0        78303          2.0                2.0                  2.0  \n", "2        76281        256.5              513.0                515.7  \n", "3        76278         18.0               36.0                 36.0  \n", "4        76277         49.5               99.0                 99.0  \n", "5        76270         14.0               28.0                178.0  \n", "6        62141          1.0                1.0                  1.0  \n", "8        76280        279.0              279.0                496.2  \n", "9        76274         42.3               42.3                 54.3  \n", "10       76271         12.2               12.2                 12.2  \n", "11       76270        150.0              150.0                178.0  \n", "12       61769          1.0                1.0                  1.0  \n", "14       76281          2.7                2.7                515.7  \n", "15       76280        213.0              213.0                496.2  \n", "16       76274         12.0               12.0                 54.3  \n", "17       76908          6.0                6.0                  6.0  \n", "19       76280          0.7                4.2                496.2  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sys\n", "# sys.path.append('../../../../../app/')\n", "sys.path.append('../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "# from src.shwethe_color.database import get_session\n", "from src.config.shwethe_color.database import get_session\n", "from src.shwethe_color.models.model import color_insert_tb, color_insert_oa_tb\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "\n", "\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def dataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "def get_color_insert_tb():\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        # GET TODAY DATA\n", "        week_ago = today - DT.<PERSON><PERSON>(days=30)\n", "\n", "        # heroesPersonal = db.exec(select(color_insert_tb).where(color_insert_tb.datetime > week_ago).order_by(color_insert_tb.datetime.desc())).all()\n", "\n", "        # First, create a subquery for SuccessGroups\n", "        success_groups_subquery = select(color_insert_tb.group_id).where(\n", "            color_insert_tb.status[(\"status_big_table\")].as_string() == \"success\"\n", "        ).subquery()\n", "        # Now, create the main query\n", "        query = select(color_insert_tb).where(\n", "            color_insert_tb.group_id.in_(success_groups_subquery),\n", "            color_insert_tb.datetime >= week_ago\n", "        ).order_by(color_insert_tb.datetime.desc())\n", "        # Execute the query\n", "        heroesPersonal = db.exec(query).all()\n", "\n", "        mergeDF = dataframe(heroesPersonal, to_dict=False)\n", "        mergeDF = mergeDF[['datetime', 'lei_a', 'fen', 'group_id', 'device_id', 'product_id', 'product_qty']]\n", "        mergeDF = mergeDF.head(20)\n", "    return mergeDF\n", "\n", "\n", "def get_color_insert_oa_tb():\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        # GET TODAY DATA\n", "        week_ago = today - DT.<PERSON><PERSON>(days=30)\n", "\n", "        # heroesPersonal = db.exec(select(color_insert_oa_tb).where(color_insert_oa_tb.datetime > week_ago, color_insert_oa_tb.product_qty == -1000).order_by(color_insert_oa_tb.datetime.desc())).all()\n", "\n", "        # First, create a subquery for SuccessGroups\n", "        success_groups_subquery = select(color_insert_oa_tb.group_id).where(\n", "            color_insert_oa_tb.status[(\"status_big_table\")].as_string() == \"success\"\n", "        ).subquery()\n", "        # Now, create the main query\n", "        query = select(color_insert_oa_tb).where(\n", "            color_insert_oa_tb.group_id.in_(success_groups_subquery),\n", "            color_insert_oa_tb.product_qty == -1000,\n", "            color_insert_oa_tb.datetime >= week_ago\n", "        ).order_by(color_insert_oa_tb.datetime.desc())\n", "        # Execute the query\n", "        heroesPersonal = db.exec(query).all()\n", "\n", "        mergeDF = dataframe(heroesPersonal, to_dict=False)\n", "        mergeDF = mergeDF[['datetime', 'lei_a', 'fen', 'group_id', 'device_id', 'product_id', 'product_qty']]\n", "    return mergeDF\n", "\n", "\n", "def calOne():\n", "    df = get_color_insert_tb()\n", "\n", "    # Filter out duplicates for lei_a = 22\n", "    mask = (df['lei_a'] == 22) & (df.duplicated(subset=['group_id', 'product_qty'], keep=False))\n", "    filtered_df = df[~mask]\n", "\n", "    df = filtered_df.sort_values(by=['datetime', 'group_id'], ascending=[False, False])\n", "\n", "    # Get the multiplier for each group_id where lei_a is 31\n", "    multiplier = df[df['lei_a'] == 31].set_index('group_id')['product_qty']\n", "    # Create a new column for the multiplied values\n", "    df['product_qty_group'] = df.apply(lambda row: row['product_qty'] * multiplier.get(row['group_id'], 1) if row['lei_a'] == 22 else row['product_qty'], axis=1)\n", "\n", "\n", "    # df['total_qty'] = df.groupby(['device_id', 'product_id'])['product_qty'].transform('sum')\n", "\n", "    \n", "    return df\n", "\n", "\n", "def calTwo():\n", "    df = calOne()\n", "\n", "    # Sum product_qty_group for each group_id and create a new column\n", "    # df['sum_qty_per_product'] = df.groupby('product_id')['product_qty_group'].transform('sum')\n", "    df['sum_qty_per_product'] = df.groupby(['device_id', 'product_id'])['product_qty_group'].transform('sum')\n", "\n", "    return df\n", "\n", "\n", "def calThree():\n", "    df1 = calTwo()\n", "    df2 = get_color_insert_oa_tb()\n", "\n", "    result = pd.concat([df1, df2], ignore_index=True)\n", "\n", "    return result\n", "\n", "# display(get_color_insert_tb())\n", "display(calThree())\n", "display(calOne())\n", "display(calTwo())\n", "# display(get_color_insert_oa_tb())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}