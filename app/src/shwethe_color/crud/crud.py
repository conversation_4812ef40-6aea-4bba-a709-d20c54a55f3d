from sqlmodel import Session, select
from typing import List, Dict
from sqlalchemy import func,and_, bindparam,desc, distinct,or_,cast, String,func, text, inspect
from sqlalchemy.dialects.postgresql import JSONB
import uuid
from datetime import datetime
# from src.shwethe_color.models import (
# jia_yi_fang,product,find_new_goods,name_storage_tb,product_price_tb)
from src.shwethe_color.models.model import color_device_tb, type_device_tb, color_brand_tb, color_prefix_idname, color_base_tb, pei_liao_oa_tb, color_cai_liao_tb, color_insert_tb, color_insert_oa_tb, code_color_tb
from src.shwethe_color.schemas.schemas import (
    color_cai_liao_tb_post,
    color_insert_tb_post
    # jia_yi_fangCreate, jia_yi_fangUpdate,
    # jia_yi_fang as jia_yi_fangSchema,   
    # Product as ProductSchema,  
    # ProductCreate ,
    # ProductUpdate,
    # findNewGoodsPost,
    # nameStorageTbPost,
    # post_product_category_tb_item
)
from fastapi import HTTPException, status
from src.config.shwethe_color.database import get_session
from fastapi import APIRouter, Depends, Query, Body

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session, Load, defer
import requests
import pandas as pd
from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api
import json
import random
from sqlalchemy import update
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy.exc import SQLAlchemyError
from helper import generate_id
import datetime as DT


def random_groupID():
    import datetime
    import random

    # Get today's date in YYYYMMDD format
    today_date = datetime.datetime.now().strftime("%Y%m%d")

    # Generate a random 6-digit number
    random_number = random.randint(1000, 9999)

    # Combine the date, dead number, and random number
    result = f"{today_date}000{random_number}"

    return result


def getCarAtive100(db: Session = Depends(get_session)):
    # try:
    #     heroesPersonal = db.exec(select(item_insert)).all()
    #     records = [i.dict() for i in heroesPersonal]   
    #     df = pd.DataFrame.from_records(records).fillna(0)

    #     mergeDF = df.to_dict("records")
    # except:
    #     mergeDF = []
    return "mergeDF"


def get_type_prefix100(ID: int, db: Session = Depends(get_session)):

    # url = f"http://192.168.1.11:8200/mongodb_data_api/api/v1/search/product_id?ID={ID}"
    # http://192.168.1.11:8200/mongodb_data_api/api/v1/search/jia_yi_name_id?ID=49019
    # url = f"{mongodb_data_api}/api/v1/search/product_id?ID={ID}"
    url = f"{mongodb_data_api}/api/v1/search/jia_yi_name_id?ID={ID}"

    response = requests.get(url)
    a1 = response.json()


    a2 = db.query(color_device_tb).filter(color_device_tb.device_id == ID).first()
    print(a2.type_device_id)


    a3 = db.query(type_device_tb).filter(type_device_tb.auto_id == a2.type_device_id).first()
    print(a3.brand_id)

    a4 = db.query(color_brand_tb).filter(color_brand_tb.color_brand_id == a3.brand_id).first()
    # print(a4.brand_id)

    a5 = db.query(color_prefix_idname).filter(color_prefix_idname.brand_id == a4.color_brand_id).all()
    a5 = pd.DataFrame.from_records([item.dict() for item in a5])
    a5 = a5.rename(columns={
        'prefix_idname': 'name',
    })
    a5['name'] = a5['name'] + ' | ' + a5['prefix_details']
    # print(a5)

    list_detail = {"device": a1, "prefix": a5.to_dict("records")}

    return list_detail


def get_base_color100(brand_id: int, db: Session = Depends(get_session)):

    a1 = db.query(color_base_tb).filter(color_base_tb.brand_id == brand_id).all()

    result_list = []

    for item in a1:
        base_id = item.base_id
        # url = f"http://192.168.1.11:8200/mongodb_data_api/api/v1/search/product_id?ID={base_id}"
        url = f"{mongodb_data_api}/api/v1/search/product_id?ID={base_id}"

        response = requests.get(url)
        result = response.json()
        result_list.extend(result)

    return result_list


def search_base_color100(brand_id: int, base_idname: str, db: Session = Depends(get_session)):

    # Convert base_idname to uppercase
    base_idname_upper = base_idname.upper()

    url = f"{mongodb_data_api}/api/v2/search/product_idname?ID={base_idname_upper}"
    response = requests.get(url)
    result = response.json()
    print(result[0]['product_id'])

    a1 = db.query(color_base_tb).filter(color_base_tb.brand_id == brand_id, color_base_tb.base_id == result[0]['product_id']).all()
    if not a1:
        return []

    return result


def get_machine_color100(brand_id: int, db: Session = Depends(get_session)):

    a1 = db.query(type_device_tb).filter(type_device_tb.brand_id == brand_id).first()


    a2 = db.query(pei_liao_oa_tb).filter(pei_liao_oa_tb.type_device_id == a1.auto_id).all()
    a22 = pd.DataFrame.from_records([item.dict() for item in a2])


    result_df = pd.DataFrame()
    for item in a2:
        product_id_a = item.product_id_a
        # url = f"http://192.168.1.11:8200/mongodb_data_api/api/v1/search/product_id?ID={product_id_a}"
        url = f"{mongodb_data_api}/api/v1/search/product_id?ID={product_id_a}"

        response = requests.get(url)
        result = response.json()

        result_df = result_df.append(pd.DataFrame(result))


    merged_df = pd.merge(a22, result_df, left_on='product_id_a', right_on='product_id')
    merged_df = merged_df.to_dict("records")

    return merged_df


# def get_price100(db: Session = Depends(get_session)):

#     list_data = [{"product_id_a": 57497}, {"product_id_a": 57499}]

#     # url = f"http://192.168.1.11:8200/mongodb_data_api/api/v2/search/price?ID=37404"
#     url = f"{mongodb_data_api}/api/v2/search/price?ID=product_id_a"
#     response = requests.get(url)
#     result = response.json()
#     result_list.append({"product_id_a": item['cai_liao_id'], "price": result})

#     return "sdcsdcsdcsdc"
def get_price100(list_data: List[Dict], db: Session = Depends(get_session)):

    # list_data = [{"product_id_a": 57497}, {"product_id_a": 57499}]
    result_list = []

    # Assuming 'mongodb_data_api' is a defined variable with the base URL
    base_url = mongodb_data_api + "/api/v2/search/price?ID="

    for item in list_data:
        product_id = item['product_id_a']
        url = base_url + str(product_id)
        response = requests.get(url)
        result = response.json()
        result_list.append({"product_id_a": product_id, "price": result})

    return result_list



def get_result_color100(product_idname: str, db: Session = Depends(get_session)):

    # url = f"http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_idname?ID={product_idname}"
    url = f"{mongodb_data_api}/api/v2/search/product_idname?ID={product_idname}"

    response = requests.get(url)
    a1 = response.json()

    return a1


def insert_mix_color100(list_data: Dict, db: Session = Depends(get_session)):

    color_base_id = list_data.get('color_base_id', [])
    print(color_base_id)


    data = list_data.get('data', [])
    print(data)


    per1_cai_liao_ids = [row['cai_liao_id'] for row in data if row.get('qty_lei') == 'per1']
    print(per1_cai_liao_ids)


    # a1 = db.query(color_cai_liao_tb).filter(color_cai_liao_tb.color_base_id == color_base_id).first()
    a1 = db.query(color_cai_liao_tb).filter(
        color_cai_liao_tb.status == 'correct',
        color_cai_liao_tb.cai_liao_id == per1_cai_liao_ids[0],
        color_cai_liao_tb.color_base_id == color_base_id
    ).first()


    if a1:
        print("Have value")
    else:
        print("No value")
        # list_data = [
        #     {'cai_liao_id': 10, 'cai_liao_qty': "58.3", 'qty_lei': "jjjj", 'color_base_id': 7855, 'detail_json': {"aaa": 10}, 'status': "aaaa"},
        #     {'cai_liao_id': 22, 'cai_liao_qty': "5563", 'qty_lei': "AAAAAA", 'color_base_id': 3434, 'detail_json': {"aaa": 10}, 'status': "aaaa"}
        #     # {'cai_liao_id': 22, 'cai_liao_qty': "5563", 'qty_lei': "AAAAAA", 'color_base_id': 3434}
        # ]
        # print(list_data)

        
        # Create a new variable new_list based on the structure you want
        new_list = {
            "data": [],
        }

        random_group_id = "COLOR" + generate_id()
        # random_group_id = random_groupID()
        # Iterate over the original list_data["data"]
        for i, item in enumerate(list_data.get("data", [])):
            # Create a new dictionary for each item
            new_item = {
                "cai_liao_id": item.get("cai_liao_id"),
                "cai_liao_qty": item.get("cai_liao_qty"),
                "qty_lei": item.get("qty_lei"),
                "color_base_id": item.get("color_base_id"),
                "status": "checking" if i == 0 else "None",
                "group_id": random_group_id
            }

            # Add detail_json to the first row only
            if i == 0:
                new_item["detail_json"] = list_data.get("detail_json", {})

            # Append the new item to new_list["data"]
            new_list["data"].append(new_item)

        print(new_list["data"][0]['detail_json']['form']['get_brand_id'])
        print(new_list["data"][0]['detail_json']['form']['value_result_color'])

        # INSERT TO color_cai_liao_tb
        data_list = new_list.get('data', [])
        for data in data_list:
            color_cai_liao_tb_fang = color_cai_liao_tb(**data)
            db.add(color_cai_liao_tb_fang)
        
        # INSERT TO code_color_tb
        data_sub = {"color_base_id": new_list["data"][0]['detail_json']['form']['value_result_color'], "brand_id": new_list["data"][0]['detail_json']['form']['get_brand_id']}
        code_color_tb_fang = code_color_tb(**data_sub)
        db.add(code_color_tb_fang)

        # Commit the transaction
        db.commit()
        # Refresh the instances to reflect committed changes
        db.refresh(color_cai_liao_tb_fang)
        db.refresh(code_color_tb_fang)

    return "new_jia_yi_fang"


def check_cai_liao100(group_id: str, cai_liao_id: int, db: Session = Depends(get_session)):

    a1 = (
        db.query(color_cai_liao_tb.cai_liao_id, color_cai_liao_tb.qty_lei, color_cai_liao_tb.status, color_cai_liao_tb.check_json) 
        .filter(color_cai_liao_tb.group_id == group_id, color_cai_liao_tb.cai_liao_id == cai_liao_id, color_cai_liao_tb.status == 'correct')
        .first()
    )

    # Convert the result to a dictionary
    data = {
        "cai_liao_id": a1.cai_liao_id,
        "qty_lei": a1.qty_lei,
        "status": a1.status,
        "check_json": a1.check_json
    }
    
    # Convert the dictionary to a pandas DataFrame
    df = pd.DataFrame([data])
    
    # Define a function to extract 'count_check' from 'check_json'
    def get_fill_status(check_json):
        if check_json is None:
            return 0  # If 'check_json' is null, set 'fill_status' to 0
        if isinstance(check_json, dict):  # Check if 'check_json' is already a dictionary
            return check_json.get("count_check", 0)  # Extract 'count_check' or default to 0
        return 0  # Handle other cases (e.g., invalid data)
    
    # Apply the function to create the 'fill_status' column
    df['fill_status'] = df['check_json'].apply(get_fill_status)
    
    
    return df.to_dict(orient='records')


def get_cai_liao100(product_idname: str, base_value: int, db: Session = Depends(get_session)):

    print(product_idname, base_value)

    # url = f"http://192.168.1.11:8200/mongodb_data_api/api/v1/search/product_id?ID={ID}"
    url = f"{mongodb_data_api}/api/v2/search/product_idname?ID={product_idname}"
    response = requests.get(url)
    a1 = response.json()
    print(a1, "aaaaaaaa")


    # # Assuming you want to exclude the 'column_to_exclude' column
    columns_to_exclude = ['detail_json']


    a2 = (
        db.query(color_cai_liao_tb)
        .filter(color_cai_liao_tb.color_base_id == a1[0]['product_id'], color_cai_liao_tb.cai_liao_id == base_value, color_cai_liao_tb.status == 'correct')
        .options(*[defer(column) for column in columns_to_exclude])
        .first()
    )
    print(a2, "llllllllllllllllllllllll")

    
    b2 = (
        db.query(color_cai_liao_tb)
        .filter(color_cai_liao_tb.group_id == a2.group_id)
        .options(*[defer(column) for column in columns_to_exclude])
        .all()
    )
    # print(b2)
    b22 = pd.DataFrame.from_records([item.dict() for item in b2])


    # Extract cai_liao_id values from a2
    cai_liao_ids = [item.cai_liao_id for item in b2]
    a3 = db.query(pei_liao_oa_tb).filter(pei_liao_oa_tb.product_id_b.in_(cai_liao_ids)).all()
    a33 = pd.DataFrame.from_records([item.dict() for item in a3])


    a4 = pd.merge(b22, a33, how="outer", left_on='cai_liao_id', right_on='product_id_b').fillna(0)


    result_df = pd.DataFrame()
    for _, item in a4.iterrows():
        product_id_a = item['cai_liao_id']
        # url = f"http://192.168.1.11:8200/mongodb_data_api/api/v1/search/product_id?ID={product_id_a}"
        url = f"{mongodb_data_api}/api/v1/search/product_id?ID={product_id_a}"

        response = requests.get(url)
        result = response.json()

        result_df = result_df.append(pd.DataFrame(result))

    a5 = pd.merge(a4, result_df, left_on='cai_liao_id', right_on='product_id')


    result_df2 = pd.DataFrame()
    result_list = []
    for _, item in a4.iterrows():
        product_id_a = round(item['product_id_a'])
        
        if product_id_a == 0:
            # url = f"http://192.168.1.11:8200/mongodb_data_api/api/v2/search/price?ID=37404"
            url = f"{mongodb_data_api}/api/v2/search/price?ID={item['cai_liao_id']}"
            response = requests.get(url)
            result = response.json()
            result_list.append({"product_id_a": item['cai_liao_id'], "price": result})

        if product_id_a != 0:
            # url = f"http://192.168.1.11:8200/mongodb_data_api/api/v2/search/price?ID=37404"
            url = f"{mongodb_data_api}/api/v2/search/price?ID={product_id_a}"
            response = requests.get(url)
            result = response.json()
            result_list.append({"product_id_a": product_id_a, "price": result})
            
    print(result_list)
    result_df = pd.DataFrame(result_list)

    a6 = pd.merge(a5, result_df, how="outer", left_on='product_id_a', right_on='product_id_a').fillna(0)

    # Function to update the 'price' column
    def update_price(row):
        if row['price'] == 0:
            # If 'price' is 0, update it with 'price' from 'product_id_a'
            return a6.loc[a6['product_id'] == row['product_id_a'], 'price'].iloc[0]
        else:
            # Otherwise, keep the existing 'price'
            return row['price']
    # Apply the function to update the 'price' column
    a6['price'] = a6.apply(update_price, axis=1)
    # Filter out rows with 'cai_liao_qty' greater than 0
    a6 = a6[a6['cai_liao_qty'] > 0]
    a6['price_per_Ml'] = (a6['price'] / 1000) * a6['cai_liao_qty']
    a6['price_per_M2'] = (a6['price'] / 1000)
    # Calculate the sum of prices based on the conditions
    # sum_price = a6[a6['cai_liao_qty'] == 1]['price'].values[0] + a6[a6['cai_liao_qty'] != 1]['price_per_Ml'].sum()
    sum_price = a6[a6['qty_lei'] == 'per1']['price'].values[0] + a6[a6['qty_lei'] == 'lte']['price_per_Ml'].sum()
    # Add the 'sum_price' column to the DataFrame
    a6['sum_price'] = sum_price
    # Set 'price_per_Ml' equal to 'price' when "qty_lei": "per1"
    a6.loc[a6['qty_lei'] == 'per1', 'price_per_Ml'] = a6.loc[a6['qty_lei'] == 'per1', 'price']
    a6['price_per_Ml'] = a6['price_per_Ml'].round(2)
    a6['sum_price'] = a6['sum_price'].round(2)
    mergeDF = a6.to_dict("records")
    
    return mergeDF



def check_color_insert_tb100(list_data: List[Dict], group_id: str, db: Session = Depends(get_session)):
    print(group_id)
    print(list_data)

    # Filter out items in list_data that do not have the 'qty_lei' column
    filtered_list_data = [item for item in list_data if 'qty_lei' in item]

    print("Filtered list_data (only items with 'qty_lei'):")
    print(filtered_list_data)

    # Query the database to get the relevant records
    a1 = (
        db.query(color_cai_liao_tb)
        .filter(color_cai_liao_tb.group_id == group_id)
        .all()
    )

    print(a1)

    # Create a dictionary from a1 for easy lookup
    a1_dict = {item.cai_liao_id: item.cai_liao_qty for item in a1}

    # Flag to track if all items match
    all_match = True

    # Iterate through filtered_list_data and compare with a1
    for item in filtered_list_data:
        product_id = item.get('product_id')
        product_qty = item.get('product_qty')

        if product_id in a1_dict:
            # Convert both values to float for comparison
            product_qty_float = float(product_qty)  # Ensure product_qty is a float
            cai_liao_qty_float = float(a1_dict[product_id])  # Ensure cai_liao_qty is a float

            if product_qty_float == cai_liao_qty_float:
                print(f"Match found for product_id {product_id}: product_qty {product_qty} matches cai_liao_qty {a1_dict[product_id]}")
            else:
                print(f"Mismatch found for product_id {product_id}: product_qty {product_qty} does not match cai_liao_qty {a1_dict[product_id]}")
                all_match = False  # Set flag to False if any mismatch is found
        else:
            print(f"No matching cai_liao_id found for product_id {product_id}")
            all_match = False  # Set flag to False if any product_id is not found in a1_dict

    # Return 'match' if all items match, otherwise 'not match'
    return 'match' if all_match else 'not match'


    
# def insert_color_insert_tb100(list_data: List[Dict], db: Session = Depends(get_session)):

#     # list_data = [
#     #     {'jia_yi_fang_a': 5563, 'jia_yi_fang_b': 5563, 'lei_a': 3434, 'lei_b': 3434, 'device_id': 3434, 'product_id': 3434, 'product_qty': 3434, 'product_price': 3434, 'bi_zhi': 3434, 'fen': 3434},
#     #     {'group_id': "22", 'jia_yi_fang_a': 5563, 'jia_yi_fang_b': 5563, 'lei_a': 3434, 'lei_b': 3434, 'device_id': 3434, 'product_id': 3434, 'product_qty': 3434, 'product_price': 3434, 'bi_zhi': 3434, 'fen': 3434}
#     # ]
#     # print(list_data)

#     # Generate a random group_id
#     # random_group_id = str(uuid.uuid4().hex)[0:8]  # Adjust the range as needed
#     random_group_id = "COLOR" + generate_id()
#     # random_group_id = random_groupID()
    
#     # Use a flag to check if detail_json has been set for the group
#     detail_json_set = False

#     product_qty_for_lei_a_31 = next((item['product_qty'] for item in list_data if item.get('lei_a') == 31), None)
#     print(product_qty_for_lei_a_31, "oooooooooooooooooooooooooooooooooooooooooooooooooooo")

#     for data in list_data:

#         # Assign the random group_id to each dictionary
#         data['group_id'] = random_group_id

#         # If product_qty is 1, replace it with the product_qty value from the entry where lei_a is 31
#         if data.get('product_qty') == 1 and data.get('qty_lei') == "per1":
#             data['product_qty'] = product_qty_for_lei_a_31 if product_qty_for_lei_a_31 is not None else data['product_qty']
#             detailGot = data['detail_json']
#         print(data['product_qty'], data['lei_a'])

#         new_jia_yi_fang = color_insert_tb(**data)

#         # Check if product_qty is equal to -1, then set detail_json only once
#         if data.get('lei_a') == 31 and not detail_json_set:
#             # print("aaaaaaaaaaaaaaaaaaaaaaaaaa", detailGot)
#             new_jia_yi_fang.detail_json = detailGot
#             new_jia_yi_fang.status = {"status_customer": "waiting", "datetime_customer": datetime.now().isoformat(), "status_big_table": "waiting", "datetime_big_table": datetime.now().isoformat()}
#             detail_json_set = True
#         else:
#             new_jia_yi_fang.detail_json = {}
#             new_jia_yi_fang.status = {}

#         db.add(new_jia_yi_fang)
    
#     db.commit()
#     db.refresh(new_jia_yi_fang)

#     return "new_jia_yi_fang"
# def insert_color_insert_tb100(list_data: List[Dict], db: Session = Depends(get_session)):
#     try:
#         # Start a transaction
#         db.begin()

#         # list_data = [
#         #     {'jia_yi_fang_a': 5563, 'jia_yi_fang_b': 5563, 'lei_a': 3434, 'lei_b': 3434, 'device_id': 3434, 'product_id': 3434, 'product_qty': 3434, 'product_price': 3434, 'bi_zhi': 3434, 'fen': 3434},
#         #     {'group_id': "22", 'jia_yi_fang_a': 5563, 'jia_yi_fang_b': 5563, 'lei_a': 3434, 'lei_b': 3434, 'device_id': 3434, 'product_id': 3434, 'product_qty': 3434, 'product_price': 3434, 'bi_zhi': 3434, 'fen': 3434}
#         # ]
#         # print(list_data)

#         # Generate a random group_id
#         # random_group_id = str(uuid.uuid4().hex)[0:8]  # Adjust the range as needed
#         random_group_id = "COLOR" + generate_id()
#         # random_group_id = random_groupID()
        
#         # Use a flag to check if detail_json has been set for the group
#         detail_json_set = False

#         product_qty_for_lei_a_31 = next((item['product_qty'] for item in list_data if item.get('lei_a') == 31), None)
#         print(product_qty_for_lei_a_31, "oooooooooooooooooooooooooooooooooooooooooooooooooooo")

#         for data in list_data:

#             # Assign the random group_id to each dictionary
#             data['group_id'] = random_group_id

#             # If product_qty is 1, replace it with the product_qty value from the entry where lei_a is 31
#             if data.get('product_qty') == 1 and data.get('qty_lei') == "per1":
#                 data['product_qty'] = product_qty_for_lei_a_31 if product_qty_for_lei_a_31 is not None else data['product_qty']
#                 detailGot = data['detail_json']

#                 # print("aaaaaaaaaa", data['detail_json']['form']['mix_list'])

#                 # Loop through each item in the mix_list
#                 for item in data['detail_json']['form']['mix_list']:
#                     # Check if the 'qty_lei' key exists and is equal to "per1"
#                     if item.get('qty_lei') == "per1":
#                         # Extract group_id and cai_liao_id from the item
#                         group_id = item.get('group_id')
#                         cai_liao_id = item.get('cai_liao_id')

#                         # Print or store the values in new variables
#                         print("Group ID:", group_id)
#                         print("Cai Liao ID:", cai_liao_id)

#                         # You can also store them in new variables if needed
#                         new_group_id = group_id
#                         new_cai_liao_id = cai_liao_id

#             print("vvvvvvvvv", data['product_qty'], data['lei_a'])
            

#             new_jia_yi_fang = color_insert_tb(**data)

#             # Check if product_qty is equal to -1, then set detail_json only once
#             if data.get('lei_a') == 31 and not detail_json_set:
#                 print("dfdfdf", new_group_id, new_cai_liao_id)

#                 # Query the database to find the matching record
#                 result = db.query(color_cai_liao_tb).filter(
#                     color_cai_liao_tb.group_id == new_group_id,
#                     color_cai_liao_tb.cai_liao_id == new_cai_liao_id
#                 ).first()

#                 # If the record is found, update the check_json field based on its current value
#                 if result:
#                     # Get the current value of check_json
#                     current_check_json = result.check_json

#                     # Define the new check_json value based on the current value
#                     if current_check_json is None:
#                         new_check_json = {"count_check": 1}
#                     if current_check_json == {}:
#                         new_check_json = {"count_check": 1}
#                     elif current_check_json == {"count_check": 1}:
#                         new_check_json = {"count_check": 2}
#                     # elif current_check_json == {"count_check": 2}:
#                     #     new_check_json = {"count_check": 3}
#                     # elif current_check_json == {"count_check": 3}:
#                     #     new_check_json = {"count_check": 4}
#                     # elif current_check_json == {"count_check": 4}:
#                     #     new_check_json = {"count_check": 5}
#                     else:
#                         # Handle unexpected values (optional)
#                         new_check_json = current_check_json  # Keep the current value or handle as needed

#                     # Update the check_json field
#                     result.check_json = new_check_json

#                     # Commit the changes to the database
#                     db.add(result)


#                 print("aaaaaaaaaaaaaaaaaaaaaaaaaa", detailGot)
#                 new_jia_yi_fang.detail_json = detailGot
#                 new_jia_yi_fang.status = {"status_customer": "waiting", "datetime_customer": datetime.now().isoformat(), "status_big_table": "waiting", "datetime_big_table": datetime.now().isoformat()}
#                 detail_json_set = True
#             else:
#                 new_jia_yi_fang.detail_json = {}
#                 new_jia_yi_fang.status = {}
#                 pass

#             db.add(new_jia_yi_fang)
        
#         db.commit()

#         return "new_jia_yi_fang"
    
#     except SQLAlchemyError as e:
#         # If an error occurs, rollback the transaction
#         db.rollback()
#         raise e  # Re-raise the exception to propagate it
def insert_color_insert_tb100(list_data: List[Dict], db: Session = Depends(get_session)):
    try:
        # Generate a random group_id
        random_group_id = "COLOR" + generate_id()

        # Use a flag to check if detail_json has been set for the group
        detail_json_set = False

        # Find product_qty for lei_a == 31
        product_qty_for_lei_a_31 = next((item['product_qty'] for item in list_data if item.get('lei_a') == 31), None)
        print(product_qty_for_lei_a_31, "Product Qty for lei_a == 31")

        for data in list_data:
            # Assign the random group_id to each dictionary
            data['group_id'] = random_group_id

            # If product_qty is 1, replace it with the product_qty value from the entry where lei_a is 31
            if data.get('product_qty') == 1 and data.get('qty_lei') == "per1":
                data['product_qty'] = product_qty_for_lei_a_31 if product_qty_for_lei_a_31 is not None else data['product_qty']
                detailGot = data['detail_json']

                # Loop through each item in the mix_list
                for item in data['detail_json']['form']['mix_list']:
                    if item.get('qty_lei') == "per1":
                        group_id = item.get('group_id')
                        cai_liao_id = item.get('cai_liao_id')
                        print("Group ID:", group_id)
                        print("Cai Liao ID:", cai_liao_id)

            # Insert into color_insert_tb
            new_jia_yi_fang = color_insert_tb(**data)

            # Update color_cai_liao_tb if lei_a == 31 and detail_json_set is False
            if data.get('lei_a') == 31 and not detail_json_set:
                # Query the database to find the matching record
                result = db.query(color_cai_liao_tb).filter(
                    color_cai_liao_tb.group_id == group_id,
                    color_cai_liao_tb.cai_liao_id == cai_liao_id
                ).first()

                if result:
                    # Update check_json based on its current value
                    current_check_json = result.check_json

                    if current_check_json is None or current_check_json == {}:
                        new_check_json = {"count_check": 1}
                    elif current_check_json == {"count_check": 1}:
                        new_check_json = {"count_check": 2}
                    else:
                        new_check_json = current_check_json  # Keep the current value

                    result.check_json = new_check_json
                    print("Updated check_json:", new_check_json)

                # Set detail_json and status for the new_jia_yi_fang record
                new_jia_yi_fang.detail_json = detailGot
                new_jia_yi_fang.status = {
                    "status_customer": "waiting",
                    "datetime_customer": datetime.now().isoformat(),
                    "status_big_table": "waiting",
                    "datetime_big_table": datetime.now().isoformat()
                }
                detail_json_set = True
            else:
                new_jia_yi_fang.detail_json = {}
                new_jia_yi_fang.status = {}

            db.add(new_jia_yi_fang)

        # Commit the transaction
        db.commit()

        return "new_jia_yi_fang"

    except SQLAlchemyError as e:
        # Rollback the transaction in case of error
        db.rollback()
        raise e  # Re-raise the exception to propagate it



# def get_all_color_cai_liao100(page: int = 1, per_page: int = 2, db: Session = Depends(get_session)):

#     offset = (page - 1) * per_page

#     a1 = (
#         db.query(color_cai_liao_tb)
#         .filter(color_cai_liao_tb.qty_lei == 'per1')
#         .order_by(color_cai_liao_tb.datetime.desc())
#         .offset(offset)
#         .limit(per_page)
#         # .options(*[defer(column) for column in columns_to_exclude])
#         .all()
#     )

#     return a1
def get_all_color_cai_liao100(days_ago: int, page: int = 1, per_page: int = 2, db: Session = Depends(get_session)):

    if page == 0 and per_page == 0:
        return []

    if page == 1000 and per_page == 1000:
        
        # Fetch the maximum datetime value from the table
        max_date_query = select(func.max(color_cai_liao_tb.datetime))
        max_date_result = db.execute(max_date_query).scalar()
        if max_date_result:
            max_date = max_date_result.date()
            two_days_ago = max_date - DT.timedelta(days=days_ago)
        else:
            return {"error": "No data available in the table"}

        a1 = (
            db.query(color_cai_liao_tb)
            .filter(color_cai_liao_tb.qty_lei == 'per1', color_cai_liao_tb.datetime >= two_days_ago)
            .order_by(color_cai_liao_tb.datetime.desc())
            # .options(*[defer(column) for column in columns_to_exclude])
            .all()
        )
        return a1

    offset = (page - 1) * per_page
    a1 = (
        db.query(color_cai_liao_tb)
        .filter(color_cai_liao_tb.qty_lei == 'per1')
        .order_by(color_cai_liao_tb.datetime.desc())
        .offset(offset)
        .limit(per_page)
        # .options(*[defer(column) for column in columns_to_exclude])
        .all()
    )

    return a1


def search_color_cai_liao100(search_term: str, db: Session = Depends(get_session)):

    # Check if search_term is None or empty, return None or an empty list
    if not search_term:
        return []

    search_lower = f'%{search_term.lower()}%'
    # Define a common table expression (CTE) for the expanded mix_list
    cte_mix_list = (
        select([
            color_cai_liao_tb.auto_id.label("auto_id"),
            func.jsonb_array_elements_text(color_cai_liao_tb.detail_json.op('->')('form').op('->')('mix_list')).label("mix_element")
        ])
        .where(color_cai_liao_tb.qty_lei == 'per1')
        .cte("cte_mix_list")
    )
    # Main query
    statement = (
        select([color_cai_liao_tb])
        .join(cte_mix_list, color_cai_liao_tb.auto_id == cte_mix_list.c.auto_id)
        .where(
            or_(
                func.lower(cte_mix_list.c.mix_element).like(search_lower),
                cast(color_cai_liao_tb.datetime, String).ilike(search_lower),
                cast(color_cai_liao_tb.detail_json['form']['base_text'], String).ilike(search_lower),
                cast(color_cai_liao_tb.detail_json['form']['input_result_colorID'], String).ilike(search_lower),
                color_cai_liao_tb.status.ilike(search_lower),
            )
        )
        .where(color_cai_liao_tb.qty_lei == 'per1')
        .distinct()
        .order_by(color_cai_liao_tb.datetime.desc())
    )

    a1 = db.exec(statement).all()


    return a1


def update_status_color_cai_liao100(auto_id: int, status: str, db: Session = Depends(get_session)):

    result = db.query(color_cai_liao_tb).filter(color_cai_liao_tb.auto_id == auto_id).first()
    
    if result:

        result.status = status
        db.add(result)
        db.commit()
        db.refresh(result)
        
        return result

    return result


def get_customer100(name: str, db: Session = Depends(get_session)):

    # print(name)
    # print(type(name))

    try:
        name = int(name)
        print(name)
        print(type(name))

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        # url = Arter_api + 'jia_yi_name_list_id'
        # body_raw = {"data_api": [{"jia_yi_id": 36557}]}
        body_raw = {"data_api": [{"jia_yi_id": name}]}
        # body_raw = {"data_api": df}
        df2 = requests.get(url=url, json=body_raw)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')
        # print(df2)
    except:
        print('The provided value is not an integer')
        print(name)
        print(type(name))

        # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={name}'
        url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={name}'
        df2 = requests.get(url=url)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')
        # print(df2)

    return df2




def get_all_color_insert100(fen: int, days_ago: int, page: int = 1, per_page: int = 2, db: Session = Depends(get_session)):

    today = DT.date.today()
    week_ago = today - DT.timedelta(days=2)
    week_ago_2 = today - DT.timedelta(days=2)
    print(week_ago)

    offset = (page - 1) * per_page

    if fen == 0:

        # Fetch the maximum datetime value from the table
        max_date_query = select(func.max(color_insert_tb.datetime))
        max_date_result = db.execute(max_date_query).scalar()
        if max_date_result:
            max_date = max_date_result.date()
            two_days_ago = max_date - DT.timedelta(days=days_ago)
        else:
            return {"error": "No data available in the table"}
            
        # Inspect the table to get all columns
        inspector = inspect(color_insert_tb)
        all_columns = inspector.columns.keys()

        # Exclude the 'detail_json', 'jia_yi_fang_a', and 'jia_yi_fang_b' columns
        columns_to_select = [col for col in all_columns if col not in ['jia_yi_fang_a', 'jia_yi_fang_b', 'lei_a', 'lei_b', 'bi_zhi', 'unit_price_sum', 'product_qty_sum', 'sum_price', 'unit_price', 'color_price']]

        # Build the query with all columns except 'detail_json', 'jia_yi_fang_a', and 'jia_yi_fang_b' and add new columns
        query = (
            select(
                [getattr(color_insert_tb, col) for col in columns_to_select]
            )
            .where(color_insert_tb.lei_a == 31, color_insert_tb.datetime >= two_days_ago)
            .order_by(color_insert_tb.datetime.desc())
        )

        # Execute the query
        a1 = db.execute(query).all()
  
        # a1 = (
        #     db.query(color_insert_tb)
        #     .filter(color_insert_tb.lei_a == 31, color_insert_tb.detail_json != {})
        #     .where(color_insert_tb.datetime > week_ago)
        #     .order_by(color_insert_tb.datetime.desc())
        #     # .options(*[defer(column) for column in columns_to_exclude])
        #     .all()
        # )
        # return a1

        # # Convert the SQL query result to a pandas DataFrame
        # sql_df = pd.DataFrame(a1, columns=columns_to_select)

        # # Extract product_id from the SQL query result and convert to native int
        # product_ids = sql_df['product_id'].unique().tolist()

        # # Prepare the request body for the MongoDB API
        # body = {
        #     "data_api": [{"product_id": pid} for pid in product_ids]
        # }
        # print(body)

        # # Make a single request to the MongoDB API
        # url = f'{mongodb_data_api}/api/v1/search/qty_list'
        # response = requests.post(url=url, json=body)
        # api_data = response.json()

        # # Convert the MongoDB API response to a pandas DataFrame
        # mongo_df = pd.DataFrame(api_data)

        # # Merge the two DataFrames on product_id
        # combined_df = pd.merge(sql_df, mongo_df, on=['product_id', 'fen'], how='left')

        # # Convert the combined DataFrame to a list of dictionaries for the response
        # combined_data = combined_df.fillna(0).to_dict(orient='records')

        return a1

    else:
        a1 = (
            db.query(color_insert_tb)
            .filter(color_insert_tb.fen == fen, color_insert_tb.lei_a == 31, color_insert_tb.detail_json != {})
            # .where(color_insert_tb.datetime > week_ago_2)
            .order_by(color_insert_tb.datetime.desc())
            .offset(offset)
            .limit(per_page)
            # .options(*[defer(column) for column in columns_to_exclude])
            .all()
        )
        return a1


def get_color_not_sell100(days_ago: int, db: Session = Depends(get_session)):

    try:
        # Fetch the maximum datetime value from the table
        max_date_query = select(func.max(color_insert_tb.datetime))
        max_date_result = db.execute(max_date_query).scalar()
        if max_date_result:
            max_date = max_date_result.date()
            two_days_ago = max_date - DT.timedelta(days=days_ago)
        else:
            return {"error": "No data available in the table"}
        
        # Execute the SQL query
        heroesPersonal = db.exec(
            select(
                color_insert_tb.datetime, color_insert_tb.product_id, color_insert_tb.fen
            )
            .where(
                color_insert_tb.datetime >= two_days_ago,
                color_insert_tb.lei_a == 31
            )
            .order_by(color_insert_tb.product_id, color_insert_tb.datetime.desc())
            .distinct(color_insert_tb.product_id)
        ).all()
        
        # Convert the result to a DataFrame
        sql_df = pd.DataFrame(heroesPersonal, columns=['datetime', 'product_id', 'fen'])

        # Extract product_id from the SQL query result and convert to native int
        product_ids = sql_df['product_id'].unique().tolist()

        # Prepare the request body for the MongoDB API
        body = {
            "data_api": [{"product_id": pid} for pid in product_ids]
        }
        print(body)

        # Make a single request to the MongoDB API
        url = f'{mongodb_data_api}/api/v1/search/qty_list'
        response = requests.post(url=url, json=body)
        api_data = response.json()

        # Convert the MongoDB API response to a pandas DataFrame
        mongo_df = pd.DataFrame(api_data)

        # Merge the two DataFrames on product_id
        combined_df = pd.merge(sql_df, mongo_df, on=['product_id'], how='left').fillna(0)
        print(combined_df)

        # Filter the combined DataFrame to include only rows where product_qty is not equal to zero
        filtered_df = combined_df[combined_df['product_qty'] != 0]

        # Convert the filtered DataFrame to a list of dictionaries for the response
        combined_data = filtered_df.to_dict(orient='records')
    except:
        return []

    return combined_data



def get_all_color_oa_insert100(days_ago: int, fen: int, page: int = 1, per_page: int = 2, db: Session = Depends(get_session)):

    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=15)
    week_ago_2 = today - DT.timedelta(days=10)
    print(week_ago)

    offset = (page - 1) * per_page

    if fen == 0:
        # Fetch the maximum datetime value from the table
        max_date_query = select(func.max(color_insert_oa_tb.datetime))
        max_date_result = db.execute(max_date_query).scalar()
        if max_date_result:
            max_date = max_date_result.date()
            two_days_ago = max_date - DT.timedelta(days=days_ago)
        else:
            return {"error": "No data available in the table"}

        a1 = (
            db.query(color_insert_oa_tb)
            .filter(color_insert_oa_tb.product_qty == 1, color_insert_oa_tb.detail_json != {})
            .where(color_insert_oa_tb.datetime >= two_days_ago)
            .order_by(color_insert_oa_tb.datetime.desc())
            # .options(*[defer(column) for column in columns_to_exclude])
            .all()
        )
    else:
        a1 = (
            db.query(color_insert_oa_tb)
            .filter(color_insert_oa_tb.fen == fen, color_insert_oa_tb.product_qty == 1, color_insert_oa_tb.detail_json != {})
            # .where(color_insert_oa_tb.datetime > week_ago_2)
            .order_by(color_insert_oa_tb.datetime.desc())
            .offset(offset)
            .limit(per_page)
            # .options(*[defer(column) for column in columns_to_exclude])
            .all()
        )

    return a1

def get_warehouse_name100(name: int, db: Session = Depends(get_session)):

    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
    body_raw = {"data_api": [{"jia_yi_id": name}]}
    df2 = requests.get(url=url, json=body_raw)
    df2 = df2.json()

    return df2


def insert_color_insert_oa_tb100(list_data: List[Dict], db: Session = Depends(get_session)):

    # list_data = [
    #     {'jia_yi_fang_a': 5563, 'jia_yi_fang_b': 5563, 'lei_a': 3434, 'lei_b': 3434, 'device_id': 3434, 'product_id': 3434, 'product_qty': 3434, 'fen': 3434},
    #     {'group_id': "22", 'jia_yi_fang_a': 5563, 'jia_yi_fang_b': 5563, 'lei_a': 3434, 'lei_b': 3434, 'device_id': 3434, 'product_id': 3434, 'product_qty': 3434, 'fen': 3434}
    # ]
    print(list_data)

    # Generate a random group_id
    # random_group_id = str(uuid.uuid4().hex)[0:8]  # Adjust the range as needed
    random_group_id = "COLOROA" + generate_id()
    # random_group_id = random_groupID()
    
    # Use a flag to check if detail_json has been set for the group
    detail_json_set = False

    for data in list_data:
        print(data)

        # Assign the random group_id to each dictionary
        data['group_id'] = random_group_id

        new_jia_yi_fang = color_insert_oa_tb(**data)

        # Check if product_qty is equal to -1, then set detail_json only once
        if data.get('product_qty') == 1 and not detail_json_set:
            new_jia_yi_fang.detail_json = data['detail_json']
            new_jia_yi_fang.status = {"status_big_table": "waiting", "datetime_big_table": datetime.now().isoformat()}
            detail_json_set = True
        else:
            new_jia_yi_fang.detail_json = {}
            
        db.add(new_jia_yi_fang)
    
    db.commit()
    db.refresh(new_jia_yi_fang)

    return "new_jia_yi_fang"

def update_status_color_insert100(record_id: str, status: str, db: Session):
    try:
        # Fetch the record
        result = db.query(color_insert_tb).filter(color_insert_tb.record_id == record_id).first()

        if result:
            # Update the status field
            result.status.update({"status_big_table": status})

            # Explicitly mark the JSON field as modified
            flag_modified(result, "status")

            # Commit the changes
            db.commit()
            db.refresh(result)
            return result

    except SQLAlchemyError as e:
        db.rollback()
        print(f"Error occurred: {e}")
        # Handle or re-raise the error as appropriate for your application

    return None


def update_status_color_oa_insert100(record_id: str, status: str, db: Session):
    try:
        # Fetch the record
        result = db.query(color_insert_oa_tb).filter(color_insert_oa_tb.record_id == record_id).first()

        if result:
            # Update the status field
            result.status.update({"status_big_table": status})

            # Explicitly mark the JSON field as modified
            flag_modified(result, "status")

            # Commit the changes
            db.commit()
            db.refresh(result)
            return result

    except SQLAlchemyError as e:
        db.rollback()
        print(f"Error occurred: {e}")
        # Handle or re-raise the error as appropriate for your application

    return None


def send_big_table100(db: Session = Depends(get_session)):

    from src.Connect.postgresql_nern import postgresql_shwethe_color
    import pandas as pd

    # columns_to_exclude = ['detail_json', 'status']
    # a1 = (
    #     db.query(color_insert_tb)
    #     # .filter(color_insert_tb.product_qty == 1)
    #     .options(*[defer(column) for column in columns_to_exclude])
    #     .all()
    # )

    columns_to_exclude = ['detail_json', 'status']
    # Constructing the SELECT statement
    # select_statement = f"""
    #     SELECT * 
    #     FROM color_insert_tb
    #     WHERE datetime >= NOW() - INTERVAL '10 days' 
    # """
    select_statement = """
        WITH SuccessGroups AS (
            SELECT group_id
            FROM color_insert_tb
            WHERE status::json->>'status_big_table' = 'success'
        )
        SELECT *
        FROM color_insert_tb
        WHERE group_id IN (SELECT group_id FROM SuccessGroups)
              AND datetime >= NOW() - INTERVAL '10 days'
    """

    df_a1 = pd.read_sql(select_statement, postgresql_shwethe_color)
    df_a1 = df_a1.drop(columns=columns_to_exclude)
    json_nor = df_a1.to_dict("records")


    # INSERT TO ARTER DATABASE
    aaa = []
    for ioo in json_nor:
        d = {
            'a_id': ioo['auto_id'],
            'b_id': ioo['auto_id'],
            'product_id': ioo['product_id'],
            'product_qty': ioo['product_qty'],
            'product_price_a' : ioo['unit_price'],
            'product_price_b' : ioo['unit_price'],
            'ke_bian': 0,
            'jia_yi_fang_a': ioo['jia_yi_fang_a'],
            'jia_yi_fang_b': ioo['jia_yi_fang_b'],
            'lei_a': ioo['lei_a'],
            'lei_b': ioo['lei_b'],
            'bu_bian': 383,
            'jin_huo_bian': 0,   
            'jin_huo_dang': ioo['group_id'],   
            'ci_bian': 0,
            'u_id': 0,
            'shu_riqi_datetime': str(ioo['datetime']),
            'riqi_datetime': str(ioo['datetime']),
            'che_liang': 0,
            'kind': 11235,
            'product_qtyp': 0,
            'product_qtyn': 0,
            'bi_zhi': ioo['bi_zhi']
            }
        aaa.append(d)
    df1 = pd.DataFrame(aaa) 
    # df1 = df1.to_dict("records")
    mata = {
    'data' : df1.to_dict(orient='records')
    }

    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v2/table/mysql', data= json.dumps(mata))
    url = shwethe_mysql_api + '/api/v1/table/mysql_big_table'
    dd = requests.post(url, data= json.dumps(mata))
    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v1/table/mysql_big_table', data= json.dumps(mata))
    out = dd.status_code
    print(out)

    return out


def send_big_table_oa100(db: Session = Depends(get_session)):

    from src.Connect.postgresql_nern import postgresql_shwethe_color
    import pandas as pd

    # columns_to_exclude = ['detail_json', 'status']
    # a1 = (
    #     db.query(color_insert_tb)
    #     # .filter(color_insert_tb.product_qty == 1)
    #     .options(*[defer(column) for column in columns_to_exclude])
    #     .all()
    # )

    columns_to_exclude = ['detail_json', 'status']
    # Constructing the SELECT statement
    # select_statement = f"""
    #     SELECT * 
    #     FROM color_insert_oa_tb
    # """
    select_statement = """
        WITH SuccessGroups AS (
            SELECT group_id
            FROM color_insert_oa_tb
            WHERE status::json->>'status_big_table' = 'success'
        )
        SELECT *
        FROM color_insert_oa_tb
        WHERE group_id IN (SELECT group_id FROM SuccessGroups)
              AND datetime >= NOW() - INTERVAL '10 days'
    """

    df_a1 = pd.read_sql(select_statement, postgresql_shwethe_color)
    df_a1 = df_a1.drop(columns=columns_to_exclude)
    json_nor = df_a1.to_dict("records")


    # INSERT TO ARTER DATABASE
    aaa = []
    for ioo in json_nor:
        d = {
            'a_id': ioo['auto_id'],
            'b_id': ioo['auto_id'],
            'product_id': ioo['product_id'],
            'product_qty': ioo['product_qty'],
            'product_price_a' : ioo['unit_price'],
            'product_price_b' : ioo['unit_price'],
            'ke_bian': 0,
            'jia_yi_fang_a': ioo['jia_yi_fang_a'],
            'jia_yi_fang_b': ioo['jia_yi_fang_b'],
            'lei_a': ioo['lei_a'],
            'lei_b': ioo['lei_b'],
            'bu_bian': 383,
            'jin_huo_bian': 0,   
            'jin_huo_dang': ioo['group_id'],   
            'ci_bian': 0,
            'u_id': 0,
            'shu_riqi_datetime': str(ioo['datetime']),
            'riqi_datetime': str(ioo['datetime']),
            'che_liang': 0,
            'kind': 11236,
            'product_qtyp': 0,
            'product_qtyn': 0,
            'bi_zhi': 138
            }
        aaa.append(d)
    df1 = pd.DataFrame(aaa) 
    # df1 = df1.to_dict("records")
    mata = {
    'data' : df1.to_dict(orient='records')
    }

    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v2/table/mysql', data= json.dumps(mata))
    url = shwethe_mysql_api + '/api/v1/table/mysql_big_table'
    dd = requests.post(url, data= json.dumps(mata))
    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v1/table/mysql_big_table', data= json.dumps(mata))
    out = dd.status_code
    print(out)

    return out


