from fastapi import FastAP<PERSON>, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie,generate_datetime_selie_v1
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np

router = APIRouter()

@router.get("/order_s_profit_2", status_code=200)
def order_s_profit():
    
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    A100001 = (''' select data_detail,jin_huo_bian from duty_car_in where data_sub ->> 'row_type' = 'for_duty_cost'  and data_sub ->> 'status' = 'wait_sucess'  ''' )
    A200001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
    # A200002 = A200001.reindex(columns=['data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail','order_s_insert_bill_id','order_s_insert_id'])
    # A200003 = A200002.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
    A200004 = A200001.to_dict('records')
    A200005 = pd.json_normalize(A200004,'data_detail',['jin_huo_bian'])
    A200006 = A200005.rename(columns={'product_id': 'product_id_x'})
    A200007 = A200006.to_json(orient='records')
    print(A200007)

    psycopg2_conn_insert_data_s.close()

    return json.loads(A200007)

@router.get("/order_s_profit_1", status_code=200)
def order_s_profit():
    
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    A100001 = (''' select data_detail,data_sub from order_s_insert where data_sub ->> 'row_type' = 'for_product'  and data_sub ->> 'status' = 'sucess'  ''' )
    A200001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
    A200002 = A200001.reindex(columns=['data_sub.jin_huo_bian','data_detail'])
    A200003 = A200002.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' })
    A200004 = A200003.to_dict('records')
    # print(A200004)
    A200005 = pd.json_normalize(A200004,'data_detail',['jin_huo_bian'])
    A200006 = A200005.rename(columns={'product_id': 'product_id_x'})
    A200007 = A200006.to_json(orient='records')
    # print(A200007)

    psycopg2_conn_insert_data_s.close()

    return json.loads(A200007)