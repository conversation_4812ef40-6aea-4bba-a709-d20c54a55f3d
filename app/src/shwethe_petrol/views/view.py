from typing import List, Dict
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from src.config.shwethe_petrol.database import get_session
from src.shwethe_petrol.crud.crud import (
    getTest100,
    getIndayItem100,
    getFuelPrice100,
    getMasadHourLiter100,
    Inday100,
    listOfCar100,
    sendListOfCar100,
    list_details_masad100,
    listOfMasad100,
    getToken100,
    getForm_wareHouse100,
    getPayment100,
    getCurrency100,
    Index_CarMoto100,
    IndexData100
)
# from src.shwethe_petrol.models import name_storage_tb
# from src.shwethe_petrol.schemas.schemas import (
#     color_cai_liao_tb_post,
#     color_insert_tb_post
# )
from sqlalchemy.exc import IntegrityError
router = APIRouter()


@router.get("/getTest")
def getTest(db: Session = Depends(get_session)):
    return getTest100(db=db)

@router.get("/getIndayItem/{che_liang_id}")
def getIndayItem(che_liang_id: int, db: Session = Depends(get_session)):
    return getIndayItem100(che_liang_id=che_liang_id, db=db)

@router.get("/getFuelPrice")
def getFuelPrice(db: Session = Depends(get_session)):
    return getFuelPrice100(db=db)

@router.get("/getMasadHourLiter")
def getMasadHourLiter(che_liang_id: int, db: Session = Depends(get_session)):
    return getMasadHourLiter100(che_liang_id=che_liang_id, db=db)

@router.get("/Inday/{get_date}")
def Inday(get_date: str, db: Session = Depends(get_session)):
    return Inday100(get_date=get_date, db=db)

@router.get("/listOfCar")
def listOfCar(db: Session = Depends(get_session)):
    return listOfCar100(db=db)

@router.get("/sendListOfCar")
def sendListOfCar(db: Session = Depends(get_session)):
    return sendListOfCar100(db=db)

@router.get("/list_details_masad/{che_liang_id}")
def list_details_masad(che_liang_id: int, db: Session = Depends(get_session)):
    return list_details_masad100(che_liang_id=che_liang_id, db=db)

@router.get("/listOfMasad")
def listOfMasad(db: Session = Depends(get_session)):
    return listOfMasad100(db=db)

@router.get("/getToken/{type_user_jia_yi}")
def getToken(type_user_jia_yi: int, db: Session = Depends(get_session)):
    return getToken100(type_user_jia_yi=type_user_jia_yi, db=db)

@router.get("/getForm_wareHouse")
def getForm_wareHouse(db: Session = Depends(get_session)):
    return getForm_wareHouse100(db=db)

@router.get("/getPayment")
def getPayment(db: Session = Depends(get_session)):
    return getPayment100(db=db) 

@router.get("/getCurrency")
def getCurrency(db: Session = Depends(get_session)):
    return getCurrency100(db=db) 

@router.get("/Index_CarMoto")
def Index_CarMoto(page_size: int = 10, page: int = 1, db: Session = Depends(get_session)):
    return Index_CarMoto100(page_size=page_size, page=page, db=db) 

@router.get("/IndexData")
def IndexData(fen_dian_id: int, getDate: str, db: Session = Depends(get_session)):
    return IndexData100(fen_dian_id=fen_dian_id, getDate=getDate, db=db) 