{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-01-12 03:41:25,700 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-01-12 03:41:25,712 INFO sqlalchemy.engine.Engine SELECT color_insert_tb.record_id, color_insert_tb.detail_json, color_insert_tb.status, color_insert_tb.auto_id, color_insert_tb.datetime, color_insert_tb.group_id, color_insert_tb.jia_yi_fang_a, color_insert_tb.jia_yi_fang_b, color_insert_tb.lei_a, color_insert_tb.lei_b, color_insert_tb.device_id, color_insert_tb.product_id, color_insert_tb.product_qty, color_insert_tb.color_price, color_insert_tb.unit_price, color_insert_tb.bi_zhi, color_insert_tb.fen, color_insert_tb.sum_price, color_insert_tb.product_qty_sum, color_insert_tb.unit_price_sum \n", "FROM color_insert_tb \n", "WHERE color_insert_tb.datetime > %(datetime_1)s ORDER BY color_insert_tb.datetime DESC\n", "2024-01-12 03:41:25,714 INFO sqlalchemy.engine.Engine [cached since 94.18s ago] {'datetime_1': datetime.date(2023, 12, 13)}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024-01-12 03:41:25,860 INFO sqlalchemy.engine.Engine ROLLBACK\n", "2024-01-12 03:41:25,892 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-01-12 03:41:25,894 INFO sqlalchemy.engine.Engine SELECT color_insert_oa_tb.record_id, color_insert_oa_tb.detail_json, color_insert_oa_tb.status, color_insert_oa_tb.auto_id, color_insert_oa_tb.datetime, color_insert_oa_tb.group_id, color_insert_oa_tb.jia_yi_fang_a, color_insert_oa_tb.jia_yi_fang_b, color_insert_oa_tb.lei_a, color_insert_oa_tb.lei_b, color_insert_oa_tb.device_id, color_insert_oa_tb.product_id, color_insert_oa_tb.product_qty, color_insert_oa_tb.fen, color_insert_oa_tb.color_price, color_insert_oa_tb.unit_price \n", "FROM color_insert_oa_tb \n", "WHERE color_insert_oa_tb.datetime > %(datetime_1)s AND color_insert_oa_tb.product_qty = %(product_qty_1)s ORDER BY color_insert_oa_tb.datetime DESC\n", "2024-01-12 03:41:25,895 INFO sqlalchemy.engine.Engine [cached since 94.2s ago] {'datetime_1': datetime.date(2023, 12, 13), 'product_qty_1': -1000}\n", "2024-01-12 03:41:25,903 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>lei_a</th>\n", "      <th>fen</th>\n", "      <th>group_id</th>\n", "      <th>device_id</th>\n", "      <th>product_id</th>\n", "      <th>product_qty</th>\n", "      <th>new_product_qty</th>\n", "      <th>sum_qty_per_product</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-11 16:13:04.165470+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>cc759f01</td>\n", "      <td>49019</td>\n", "      <td>62141</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-01-11 16:13:04.165043+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>cc759f01</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>279.0</td>\n", "      <td>279.0</td>\n", "      <td>2091.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-01-11 16:13:04.164691+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>cc759f01</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>42.3</td>\n", "      <td>42.3</td>\n", "      <td>123.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01-11 16:13:04.164260+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>cc759f01</td>\n", "      <td>49019</td>\n", "      <td>76271</td>\n", "      <td>12.2</td>\n", "      <td>12.2</td>\n", "      <td>12.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-01-11 16:13:04.162999+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>cc759f01</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>150.0</td>\n", "      <td>150.0</td>\n", "      <td>150.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-01-11 16:12:05.931332+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>4b3caabe</td>\n", "      <td>49019</td>\n", "      <td>61318</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-01-11 16:12:05.930917+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>4b3caabe</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>0.7</td>\n", "      <td>3.5</td>\n", "      <td>37.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-01-11 16:12:05.930682+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>4b3caabe</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>77.5</td>\n", "      <td>387.5</td>\n", "      <td>2091.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-01-11 16:12:05.930249+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>4b3caabe</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>4.2</td>\n", "      <td>21.0</td>\n", "      <td>123.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-01-11 16:11:08.284076+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>91d2554e</td>\n", "      <td>49019</td>\n", "      <td>61769</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-01-11 16:11:08.282494+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>91d2554e</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>2.7</td>\n", "      <td>13.5</td>\n", "      <td>37.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-01-11 16:11:08.282264+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>91d2554e</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>213.0</td>\n", "      <td>1065.0</td>\n", "      <td>2091.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-01-11 16:11:08.281778+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>91d2554e</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>12.0</td>\n", "      <td>60.0</td>\n", "      <td>123.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-01-10 09:46:38.430000+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>61e0a039</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>72.0</td>\n", "      <td>360.0</td>\n", "      <td>2091.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-01-10 09:46:38.430000+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>61e0a039</td>\n", "      <td>49019</td>\n", "      <td>63735</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-01-10 09:46:38.430000+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>61e0a039</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>4.0</td>\n", "      <td>20.0</td>\n", "      <td>37.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-01-11 13:32:09.561581+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>beb630da</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-01-11 13:06:54.493296+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>47d0b457</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2024-01-11 13:06:54.492621+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>47d0b457</td>\n", "      <td>49019</td>\n", "      <td>76279</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-01-08 14:01:38.626857+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>66807ed4</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2024-01-08 13:18:37.009366+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>41acfc61</td>\n", "      <td>49019</td>\n", "      <td>76277</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2024-01-08 13:18:37.008507+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>41acfc61</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2024-01-08 13:18:37.007576+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>41acfc61</td>\n", "      <td>49019</td>\n", "      <td>76273</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2024-01-08 13:18:37.006841+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>41acfc61</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2024-01-07 15:45:23.247352+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>149e2ed4</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>-1000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime  lei_a  fen  group_id  device_id   \n", "0  2024-01-11 16:13:04.165470+06:30     31    1  cc759f01      49019  \\\n", "1  2024-01-11 16:13:04.165043+06:30     22    1  cc759f01      49019   \n", "2  2024-01-11 16:13:04.164691+06:30     22    1  cc759f01      49019   \n", "3  2024-01-11 16:13:04.164260+06:30     22    1  cc759f01      49019   \n", "4  2024-01-11 16:13:04.162999+06:30     22    1  cc759f01      49019   \n", "5  2024-01-11 16:12:05.931332+06:30     31    1  4b3caabe      49019   \n", "6  2024-01-11 16:12:05.930917+06:30     22    1  4b3caabe      49019   \n", "7  2024-01-11 16:12:05.930682+06:30     22    1  4b3caabe      49019   \n", "8  2024-01-11 16:12:05.930249+06:30     22    1  4b3caabe      49019   \n", "9  2024-01-11 16:11:08.284076+06:30     31    1  91d2554e      49019   \n", "10 2024-01-11 16:11:08.282494+06:30     22    1  91d2554e      49019   \n", "11 2024-01-11 16:11:08.282264+06:30     22    1  91d2554e      49019   \n", "12 2024-01-11 16:11:08.281778+06:30     22    1  91d2554e      49019   \n", "13 2024-01-10 09:46:38.430000+06:30     22    1  61e0a039      49019   \n", "14 2024-01-10 09:46:38.430000+06:30     31    1  61e0a039      49019   \n", "15 2024-01-10 09:46:38.430000+06:30     22    1  61e0a039      49019   \n", "16 2024-01-11 13:32:09.561581+06:30     22    1  beb630da      49019   \n", "17 2024-01-11 13:06:54.493296+06:30     22    1  47d0b457      49019   \n", "18 2024-01-11 13:06:54.492621+06:30     22    1  47d0b457      49019   \n", "19 2024-01-08 14:01:38.626857+06:30     22    1  66807ed4      49019   \n", "20 2024-01-08 13:18:37.009366+06:30     22    1  41acfc61      49019   \n", "21 2024-01-08 13:18:37.008507+06:30     22    1  41acfc61      49019   \n", "22 2024-01-08 13:18:37.007576+06:30     22    1  41acfc61      49019   \n", "23 2024-01-08 13:18:37.006841+06:30     22    1  41acfc61      49019   \n", "24 2024-01-07 15:45:23.247352+06:30     22    1  149e2ed4      49019   \n", "\n", "    product_id  product_qty  new_product_qty  sum_qty_per_product  \n", "0        62141          1.0              1.0                  1.0  \n", "1        76280        279.0            279.0               2091.5  \n", "2        76274         42.3             42.3                123.3  \n", "3        76271         12.2             12.2                 12.2  \n", "4        76270        150.0            150.0                150.0  \n", "5        61318          5.0              5.0                  5.0  \n", "6        76281          0.7              3.5                 37.0  \n", "7        76280         77.5            387.5               2091.5  \n", "8        76274          4.2             21.0                123.3  \n", "9        61769          5.0              5.0                  5.0  \n", "10       76281          2.7             13.5                 37.0  \n", "11       76280        213.0           1065.0               2091.5  \n", "12       76274         12.0             60.0                123.3  \n", "13       76280         72.0            360.0               2091.5  \n", "14       63735          5.0              5.0                  5.0  \n", "15       76281          4.0             20.0                 37.0  \n", "16       76280      -1000.0              NaN                  NaN  \n", "17       76280      -1000.0              NaN                  NaN  \n", "18       76279      -1000.0              NaN                  NaN  \n", "19       76280      -1000.0              NaN                  NaN  \n", "20       76277      -1000.0              NaN                  NaN  \n", "21       76274      -1000.0              NaN                  NaN  \n", "22       76273      -1000.0              NaN                  NaN  \n", "23       76270      -1000.0              NaN                  NaN  \n", "24       76281      -1000.0              NaN                  NaN  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2024-01-12 03:41:25,982 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-01-12 03:41:25,986 INFO sqlalchemy.engine.Engine SELECT color_insert_tb.record_id, color_insert_tb.detail_json, color_insert_tb.status, color_insert_tb.auto_id, color_insert_tb.datetime, color_insert_tb.group_id, color_insert_tb.jia_yi_fang_a, color_insert_tb.jia_yi_fang_b, color_insert_tb.lei_a, color_insert_tb.lei_b, color_insert_tb.device_id, color_insert_tb.product_id, color_insert_tb.product_qty, color_insert_tb.color_price, color_insert_tb.unit_price, color_insert_tb.bi_zhi, color_insert_tb.fen, color_insert_tb.sum_price, color_insert_tb.product_qty_sum, color_insert_tb.unit_price_sum \n", "FROM color_insert_tb \n", "WHERE color_insert_tb.datetime > %(datetime_1)s ORDER BY color_insert_tb.datetime DESC\n", "2024-01-12 03:41:25,987 INFO sqlalchemy.engine.Engine [cached since 94.45s ago] {'datetime_1': datetime.date(2023, 12, 13)}\n", "2024-01-12 03:41:26,168 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>lei_a</th>\n", "      <th>fen</th>\n", "      <th>group_id</th>\n", "      <th>device_id</th>\n", "      <th>product_id</th>\n", "      <th>product_qty</th>\n", "      <th>new_product_qty</th>\n", "      <th>sum_qty_per_product</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-11 16:13:04.165470+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>cc759f01</td>\n", "      <td>49019</td>\n", "      <td>62141</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-01-11 16:13:04.165043+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>cc759f01</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>279.0</td>\n", "      <td>279.0</td>\n", "      <td>2091.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01-11 16:13:04.164691+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>cc759f01</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>42.3</td>\n", "      <td>42.3</td>\n", "      <td>123.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-01-11 16:13:04.164260+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>cc759f01</td>\n", "      <td>49019</td>\n", "      <td>76271</td>\n", "      <td>12.2</td>\n", "      <td>12.2</td>\n", "      <td>12.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-01-11 16:13:04.162999+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>cc759f01</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>150.0</td>\n", "      <td>150.0</td>\n", "      <td>150.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-01-11 16:12:05.931332+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>4b3caabe</td>\n", "      <td>49019</td>\n", "      <td>61318</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-01-11 16:12:05.930917+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>4b3caabe</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>0.7</td>\n", "      <td>3.5</td>\n", "      <td>37.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-01-11 16:12:05.930682+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>4b3caabe</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>77.5</td>\n", "      <td>387.5</td>\n", "      <td>2091.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-01-11 16:12:05.930249+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>4b3caabe</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>4.2</td>\n", "      <td>21.0</td>\n", "      <td>123.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-01-11 16:11:08.284076+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>91d2554e</td>\n", "      <td>49019</td>\n", "      <td>61769</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-01-11 16:11:08.282494+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>91d2554e</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>2.7</td>\n", "      <td>13.5</td>\n", "      <td>37.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-01-11 16:11:08.282264+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>91d2554e</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>213.0</td>\n", "      <td>1065.0</td>\n", "      <td>2091.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-01-11 16:11:08.281778+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>91d2554e</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>12.0</td>\n", "      <td>60.0</td>\n", "      <td>123.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-01-10 09:46:38.430000+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>61e0a039</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>72.0</td>\n", "      <td>360.0</td>\n", "      <td>2091.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-01-10 09:46:38.430000+06:30</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>61e0a039</td>\n", "      <td>49019</td>\n", "      <td>63735</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-01-10 09:46:38.430000+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>61e0a039</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>4.0</td>\n", "      <td>20.0</td>\n", "      <td>37.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime  lei_a  fen  group_id  device_id   \n", "0  2024-01-11 16:13:04.165470+06:30     31    1  cc759f01      49019  \\\n", "2  2024-01-11 16:13:04.165043+06:30     22    1  cc759f01      49019   \n", "3  2024-01-11 16:13:04.164691+06:30     22    1  cc759f01      49019   \n", "4  2024-01-11 16:13:04.164260+06:30     22    1  cc759f01      49019   \n", "5  2024-01-11 16:13:04.162999+06:30     22    1  cc759f01      49019   \n", "6  2024-01-11 16:12:05.931332+06:30     31    1  4b3caabe      49019   \n", "8  2024-01-11 16:12:05.930917+06:30     22    1  4b3caabe      49019   \n", "9  2024-01-11 16:12:05.930682+06:30     22    1  4b3caabe      49019   \n", "10 2024-01-11 16:12:05.930249+06:30     22    1  4b3caabe      49019   \n", "11 2024-01-11 16:11:08.284076+06:30     31    1  91d2554e      49019   \n", "13 2024-01-11 16:11:08.282494+06:30     22    1  91d2554e      49019   \n", "14 2024-01-11 16:11:08.282264+06:30     22    1  91d2554e      49019   \n", "15 2024-01-11 16:11:08.281778+06:30     22    1  91d2554e      49019   \n", "16 2024-01-10 09:46:38.430000+06:30     22    1  61e0a039      49019   \n", "17 2024-01-10 09:46:38.430000+06:30     31    1  61e0a039      49019   \n", "19 2024-01-10 09:46:38.430000+06:30     22    1  61e0a039      49019   \n", "\n", "    product_id  product_qty  new_product_qty  sum_qty_per_product  \n", "0        62141          1.0              1.0                  1.0  \n", "2        76280        279.0            279.0               2091.5  \n", "3        76274         42.3             42.3                123.3  \n", "4        76271         12.2             12.2                 12.2  \n", "5        76270        150.0            150.0                150.0  \n", "6        61318          5.0              5.0                  5.0  \n", "8        76281          0.7              3.5                 37.0  \n", "9        76280         77.5            387.5               2091.5  \n", "10       76274          4.2             21.0                123.3  \n", "11       61769          5.0              5.0                  5.0  \n", "13       76281          2.7             13.5                 37.0  \n", "14       76280        213.0           1065.0               2091.5  \n", "15       76274         12.0             60.0                123.3  \n", "16       76280         72.0            360.0               2091.5  \n", "17       63735          5.0              5.0                  5.0  \n", "19       76281          4.0             20.0                 37.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2024-01-12 03:41:26,276 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-01-12 03:41:26,278 INFO sqlalchemy.engine.Engine SELECT color_insert_oa_tb.record_id, color_insert_oa_tb.detail_json, color_insert_oa_tb.status, color_insert_oa_tb.auto_id, color_insert_oa_tb.datetime, color_insert_oa_tb.group_id, color_insert_oa_tb.jia_yi_fang_a, color_insert_oa_tb.jia_yi_fang_b, color_insert_oa_tb.lei_a, color_insert_oa_tb.lei_b, color_insert_oa_tb.device_id, color_insert_oa_tb.product_id, color_insert_oa_tb.product_qty, color_insert_oa_tb.fen, color_insert_oa_tb.color_price, color_insert_oa_tb.unit_price \n", "FROM color_insert_oa_tb \n", "WHERE color_insert_oa_tb.datetime > %(datetime_1)s AND color_insert_oa_tb.product_qty = %(product_qty_1)s ORDER BY color_insert_oa_tb.datetime DESC\n", "2024-01-12 03:41:26,279 INFO sqlalchemy.engine.Engine [cached since 94.58s ago] {'datetime_1': datetime.date(2023, 12, 13), 'product_qty_1': -1000}\n", "2024-01-12 03:41:26,303 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>lei_a</th>\n", "      <th>fen</th>\n", "      <th>group_id</th>\n", "      <th>device_id</th>\n", "      <th>product_id</th>\n", "      <th>product_qty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-11 13:32:09.561581+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>beb630da</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-01-11 13:06:54.493296+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>47d0b457</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-01-11 13:06:54.492621+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>47d0b457</td>\n", "      <td>49019</td>\n", "      <td>76279</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01-08 14:01:38.626857+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>66807ed4</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-01-08 13:18:37.009366+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>41acfc61</td>\n", "      <td>49019</td>\n", "      <td>76277</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-01-08 13:18:37.008507+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>41acfc61</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-01-08 13:18:37.007576+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>41acfc61</td>\n", "      <td>49019</td>\n", "      <td>76273</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-01-08 13:18:37.006841+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>41acfc61</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-01-07 15:45:23.247352+06:30</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>149e2ed4</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          datetime  lei_a  fen  group_id  device_id   \n", "0 2024-01-11 13:32:09.561581+06:30     22    1  beb630da      49019  \\\n", "1 2024-01-11 13:06:54.493296+06:30     22    1  47d0b457      49019   \n", "2 2024-01-11 13:06:54.492621+06:30     22    1  47d0b457      49019   \n", "3 2024-01-08 14:01:38.626857+06:30     22    1  66807ed4      49019   \n", "4 2024-01-08 13:18:37.009366+06:30     22    1  41acfc61      49019   \n", "5 2024-01-08 13:18:37.008507+06:30     22    1  41acfc61      49019   \n", "6 2024-01-08 13:18:37.007576+06:30     22    1  41acfc61      49019   \n", "7 2024-01-08 13:18:37.006841+06:30     22    1  41acfc61      49019   \n", "8 2024-01-07 15:45:23.247352+06:30     22    1  149e2ed4      49019   \n", "\n", "   product_id  product_qty  \n", "0       76280      -1000.0  \n", "1       76280      -1000.0  \n", "2       76279      -1000.0  \n", "3       76280      -1000.0  \n", "4       76277      -1000.0  \n", "5       76274      -1000.0  \n", "6       76273      -1000.0  \n", "7       76270      -1000.0  \n", "8       76281      -1000.0  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sys\n", "# sys.path.append('../../../../../app/')\n", "sys.path.append('../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "# from src.shwethe_color.database import get_session\n", "from src.config.shwethe_color.database import get_session\n", "from src.shwethe_color.models.model import color_insert_tb, color_insert_oa_tb\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "\n", "\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def dataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "def get_color_insert_tb():\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        # GET TODAY DATA\n", "        week_ago = today - DT.<PERSON><PERSON>(days=30)\n", "        heroesPersonal = db.exec(select(color_insert_tb).where(color_insert_tb.datetime > week_ago).order_by(color_insert_tb.datetime.desc())).all()\n", "        mergeDF = dataframe(heroesPersonal, to_dict=False)\n", "        mergeDF = mergeDF[['datetime', 'lei_a', 'fen', 'group_id', 'device_id', 'product_id', 'product_qty']]\n", "        mergeDF = mergeDF.head(20)\n", "    return mergeDF\n", "\n", "\n", "def get_color_insert_oa_tb():\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        # GET TODAY DATA\n", "        week_ago = today - DT.<PERSON><PERSON>(days=30)\n", "        heroesPersonal = db.exec(select(color_insert_oa_tb).where(color_insert_oa_tb.datetime > week_ago, color_insert_oa_tb.product_qty == -1000).order_by(color_insert_oa_tb.datetime.desc())).all()\n", "        mergeDF = dataframe(heroesPersonal, to_dict=False)\n", "        mergeDF = mergeDF[['datetime', 'lei_a', 'fen', 'group_id', 'device_id', 'product_id', 'product_qty']]\n", "    return mergeDF\n", "\n", "\n", "def calOne():\n", "    df = get_color_insert_tb()\n", "\n", "    # Filter out duplicates for lei_a = 22\n", "    mask = (df['lei_a'] == 22) & (df.duplicated(subset=['group_id', 'product_qty'], keep=False))\n", "    filtered_df = df[~mask]\n", "\n", "    df = filtered_df.sort_values(by=['datetime', 'group_id'], ascending=[False, False])\n", "\n", "    # Get the multiplier for each group_id where lei_a is 31\n", "    multiplier = df[df['lei_a'] == 31].set_index('group_id')['product_qty']\n", "    # Create a new column for the multiplied values\n", "    df['new_product_qty'] = df.apply(lambda row: row['product_qty'] * multiplier.get(row['group_id'], 1) if row['lei_a'] == 22 else row['product_qty'], axis=1)\n", "    \n", "    return df\n", "\n", "\n", "def calTwo():\n", "    df = calOne()\n", "\n", "    # Sum new_product_qty for each group_id and create a new column\n", "    # df['sum_new_product_qty'] = df.groupby('group_id')['new_product_qty'].transform('sum')\n", "    df['sum_qty_per_product'] = df.groupby('product_id')['new_product_qty'].transform('sum')\n", "\n", "    return df\n", "\n", "\n", "def calThree():\n", "    df1 = calTwo()\n", "    df2 = get_color_insert_oa_tb()\n", "\n", "    result = pd.concat([df1, df2], ignore_index=True)\n", "\n", "    return result\n", "\n", "# display(calOne())\n", "display(calThree())\n", "display(calTwo())\n", "display(get_color_insert_oa_tb())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}