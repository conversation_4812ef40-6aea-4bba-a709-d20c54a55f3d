import numpy as np
from sqlmodel import Session, select
from typing import List, Dict
from sqlalchemy import and_,desc, func, not_,or_,cast, String
from sqlalchemy import join
from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT
import uuid
from datetime import datetime
# from src.shwethe_petrol.models import (
# jia_yi_fang,product,find_new_goods,name_storage_tb,product_price_tb)
from src.shwethe_petrol.models.model import PetrolModel, Calculate, PetrolModel2, TypeBi_zhi, TypeLei
# from src.shwethe_petrol.schemas.schemas import (
#     color_cai_liao_tb_post,
#     color_insert_tb_post
#     # jia_yi_fangCreate, jia_yi_fangUpdate,
#     # jia_yi_fang as jia_yi_fangSchema,   
#     # Product as ProductSchema,  
#     # ProductCreate ,
#     # ProductUpdate,
#     # findNewGoodsPost,
#     # nameStorageTbPost,
#     # post_product_category_tb_item
# )
from fastapi import HTTPException, status
from src.config.shwethe_petrol.database import get_session
from fastapi import APIRouter, Depends, Query, Body

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session, Load, defer
import requests
import pandas as pd
from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api
import json
import random
from sqlalchemy import update
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy.exc import SQLAlchemyError
from sklearn.ensemble import IsolationForest


def getTest100(db: Session = Depends(get_session)):
    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=0)

    # Create a query
    query = select([
        PetrolModel.petrol_id,
        PetrolModel.datetime,
        PetrolModel.data_details[0]['che_liang_id'],
        PetrolModel.data_details[0]['distant'],
        PetrolModel.data_details[0]['qty'],
        PetrolModel.data_details[0]['price'],
        PetrolModel.data_details[0]['product_id'],
    ]).filter(PetrolModel.datetime > week_ago)

    # Execute the query and fetch results
    result = db.execute(query).fetchall()

    # Convert to DataFrame
    df20 = pd.DataFrame(result, columns=[
        'petrol_id', 'datetime', 'che_liang_id', 'distant', 'qty', 'price', 'product_id'
    ])

    # Convert DataFrame to dictionary records
    df20_dict = df20.to_dict("records")

    return df20_dict


def getIndayItem100(che_liang_id: int, db: Session = Depends(get_session)):

    query = db.query(PetrolModel)\
        .filter(cast(PetrolModel.data_details[0]['che_liang_id'], Integer) == che_liang_id)\
        .order_by(PetrolModel.datetime.desc())\
        .limit(15)\
        .with_entities(
            PetrolModel.data_details[0]['fen_dian_id'],
            PetrolModel.datetime,
            PetrolModel.datetime_bill,
            PetrolModel.petrol_id,
            PetrolModel.data_details[0]['che_liang_id'],
            PetrolModel.data_details[0]['distant'],
            PetrolModel.data_details[0]['bill'],
            PetrolModel.status_details['status'],
            PetrolModel.data_details[0]['imageKm'],
            PetrolModel.data_details[0]['imageAccept'],
            PetrolModel.data_details[0]['imageKmDate'],
            PetrolModel.data_details[0]['imageKm2'],
            PetrolModel.data_details[0]['imageBill'],
            PetrolModel.data_details[0]['qty']
        )

    # Execute the query and fetch the results
    result = query.all()

    # Create a DataFrame from the query results
    df20 = pd.DataFrame(result, columns=[
        'fen_dian_id', 'datetime', 'datetime_bill', 'petrol_id', 'che_liang_id',
        'distant', 'bill', 'status', 'imageKm', 'imageAccept', 'imageKmDate',
        'imageKm2', 'imageBill', 'qty'
    ])

    df40 = df20
    df40 = df40.loc[:, df40.columns.isin(['che_liang_id', 'petrol_id', 'product_id'])]
    df40 = df40.rename(columns={"che_liang_id": 'jia_yi_id'})
    # # display('df40', df40)

    # url = Arter_api + 'jia_yi_name_list_id'
    url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
    to_dict = df40.to_dict('records')
    body_raw = {"data_api": to_dict}
    df50 = requests.get(url=url, json=body_raw).json()
    df50 = pd.DataFrame(df50)
    # # display('df50', df50)
    
    mergeA = df20.merge(df50, left_on='che_liang_id', right_on='jia_yi_id', how='left')
    mergeA = mergeA.to_dict("records")

    return mergeA

def getFuelPrice100(db: Session = Depends(get_session)):
    query = db.query(PetrolModel)\
        .with_entities(
            PetrolModel.petrol_id,
            PetrolModel.datetime,
            PetrolModel.data_details[0]['product_id'],
            PetrolModel.data_details[0]['price']
        )\
        .filter(cast(PetrolModel.data_details[0]['price'], String) != "0.0")\
        .order_by(PetrolModel.data_details[0]['product_id'], PetrolModel.datetime.desc())\
        .distinct(PetrolModel.data_details[0]['product_id'])

    # Execute the query and fetch results
    result = query.all()

    # Create a DataFrame from the query results
    df10 = pd.DataFrame(result, columns=['petrol_id', 'datetime', 'product_id', 'price'])

    # Adding a new column 'ids' with predefined values
    df10['ids'] = pd.Series([100, 200])

    url_link = 'https://xn--42cah7d0cxcvbbb9x.com/%E0%B8%A3%E0%B8%B2%E0%B8%84%E0%B8%B2%E0%B8%99%E0%B9%89%E0%B8%B3%E0%B8%A1%E0%B8%B1%E0%B8%99%E0%B8%A7%E0%B8%B1%E0%B8%99%E0%B8%99%E0%B8%B5%E0%B9%89/'
    url = url_link
    df20 = pd.read_html(url, header = 0)
    df20 = df20[0]
    df20['ids'] = pd.Series([100,200], index=[1, 6])
    
    merge = df20.merge(df10, on='ids', how='left')
    merge = merge.rename(columns={ "Unnamed: 0": 'petrol', "ปตท.": 'petrol_price',})
    merge = merge.fillna('-').to_dict('records')

    return merge

def getMasadHourLiter100(che_liang_id: int, db: Session = Depends(get_session)):
    
    query = db.query(Calculate)\
        .with_entities(
            Calculate.calTwo['che_liang_id'],
            Calculate.calTwo['datetime'],
            Calculate.calTwo['oneKmLit'],
            Calculate.calTwo['minute'],
        )\
        .filter(cast(Calculate.calTwo['che_liang_id'], Integer) == che_liang_id)

    # Execute the query and fetch results
    result = query.all()

    # Create a DataFrame from the query results
    df10 = pd.DataFrame(result, columns=['che_liang_id', 'datetime', 'oneKmLit', 'minute'])
    df10 = df10.to_dict("records")

    return df10

def Inday100(get_date: int, db: Session = Depends(get_session)):
    
    try:    
        query = db.query(PetrolModel)\
            .order_by(PetrolModel.datetime.desc())\
            .filter(cast(PetrolModel.datetime, Date) == get_date)\
            .with_entities(
                PetrolModel.data_details[0]['fen_dian_id'],
                PetrolModel.datetime,
                PetrolModel.datetime_bill,
                PetrolModel.petrol_id,
                PetrolModel.data_details[0]['che_liang_id'],
                PetrolModel.data_details[0]['distant'],
                PetrolModel.data_details[0]['bill'],
                PetrolModel.status_details['status'],
                PetrolModel.data_details[0]['imageKm'],
                PetrolModel.data_details[0]['imageKm2'],
                PetrolModel.data_details[0]['imageAccept'],
                PetrolModel.data_details[0]['imageKmDate'],
                PetrolModel.data_details[0]['qty'],
                PetrolModel.data_details[0]['imageBill'],
                PetrolModel.data_details[0]['driver']
            )

        # Execute the query and fetch results
        result = query.all()

        # Create a DataFrame from the query results
        df20 = pd.DataFrame(result, columns=[
            'fen_dian_id', 'datetime', 'datetime_bill', 'petrol_id', 'che_liang_id', 
            'distant', 'bill', 'status', 'imageKm', 'imageKm2', 'imageAccept', 
            'imageKmDate', 'qty', 'imageBill', 'driver'
        ])

        try: 
            df20['imageKmDate'] = pd.to_datetime(df20['imageKmDate']).dt.tz_convert('Asia/Yangon')
        except: 
            df20['imageKmDate'] = None
        df21 = df20['petrol_id'].tolist()
        print('df21', df21)


        # Build the query
        query = db.query(Calculate)\
            .filter(func.jsonb_extract_path_text(Calculate.calOne, 'petrol_id').in_(df21))\
            .with_entities(
                Calculate.calOne['petrol_id'],
                Calculate.calOne['distance_used'],
                Calculate.calOne['mean_distance_used_2'],
                Calculate.calOne['average_percent_2'],
                Calculate.calOne['mean'],
                Calculate.calOne['count'],
                Calculate.calOne['uniqleQty'],
                Calculate.calOne['FirstuniqleQty'],
                Calculate.calOne['LastuniqleQty'],
                Calculate.calOne['diff_datetime_bill'],
                Calculate.calOne['mean_datetime_bill'],
                Calculate.calOne['percent_date_bill'],
                Calculate.calOne['uniqleDriver'],
                Calculate.calOne['FirstuniqleDriver'],
                Calculate.calOne['LastuniqleDriver'],
                Calculate.calOne['driverCount'],
            )

        # Execute the query and fetch results
        result = query.all()

        # Create a DataFrame from the query results
        df30 = pd.DataFrame(result, columns=[
            'petrol_id', 'distance_used', 'mean_distance_used_2', 'average_percent_2', 
            'mean', 'count', 'uniqleQty', 'FirstuniqleQty', 'LastuniqleQty', 
            'diff', 'mean', 'percent', 
            'uniqleDriver', 'FirstuniqleDriver', 'LastuniqleDriver', 'driverCount'
        ])

        df40 = df20
        df40 = df40.loc[:, df40.columns.isin(['che_liang_id', 'petrol_id', 'product_id'])]
        df40 = df40.rename(columns={"che_liang_id": 'jia_yi_id'})
        # display('df40', df40)

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        # url = f'{pv_api}/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        to_dict = df40.to_dict('records')
        body_raw = {"data_api": to_dict}
        df50 = requests.get(url=url, json=body_raw).json()
        df50 = pd.DataFrame(df50)
        # display('df50', df50)
        
        merge = df20.merge(df50, left_on='che_liang_id', right_on='jia_yi_id', how='left').merge(df30, on='petrol_id', how='outer')
        merge = merge.fillna("").to_dict("records")

    except:
        # merge = pd.DataFrame()
        merge = []
    return merge


def listOfCar100(db: Session = Depends(get_session)):

    query = db.query(PetrolModel)\
        .with_entities(
            PetrolModel.data_details[0]['che_liang_id']
        )\
        .distinct(PetrolModel.data_details[0]['che_liang_id'])\
        .filter(func.jsonb_extract_path_text(PetrolModel.data_details[0], 'bill') == "")

    # Execute the query and fetch results
    result = query.all()

    # Create a DataFrame from the query results
    df81 = pd.DataFrame(result, columns=['che_liang_id'])


    query = db.query(Calculate)\
        .with_entities(
            Calculate.calTwo['petrol_id'],
            Calculate.calTwo['datetime'],
            Calculate.calTwo['che_liang_id'],
            Calculate.calTwo['average'],
            Calculate.calTwo['minute'],
            Calculate.calTwo['oneKmLit'],
            Calculate.calTwo['hundredKmLit'],
            Calculate.calTwo['mean_distance_used_2'],
            Calculate.calTwo['average_2'],
            Calculate.calTwo['mean'],
            Calculate.calTwo['kmLitStd'],
            Calculate.calTwo['kmLitMin'],
            Calculate.calTwo['kmLit25Per'],
            Calculate.calTwo['kmLit50Per'],
            Calculate.calTwo['kmLit75Per'],
            Calculate.calTwo['kmLitMax'],
            Calculate.calTwo['countDuplicate'],
            Calculate.calTwo['che_liang_id_Count'],
            Calculate.calTwo['qty'],
            Calculate.calTwo['meanQty'],
            Calculate.calTwo['sumQty'],
            Calculate.calTwo['mean_datetime_bill'],
        )\
        .distinct(Calculate.calTwo['che_liang_id'])\
        .order_by(Calculate.calTwo['che_liang_id'], Calculate.calTwo['datetime'].desc())

    # Execute the query and fetch results
    result = query.all()

    # Create a DataFrame from the query results
    df91 = pd.DataFrame(result, columns=[
        'petrol_id', 'datetime', 'che_liang_id', 'average', 'minute', 'oneKmLit',
        'hundredKmLit', 'mean_distance_used_2', 'average_2', 'mean', 'kmLitStd', 
        'kmLitMin', 'kmLit25Per', 'kmLit50Per', 'kmLit75Per', 'kmLitMax', 
        'countDuplicate', 'che_liang_id_Count', 'qty', 'meanQty', 'sumQty', 
        'mean_datetime_bill'
    ]).round(decimals = 1)

    df100 = df81
    df100 = df100.rename(columns={"che_liang_id": 'jia_yi_id'})
    # display(df100)

    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    # url = Arter_api + 'jia_yi_name_list_id'
    url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
    to_dict = df100.to_dict('records')
    body_raw = {"data_api": to_dict}
    df110 = requests.get(url=url, json=body_raw).json()
    df110 = pd.DataFrame(df110)
    # display(df110)


    merge = df81.merge(df91, on='che_liang_id', how='left').merge(df110, left_on='che_liang_id', right_on='jia_yi_id', how='left')
    merge = merge.fillna("")
    # display(merge)

    merge = merge.to_dict("records")

    return merge


def sendListOfCar100(db: Session = Depends(get_session)):

    # Forming the query
    query = db.query(
            PetrolModel.datetime,
            PetrolModel.data_details[0]['che_liang_id'].label('che_liang_id'),
            PetrolModel.data_details[0]['bill'].label('bill')
        )\
        .distinct(PetrolModel.data_details[0]['che_liang_id'])\
        .order_by(PetrolModel.data_details[0]['che_liang_id'], PetrolModel.datetime.desc())

    # Execute the query and fetch results
    result = query.all()

    # Create a DataFrame from the query results
    df = pd.DataFrame(result, columns=['datetime', 'che_liang_id', 'bill'])

    # Additional DataFrame processing
    df['type'] = np.where(df['bill'] == 'success', 'masad', 'car')

    merge = df.to_dict('records')

    return merge


def list_details_masad100(che_liang_id: int, db: Session = Depends(get_session)):

    # Calculate the date 180 days ago
    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=180)
    print(week_ago)

    # Forming the query
    query = db.query(
            PetrolModel.petrol_id,
            PetrolModel.datetime,
            PetrolModel.datetime_bill,
            PetrolModel.data_details[0]['che_liang_id'].label('che_liang_id'),
            PetrolModel.data_details[0]['distant'].label('distant'),
            PetrolModel.data_details[0]['qty'].label('qty'),
            PetrolModel.data_details[0]['driver'].label('driver')
        ).filter(PetrolModel.datetime > week_ago)

    # Execute the query and fetch results
    result = query.all()

    # Create a DataFrame from the query results
    df20 = pd.DataFrame(result, columns=['petrol_id', 'datetime', 'datetime_bill', 'che_liang_id', 'distant', 'qty', 'driver'])


    query = db.query(
            PetrolModel2.petrol_id,
            PetrolModel2.datetime,
            PetrolModel2.data_details[0]['che_liang_id'],
            PetrolModel2.data_details[0]['distant'],
            PetrolModel2.data_details[0]['qty'],
        ).filter(PetrolModel2.datetime > week_ago)

    # Execute the query and fetch results
    result = query.all()

    df30 = pd.DataFrame(result, columns=['che_liang_id', 'distant', 'qty'])


    df40 = pd.concat([df20, df30], ignore_index=True)
    df40 = df40.sort_values(['datetime'], ascending=[True])
    # # display(df40)

    df50 = df40[df40['che_liang_id'] == che_liang_id]

    df50['previous_datetime_bill'] = df50['datetime_bill'].shift()
    df50['diff_datetime_bill'] = (df50['datetime_bill'] - df50['previous_datetime_bill']).dt.days
    df50['mean_datetime_bill'] = df50['diff_datetime_bill'].mean()
    df50['percent_date_bill'] = df50.apply(lambda x: x.mean_datetime_bill and (x.diff_datetime_bill - x.mean_datetime_bill) / x.mean_datetime_bill * 100, axis=1)

    df50['previous_distant'] = df50['distant'].shift()
    df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)
    df50['mean_distance_used'] = df50['distance_used'].mean()
    df50['previous_qty'] = df50['qty'].shift()
    df50['count'] = df50['distance_used']
    df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         
    df50['average'] = df50[df50['km_lit'] != 0]["km_lit"].mean()
    df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)

    outlier_detector = IsolationForest(random_state=42)
    distance_used_fillna = df50[['km_lit']].fillna(0)
    outlier_detector.fit(distance_used_fillna)
    prediction = outlier_detector.predict(distance_used_fillna)
    prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
    df50['outlier_flag'] = prediction_strings
    
    df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)
    df50['average_2'] = df50[df50['km_lit_2'] != 0]["km_lit_2"].mean()
    df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01
    df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)
    # df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100
    df50['mean'] = df50['average_percent_2'].abs().sum() / len(df50)

    df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')
    df60 = pd.DataFrame(df60)
    df60 = df60.rename(columns={0: 'countoo'}).reset_index()
    df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())
    
    df50 = df50.fillna(0).round(decimals = 1)
    df50 = df50.to_dict('records')

    return df50


def listOfMasad100(db: Session = Depends(get_session)):

    # Forming the query
    query = db.query(
            PetrolModel.data_details[0]['che_liang_id'].label('che_liang_id'),
            PetrolModel.data_details[0]['fen_dian_id'].label('fen_dian_id')
        )\
        .distinct(PetrolModel.data_details[0]['che_liang_id'])\
        .order_by(PetrolModel.data_details[0]['che_liang_id'], PetrolModel.datetime.desc())\
        .filter(
            # not_(cast(PetrolModel.data_details[0]['bill'], String) == "")
            not_(func.jsonb_extract_path_text(PetrolModel.data_details[0], 'bill') == "")
            # .filter(func.jsonb_extract_path_text(PetrolModel.data_details[0], 'bill') == "")
        )

    # Execute the query and fetch results
    result = query.all()

    # Create a DataFrame from the query results
    df81 = pd.DataFrame(result, columns=['che_liang_id', 'fen_dian_id'])


    query = db.query(
            Calculate.calTwo['petrol_id'].label('petrol_id'),
            Calculate.calTwo['datetime'].label('datetime'),
            Calculate.calTwo['che_liang_id'].label('che_liang_id'),
            Calculate.calTwo['average'].label('average'),
            Calculate.calTwo['minute'].label('minute'),
            Calculate.calTwo['oneKmLit'].label('oneKmLit'),
            Calculate.calTwo['hundredKmLit'].label('hundredKmLit'),
            Calculate.calTwo['mean_distance_used_2'].label('mean_distance_used_2'),
            Calculate.calTwo['average_2'].label('average_2'),
            Calculate.calTwo['mean'].label('mean'),
            Calculate.calTwo['kmLitStd'].label('kmLitStd'),
            Calculate.calTwo['kmLitMin'].label('kmLitMin'),
            Calculate.calTwo['kmLit25Per'].label('kmLit25Per'),
            Calculate.calTwo['kmLit50Per'].label('kmLit50Per'),
            Calculate.calTwo['kmLit75Per'].label('kmLit75Per'),
            Calculate.calTwo['kmLitMax'].label('kmLitMax'),
            Calculate.calTwo['countDuplicate'].label('countDuplicate'),
            Calculate.calTwo['che_liang_id_Count'].label('che_liang_id_Count'),
            Calculate.calTwo['mean_distance_used'].label('mean_distance_used'),
            Calculate.calTwo['qty'].label('qty'),
            Calculate.calTwo['meanQty'].label('meanQty'),
            Calculate.calTwo['sumQty'].label('sumQty'),
            Calculate.calTwo['mean_datetime_bill'].label('mean_datetime_bill'),
        )\
        .distinct(Calculate.calTwo['che_liang_id'])\
        .order_by(Calculate.calTwo['che_liang_id'], Calculate.calTwo['datetime'].desc())

    result = query.all()

    df91 = pd.DataFrame(result, columns=[
        'petrol_id', 'datetime', 'che_liang_id', 'average', 'minute', 
        'oneKmLit', 'hundredKmLit', 'mean_distance_used_2', 'average_2', 
        'mean', 'kmLitStd', 'kmLitMin', 'kmLit25Per', 'kmLit50Per', 
        'kmLit75Per', 'kmLitMax', 'countDuplicate', 'che_liang_id_Count',  
        'mean_distance_used', 'qty', 'meanQty', 'sumQty', 'mean_datetime_bill'
    ]).round(decimals=1).fillna(0)


    df100 = df81
    df100 = df100.rename(columns={"che_liang_id": 'jia_yi_id'})
    # display(df100)

    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    # url = Arter_api + 'jia_yi_name_list_id'
    url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
    to_dict = df100.to_dict('records')
    body_raw = {"data_api": to_dict}
    df110 = requests.get(url=url, json=body_raw).json()
    df110 = pd.DataFrame(df110)
    # display(df110)


    merge = df81.merge(df91, on='che_liang_id', how='left').merge(df110, left_on='che_liang_id', right_on='jia_yi_id', how='left')
    merge = merge.fillna("")
    # display(merge)
    
    merge = merge.to_dict('records')

    return merge


def getToken100(type_user_jia_yi: int, db: Session = Depends(get_session)):

    dict = [
            {
            "jia_yi_id": type_user_jia_yi
            }
        ]
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    # url = Arter_api + 'jia_yi_name_list_id'
    url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
    body_raw = {"data_api": dict}
    df40 = requests.get(url=url, json=body_raw).json()
    df40 = pd.DataFrame(df40)
    df40 = df40.to_dict('records')

    return df40


def getForm_wareHouse100(db: Session = Depends(get_session)):

    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=90)
    print(week_ago)

    result = db.query(PetrolModel)\
                    .filter(PetrolModel.datetime > week_ago)\
                    .order_by(PetrolModel.datetime.desc())\
                    .all()

    df20 = pd.DataFrame([r.__dict__ for r in result])
    df20 = df20.to_dict('records')
    df20 = pd.json_normalize(df20, "data_details", ["auto_id", "petrol_id", "datetime", "status_details"])
    # df20 = df20.fillna(0)

    df30 = df20
    df30 = df30.loc[:, df30.columns.isin(['che_liang_id', 'petrol_id', 'product_id'])]
    df30 = df30.rename(columns={"che_liang_id": 'jia_yi_id'})
    # display('df30', df30)

    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    # url = Arter_api + 'jia_yi_name_list_id'
    url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
    to_dict = df30.to_dict('records')
    body_raw = {"data_api": to_dict}
    df40 = requests.get(url=url, json=body_raw).json()
    df40 = pd.DataFrame(df40)
    # display('df40', df40)

    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
    # url = Arter_api + 'product_list_id'
    url = mongodb_data_api + '/api/v2/search/product_list_id'
    to_dict = df30.to_dict('records')
    body_raw = {"data_api": to_dict}
    df50 = requests.get(url=url, json=body_raw).json()
    df50 = pd.DataFrame(df50)
    # display('df50', df50)

    merge = df20.merge(df40, left_on='che_liang_id', right_on='jia_yi_id', how='left').merge(df50, on='product_id', how='left')
    merge = merge.to_dict('records')
    # display('merge', merge)

    return merge


def getPayment100(db: Session = Depends(get_session)):

    # df = pd.read_sql_table('lei_type', engine)
    # # df = df.loc[:, ~df.columns.isin(['data_sub'])]
    # df = df.drop([('data_sub')], axis=1)
    # df = df.sort_values(by=['auto_id'])
    # df = df.to_dict('records')

    # Query all records from the 'lei_type' table
    result = db.query(TypeLei).all()

    # Convert to DataFrame
    df = pd.DataFrame([r.__dict__ for r in result])

    # Drop the 'data_sub' column
    df = df.drop(['data_sub'], axis=1)

    # Sort by 'auto_id'
    df = df.sort_values(by=['auto_id'])

    # Convert to a list of dictionaries
    records = df.to_dict('records')

    return records


def getCurrency100(db: Session = Depends(get_session)):

    # Query all records from the 'lei_type' table
    result = db.query(TypeBi_zhi).all()

    # Convert to DataFrame
    df = pd.DataFrame([r.__dict__ for r in result])

    # Convert to a list of dictionaries
    records = df.to_dict('records')

    return records


def Index_CarMoto100(page_size: int = 10, page: int = 1, db: Session = Depends(get_session)):

    page -= 1  # Adjusting page number to zero-based index
    try:
        # Querying data with pagination
        query = db.query(PetrolModel).order_by(PetrolModel.datetime.desc()).limit(page_size).offset(page * page_size)
        result = query.all()

        # Convert to DataFrame
        df = pd.DataFrame([r.__dict__ for r in result])

        # Normalize data_details and merge with other columns
        df_normalized = pd.json_normalize(df.to_dict('records'), "data_details", ["auto_id", "petrol_id", "datetime", "status_details"])

        # Rename columns and fill missing values
        df_normalized.rename(columns={"status_details.status": 'status'}, inplace=True)
        df_normalized[['bill']] = df_normalized[['bill']].fillna(value="")
        df_normalized[['user_id', 'fen_dian_id', 'warehouse_id']] = df_normalized[['user_id', 'fen_dian_id', 'warehouse_id']].fillna(value=0)

        # Filter data where 'bill' is empty
        filtered_df = df_normalized[df_normalized['bill'] == ""]
        
        # Convert to a list of dictionaries
        df20 = filtered_df.to_dict('records')

    except:
        df20 = []    

    return df20


def IndexData100(fen_dian_id: int, getDate: str, db: Session = Depends(get_session)):

    get_date = getDate

    # # query = db.query(
    # #         PetrolModel2.petrol_id,
    # #         PetrolModel2.datetime,
    # #         PetrolModel2.data_details[0]['che_liang_id'],
    # #         PetrolModel2.data_details[0]['distant'],
    # #         PetrolModel2.data_details[0]['qty'],
    # #     ).filter(PetrolModel2.datetime > week_ago)
    # # result = query.all()
    
    # # Create the query
    # query = (db.query(PetrolModel)
    #          .order_by(PetrolModel.datetime.desc())
    #          .filter(cast(PetrolModel.datetime, Date) == get_date)
    #          .filter(func.jsonb_extract_path_text(PetrolModel.data_details[0]['fen_dian_id'], Integer) == fen_dian_id)\
    #         #  .filter(func.jsonb_extract_path_text(PetrolModel.data_details[0], 'fen_dian_id') == fen_dian_id)
    #         #  .filter(func.jsonb_extract_path_text(PetrolModel.data_details[0], 'bill') == "")
    #          )


    # # Execute the query and fetch data
    # result = query.all()

    # # Convert the result to a DataFrame
    # df = pd.DataFrame([r.__dict__ for r in result])

    # df = df.to_dict('records')

    # # Normalize the data_details field
    # df_normalized = pd.json_normalize(df, "data_details", ["auto_id", "petrol_id", "datetime", "status_details"])

    # df20 = df_normalized.to_dict('records')

    # Build the query
    query = db.query(PetrolModel)\
                   .order_by(PetrolModel.datetime.desc())\
                   .filter(cast(PetrolModel.datetime, Date) == get_date)\
                   .filter(cast(PetrolModel.data_details[0]['fen_dian_id'], Integer) == fen_dian_id)\
                   .filter(PetrolModel.data_details[0]['bill'].astext == "")

    # Execute the query and fetch the result
    result = query.all()

    # Convert to DataFrame
    df = pd.DataFrame([r._asdict() for r in result])

    # Normalize JSON data
    df20 = pd.json_normalize(df, "data_details", ["auto_id", "petrol_id", "datetime", "status_details"])
    
    # Convert DataFrame to dictionary records
    df20_records = df20.to_dict('records')

    return df20_records
