from dateutil.parser import parse
from typing import Optional
from pydantic import BaseModel, validator
from datetime import datetime
import json



# -------------------------------------------------------
# class color_insert_tb_post(BaseModel):
#     # datetime: Optional[int] = Field(default=..., title="cai_liao_id")
#     # record_id: Optional[str]
#     group_id: Optional[str]
#     jia_yi_fang_a: Optional[int]
#     jia_yi_fang_b: Optional[int] 
#     lei_a: Optional[int] 
#     lei_b: Optional[int] 
#     device_id: Optional[int] 
#     product_id: Optional[int] 
#     product_qty: Optional[int] 
#     product_price: Optional[float] 
#     bi_zhi: Optional[int] 
#     fen: Optional[int] 

