from datetime import datetime
from typing import Optional, Dict
from sqlmodel import SQLModel, Field
from sqlalchemy import <PERSON>umn, Integer, JSON, Text
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import TIMESTAMP, JSONB
from sqlalchemy import TEXT
from sqlalchemy.sql.expression import table, text


# class color_insert_tb(SQLModel, table=True):
#     auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
#     # datetime: Optional[int] = Field(default=..., title="cai_liao_id")
#     datetime: Optional[str] = Field(
#         default_factory=lambda: datetime.now().isoformat(), title="datetime"
#     )  
#     # record_id: Optional[str]
#     record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'COLOR'::text || lpad(nextval('color_insert_sequence'::regclass)::text, 8, '0'::text)")))
#     group_id: Optional[str] = Field(default=..., title="group_id")
#     jia_yi_fang_a: Optional[int] = Field(default=..., title="jia_yi_fang_a")
#     jia_yi_fang_b: Optional[int] = Field(default=..., title="jia_yi_fang_b")
#     lei_a: Optional[int] = Field(default=..., title="lei_a")
#     lei_b: Optional[int] = Field(default=..., title="lei_b")
#     device_id: Optional[int] = Field(default=..., title="device_id")
#     product_id: Optional[int] = Field(default=..., title="product_id")
#     product_qty: Optional[float] = Field(default=..., title="product_qty")
#     color_price: Optional[float] = Field(default=..., title="color_price")
#     unit_price: Optional[float] = Field(default=..., title="unit_price")
#     bi_zhi: Optional[int] = Field(default=..., title="bi_zhi")
#     fen: Optional[int] = Field(default=..., title="fen")
#     sum_price: Optional[int] = Field(default=..., title="sum_price")
#     detail_json: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
#     status: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
#     product_qty_sum: Optional[float] = Field(default=..., title="product_qty_sum")
#     unit_price_sum: Optional[float] = Field(default=..., title="unit_price_sum")

# --------------------------------------------------------------------------------------
    
class PetrolModel(SQLModel, table=True):
    __tablename__ = 'petrol'
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    petrol_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'petrol'::text || lpad(nextval('petrol_sequence'::regclass)::text, 8, '0'::text)")))
    datetime: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="datetime"
    )  
    data_details: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
    status_details: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
    update_time: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="update_time"
    )  
    datetime_bill: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="datetime_bill"
    )  

# --------------------------------------------------------------------------------------

class PetrolModel2(SQLModel, table=True):
    __tablename__ = 'petrol2'
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    petrol_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'petrol'::text || lpad(nextval('petrol_sequence'::regclass)::text, 8, '0'::text)")))
    datetime: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="datetime"
    )  
    data_details: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
    status_details: Optional[Dict] = Field(default={}, sa_column=Column(JSON))

# --------------------------------------------------------------------------------------
    
class TypeCar(SQLModel, table=True):
    __tablename__ = 'car_type'
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    jia_yi_id: Optional[int] = Field(default=..., title="jia_yi_id")
    data_details: Optional[Dict] = Field(default={}, sa_column=Column(JSON))

# --------------------------------------------------------------------------------------
    
class TypeLei(SQLModel, table=True):
    __tablename__ = 'lei_type'
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    lei_a: Optional[int] = Field(default=..., title="lei_a")
    lei_b: Optional[int] = Field(default=..., title="lei_b")
    lei_name: Optional[str] = Field(default=..., title="lei_name")
    data_sub: Optional[Dict] = Field(default={}, sa_column=Column(JSON))

# --------------------------------------------------------------------------------------
    
class TypeBi_zhi(SQLModel, table=True):
    __tablename__ = 'bi_zhi'
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    bi_zhi: Optional[int] = Field(default=..., title="bi_zhi")
    idname: Optional[str] = Field(default=..., title="idname")

# --------------------------------------------------------------------------------------
    
class Calculate(SQLModel, table=True):
    __tablename__ = 'petrol_calculate'
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    # petrol_id: Optional[str] = Field(default=..., title="petrol_id")
    petrol_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True)))
    calOne: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
    calTwo: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
    type: Optional[str] = Field(default=..., title="type")

# --------------------------------------------------------------------------------------
    
class Calculate2(SQLModel, table=True):
    __tablename__ = 'petrol_calculate_2'
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    datetime: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="datetime"
    )  
    oneMonth: Optional[Dict] = Field(default={}, sa_column=Column(JSON))