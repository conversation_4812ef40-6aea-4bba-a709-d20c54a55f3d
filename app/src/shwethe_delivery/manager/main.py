from fastapi import FastAPI, APIRouter, Body, Response,BackgroundTasks,Header
from pydantic import BaseModel
from helper import generate_id,generate_datetime
from typing import List,Optional
import pandas as pd
import requests
import datetime
import numpy as np
import json
import time
router = APIRouter()




@router.get("/list_data", status_code = 200)
def delivery():

    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api


    try:

        def get_data():
            sql_insert = """
            select * from delivery_insert where shu_riqi_datetime > current_date - 2 
            """ 
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)
            FD100003 = FD100003.replace(np.nan, 0)
            FD100003 = FD100003.loc[FD100003['product_qty'] == 0.0]
            # FD100003['ANS'] = (FD100003['data_sub.product_qty_a'] - FD100003['data_sub.product_qty_b'])
            # FD100003 = FD100003.loc[(FD100003['ANS'] != 0.0)|(FD100003['data_sub.product_qty_b_pre'] == 0.0)]
            return FD100003

        def get_data_api(res_1,str):
                # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            print(res_1)
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})

            GF10001 = pd.DataFrame(json.loads(r.json()))

            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            # print(GF10001)

            return GF10001

        def get_data_jia_yi_name(res_1,str):
                # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))

            r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                        json={"data_api": res_1})

            GF10001 = pd.DataFrame(json.loads(r.json()))

            GF10001 = GF10001.rename(columns={'jia_yi_id': str,'jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mm_name'})
            print(GF10001)

            return GF10001

        GG100001 = get_data()

        ER100001 = get_data_api(GG100001,"product")

        CC100001 = GG100001.merge(ER100001,
                        left_on=['product_id'],right_on = ['product_id'], how='inner')
        CC100001 = CC100001.rename(columns={'jia_yi_fang_a': 'jia_yi_id'})

        YU100001 = get_data_jia_yi_name(CC100001,"jia_yi_fang_a")

        CC100001 = CC100001.merge(YU100001,
                left_on=['jia_yi_id'],right_on = ['jia_yi_fang_a'], how='inner')

        CC100001 = CC100001.drop(columns=['jia_yi_id'])      

        CC100001 = CC100001.rename(columns={'jia_yi_fang_b': 'jia_yi_id'})
        YU100001 = get_data_jia_yi_name(CC100001,"jia_yi_fang_b") 

        CC100001 = CC100001.merge(YU100001,
                left_on=['jia_yi_id'],right_on = ['jia_yi_fang_b'], how='inner')  

        CC100001 = CC100001.drop(columns=['jia_yi_id'])   


        CC100001['shu_riqi_datetime'] = CC100001['shu_riqi_datetime'].astype(str)
        CC100001 = CC100001.sort_values(['shu_riqi_datetime'], ascending=[False])
              

        GG100002 = CC100001.to_json(orient='records')
        return json.loads(GG100002)
    finally:
        psycopg2_conn_shwethe_delivery.close()