from fastapi import FastAPI, APIRouter, Body, Response,BackgroundTasks,Header
from pydantic import BaseModel
from helper import generate_id,generate_datetime
from typing import List,Optional
import pandas as pd
import requests
import datetime
import json
import time
router = APIRouter()

class che_liang_(BaseModel):
    lei_a : int = 22
    lei_b : int = 22
    jia_yi_fang_a : int
    jia_yi_fang_b : int 
    data_sub : dict = Body(...)


@router.post("/delivery/{fen}/{product_id}", status_code = 200)
def delivery(fen:int,product_id:int,req_body : che_liang_ = Body(...)):

    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery


    A100001 = datetime.datetime.now()
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def insert_data(product_id,lei_a,lei_b,jia_yi_fang_a,jia_yi_fang_b,shu_riqi_datetime,data_sub):
            sql_insert = """ insert into delivery_insert(product_id,lei_a,lei_b,jia_yi_fang_a,jia_yi_fang_b,shu_riqi_datetime,data_sub) values (%s,%s,%s,%s,%s,%s,%s)  """
            cur.execute(sql_insert, (product_id,lei_a,lei_b,jia_yi_fang_a,jia_yi_fang_b,shu_riqi_datetime,data_sub))
            return "aaaa"

        insert_data(product_id,FFF['lei_a'],FFF['lei_b'],FFF['jia_yi_fang_a'],FFF['jia_yi_fang_b'],A100001,json.dumps(FFF['data_sub']))
        return "json.loads(GF10001)"


    finally:
        psycopg2_conn_shwethe_delivery.close()

class che_liang_A_put_json(BaseModel):
    product_qty_a: float

class che_liang_A(BaseModel):
    auto_id : int
    jia_yi_fang_a : int
    jia_yi_fang_b : int 
    data_sub : che_liang_A_put_json


@router.put("/delivery/{fen}/{product_id}", status_code = 200)
def delivery(fen:int,product_id:int,req_body : che_liang_A = Body(...)):

    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery


    A100001 = datetime.datetime.now()
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def update_data(data_sub,jia_yi_fang_a,jia_yi_fang_b,auto_id):
            sql_insert = """ update delivery_insert set data_sub = data_sub || %s ::jsonb ,  jia_yi_fang_a = %s , jia_yi_fang_b = %s where auto_id = '%s' and data_sub ->> 'status' != 'sucess'  """ 
            cur.execute(sql_insert, (data_sub,jia_yi_fang_a,jia_yi_fang_b,auto_id))
            return "aaaa"

        update_data(json.dumps(FFF['data_sub']),FFF['jia_yi_fang_a'],FFF['jia_yi_fang_b'],FFF['auto_id'])
        return "json.loads(GF10001)"


    finally:
        psycopg2_conn_shwethe_delivery.close()


class delivery_jia_yi_fang_a_json(BaseModel):
    product_qty_a: float
    fen_id: int


class delivery_jia_yi_fang_a(BaseModel):
    auto_id : int
    jia_yi_fang_a : int
    data_sub : delivery_jia_yi_fang_a_json


@router.put("/delivery/jia_yi_fang_a", status_code = 200)
def delivery(req_body : delivery_jia_yi_fang_a = Body(...)):

    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery


    A100001 = datetime.datetime.now()
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def update_data(data_sub,jia_yi_fang_a,auto_id):
            sql_insert = """ update delivery_insert set data_sub = data_sub || %s ::jsonb ,  jia_yi_fang_a = %s  where auto_id = '%s' and data_sub ->> 'status' != 'sucess'  """ 
            cur.execute(sql_insert, (data_sub,jia_yi_fang_a,auto_id))
            return "aaaa"

        update_data(json.dumps(FFF['data_sub']),FFF['jia_yi_fang_a'],FFF['auto_id'])
        return "json.loads(GF10001)"


    finally:
        psycopg2_conn_shwethe_delivery.close()







class delivery_jia_yi_fang_b(BaseModel):
    auto_id : int
    jia_yi_fang_b : int


@router.put("/delivery/jia_yi_fang_b", status_code = 200)
def delivery(req_body : delivery_jia_yi_fang_b = Body(...)):

    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery


    A100001 = datetime.datetime.now()
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def update_data(jia_yi_fang_b,auto_id):
            sql_insert = """ update delivery_insert set jia_yi_fang_b = %s  where auto_id = '%s' and data_sub ->> 'status' != 'sucess'   """ 
            cur.execute(sql_insert, (jia_yi_fang_b,auto_id))
            return "aaaa"

        update_data(FFF['jia_yi_fang_b'],FFF['auto_id'])
        return "json.loads(GF10001)"


    finally:
        psycopg2_conn_shwethe_delivery.close()







@router.get("/delivery", status_code = 200)
def delivery(fen:int):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    

    A100001 = datetime.datetime.now()
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001

        def get_jia_yi_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                        json={"data_api": res_1})
            try:
                GF10001 = pd.DataFrame(json.loads(r.json()))
            except :
                GF10001 = pd.DataFrame(r.json())
            GF10001 = GF10001.rename(columns={'jia_yi_id': str,'jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mm_name'})
            print(GF10001)    
            # GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001

        def get_data(fen):
            sql_insert = """
            select * from delivery_insert where che_ci = 0 and shu_riqi_datetime > current_date  and data_sub ->> 'fen_id' != '%s'
            """ % (fen)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)

            return FD100003

        GG100001 = get_data(fen)
        try:
            H1000001 = GG100001[['product_id']]
            H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
            FG10001 = get_data_api(H1000001,"product")
            GG100009 = FG10001.merge(GG100001, on=['product_id'] , how='inner')
            GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)

            
            try:
                H1000001 = GG100009[['jia_yi_fang_a']]
                H1000001 = H1000001.rename(columns={'jia_yi_fang_a': 'jia_yi_id'})
                FG10001 = get_jia_yi_api(H1000001,"jia_yi_fang_a")

                GG100009 = FG10001.merge(GG100009, on=['jia_yi_fang_a'] , how='right')

                print(GG100009)
                print("GG100009")


                H1000001 = GG100009[['jia_yi_fang_b']]
                H1000001 = H1000001.rename(columns={'jia_yi_fang_b': 'jia_yi_id'})
                FG10001 = get_jia_yi_api(H1000001,"jia_yi_fang_b")

                GG100009 = FG10001.merge(GG100009, on=['jia_yi_fang_b'] , how='right')
                
                GG100011 = GG100009.to_json(orient='records')
                return json.loads(GG100011)
            except:
                GG100011 = GG100009.to_json(orient='records')
                return json.loads(GG100011)
        except:
            return []

    finally:
        psycopg2_conn_shwethe_delivery.close()


@router.get("/delivery_che_ci/{che_ci}", status_code = 200)
def delivery(che_ci:int):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    

    A100001 = datetime.datetime.now()
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001

        def get_jia_yi_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                        json={"data_api": res_1})
            try:
                GF10001 = pd.DataFrame(json.loads(r.json()))
            except :
                GF10001 = pd.DataFrame(r.json())
            GF10001 = GF10001.rename(columns={'jia_yi_id': str,'jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mm_name'})
            print(GF10001)    
            # GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001

        def get_data(che_ci):
            sql_insert = """
            select * from delivery_insert where che_ci = %s and shu_riqi_datetime > current_date
            """ % (che_ci,)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            print(FF100001)
            print("FF100001")
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)

            return FD100003

        GG100001 = get_data(che_ci)

        try:
            H1000001 = GG100001[['product_id']]
        except:
            GG100001 = pd.concat([GG100001,GG100001])
            H1000001 = GG100001[['product_id']]

        H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
        FG10001 = get_data_api(H1000001,"product")
        GG100009 = FG10001.merge(GG100001, on=['product_id'] , how='inner')
        GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)

        
        try:
            H1000001 = GG100009[['jia_yi_fang_a']]
            H1000001 = H1000001.rename(columns={'jia_yi_fang_a': 'jia_yi_id'})
            FG10001 = get_jia_yi_api(H1000001,"jia_yi_fang_a")

            GG100009 = FG10001.merge(GG100009, on=['jia_yi_fang_a'] , how='right')
        except:
            pass
            # GG100009 = GG100009
            # print(GG100009)
            # print("GG100009")

        try:
            H1000001 = GG100009[['jia_yi_fang_b']]
            H1000001 = H1000001.rename(columns={'jia_yi_fang_b': 'jia_yi_id'})
            FG10001 = get_jia_yi_api(H1000001,"jia_yi_fang_b")

            GG100009 = FG10001.merge(GG100009, on=['jia_yi_fang_b'] , how='right')
        except:
            pass
        
        GG100011 = GG100009.to_json(orient='records')
        return json.loads(GG100011)

    finally:
        psycopg2_conn_shwethe_delivery.close()


@router.get("/delivery_info", status_code = 200)
def delivery_info():
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api

    A100001 = datetime.datetime.now()


    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v2/search/jia_yi_name_list_id',
                        json={"data_api": res_1})
            print(r)
            GF10001 = pd.DataFrame(r.json())
            GF10001 = GF10001.rename(columns={'jia_yi_id': str+'_id','jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mm_name'})
            return GF10001

        def re_get_data_api():
            print(mongodb_data_api + '/api/v1/user_info/user_info')
            r = requests.get(mongodb_data_api + '/api/v1/user_info/user_info')
            print(r)
            GF10001 = pd.DataFrame(r.json())

            return GF10001

        def get_data():
            sql_insert = """
            select * from delivery_insert where   shu_riqi_datetime > current_date
            """ 
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)

            return FD100003
        GG100001 = get_data()
        GG200001 = re_get_data_api()
        print(GG100001)
        print(GG200001)



        H1000001 = GG200001[['warehouse_id']]
        H1000001 = H1000001.rename(columns={'warehouse_id': 'jia_yi_id'})
        FG10001 = get_data_api(H1000001,"warehouse")
        GG100009 = FG10001.merge(GG200001, on=['warehouse_id'] , how='inner')
        GG100011 = GG100009.to_json(orient='records')
        return json.loads(GG100011)

    finally:
        psycopg2_conn_shwethe_delivery.close()



@router.get("/delivery_a/{fen}/{jia_yi_id}", status_code = 200)
def delivery(fen:int=0,jia_yi_id:int=0):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    
    print(fen)
    
    A100001 = datetime.datetime.now()
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001

        def get_data(jia_yi_id):
            sql_insert = """
            select * from delivery_insert where jia_yi_fang_a = %s and shu_riqi_datetime > current_date
            """ % (jia_yi_id)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)

            return FD100003

        GG100001 = get_data(jia_yi_id)
        try:
            H1000001 = GG100001[['product_id']]
            H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
            FG10001 = get_data_api(H1000001,"product")
            GG100009 = FG10001.merge(GG100001, on=['product_id'] , how='inner')
            GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)
            
            GG100011 = GG100009.to_json(orient='records')
            return json.loads(GG100011)
        except:
            return []

    finally:
        psycopg2_conn_shwethe_delivery.close()

class delivery_a_put_json(BaseModel):
    product_qty_a: float


class delivery_a_put(BaseModel):
    auto_id : int
    product_id : int 
    data_sub : delivery_a_put_json

@router.put("/delivery_a/{fen}/{jia_yi_id}", status_code = 200)
def delivery_a(fen:int=0,jia_yi_id:int=0,req_body : delivery_a_put = Body(...)):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    

    A100001 = datetime.datetime.now()
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    jia_yi_fang_a = jia_yi_id
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def update_data(jia_yi_fang_a,auto_id,json):
            sql_insert = """ update delivery_insert set data_sub = data_sub || %s ::jsonb  where auto_id = %s and data_sub ->> 'status' != 'sucess'  """ 
            cur.execute(sql_insert, (json,auto_id))
            return "aaaa"
        print(json.dumps(FFF['data_sub']))
        update_data(jia_yi_fang_a,FFF['auto_id'],json.dumps(FFF['data_sub']))
        
        return "json.loads(GG100011)"

    finally:
        psycopg2_conn_shwethe_delivery.close()
    

@router.get("/delivery/car_id", status_code = 200)
def delivery():
    import src.Connect.https_connect as shwethe_n_api
    import importlib
    importlib.reload(shwethe_n_api)
    from src.Connect.https_connect import shwethe_n_api
    try:
        r = requests.get(shwethe_n_api + '/api/v2/order_list/add_checi')
        GF10001 = pd.DataFrame(json.loads(r.json()))
        GF10002 = GF10001.to_json(orient='records')
        return json.loads(GF10002)
    except:
        return []




@router.get("/delivery/che_ci_on_table", status_code = 200)
def delivery():
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    

    A100001 = datetime.datetime.now()
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001
        def get_data():
            sql_insert = """
            select che_ci,che_liang from delivery_insert where che_ci != 0 and shu_riqi_datetime > current_date
            """ 
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF100001['count_data'] = 1
            FF100002=FF100001.groupby(['che_ci','che_liang'])['count_data'].sum().reset_index()
            FF100002 = FF100002.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)
            return FD100003
        GG100001 = get_data()
        # H1000001 = GG100001[['product_id']]
        # H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
        # FG10001 = get_data_api(H1000001,"product")
        # GG100009 = FG10001.merge(GG100001, on=['product_id'] , how='inner')
        # GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)
        GG100011 = GG100001.to_json(orient='records')
        return json.loads(GG100011)

    finally:
        psycopg2_conn_shwethe_delivery.close()



class delivery_che_ci_che_ci(BaseModel):
    auto_id : int
    che_ci : int 
    che_liang: int

@router.put("/delivery/che_ci/{fen}/{product_id}", status_code = 200)
def delivery_che_ci(fen:int,product_id:int,req_body : delivery_che_ci_che_ci = Body(...)):
    # print(fen)
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery

    A100001 = datetime.datetime.now()
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    try:
        
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def update_data(che_ci,che_liang,auto_id):
            sql_insert = """ update delivery_insert set  che_ci = %s,che_liang = %s where auto_id = '%s' and data_sub ->> 'status' != 'sucess'  """ 
            cur.execute(sql_insert, (che_ci,che_liang,auto_id))
            return "aaaa"

        update_data(FFF['che_ci'],FFF['che_liang'],FFF['auto_id'])

        return "json.loads(GG100011)"

    finally:
        psycopg2_conn_shwethe_delivery.close()


@router.get("/delivery/qty/{product_id}", status_code = 200)
def delivery_qty(product_id:str):

    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(mongodb_data_api)
    from src.Connect.https_connect import mongodb_data_api


    try:
        r = requests.get(mongodb_data_api + '/api/v1/search/qty/'+product_id)

        r2 = requests.get(mongodb_data_api + '/api/v1/search/qty_not_lei/'+product_id)


        GF10001 = pd.DataFrame(r.json())

        GF20001 = pd.DataFrame(r2.json())
        print(GF10001)
        print(GF20001)
        try:
            # try:
            GF10001 = pd.concat([GF10001,GF20001]).drop_duplicates()
            GF10001 = GF10001.loc[GF10001['type'] == 0]

            GF10001 = GF10001.sort_values(['product_id','product_qty'], ascending=[False,False])

            GF10001 = GF10001.drop_duplicates(subset=['product_id','fen'], keep='first')


            # except:
            #     GF10001 = pd.DataFrame(r.json())
            print("GF10001")
            print(GF10001)
            print(GF20001)

            GF10002 = GF10001.to_json(orient='records')

            return json.loads(GF10002)
        
        except:
            return []

    finally:
        "psycopg2_conn_shwethe_delivery.close()"



@router.get("/product_search_text", status_code = 200)
def product_search_text(text : str):

    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(https_connect)
    from src.Connect.https_connect import mongodb_data_api
    params = {'text':text}
    try:
        r = requests.get(mongodb_data_api + '/api/v1/search/product_search_text',params=params
                            )

        GF10001 = pd.DataFrame(r.json())

        GF10001 = GF10001.loc[(GF10001['product_idname'] == text)|(GF10001['product_idname'] == text.upper())]

        GF10001 = GF10001.to_json(orient='records')
        return json.loads(GF10001)
    except:
        return []
    


@router.get("/delivery_b/{fen}/{jia_yi_id}", status_code = 200)
def delivery(fen:int=0,jia_yi_id:int=0):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    

    A100001 = datetime.datetime.now()
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001

        def get_data(jia_yi_id):
            sql_insert = """
            select * from delivery_insert where jia_yi_fang_b = %s and shu_riqi_datetime > current_date 
            """ % (jia_yi_id)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)

            return FD100003
        try:
            GG100001 = get_data(jia_yi_id)
            H1000001 = GG100001[['product_id']]
            H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
            FG10001 = get_data_api(H1000001,"product")
            GG100009 = FG10001.merge(GG100001, on=['product_id'] , how='inner')
            GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)
            
            GG100011 = GG100009.to_json(orient='records')
            return json.loads(GG100011)
        except:
            return []
    finally:
        psycopg2_conn_shwethe_delivery.close()

class delivery_b_pre_put_json(BaseModel):
    product_qty_b_pre: float


class delivery_b_pre_put(BaseModel):
    auto_id : int
    product_id : int 
    data_sub : delivery_b_pre_put_json

@router.put("/delivery_b_pre/{fen}/{jia_yi_id}", status_code = 200)
def delivery_b_pre(fen:int=0,jia_yi_id:int=0,req_body : delivery_b_pre_put = Body(...)):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    

    A100001 = datetime.datetime.now()
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    jia_yi_fang_a = jia_yi_id
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def update_data(jia_yi_fang_a,auto_id,json):
            sql_insert = """ update delivery_insert set data_sub = data_sub || %s ::jsonb  where auto_id = %s and data_sub ->> 'status_a' != 'sucess'  """ 
            cur.execute(sql_insert, (json,auto_id))
            return "aaaa"
        print(json.dumps(FFF['data_sub']))
        update_data(jia_yi_fang_a,FFF['auto_id'],json.dumps(FFF['data_sub']))
        
        return "json.loads(GG100011)"

    finally:
        psycopg2_conn_shwethe_delivery.close()

class delivery_b_put_json(BaseModel):
    product_qty_b: float


class delivery_b_put(BaseModel):
    auto_id : int
    product_id : int 
    product_qty:float
    data_sub : delivery_b_put_json

@router.put("/delivery_b/{fen}/{jia_yi_id}", status_code = 200)
def delivery_b(fen:int=0,jia_yi_id:int=0,req_body : delivery_b_put = Body(...)):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api

    A100001 = datetime.datetime.now()
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    jia_yi_fang_a = jia_yi_id
    try:
            
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def update_data(product_qty,auto_id,json):
            sql_insert = """ update delivery_insert set data_sub = data_sub || %s ::jsonb ,product_qty = %s where auto_id = %s  and data_sub ->> 'status' != 'sucess' """ 
            cur.execute(sql_insert, (json,product_qty,auto_id))
            return "aaaa"
        print(json.dumps(FFF['data_sub']))
        update_data(FFF['product_qty'],FFF['auto_id'],json.dumps(FFF['data_sub']))
        return "json.loads(GG100011)"
    finally:
        psycopg2_conn_shwethe_delivery.close()




@router.get("/delivery_b_pre/{fen}/{jia_yi_id}", status_code = 200)
def delivery_b_pre(fen:int=0,jia_yi_id:int=0):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    

    A100001 = datetime.datetime.now()
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            
            return GF10001

        def get_data(jia_yi_id):
            sql_insert = """
            select * from delivery_insert where jia_yi_fang_b = %s and shu_riqi_datetime > current_date 
            """ % (jia_yi_id)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)

            return FD100003
            
        try:
            GG100001 = get_data(jia_yi_id)
            H1000001 = GG100001[['product_id']]
            H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
            FG10001 = get_data_api(H1000001,"product")
            GG100009 = FG10001.merge(GG100001, on=['product_id'] , how='inner')
            GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)
            GG100009 = GG100009.sort_values(['data_sub.product_qty_b_pre'], ascending=[True])
            GG100011 = GG100009.to_json(orient='records')
            return json.loads(GG100011)
        except:
            return []
    finally:
        psycopg2_conn_shwethe_delivery.close()
        
        
        
@router.get("/everyMonth", status_code = 200)
def shwethe_deliveryGoods(day:int=None):
    
    
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    

    
    try:
        def get_data():
        
            sql_insert = """
                select * from delivery_insert where shu_riqi_datetime > current_date
                """ 
                
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)
            
            print(FD100003)
            
            return FD100003
        
        FD100003 = get_data()
        
    except :
        FD100003 = pd.DataFrame()
        
        print(FD100003)
    finally:
        psycopg2_conn_shwethe_delivery.close()
        
        
    from src.common.http.mongodbApi.main import(
        get_everyMonth_for_deleverly
    )
    # A10001 = get_everyMonth_for_deleverly().head()
    A10001 = get_everyMonth_for_deleverly()
    
    if day != None:     
        
        A10001 = A10001.loc[A10001['day'] < 15]
        
    B10001 = A10001.loc[A10001['day'] > 30]
        
    A10001 = A10001.loc[A10001['day'] < 15]
    
    A10001 = A10001[A10001['product_id'].isin (B10001['product_id'].tolist())]
    
    if not FD100003.empty:
        A10001 = A10001[~A10001.set_index(['product_id','fen']).index.isin(FD100003.set_index(['product_id','data_sub.fen_id']).index)]
        
    A10001   = A10001.sort_values(['per_day'], ascending=[False])
    
    GG100011 = A10001.to_json(orient='records')
    
    return json.loads(GG100011)


@router.get("/everyMonthBy", status_code = 200)
def shwethe_deliveryGoods(fen:int,product_id:int):
    from src.common.http.mongodbApi.main import(
        get_everyMonth_for_deleverly_by
    )
    # A10001 = get_everyMonth_for_deleverly().head()
    A10001 = get_everyMonth_for_deleverly_by(str(fen),str(product_id))
    
    GG100011 = A10001.to_json(orient='records')
    
    return json.loads(GG100011)
