from fastapi import FastAPI, APIRouter, Body, Response,BackgroundTasks,Header
from pydantic import BaseModel
from helper import generate_id,generate_datetime
from typing import List,Optional
import pandas as pd
import requests
import datetime
import json
import time
router = APIRouter()

@router.get("/delivery", status_code = 200)
def delivery(fen:int):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api

    A100001 = datetime.datetime.now()
    try:
    
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001

        def get_jia_yi_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                        json={"data_api": res_1})
            try:
                GF10001 = pd.DataFrame(json.loads(r.json()))
            except :
                GF10001 = pd.DataFrame(r.json())
            GF10001 = GF10001.rename(columns={'jia_yi_id': str,'jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mm_name'})
            print(GF10001)    
            # GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001

        def get_data_a(fen):
            sql_insert = """
            select * from delivery_insert where che_ci = 0 and shu_riqi_datetime > current_date  and data_sub ->> 'fen_id' = '%s'  and data_sub ->> 'status_a_b' = 'wait_sucess' and data_sub ->> 'from_data' != 'ai'
            """ % (fen)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)

            return FD100003

        def get_data_b(fen):
            sql_insert = """
            select * from delivery_insert where che_ci = 0 and shu_riqi_datetime > current_date  and  data_sub ->> 'fen_id_a' = '%s' and data_sub ->> 'status_a_b' = 'sucess' and data_sub ->> 'from_data' != 'ai'
            """ % (fen)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)

            return FD100003

        def get_data_3(fen):
            sql_insert = """
            select * from delivery_insert where che_ci = 0 and shu_riqi_datetime > current_date  and data_sub ->> 'fen_id' != '%s'  and data_sub ->> 'status_a_b' = 'wait_sucess' and data_sub ->> 'from_data' = 'ai'
            """ % (fen)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)

            return FD100003

        def get_data_4(fen):
            sql_insert = """
            select * from delivery_insert where che_ci = 0 and shu_riqi_datetime > current_date  and data_sub ->> 'fen_id' = '%s' 
            """ % (fen)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)

            return FD100003



        GG100001 = get_data_a(fen)
        GG100002 = get_data_b(fen)
        GG100003 = get_data_3(fen)
        GG100004 = get_data_4(fen)


        GG100001 = pd.concat([GG100001,GG100002,GG100003,GG100004]).drop_duplicates()



        try:
            H1000001 = GG100001[['product_id']]
            H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
            FG10001 = get_data_api(H1000001,"product")
            GG100009 = FG10001.merge(GG100001, on=['product_id'] , how='inner')
            GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)

            
            try:
                H1000001 = GG100009[['jia_yi_fang_a']]
                H1000001 = H1000001.rename(columns={'jia_yi_fang_a': 'jia_yi_id'})
                FG10001 = get_jia_yi_api(H1000001,"jia_yi_fang_a")

                GG100009 = FG10001.merge(GG100009, on=['jia_yi_fang_a'] , how='right')

                print(GG100009)
                print("GG100009")


                H1000001 = GG100009[['jia_yi_fang_b']]
                H1000001 = H1000001.rename(columns={'jia_yi_fang_b': 'jia_yi_id'})
                FG10001 = get_jia_yi_api(H1000001,"jia_yi_fang_b")

                GG100009 = FG10001.merge(GG100009, on=['jia_yi_fang_b'] , how='right')
                
                GG100011 = GG100009.to_json(orient='records')
                return json.loads(GG100011)
            except:
                GG100011 = GG100009.to_json(orient='records')
                return json.loads(GG100011)
        except:
            return []

    finally:
        psycopg2_conn_shwethe_delivery.close()



class status_a_b_(BaseModel):
    status_a_b : str


class jia_yi_fang_a_status_a_b(BaseModel):
    auto_id : int
    data_sub : status_a_b_


@router.put("/delivery/jia_yi_fang_a/status_a_b", status_code = 200)
def jia_yi_fang_a_status_a_b(req_body : jia_yi_fang_a_status_a_b = Body(...)):

    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery


    A100001 = datetime.datetime.now()
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def update_data(data_sub,auto_id):
            sql_insert = """ update delivery_insert set data_sub = data_sub || %s ::jsonb   where auto_id = '%s' and data_sub ->> 'status' != 'sucess'  """ 
            cur.execute(sql_insert, (data_sub,auto_id))
            return "aaaa"

        update_data(json.dumps(FFF['data_sub']),FFF['auto_id'])
        return "json.loads(GF10001)"


    finally:
        psycopg2_conn_shwethe_delivery.close()


class delivery_jia_yi_fang_a_json(BaseModel):
    product_qty_a: float
    fen_id_a: int
    status_a_b : str



class delivery_jia_yi_fang_a(BaseModel):
    auto_id : int
    jia_yi_fang_a : int
    data_sub : delivery_jia_yi_fang_a_json


@router.put("/delivery/jia_yi_fang_a", status_code = 200)
def delivery(req_body : delivery_jia_yi_fang_a = Body(...)):

    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery


    A100001 = datetime.datetime.now()
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def update_data(data_sub,jia_yi_fang_a,auto_id):
            sql_insert = """ update delivery_insert set data_sub = data_sub || %s ::jsonb ,  jia_yi_fang_a = %s  where auto_id = '%s' and data_sub ->> 'status' != 'sucess'  """ 
            cur.execute(sql_insert, (data_sub,jia_yi_fang_a,auto_id))
            return "aaaa"

        update_data(json.dumps(FFF['data_sub']),FFF['jia_yi_fang_a'],FFF['auto_id'])
        return "json.loads(GF10001)"


    finally:
        psycopg2_conn_shwethe_delivery.close()



@router.get("/delivery/che_ci_on_table/{fen}", status_code = 200)
def delivery(fen:int):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    

    A100001 = datetime.datetime.now()
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001
        def get_data(fen):
            sql_insert = """
            select che_ci,che_liang from delivery_insert where che_ci != 0 and shu_riqi_datetime > current_date and data_sub ->> 'fen_id_a' = '%s'
            """ % (fen)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF100001['count_data'] = 1
            FF100002=FF100001.groupby(['che_ci','che_liang'])['count_data'].sum().reset_index()
            FF100002 = FF100002.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)
            return FD100003
        GG100001 = get_data(fen)
        # H1000001 = GG100001[['product_id']]
        # H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
        # FG10001 = get_data_api(H1000001,"product")
        # GG100009 = FG10001.merge(GG100001, on=['product_id'] , how='inner')
        # GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)
        GG100011 = GG100001.to_json(orient='records')
        return json.loads(GG100011)

    finally:
        psycopg2_conn_shwethe_delivery.close()


@router.get("/delivery/qty/{product_id}", status_code = 200)
def delivery_qty(product_id:str):

    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(mongodb_data_api)
    from src.Connect.https_connect import mongodb_data_api


    try:
        r = requests.get(mongodb_data_api + '/api/v1/search/qty/'+product_id)

        r2 = requests.get(mongodb_data_api + '/api/v1/search/qty_not_lei/'+product_id)


        GF10001 = pd.DataFrame(r.json())

        GF20001 = pd.DataFrame(r2.json())
        print(GF10001)
        print(GF20001)
        # try:
        # try:
        GF10001 = pd.concat([GF10001,GF20001,GF10001,GF20001])
        GF10001 = GF10001.loc[GF10001['type'] == 0]

        GF10002 = GF10001.sort_values(['product_id','fen'], ascending=[False,False])

        GF10003 = GF10002.drop_duplicates(subset=['product_id','fen'], keep='first')
        GF10003


        # except:
        #     GF10001 = pd.DataFrame(r.json())
        print("GF10001")
        print(GF10001)
        print(GF20001)

        GF10002 = GF10003.to_json(orient='records')

        return json.loads(GF10002)
        
        # except:
        #     return []

    finally:
        "psycopg2_conn_shwethe_delivery.close()"


@router.get("/delivery_b_pre/{fen}/{jia_yi_id}", status_code = 200)
def delivery_b_pre(fen:int=0,jia_yi_id:int=0):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    

    A100001 = datetime.datetime.now()
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            
            return GF10001

        def get_data(jia_yi_id,fen):
            sql_insert = """
            select * from delivery_insert where jia_yi_fang_b = %s and shu_riqi_datetime > current_date 
            """ % (jia_yi_id)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF100001['drop_type'] = 1

            sql_insert = """
            select * from delivery_insert where  shu_riqi_datetime > current_date and data_sub ->> 'pre_qty' =  'true' and data_sub ->> 'fen_id_b' =  '%s'
            """ % (fen)
            FF200001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF200001['drop_type'] = 2

            FF100001 = pd.concat([FF100001,FF200001])
            FF100001 = FF100001.sort_values(['auto_id','product_id','drop_type'], ascending=[False,False,False])

            FF100001 = FF100001.drop_duplicates(subset=['auto_id','product_id'], keep='first')

            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)
            return FD100003
            
        try:
            GG100001 = get_data(jia_yi_id,fen)
            H1000001 = GG100001[['product_id']]
            H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
            FG10001 = get_data_api(H1000001,"product")
            GG100009 = FG10001.merge(GG100001, on=['product_id'] , how='inner')
            GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)
            GG100009 = GG100009.sort_values(['data_sub.product_qty_b_pre'], ascending=[True])
            GG100011 = GG100009.to_json(orient='records')
            return json.loads(GG100011)
        except:
            return []
    finally:
        psycopg2_conn_shwethe_delivery.close()



class delivery_b_pre_put_json(BaseModel):
    product_qty_b_pre: float


class delivery_b_pre_put(BaseModel):
    auto_id : int
    product_id : int 
    data_sub : delivery_b_pre_put_json

@router.put("/delivery_b_pre/{fen}/{jia_yi_id}", status_code = 200)
def delivery_b_pre(fen:int=0,jia_yi_id:int=0,req_body : delivery_b_pre_put = Body(...)):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    

    A100001 = datetime.datetime.now()
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    jia_yi_fang_b = jia_yi_id
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def update_data(jia_yi_fang_b,auto_id,json):
            sql_insert = """ update delivery_insert set data_sub = data_sub || %s ::jsonb , jia_yi_fang_b = %s where auto_id = %s  and data_sub ->> 'status_a' != 'sucess'  """ 
            cur.execute(sql_insert, (json,jia_yi_fang_b,auto_id))
            return "aaaa"
        print(json.dumps(FFF['data_sub']))
        update_data(jia_yi_fang_b,FFF['auto_id'],json.dumps(FFF['data_sub']))
        
        return "json.loads(GG100011)"

    finally:
        psycopg2_conn_shwethe_delivery.close()





@router.get("/delivery_a/{fen}/{jia_yi_id}", status_code = 200)
def delivery(fen: int = 0, jia_yi_id: int = 0, page: int = 0):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    
    print(fen)

    items_per_page = 40
    offset = page * items_per_page
    
    A100001 = datetime.datetime.now()
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001

        def get_data(jia_yi_id, offset, items_per_page):

            sql_insert = """
                SELECT *, (data_sub->>'product_qty_b_pre')::double precision AS product_qty_b_pre  , (data_sub->>'product_qty_a')::double precision AS product_qty_a
                FROM delivery_insert
                WHERE jia_yi_fang_a = %s AND shu_riqi_datetime > current_date
                ORDER BY product_qty_a asc , product_qty_b_pre desc
                LIMIT %s OFFSET %s
                """ % (jia_yi_id, items_per_page, offset)

            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)

            return FD100003
        
        def get_total_pages(jia_yi_id, items_per_page):
            sql_count = """
            select count(*) from delivery_insert where jia_yi_fang_a = %s  and shu_riqi_datetime > current_date
            """ % (jia_yi_id)
            cur.execute(sql_count)
            total_records = cur.fetchone()[0]
            total_pages = (total_records + items_per_page - 1) // items_per_page

            return total_pages

        GG100001 = get_data(jia_yi_id, offset, items_per_page)
        total_pages = get_total_pages(jia_yi_id, items_per_page)


        try:
            H1000001 = GG100001[['product_id']]
            H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
            FG10001 = get_data_api(H1000001,"product")
            GG100009 = GG100001.merge(FG10001, on=['product_id'] , how='inner')
            GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)
            
            # GG100011 = GG100009.to_json(orient='records')


            return {"data": json.loads(GG100009.to_json(orient='records')), "total_pages": total_pages}
        except:
            return []

    finally:
        psycopg2_conn_shwethe_delivery.close()