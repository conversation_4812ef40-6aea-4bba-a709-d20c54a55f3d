from fastapi import FastAPI, APIRouter, Body, Response,BackgroundTasks,Header
from pydantic import BaseModel
from helper import generate_id,generate_datetime
from typing import List,Optional
import pandas as pd
import requests
import datetime
import json
import math
import time
router = APIRouter()


@router.get("/delivery_b_pre/{fen}/{jia_yi_id}", status_code = 200)
def delivery_b_pre(fen:int=0,jia_yi_id:int=0, page: int = 0, page_size: int = 10):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    

    A100001 = datetime.datetime.now()
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            
            return GF10001

        # def get_data(jia_yi_id,fen):
        #     sql_insert = """
        #     select * from delivery_insert where jia_yi_fang_b = %s and shu_riqi_datetime > current_date 
        #     """ % (jia_yi_id)
        #     FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
        #     FF100001['drop_type'] = 1

        #     sql_insert = """
        #     select * from delivery_insert where  shu_riqi_datetime > current_date and data_sub ->> 'pre_qty' =  'true' and data_sub ->> 'fen_id_b' =  '%s'
        #     """ % (fen)
        #     FF200001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
        #     FF200001['drop_type'] = 2

        #     FF100001 = pd.concat([FF100001,FF200001])
        #     FF100001 = FF100001.sort_values(['auto_id','product_id','drop_type'], ascending=[False,False,False])

        #     FF100001 = FF100001.drop_duplicates(subset=['auto_id','product_id'], keep='first')

        #     FF100002 = FF100001.to_dict('records')
        #     FD100003 = pd.json_normalize(FF100002)
        #     return FD100003

        # 改代码 

        def get_data(jia_yi_id, fen, page, page_size):
            offset = page * page_size
            sql_insert = f"""
            SELECT * FROM (
                (SELECT *, 1 AS drop_type FROM delivery_insert 
                WHERE jia_yi_fang_b = {jia_yi_id} AND shu_riqi_datetime > current_date)
                UNION ALL
                (SELECT *, 2 AS drop_type FROM delivery_insert 
                WHERE shu_riqi_datetime > current_date AND data_sub ->> 'pre_qty' = 'true' AND data_sub ->> 'fen_id_b' = '{fen}')
            ) AS combined
            ORDER BY auto_id DESC, product_id DESC, drop_type DESC
            OFFSET {offset} ROWS
            FETCH NEXT {page_size} ROWS ONLY;
            """
            
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF100001 = FF100001.drop_duplicates(subset=['auto_id','product_id'], keep='first')

            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)
            return FD100003
        
        def get_total_rows(jia_yi_id, fen):
            sql_count = f"""
            SELECT COUNT(*) FROM (
                (SELECT * FROM delivery_insert 
                WHERE jia_yi_fang_b = {jia_yi_id} AND shu_riqi_datetime > current_date)
                UNION ALL
                (SELECT * FROM delivery_insert 
                WHERE shu_riqi_datetime > current_date AND data_sub ->> 'pre_qty' = 'true' AND data_sub ->> 'fen_id_b' = '{fen}')
            ) AS combined;
            """

            count_df = pd.read_sql(sql_count, psycopg2_conn_shwethe_delivery)
            total_rows = count_df.iloc[0, 0]
            return total_rows
            
        try:
            
            # GG100001 = get_data(jia_yi_id,fen)
            GG100001 = get_data(jia_yi_id, fen, page, page_size)
            total_rows = get_total_rows(jia_yi_id, fen)
            total_pages = math.ceil(total_rows / page_size)


            H1000001 = GG100001[['product_id']]
            H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
            FG10001 = get_data_api(H1000001,"product")
            GG100009 = FG10001.merge(GG100001, on=['product_id'] , how='inner')
            GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)
            GG100009 = GG100009.sort_values(['data_sub.product_qty_b_pre'], ascending=[True])
            GG100011 = GG100009.to_json(orient='records')
            return {'data':json.loads(GG100011),'total_page' : total_pages}
        except:
            return []
    finally:
        psycopg2_conn_shwethe_delivery.close()