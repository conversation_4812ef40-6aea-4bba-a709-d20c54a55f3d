from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,or_,and_
from sqlalchemy import text
from typing import List, Optional
from src.time_zone.time_zone_function import get_datetime
# from src.config.insert_data.database import get_session as get_session_insert_data
from src.shwethe_delivery.config import get_session_insert_data as get_session_insert_data
from src.common.product_name import product_name_package
import json
from src.shwethe_delivery.deliveryGoods.models.models import (
    delivery_productgoods,
    delivery_insert,
    delete_delivery_productgoods,
    delivery_insert_post,
    delivery_not_show_post,
    delivery_not_show,
    delivery_info_parme,
    delivery_info_parme_post
)
from src.time_zone import time_zone_function
from src.common.http.mongodbApi.main import (
    delivery_not_have,
    delivery_for_sell
)
from datetime import datetime, timedelta
import numpy as np



import pandas as pd 

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def getdeletevery(fen:int,db: Session = Depends(get_session_insert_data)):
    from datetime import datetime, timedelta
    # 获取今天的日期
    today = datetime.now().date()
    # 获取明天的日期
    tomorrow = today + timedelta(days=1)
    # 将日期格式化为字符串
    today_str = today.strftime('%Y-%m-%d')
    tomorrow_str = tomorrow.strftime('%Y-%m-%d')
    
    engine = db.get_bind()
    with engine.connect() as con:
        # 执行查询
        A200001 = pd.read_sql(text(
            "select * from delivery_insert where shu_riqi_datetime >= '%s' and shu_riqi_datetime < '%s' and data_sub ->> 'fen_id_a' = '%s' and che_ci = 0" % (today_str, tomorrow_str, fen) ),con)

    if A200001.empty:
        return []

    A200001 = pd.json_normalize(A200001.to_dict(orient="records"))
    A200001.columns = A200001.columns.str.replace('data_sub.', 'data_sub_')
    A200001 = A200001.replace(np.nan, 0)

    Function = product_name_package()
    C100001 = Function.select_product_list_id_with_http(nameStr='product',df=A200001[['product_id']])

    BV10001 = A200001.merge(C100001, on=['product_id'], how='right')

    BV10001 = BV10001.replace(np.nan, 0)

    BV10001 = BV10001.sort_values(['data_sub_from_data'], ascending=[False])

    A200002 = json.loads(BV10001.to_json(orient='records'))
    
    return A200002



def putdeletevery(fen:int,db: Session = Depends(get_session_insert_data)):
    
    engine = db.get_bind()
    with engine.connect() as con:

        # A200001 = pd.read_sql(text(
        #     " select * from delivery_insert where shu_riqi_datetime  > current_date and data_sub ->> 'fen_id_a' = '%s'  and che_ci  < 0  " % (fen) ),con)
        A200001 = pd.read_sql(text(
            " select * from delivery_insert where shu_riqi_datetime  > current_date and data_sub ->> 'fen_id_a' = '%s' and che_ci = 0    " % (fen) ),con)
    
    if A200001.empty:
        return []

    A200001 = pd.json_normalize(A200001.to_dict(orient="records"))
    A200001.columns = A200001.columns.str.replace('data_sub.', 'data_sub_')
    A200001 = A200001.replace(np.nan, 0)

    Function = product_name_package()
    C100001 = Function.select_product_list_id_with_http(nameStr='product',df=A200001[['product_id']])

    BV10001 = A200001.merge(C100001, on=['product_id'], how='right')

    BV10001 = BV10001.replace(np.nan, 0)

    A200002 = json.loads(BV10001.to_json(orient='records'))
    
    return A200002


def getCheCiListDeliverycheci(checi:int,fen:int,db: Session = Depends(get_session_insert_data)):
    engine = db.get_bind()
    with engine.connect() as con:
        A100001 = pd.read_sql(text(
            " select * from delivery_insert where shu_riqi_datetime  > current_date and cast(data_sub ->> 'fen_id_a' as int)  = %s and che_ci = '%s'    " % (fen,checi) ),con)

    print(A100001)
    print("A100001")

    if A100001.empty:
        
        return []

    Function = product_name_package()
    C100001 = Function.select_product_list_id_with_http(nameStr='product',df=A100001[['product_id']])

    # C100001 = pd.json_normalize(C100001.to_dict(orient="records"))
    # C100001.columns = C100001.columns.str.replace('data_sub.', 'data_sub_')

    BV10001 = A100001.merge(C100001, on=['product_id'], how='right')

    A100001 = BV10001.sort_values(['che_ci'], ascending=[False])
    # A100001 = A100001.drop_duplicates(subset=['che_liang'], keep='first')
    A100001 = A100001.to_json(orient='records')
    
    return json.loads(A100001)


def postCheCiListDeliverycheci(hero:delivery_productgoods,db: Session = Depends(get_session_insert_data)):

    # with engine.connect() as con:
    #     A100001 = pd.read_sql(text(
    #         " select * from delivery_insert where shu_riqi_datetime  > current_date and cast(data_sub ->> 'fen_id_a' as int)  = %s and che_ci = '%s' and product_id = '%s'   " % (hero.fen,hero.che_ci,hero.product_id) ),con)

    # if A100001.empty:
    #     return []
    # statement = select(delivery_insert).where(and_(delivery_insert.che_ci == 0,delivery_insert.product_id == hero.product_id,delivery_insert.auto_id == hero.auto_id))


    statement = select(delivery_insert).where(and_(delivery_insert.data_sub.op('->>')('fen_id_a') == str(hero.fen)
    ,delivery_insert.che_ci == 0,delivery_insert.product_id == hero.product_id,delivery_insert.auto_id == hero.auto_id))

    results = db.exec(statement).first()

    if results == None:

        return []

    print(results)
    print("results")

    V10001 = dict(results.data_sub)
    V10001['status_a'] = 'sucess'

    results.che_ci = hero.che_ci
    results.data_sub = V10001
    db.add(results)
    db.commit()
    db.refresh(results)

    return results


def deleteCheCiListDeliverycheci(autoId:int,db: Session = Depends(get_session_insert_data)):
    # with engine.connect() as con:
    #     A100001 = pd.read_sql(text(
    #         " select * from delivery_insert where shu_riqi_datetime  > current_date and cast(data_sub ->> 'fen_id_a' as int)  = %s and che_ci = '%s' and product_id = '%s'   " % (hero.fen,hero.che_ci,hero.product_id) ),con)

    # if A100001.empty:
    #     return []
    # statement = select(delivery_insert).where(and_(delivery_insert.che_ci == 0,delivery_insert.product_id == hero.product_id,delivery_insert.auto_id == hero.auto_id))


    statement = select(delivery_insert).where(and_(delivery_insert.auto_id == autoId))

    results = db.exec(statement).first()

    if results == None:

        return []

    print(results)
    print("results")

    # for key, value in hero.items():
    V10001 = dict(results.data_sub)
    V10001['status_a'] = 'wait_sucess'
    results.che_ci = 0
    results.data_sub = V10001
    db.add(results)
    db.commit()
    db.refresh(results)

    return results



def getcheciget(db: Session = Depends(get_session_insert_data)):
    
    engine = db.get_bind()

    with engine.connect() as con:

        A200001 = pd.read_sql(text(
            " select * from delivery_insert where shu_riqi_datetime  > current_date -2 " ),con)

    if A200001.empty:
        return []

    df3 = pd.json_normalize(A200001.to_dict(orient="records"))
    df3['date_column'] = pd.to_datetime(df3['shu_riqi_datetime']).dt.date
    df3['date_column'] = df3['date_column'].astype(str)


    A100001 = df3[['date_column','che_ci','data_sub.fen_id_a','data_sub.fen_id_b','data_sub.status']]
    A100001.columns = ['date_column','che_ci','fen_id_a','fen_id_b','status']
    

    A100001 = A100001.loc[A100001['che_ci'] > 0 ].drop_duplicates()

    A100001 = A100001.sort_values(['date_column','che_ci'], ascending=[False,False])

    # Function = product_name_package()
    # C100001 = Function.select_product_list_id_with_http(nameStr='product',df=A200001[['product_id']])

    # BV10001 = A200001.merge(C100001, on=['product_id'], how='right')

    A200002 = json.loads(A100001.to_json(orient='records'))
    
    return A200002


def getchecigetGoods(fenDian:int,checi:int,db: Session = Depends(get_session_insert_data)):
    from datetime import timedelta
    
    # engine = db.get_bind()

    # with engine.connect() as con:

    #     A200001 = pd.read_sql(text(
    #         " select * from delivery_insert where shu_riqi_datetime  > current_date and  data_sub ->> 'fen_id_b' = '%s'  " )%(str(fenDian)),con)

    datetimeStr = time_zone_function.get_date_delete_day(day=2) 

    print(datetimeStr)

    statement = select(delivery_insert).where(and_(delivery_insert.che_ci == checi,
    delivery_insert.data_sub.op('->>')('fen_id_b') == str(fenDian),delivery_insert.shu_riqi_datetime > datetimeStr
    ))

    results = db.exec(statement)

    df3 = sqmodel_to_df(results)

    df3 = pd.json_normalize(df3.to_dict(orient="records"))
    df3.columns = df3.columns.str.replace('data_sub.', 'data_sub_')
    df3 = df3.replace(np.nan, 0)


    try:
        df3['AnsCheckQty'] = (df3['product_qty'] - df3['data_sub_product_qty_a'])
        df3['AnsCheck'] = (df3['AnsCheckQty'] == 0)
    except:
        df3['AnsCheck'] = False

    # if A200001.empty:
    #     return []

    # df3 = pd.json_normalize(A200001.to_dict(orient="records"))

    # A100001 = df3[['che_ci','data_sub.fen_id_b']]

    Function = product_name_package()
    C100001 = Function.select_product_list_id_with_http(nameStr='product',df=df3[['product_id']])

    BV10001 = C100001.merge(df3, on=['product_id'], how='right')

    BV10001 = BV10001.sort_values(['AnsCheckQty'], ascending=[True])

    if not BV10001.empty:
        BV10001['shu_riqi_datetime'] = BV10001['shu_riqi_datetime'].astype(str)

    A200002 = json.loads(BV10001.to_json(orient='records'))
    
    return A200002

def getchecigetGoodsPrinter(fenDian:int,checi:int,db: Session = Depends(get_session_insert_data)):
    from datetime import timedelta
    
    # engine = db.get_bind()

    # with engine.connect() as con:

    #     A200001 = pd.read_sql(text(
    #         " select * from delivery_insert where shu_riqi_datetime  > current_date and  data_sub ->> 'fen_id_b' = '%s'  " )%(str(fenDian)),con)

    datetimeStr = time_zone_function.get_date_delete_day(day=2) 

    print(datetimeStr)

    statement = select(delivery_insert).where(and_(delivery_insert.che_ci == checi,
    delivery_insert.data_sub.op('->>')('fen_id_b') == str(fenDian),delivery_insert.shu_riqi_datetime > datetimeStr
    ))

    results = db.exec(statement)

    df3 = sqmodel_to_df(results)

    df3 = pd.json_normalize(df3.to_dict(orient="records"))
    df3.columns = df3.columns.str.replace('data_sub.', 'data_sub_')
    df3 = df3.replace(np.nan, 0)


    try:
        df3['AnsCheckQty'] = (df3['product_qty'] - df3['data_sub_product_qty_a'])
        df3['AnsCheck'] = (df3['AnsCheckQty'] == 0)
    except:
        df3['AnsCheck'] = False

    # if A200001.empty:
    #     return []

    # df3 = pd.json_normalize(A200001.to_dict(orient="records"))

    # A100001 = df3[['che_ci','data_sub.fen_id_b']]

    Function = product_name_package()
    C100001 = Function.select_product_list_id_with_http(nameStr='product',df=df3[['product_id']])

    BV10001 = C100001.merge(df3, on=['product_id'], how='right')

    BV10001 = BV10001.sort_values(['AnsCheckQty'], ascending=[True])

    if not BV10001.empty:
        BV10001['shu_riqi_datetime'] = BV10001['shu_riqi_datetime'].astype(str)
        
    from src.common.function.apiFunction import get_post_jia_yi_fang
        
    def merge_dataframe_on_keyword(df, keyword):
        """根据指定的关键词合并数据框"""
        base_keyword = keyword.replace('_id', '')  # 移除_id
        ids = df[keyword].tolist()
        df_api = get_post_jia_yi_fang(ids)
        
        if df_api.empty:
            return df
        
        df_api_renamed = df_api.rename(columns={
            'jia_yi_id': keyword,
            'jia_yi_idname': f"{keyword}name",
            'jia_yi_mmname': f"{base_keyword}_mm_name"
        })
        
        # 使用df的顺序合并数据，然后添加新列
        merged_df = df.merge(df_api_renamed, on=[keyword], how='left')
        
        # 组织期望的列顺序
        columns_order = list(df.columns) + [f"{keyword}name", f"{base_keyword}_mm_name"]
        return merged_df[columns_order]
    
    V10002_renamed = BV10001
    keywords = ['jia_yi_fang_a', 'jia_yi_fang_b']
    for keyword in keywords:
        V10002_renamed = merge_dataframe_on_keyword(V10002_renamed, keyword)

    V10002_renamed = V10002_renamed.sort_values(['jia_yi_fang_aname'], ascending=[True])

    A200002 = json.loads(V10002_renamed.to_json(orient='records'))
        
    return A200002





def putCheCiSummit(cheCi:int,fenDian:int,db: Session = Depends(get_session_insert_data)):
    
    statement = select(delivery_insert).where(and_(delivery_insert.che_ci == cheCi,
    delivery_insert.data_sub.op('->>')('fen_id_b') == str(fenDian)))

    results = db.exec(statement).all()

    for i in results:
        # try:
            if i.product_qty != 0:
                A10001 = i.product_qty - i.data_sub['product_qty_a']
                A10002 = A10001 == 0

                V10001 = dict(i.data_sub)
                V10001['status'] = 'sucess'
                V10001['status_a_b'] = 'sucess'

                i.data_sub = V10001

                print(A10002)

                db.add(i)

            if i.product_qty == 0:

            # if int(i.product_qty) == 0:
                # A10001 = i.product_qty - i.data_sub['product_qty_a']
                # A10002 = A10001 == 0
                V10001 = dict(i.data_sub)
                V10001['status'] = 'sucess'
                V10001['status_a_b'] = 'sucess'

                i.data_sub = V10001

                print("test")


                db.add(i)

        # except:
        #     return False

        # if A10002 == False:

        #     return False

    db.commit()

    return True


def putCheCiCancel(cheCi:int,fenDian:int,db: Session = Depends(get_session_insert_data)):
    
    statement = select(delivery_insert).where(and_(delivery_insert.che_ci == cheCi,
    delivery_insert.data_sub.op('->>')('fen_id_b') == str(fenDian)))

    results = db.exec(statement).all()

    for i in results:
        # try:

        # A10001 = i.product_qty - i.data_sub['product_qty_a']
        # A10002 = A10001 == 0

        V10001 = dict(i.data_sub)
        V10001['status'] = 'wait_sucess'
        V10001['status_a_b'] = 'wait_sucess'

        i.data_sub = V10001

        # print(A10002)

        db.add(i)

        # except:
        #     return False

        # if A10002 == False:

        #     return False

    db.commit()

    return True



def postdelivery(hero:delivery_insert_post,db: Session = Depends(get_session_insert_data)):
    
    if hero.data_sub['from_data'] == 'ai':
        
        hero.shu_riqi_datetime = str(datetime.now() +  timedelta(days=1))
        

    if hero.data_sub['from_data'] != 'ai':
        
        hero.shu_riqi_datetime = str(datetime.now())

    hero_to_db = delivery_insert.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    
    
        
    # Convert the hero to a DataFrame for updateWithKey
    hero_dict = hero.dict()
    df = pd.DataFrame([hero_dict])
    df['delevery_process'] = True
    
    print(df)
    
    # Define keys
    df_keys = ['product_id', 'fen_dian_id']
    db_keys = ['product_id', 'fen']
    
    # Columns to update
    columnList = ['delevery_process']
    
    df['fen_dian_id'] = hero.data_sub.get('fen_id')
    
    # Call updateWithKey
    updateWithKey('view', 'logistics_every_month', df, df_keys, db_keys, columnList)
    
    
    # updateWithKey('view','logistics_every_month')

    return hero_to_db



def getDeliveryNotHavefunction(db: Session = Depends(get_session_insert_data)):

    engine = db.get_bind()
    with engine.connect() as con:

        A200001 = pd.read_sql(text(
        """  select * from delivery_insert where "riqi_datetime"  > current_date and data_sub ->> 'from_data' in ('ai','input')  """ ),con)
        # A200001 = pd.read_sql(text(
        #     """  select * from delivery_insert where "riqi_datetime"  > current_date and data_sub ->> 'from_data' in ('ai','input')  """ ),con)

        A200001 = pd.json_normalize(A200001.to_dict(orient="records"))

        print(A200001.info())

    with engine.connect() as con:
        
        Q200001 = pd.read_sql(text(
                        """  select * from delivery_not_show where "riqi"  > current_date - 10   """ ),con)

        Q200001 = pd.json_normalize(Q200001.to_dict(orient="records"))


    A100001 = delivery_not_have()

    if A100001.empty:

        return [] 

    print(A100001.info())

    if not A200001.empty:

        A100001 = A100001[~A100001.set_index(['product_id','fen_x','fen_y']).index.isin(A200001.set_index(['product_id','data_sub.fen_id_b','data_sub.fen_id_a']).index)].reset_index()

    if not Q200001.empty:
    
        A100001 = A100001[~A100001.set_index(['product_id','fen_x']).index.isin(Q200001.set_index(['product_id','fen_dian_id']).index)].reset_index()


    A100001 = A100001.rename(columns={'index': 'dataIndexI'})

    # A100001 = A100001.head(500)

    A100001 = A100001.to_json(orient='records')

    return json.loads(A100001)



def deliveryForSellfunction(db: Session = Depends(get_session_insert_data)):

    engine = db.get_bind()
    with engine.connect() as con:
        E200001 = pd.read_sql(text(
                        """  select * from delivery_insert where "riqi_datetime"  > current_date and data_sub ->> 'from_data' in ('ai','input')  """ ),con)

        # A200001 = pd.read_sql(text(
        #         """  select * from delivery_insert where "riqi_datetime"  > current_date and data_sub ->> 'from_data' in ('ai','input')  """ ),con)

        E200002 = pd.read_sql(text(
                        """  select * from delivery_insert where "riqi_datetime"  > current_date - 3 and data_sub ->> 'from_data' in ('ai','input') and product_qty > 0 """ ),con)

        # A200001 = pd.read_sql(text(
        #         """  select * from delivery_insert where "riqi_datetime"  > current_date and data_sub ->> 'from_data' in ('ai','input')  """ ),con)

        A200001 = pd.concat([E200001,E200002])


        A200001 = pd.json_normalize(A200001.to_dict(orient="records"))


    with engine.connect() as con:
        Q200001 = pd.read_sql(text(
                        """  select * from delivery_not_show where "riqi"  > current_date - 10   """ ),con)

        Q200001 = pd.json_normalize(Q200001.to_dict(orient="records"))

    print(Q200001.info())
    
    A100001 = delivery_for_sell()

    print(A100001.info())

    if not A200001.empty:

        A100001 = A100001[~A100001.set_index(['product_id','fen_dian_id_x','fen_dian_id_y']).index.isin(A200001.set_index(['product_id','data_sub.fen_id_b','data_sub.fen_id_a']).index)].reset_index()
    
    if not Q200001.empty:

        A100001 = A100001[~A100001.set_index(['product_id','fen_dian_id_x']).index.isin(Q200001.set_index(['product_id','fen_dian_id']).index)].reset_index()

    A100001 = A100001.rename(columns={'index': 'dataIndexI'})

    A100001 = A100001.to_json(orient='records')

    return json.loads(A100001)




def post_delivery_not_show(hero:delivery_not_show_post,db: Session = Depends(get_session_insert_data)):
    
    hero.riqi = str(datetime.now())
    statement = select(delivery_not_show).where(and_(delivery_not_show.product_id == hero.product_id,
    delivery_not_show.fen_dian_id == hero.fen_dian_id))

    results = db.exec(statement).first()

    if results != None:

        results.riqi = str(datetime.now())
        db.add(results)
        db.commit()
        db.refresh(results)

        return results

    hero_to_db = delivery_not_show.from_orm(hero)

    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)

    return hero_to_db


def get_delivery_not_show(db: Session = Depends(get_session_insert_data)):
    
    statement = select(delivery_not_show)

    results = db.exec(statement).all()

    return results




def deliveryShowForBackToWareHourse(db: Session = Depends(get_session_insert_data)):
    
    engine = db.get_bind()
    with engine.connect() as con:
        A200001 = pd.read_sql(text(
                        """  select * from delivery_insert where "shu_riqi_datetime"  > current_date and data_sub ->> 'from_data' in ('ai','input') and jia_yi_fang_a = 0  """ ),con)

        A200001 = pd.json_normalize(A200001.to_dict(orient="records"))

    A100001 = A200001.to_json(orient='records')

    return json.loads(A100001)


def checichangeDatetime(cheCi:int,day:int,db: Session = Depends(get_session_insert_data)):

    # A100001 = get_datetime() + timedelta(days=day)

    heroese3 = db.exec(select(delivery_insert).where(delivery_insert.che_ci == cheCi))


    for hero3 in heroese3:
        A100001 = hero3.shu_riqi_datetime +  timedelta(days=day)
        hero3.shu_riqi_datetime = A100001
        db.add(hero3)

    db.commit()

    return "sucess"



def deliveryrecipientdetail(fenPush:int,che_ci,db: Session = Depends(get_session_insert_data)):

    heroese2 = db.exec(select(delivery_insert).where(and_(delivery_insert.che_ci == che_ci , delivery_insert.data_sub.op('->>')('fen_id_a') == str(fenPush)))).all()

    D1000001 = sqmodel_to_df(heroese2)


    from src.common.jia_yi_name import jia_yi_name_package
    A10001 = jia_yi_name_package()
    D1000001['jia_yi_id'] = D1000001['jia_yi_fang_a']
    A10001.select_data_list_id_with_http_v2(D1000001[['jia_yi_id']],'jia_yi')

    A200002 = json.loads(D1000001.to_json(orient='records'))
    return A200002


def warehoursenotconfig(db: Session = Depends(get_session_insert_data)):

    datetimeStr = time_zone_function.get_date_delete_day(day=3) 
    heroese2 = db.exec(select(delivery_insert).where(and_(delivery_insert.shu_riqi_datetime > datetimeStr , delivery_insert.data_sub.op('->>')('product_qty_b_pre') == str(0)))).all()
    D1000001 = sqmodel_to_df(heroese2).head(5000)

    # from src.common.jia_yi_name import jia_yi_name_package
    from src.common.product_name import product_name_package
    A10001 = product_name_package()
    # D1000001['jia_yi_id'] = D1000001['jia_yi_fang_a']
    KJ10001 = A10001.select_product_list_id_with_http(nameStr='product',df=D1000001[['product_id']])
    BV10001 = KJ10001.merge(D1000001, on=['product_id'], how='inner', indicator=True)


    BV10001 = BV10001.sort_values(['shu_riqi_datetime'], ascending=[False])



    A200002 = json.loads(BV10001.to_json(orient='records'))


    return A200002

from src.common.function.function import updateWithKey
def delivery_info_parme_post_crud(hero:delivery_info_parme_post,db: Session = Depends(get_session_insert_data)):
        
    hero.shu_ri_qi = str(datetime.now())
    hero_to_db = delivery_info_parme.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)


    return hero_to_db