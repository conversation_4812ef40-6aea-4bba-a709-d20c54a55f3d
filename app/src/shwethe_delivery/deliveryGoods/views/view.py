from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.config.insert_data.database import get_session as get_session_insert_data

from src.shwethe_delivery.deliveryGoods.crud.crud import (
    getdeletevery,
    getCheCiListDeliverycheci,
    postCheCiListDeliverycheci,
    deleteCheCiListDeliverycheci,
    getcheciget,
    getchecigetGoods,
    putCheCiSummit,
    putCheCiCancel,
    postdelivery,
    getDeliveryNotHavefunction,
    deliveryForSellfunction,
    post_delivery_not_show,
    get_delivery_not_show,
    checichangeDatetime,
    deliveryrecipientdetail,
    warehoursenotconfig,
    getchecigetGoodsPrinter,
    delivery_info_parme_post_crud
)
from src.shwethe_delivery.deliveryGoods.models.models import (
    delivery_productgoods,
    delete_delivery_productgoods,
    delivery_insert_post,
    delivery_not_show_post,
    delivery_not_show,
    delivery_info_parme,
    delivery_info_parme_post
)

router = APIRouter()

@router.get("/deleverly")
def create_a_hero(fen: int , db: Session = Depends(get_session_insert_data)):
    
    return getdeletevery(fen=fen, db=db)



@router.get("/delivery/productgoods")
def create_a_hero(checi:str,fen: str , db: Session = Depends(get_session_insert_data)):
    
    return getCheCiListDeliverycheci(checi=checi,fen=fen, db=db)

@router.post("/delivery/productgoods")
def create_a_hero(hero:delivery_productgoods, db: Session = Depends(get_session_insert_data)):
    
    return postCheCiListDeliverycheci(hero=hero, db=db)

@router.delete("/delivery/productgoods")
def create_a_hero(autoId:int, db: Session = Depends(get_session_insert_data)):
    
    return deleteCheCiListDeliverycheci(autoId=autoId, db=db)


@router.get("/delivery/get/checi")
def create_a_hero(db: Session = Depends(get_session_insert_data)):
    
    return getcheciget(db=db)


@router.put("/delivery/get/checi")
def create_a_hero(action:str,cheCi:int,fenDian:int,db: Session = Depends(get_session_insert_data)):


    if action == "summit":
    
        return putCheCiSummit(cheCi=cheCi,fenDian=fenDian,db=db)

    if action == "unsummit":
        
        return putCheCiCancel(cheCi=cheCi,fenDian=fenDian,db=db)


@router.get("/delivery/get/checi/goods")
def create_a_hero2(fenDian:int,checi:int,db: Session = Depends(get_session_insert_data)):
    
    return getchecigetGoods(fenDian=fenDian,checi=checi,db=db)


@router.get("/delivery/get/checi/goods/printer")
def create_a_hero32(fenDian:int,checi:int,db: Session = Depends(get_session_insert_data)):
    
    return getchecigetGoodsPrinter(fenDian=fenDian,checi=checi,db=db)


@router.post("/delivery/productgoods/record")
def create_a_hero2(hero:delivery_insert_post,db: Session = Depends(get_session_insert_data)):
    
    return postdelivery(hero=hero , db=db)


@router.get("/delivery/productgoods/getDeliveryNotHave")
def create_a_hero2(db: Session = Depends(get_session_insert_data)):
    
    return getDeliveryNotHavefunction(db=db)


@router.get("/delivery/productgoods/deliveryForSell")
def create_a_hero2(db: Session = Depends(get_session_insert_data)):
    
    return deliveryForSellfunction(db=db)



@router.post("/delivery/productgoods/deliveryNotShow")
def create_a_hero2(hero:delivery_not_show_post,db: Session = Depends(get_session_insert_data)):
    
    return post_delivery_not_show(hero=hero,db=db)


@router.get("/delivery/productgoods/deliveryNotShow")
def create_a_hero2(db: Session = Depends(get_session_insert_data)):
    
    return get_delivery_not_show(db=db)


@router.put("/delivery/checi/changeDatetime")
def create_a_hero2(cheCi:int,day:int,db: Session = Depends(get_session_insert_data)):
    
    return checichangeDatetime(cheCi=cheCi,day=day,db=db)


@router.get("/delivery/recipient/detail", status_code = 200)
def create_a_hero2(fenPush:int,che_ci:int,db: Session = Depends(get_session_insert_data)):

    return deliveryrecipientdetail(fenPush=fenPush,che_ci=che_ci,db=db)


@router.get("/delivery/for/warehoursenotconfig", status_code = 200)
def create_a_hero2(db: Session = Depends(get_session_insert_data)):

    return warehoursenotconfig(db=db)


@router.post("/delivery/productgoods/delivery_info_parme")
def create_a_hero2(hero:delivery_info_parme_post,db: Session = Depends(get_session_insert_data)):
    return delivery_info_parme_post_crud(hero=hero,db=db)
