from xmlrpc.client import DateTime
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id


# 资料表名称 pre_order_product 和框架
class delivery_productgoods(BaseModel):
    auto_id: int
    product_id: int
    che_ci: int
    fen: int

class delete_delivery_productgoods(BaseModel):
    auto_id: int



# 资料表名称 pre_order_product 和框架
class delivery_insert_base(SQLModel):
    product_id: Optional[int]

    

class delivery_insert(delivery_insert_base, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    product_id: Optional[int]
    product_qty: Optional[float]
    che_ci: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    shu_riqi_datetime : datetime
    data_sub : dict = Field(sa_column=Column(JSONB), default={})


class delivery_insert_post(BaseModel):
    product_id: int
    product_qty: float
    che_ci: int
    lei_a: int = 22
    lei_b: int = 22
    shu_riqi_datetime : Optional[datetime]
    jia_yi_fang_a: int = 0
    jia_yi_fang_b: int = 0
    data_sub : dict 



# 资料表名称 pre_order_product 和框架
class delivery_not_show_base(SQLModel):
    product_id: Optional[int]
    fen_dian_id: Optional[int]
    riqi : Optional[datetime]
    data_sub_name : dict = Field(sa_column=Column(JSONB), default={})

    

class delivery_not_show(delivery_not_show_base, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    product_id: Optional[int]
    fen_dian_id: Optional[int]
    riqi : Optional[datetime]
    data_sub_name : dict = Field(sa_column=Column(JSONB), default={})


class delivery_not_show_post(BaseModel):
    product_id: int
    fen_dian_id: int
    riqi : Optional[datetime]
    data_sub_name : dict
    
    
class delivery_info_parme_base(SQLModel):
    product_id: Optional[int]
    fen_dian_id: Optional[int]
    key : Optional[str]
    value : Optional[str]
    shu_ri_qi :Optional[datetime]
    
    
class delivery_info_parme(delivery_info_parme_base, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    product_id: Optional[int]
    fen_dian_id: Optional[int]
    key : Optional[str]
    value : Optional[str]
    shu_ri_qi :Optional[datetime]
    
    
class delivery_info_parme_post(BaseModel):
    product_id: Optional[int]
    fen_dian_id: Optional[int]
    key : Optional[str]
    value : Optional[str]
    shu_ri_qi :Optional[datetime]