from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,or_,and_
from sqlalchemy import text
from typing import List, Optional
from src.config.insert_data.database import get_session as get_session_insert_data
from src.common.product_name import product_name_package
import json
from src.shwethe_delivery.deliveryGoods.crud.crud import (
    delivery_insert,
)
from src.time_zone import time_zone_function
from datetime import datetime, timedelta
import numpy as np
from src.shwethe_delivery.cliend_orm.models.models import (
    delivery_post
)
from src.shwethe_delivery.deliveryGoods.models.models import (
    delivery_insert
)




import pandas as pd 

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df



def getgoodsfordeliveryinput(fen:int,formline:int,db: Session = Depends(get_session_insert_data)):
    
    engine = db.get_bind()
    with engine.connect() as con:

        A200001 = pd.read_sql(text(
            " select * from delivery_insert where shu_riqi_datetime  > current_date and data_sub ->> 'fen_id_a' = '%s' and data_sub ->> 'from_data' = 'input' and data_sub ->> 'status_a' = 'wait_sucess' and data_sub ->> 'from_line' = '%s'   " % (fen,formline) ),con)
    
    if A200001.empty:
        return []

    A200001 = pd.json_normalize(A200001.to_dict(orient="records"))
    A200001.columns = A200001.columns.str.replace('data_sub.', 'data_sub_')
    A200001 = A200001.replace(np.nan, 0)

    Function = product_name_package()
    C100001 = Function.select_product_list_id_with_http(nameStr='product',df=A200001[['product_id']])

    BV10001 = A200001.merge(C100001, on=['product_id'], how='right')

    BV10001 = BV10001.replace(np.nan, 0)


    A200002 = json.loads(BV10001.to_json(orient='records'))
    
    return A200002

def pushGoodsDelivery(hero:delivery_post , db: Session = Depends(get_session_insert_data)):
    
    statement = select(delivery_insert).where(and_(delivery_insert.auto_id == hero.auto_id,delivery_insert.data_sub.op('->>')('status_a') == 'wait_sucess'))

    results = db.exec(statement).first()

    if results == None:

        return []

    V10001 = dict(results.data_sub)

    V10001['product_qty_a'] = hero.product_qty

    results.jia_yi_fang_a = hero.jia_yi_fang
    results.data_sub = V10001

    db.add(results)
    db.commit()
    db.refresh(results)
    
    return results


def getgoodsfordeliveryget(fen:int,formline:int,db: Session = Depends(get_session_insert_data)):
    
    engine = db.get_bind()
    with engine.connect() as con:

        A200001 = pd.read_sql(text(
            " select * from delivery_insert where  jia_yi_fang_a > 0 and shu_riqi_datetime  > current_date and data_sub ->> 'fen_id_b' = '%s' and data_sub ->> 'from_line' = '%s' and data_sub ->> 'from_data' = 'input' and data_sub ->> 'status' = 'wait_sucess'    " % (fen,formline) ),con)
    
    if A200001.empty:
        return []

    A200001 = pd.json_normalize(A200001.to_dict(orient="records"))
    A200001.columns = A200001.columns.str.replace('data_sub.', 'data_sub_')
    A200001 = A200001.replace(np.nan, 0)

    Function = product_name_package()
    C100001 = Function.select_product_list_id_with_http(nameStr='product',df=A200001[['product_id']])

    BV10001 = A200001.merge(C100001, on=['product_id'], how='right')

    BV10001 = BV10001.replace(np.nan, 0)

    A200002 = json.loads(BV10001.to_json(orient='records'))
    
    return A200002

def pushGoodsDeliveryget(hero:delivery_post , db: Session = Depends(get_session_insert_data)):
    
    statement = select(delivery_insert).where(and_(delivery_insert.auto_id == hero.auto_id,delivery_insert.data_sub.op('->>')('status') == 'wait_sucess'))

    results = db.exec(statement).first()

    if results == None:

        return []

    V10001 = dict(results.data_sub)

    V10001['product_qty_b'] = hero.product_qty

    results.product_qty = hero.product_qty
    results.jia_yi_fang_b = hero.jia_yi_fang
    results.data_sub = V10001

    db.add(results)
    db.commit()
    db.refresh(results)
    
    return results