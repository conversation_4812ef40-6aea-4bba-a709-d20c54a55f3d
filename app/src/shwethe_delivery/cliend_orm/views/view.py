from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.config.insert_data.database import get_session as get_session_insert_data

from src.shwethe_delivery.cliend_orm.crud.crud import (
    getgoodsfordeliveryinput,
    pushGoodsDelivery,
    getgoodsfordeliveryget,
    pushGoodsDeliveryget
)

from src.shwethe_delivery.cliend_orm.models.models import (
    delivery_post
)
# from src.shwethe_delivery.deliveryGoods.models.models import (
#     delivery_productgoods,
#     delete_delivery_productgoods,
#     delivery_insert_post
# )

router = APIRouter()

@router.get("/deleverly/getgoodsfordeliveryinput")
def create_a_hero(fen: int ,formline:int, db: Session = Depends(get_session_insert_data)):
    
    return getgoodsfordeliveryinput(fen=fen,formline=formline, db=db)


@router.put("/deleverly/getgoodsfordeliveryinput/push")
def create_a_hero(hero: delivery_post , db: Session = Depends(get_session_insert_data)):
    
    return pushGoodsDelivery(hero=hero, db=db)

@router.get("/deleverly/getgoodsfordeliveryinput/get")
def create_a_hero(fen: int ,formline : int, db: Session = Depends(get_session_insert_data)):
    
    return getgoodsfordeliveryget(fen=fen,formline=formline, db=db)


@router.put("/deleverly/getgoodsfordeliveryinput/get")
def create_a_hero(hero: delivery_post , db: Session = Depends(get_session_insert_data)):
    
    return pushGoodsDeliveryget(hero=hero, db=db)