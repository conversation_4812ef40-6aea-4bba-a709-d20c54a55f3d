from fastapi import FastAPI, APIRouter, Body, Response,BackgroundTasks,Header
from pydantic import BaseModel
from helper import generate_id,generate_datetime
from typing import List,Optional
import pandas as pd
import requests
import datetime
import json
import time
import numpy as np
router = APIRouter()




@router.get("/recipient/{fen}", status_code = 200)
def recipienta(fen:int):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    try:

        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001

        def get_jia_yi_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                        json={"data_api": res_1})
            try:
                GF10001 = pd.DataFrame(json.loads(r.json()))
            except :
                GF10001 = pd.DataFrame(r.json())
            GF10001 = GF10001.rename(columns={'jia_yi_id': str,'jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mm_name'})
            print(GF10001)    
            # GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            return GF10001

        def get_data(fen):
                sql_insert = """
                select * from delivery_insert where data_sub ->> 'fen_id' != '%s' and shu_riqi_datetime > current_date - 1 and data_sub ->> 'from_data' !=  'ai'
                """ % (fen)
                FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
                FF100002 = FF100001.to_dict('records')
                FD100003 = pd.json_normalize(FF100002)
                # FF100002 = FF100001.to_dict('records')
                # FD100003 = pd.json_normalize(FF100002)

                return FD100003


        def get_data_2(fen):
                sql_insert = """
                select * from delivery_insert where data_sub ->> 'fen_id' = '%s' and shu_riqi_datetime > current_date - 1 and data_sub ->> 'from_data' =  'ai'
                """ % (fen)
                FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
                FF100002 = FF100001.to_dict('records')
                FD100003 = pd.json_normalize(FF100002)
                # FF100002 = FF100001.to_dict('records')
                # FD100003 = pd.json_normalize(FF100002)

                return FD100003


        GG100001 = get_data(fen)

        GG100002 = get_data_2(fen)

        GG100001 = pd.concat([GG100001,GG100002])
        


        FG10001 = get_data_api(GG100001,"product")
        GG100009 = FG10001.merge(GG100001, on=['product_id'] , how='inner')
        GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)


        H1000001 = GG100009[['jia_yi_fang_a']]
        H1000001 = H1000001.rename(columns={'jia_yi_fang_a': 'jia_yi_id'})
        FG10001 = get_jia_yi_api(H1000001,"jia_yi_fang_a")

        GG100009 = FG10001.merge(GG100009, on=['jia_yi_fang_a'] , how='right')

        print(GG100009)
        print("GG100009")


        H1000001 = GG100009[['jia_yi_fang_b']]
        H1000001 = H1000001.rename(columns={'jia_yi_fang_b': 'jia_yi_id'})
        FG10001 = get_jia_yi_api(H1000001,"jia_yi_fang_b")

        GG100009 = FG10001.merge(GG100009, on=['jia_yi_fang_b'] , how='right')
        GG100009 = GG100009.replace(np.nan, 0)
        GG100009['ANS'] = (GG100009['data_sub.product_qty_a'] - GG100009['product_qty'])
        GG100009['ANS_check'] = ((GG100009['ANS'] >= 0) & (GG100009['ANS'] <= 0) )

        # print(GG100009[['product_qty']])

        GG100011 = GG100009.to_json(orient='records')

        # GG100002 = GG100011.to_json(orient='records')、

        return json.loads(GG100011)

    finally:
        psycopg2_conn_shwethe_delivery.close()


class recipient2(BaseModel):
    auto_id : int
    status : str

@router.put("/recipient/{fen}", status_code = 200)
def recipientb(req_body : recipient2 = Body(...)):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)



    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def update_data(auto_id,json):
            sql_insert = """ update delivery_insert set data_sub = data_sub || %s ::jsonb  where auto_id = %s and shu_riqi_datetime > current_date -2    """ 
            cur.execute(sql_insert, (json,auto_id))
            return "aaaa"
        update_data(FFF['auto_id'],json.dumps({'status':FFF['status']}))
        return "abc"

    finally:
        psycopg2_conn_shwethe_delivery.close()

class recipient3(BaseModel):
    auto_id : int
    status_a : str

@router.put("/recipient_a/{fen}", status_code = 200)
def recipientc(req_body : recipient3 = Body(...)):
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api

    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)

    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)



    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def update_data(auto_id,json):
            sql_insert = """ update delivery_insert set data_sub = data_sub || %s ::jsonb  where auto_id = %s and shu_riqi_datetime > current_date -2 and data_sub ->> 'status' != 'sucess'    """ 
            cur.execute(sql_insert, (json,auto_id))
            return "aaaa"
        update_data(FFF['auto_id'],json.dumps({'status_a':FFF['status_a']}))
        
        return "abc"

    finally:

        psycopg2_conn_shwethe_delivery.close()


