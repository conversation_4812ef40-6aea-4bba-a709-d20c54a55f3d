
from sqlmodel import Field, SQLModel, create_engine

db_string = "**************************************************/insert_data_s"

db_db_info = "**************************************************/db_info"


db_db_info_engine = create_engine(db_db_info, echo=True,   
        max_overflow=2,  # 超过连接池大小外最多创建的连接
        pool_size=2,  # 连接池大小
        pool_timeout=30,  # 池中没有线程最多等待的时间，否则报错
        pool_recycle=60*60*3,  # 多久之后对线程池中的线程进行一次连接的回收（重置）
        pool_pre_ping=True
        )

# db_db_info_engine = create_engine(db_db_info, echo=True
#         )