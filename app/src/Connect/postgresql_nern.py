import psycopg2
# keepalives='1',keepalives_idle='30',keepalives_interval='10',keepalives_count='5'
postgresql_shwethe_carItemChange = psycopg2.connect("dbname='shwethe_carItemChange' user='postgres' host='************' port='5436' password='0818822095'")
postgresql_shwethe_personal = psycopg2.connect("dbname='shwethe_personal' user='postgres' host='************' port='5436' password='0818822095'")
postgresql_shwethe_miniapp_carActive = psycopg2.connect("dbname='shwethe_miniapp_carActive' user='postgres' host='************' port='5436' password='0818822095'")
postgresql_shwethe_color = psycopg2.connect("dbname='shwethe_color' user='postgres' host='************' port='5436' password='0818822095'")
postgresql_shwethe_cement_mixer = psycopg2.connect("dbname='shwethe_cement_mixer' user='postgres' host='************' port='5436' password='0818822095'")
postgresql_shwethe_car_rent = psycopg2.connect("dbname='shwethe_car_rent' user='postgres' host='************' port='5436' password='0818822095'")

real_backend_api = "http://pv-api.shwethe.com"
petrol_api = "http://************:8100/shwethe_petrol"