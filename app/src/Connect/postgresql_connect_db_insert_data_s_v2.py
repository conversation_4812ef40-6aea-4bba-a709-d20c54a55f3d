import psycopg2
from sqlalchemy import create_engine

# keepalives='1',keepalives_idle='30',keepalives_interval='10',keepalives_count='5'
# postgresql_con_insert_data = psycopg2.connect("dbname='insert_data' user='postgres' host='************' password='0818822095'")

psycopg2_conn_insert_data_s_engine = create_engine("**************************************************/insert_data_s", connect_args={"options": "-c timezone=Asia/Yangon"})
