from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header,HTTPException,Query
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np
from sqlmodel import Session,select,or_,SQLModel
router = APIRouter()
type_list = [1,3,4]
language_code_list = 'mm'



@router.get("/test/{locale}", status_code = 200)
def order_s_insert_list(locale: str,parent : int = 0,offset:int = 0,limit:int=9 , cai_chan_spec_param_id : str = None):
    
    print(offset)
    print(limit)
    from src.shwethe_cai_chan.orm.table.tb_cai_chan_spu_sku.main import tb_cai_chan_sku_engine,tb_cai_chan_sku
    from src.shwethe_cai_chan.orm.table.tb_cai_chan_app_translation.main import tb_cai_chan_translate_entry_engine,tb_cai_chan_translate_entry
    
    def get_data_tb_cai_chan_spu_sku(array_list):
        with Session(tb_cai_chan_sku_engine) as session:
            print(array_list)
            print("array_list")
            if array_list != None:
            # v for v in ('Alice', 'Bob', 'Carl')
                heroes = session.exec(select(tb_cai_chan_sku).where(or_(tb_cai_chan_sku.cai_chan_param.op("@>") (v) for v in (array_list))).offset(offset).limit(limit)).all()
            else:
                heroes = session.exec(select(tb_cai_chan_sku).offset(offset).limit(limit)).all()
            # heroes = session.exec(select(tb_cai_chan_sku).where(func.to_tsvector(tb_cai_chan_sku.cai_chan_param).match('221')).offset(offset).limit(limit)).all()
            # heroes = session.exec(select(tb_cai_chan_sku).where(tb_cai_chan_sku.cai_chan_param.op('@>')('221')).offset(offset).limit(limit)).all()
            # heroes = session.exec(select(tb_cai_chan_sku).where(tb_cai_chan_sku.cai_chan_param.op('->')('128') == '221').offset(offset).limit(limit)).all()
            # heroes = session.exec(select(tb_cai_chan_sku).where(or_(tb_cai_chan_sku.cai_chan_param.op("@>")('{"128":229}'),tb_cai_chan_sku.cai_chan_param.op("@>")('{"128":227}'))).offset(offset).limit(limit)).all()

            return heroes

    def get_data_tb_cai_chan_spu_sku_(key,value):
        with Session(tb_cai_chan_sku_engine) as session:
            # heroes = session.exec(select(tb_cai_chan_sku).where(func.to_tsvector(tb_cai_chan_sku.cai_chan_param).match('221')).offset(offset).limit(limit)).all()
            # heroes = session.exec(select(tb_cai_chan_sku).where(tb_cai_chan_sku.cai_chan_param.op('@>')('221')).offset(offset).limit(limit)).all()
            # heroes = session.exec(select(tb_cai_chan_sku).where(tb_cai_chan_sku.cai_chan_param.op('->')('128') == '221').offset(offset).limit(limit)).all()
            heroes = session.exec(select(tb_cai_chan_sku).where(tb_cai_chan_sku.cai_chan_param.op('->')(key) == value).offset(offset).limit(limit)).all()
            return heroes

    if cai_chan_spec_param_id == None or cai_chan_spec_param_id == '':
        T900001 = get_data_tb_cai_chan_spu_sku(cai_chan_spec_param_id)
    else:
        print("cai_chan_spec_param_id")
        print(cai_chan_spec_param_id)
        data = json.loads(cai_chan_spec_param_id)
        


        # PO100001 = {}
        # for key, value in json.loads(cai_chan_spec_param_id).items():
        #     # print(key)
        #     # print(value)
        #     for T in value:
        #         print(T)
        #         print(key)
        #         PO100001[key] = int(T)
        #         # PO100001.append(get_data_tb_cai_chan_spu_sku_(key,value))
        # print(PO100001)
        for key in data:
            data[key] = [int(val) for val in data[key]]


        def product(*args, repeat=1):
            # product('ABCD', 'xy') --> Ax Ay Bx By Cx Cy Dx Dy
            # product(range(2), repeat=3) --> 000 001 010 011 100 101 110 111
            pools = [tuple(pool) for pool in args] * repeat
            result = [[]]
            for pool in pools:
                result = [x+[y] for x in result for y in pool]
            for prod in result:
                yield tuple(prod)
        V10001 = [dict(zip(data, v)) for v in product(*data.values())]

        print(V10001)

        T900001 = get_data_tb_cai_chan_spu_sku(V10001)
    
    # T900001 = PO100001


    def c200001():
        with Session(tb_cai_chan_translate_entry_engine) as session:
            heroes = session.exec(select(tb_cai_chan_translate_entry).where(tb_cai_chan_translate_entry.language_code == locale)).all()
            return heroes
    T100001 = c200001()
    cai_chan_id = []
    YU100001 = []
    
    for i in T900001:
        FD100001 = {'images':i.images,'cai_chan_id':i.cai_chan_id,'spg_id':i.spg_id}
        cai_chan_id.append(i.spg_id)
        YU100001.append(FD100001)
        

    Cz100001 = pd.DataFrame(YU100001)

    YU100002 = []
    for i in T100001:
        FD100001 = {'translation_id':i.translation_id,'field_text':i.field_text,'language_code':i.language_code}
        YU100002.append(FD100001)
    PO1000007 = Cz100001.to_json(orient='records')
    return json.loads(PO1000007)

class spec_type(BaseModel):
    cai_chan_spec_param_id : List[dict] = Body(...)

@router.get("/test/spec_type/{locale}", status_code = 200)
def order_s_insert_list(locale: str):
    
    from src.shwethe_cai_chan.orm.table.tb_cai_chan_spec_param.main import tb_cai_chan_spec_param_engine,tb_cai_chan_spec_param
    from src.shwethe_cai_chan.orm.table.tb_cai_chan_spu_sku.main import tb_cai_chan_sku_engine,tb_cai_chan_sku
    from src.shwethe_cai_chan.orm.table.tb_cai_chan_app_translation.main import tb_cai_chan_translate_entry_engine,tb_cai_chan_translate_entry
    from src.shwethe_cai_chan.orm.table.tb_cai_chan_segments.main import tb_cai_chan_segments_engine,tb_cai_chan_segments


    


    # def get_data_tb_cai_chan_spu_sku():
    #     with Session(tb_cai_chan_sku_engine) as session:
    #         heroes = session.exec(select(tb_cai_chan_sku).offset(offset).limit(limit)).all()
    #         return heroes
    def c200001():
        with Session(tb_cai_chan_translate_entry_engine) as session:
            heroes = session.exec(select(tb_cai_chan_translate_entry).where(tb_cai_chan_translate_entry.language_code.in_([language_code_list,locale]))).all()
            return heroes

    def get_tb_cai_chan_segments(spec_param_id):
        with Session(tb_cai_chan_segments_engine) as session:
            heroes = session.exec(select(tb_cai_chan_segments).where(tb_cai_chan_segments.cai_chan_spec_param_id.in_([spec_param_id]))).all()
            return heroes   

    def c500001():
        with Session(tb_cai_chan_spec_param_engine) as session:
            heroes = session.exec(select(tb_cai_chan_spec_param).where(tb_cai_chan_spec_param.spg_id.in_([2]),tb_cai_chan_spec_param.segments == True)).all()
            return heroes


    T100001 = c200001()
    T900001 = c500001()
    cai_chan_id = []
    cai_chan_spec_param_id = []
    YU100001 = []
    
    for i in T900001:
        FD100001 = {'translation_id_id':i.translation_id,'translation_id':i.translation_id,'cai_chan_spec_param_id':int(i.cai_chan_spec_param_id),'spg_id':i.spg_id}
        cai_chan_id.append(i.spg_id)
        cai_chan_spec_param_id.append(i.cai_chan_spec_param_id)
        YU100001.append(FD100001)

    Cz100001 = pd.DataFrame(YU100001)


    get_tb_cai_chan_segments_10001 = get_tb_cai_chan_segments(cai_chan_spec_param_id)


    TT100002 = []
    for i in get_tb_cai_chan_segments_10001:
        FD100001 = {'cai_chan_segments_translation_id_id':i.translation_id,'cai_chan_segments_translation_id':i.translation_id,'cai_chan_spec_param_id':int(i.cai_chan_spec_param_id),'cai_chan_segments_id':i.cai_chan_segments_id}
        TT100002.append(FD100001)

    TT100003 = pd.DataFrame(TT100002)
    # print(TT100003)


    Cz100001 = Cz100001.merge(TT100003, on=['cai_chan_spec_param_id'], how='inner')
    # print(Cz100001)
# 
    # YU100002 = []
    # for i in T900001:
    #     for key, value in i.cai_chan_param.items():
    #         cai_chan_id.append(i.spg_id)
    #         FD100001 = {'cai_chan_id':i.cai_chan_id,'spec_param_translation_id':int(key),'app_translation_id':int(value),'value_id':int(value),'value':int(value)}
    #         YU100002.append(FD100001)

    # Cz100001 = pd.DataFrame(YU100002)


    YU100002 = []
    for i in T100001:
        FD100001 = {'translation_id':i.translation_id,'field_text':i.field_text,'language_code':i.language_code}
        YU100002.append(FD100001)

    CX200001 = pd.DataFrame(YU100002)
    CX200001['language_code_index'] = CX200001['language_code'].replace({locale:0, language_code_list:1})
    CX200001 = CX200001.sort_values(['translation_id','language_code_index'], ascending=[True,True])
    CX200001 = CX200001.drop_duplicates(subset=['translation_id'], keep='first')

    Cz100001[['translation_id','cai_chan_segments_translation_id']] = Cz100001[['translation_id','cai_chan_segments_translation_id']].replace(to_replace =CX200001['translation_id'].to_list(), 
                            value =CX200001['field_text'].to_list())

    PO1000007 = Cz100001.to_json(orient='records')

    return json.loads(PO1000007)

@router.get("/cai_chan_list/{locale}", status_code = 200)
def order_s_insert_list(locale: str,category_id:int):
    
    from src.shwethe_cai_chan.orm.table.tb_cai_chan_spu_sku.main import tb_cai_chan_sku_engine,tb_cai_chan_sku


    def get_data_3(spec_parmam):
        with Session(tb_cai_chan_sku_engine) as session:
            print(spec_parmam)
            print("spec_parmam")
            heroes = session.exec(select(tb_cai_chan_sku).where(tb_cai_chan_sku.spg_id.in_(([spec_parmam]))))
            return heroes

    def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df



    A100001 = get_data_3(category_id)

    A100002 = sqmodel_to_df(A100001)

    A100002 = A100002.to_json(orient='records')

    return json.loads(A100002)