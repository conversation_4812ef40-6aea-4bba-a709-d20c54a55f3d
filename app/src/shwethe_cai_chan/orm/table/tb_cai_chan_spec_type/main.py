from typing import Optional

from sqlmodel import Field, SQLModel, create_engine

from src.Connect.postgresql_sqlalchemy import db_db_info


class tb_cai_chan_spec_type_base(SQLModel):
    spec_type_id: int


class tb_cai_chan_spec_type(tb_cai_chan_spec_type_base, table=True):
    spec_type_id: Optional[int] = Field(default=None, primary_key=True)
    translation_id: int
    

tb_cai_chan_spec_type_engine = create_engine(db_db_info, echo=True,    
        max_overflow=0,  # 超过连接池大小外最多创建的连接
        pool_size=10,  # 连接池大小
        pool_timeout=30,  # 池中没有线程最多等待的时间，否则报错
        pool_recycle=60  # 多久之后对线程池中的线程进行一次连接的回收（重置）
        )

SQLModel.metadata.create_all(tb_cai_chan_spec_type_engine)