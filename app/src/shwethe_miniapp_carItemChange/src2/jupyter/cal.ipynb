{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# !pip install pandas\n", "# !pip install psycopg2-binary"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "logging.getLogger().setLevel(logging.WARNING)\n", "logging.disable(logging.CRITICAL)\n", "\n", "import sys\n", "sys.path.append('../../../../../app/')\n", "from src.Connect.postgresql_nern import postgresql_shwethe_carItemChange\n", "from src.shwethe_miniapp_carItemChange.database import get_session \n", "from src.common.withEngineFunc import SQLcall\n", "\n", "session_generator = get_session()\n", "session = next(session_generator)  # 获取生成器中的第一个元素，即 session 对象\n", "engine = session.get_bind()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>che_liang_id</th>\n", "      <th>product_id</th>\n", "      <th>qty</th>\n", "      <th>price</th>\n", "      <th>sum_qty</th>\n", "      <th>sum_price</th>\n", "      <th>total_price</th>\n", "      <th>used_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>2023-12-31 10:07:34.180036+00:00</td>\n", "      <td>194</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>2605.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2023-11-10 08:38:14.557475+00:00</td>\n", "      <td>194</td>\n", "      <td>53776</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>8.0</td>\n", "      <td>720.0</td>\n", "      <td>2605.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>2023-12-29 10:08:32.252846+00:00</td>\n", "      <td>194</td>\n", "      <td>9463</td>\n", "      <td>1.0</td>\n", "      <td>1800.0</td>\n", "      <td>1.0</td>\n", "      <td>1800.0</td>\n", "      <td>2605.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>2023-12-17 09:56:46.873931+00:00</td>\n", "      <td>18576</td>\n", "      <td>71277</td>\n", "      <td>3.0</td>\n", "      <td>862.0</td>\n", "      <td>3.0</td>\n", "      <td>2586.0</td>\n", "      <td>2586.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2023-11-10 08:40:40.996206+00:00</td>\n", "      <td>46723</td>\n", "      <td>46805</td>\n", "      <td>2.0</td>\n", "      <td>85.0</td>\n", "      <td>4.0</td>\n", "      <td>340.0</td>\n", "      <td>2280.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2023-10-22 02:37:59.145813+00:00</td>\n", "      <td>46723</td>\n", "      <td>53776</td>\n", "      <td>3.0</td>\n", "      <td>90.0</td>\n", "      <td>18.0</td>\n", "      <td>1620.0</td>\n", "      <td>2280.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2023-10-22 02:42:25.076246+00:00</td>\n", "      <td>46723</td>\n", "      <td>61350</td>\n", "      <td>2.0</td>\n", "      <td>160.0</td>\n", "      <td>2.0</td>\n", "      <td>320.0</td>\n", "      <td>2280.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>2023-11-21 09:01:21.725281+00:00</td>\n", "      <td>104</td>\n", "      <td>1108</td>\n", "      <td>1.0</td>\n", "      <td>29.0</td>\n", "      <td>1.0</td>\n", "      <td>29.0</td>\n", "      <td>1284.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>2023-11-21 09:04:39.308762+00:00</td>\n", "      <td>104</td>\n", "      <td>46805</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>1284.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>2023-12-17 09:57:42.516680+00:00</td>\n", "      <td>104</td>\n", "      <td>53776</td>\n", "      <td>6.0</td>\n", "      <td>90.0</td>\n", "      <td>13.0</td>\n", "      <td>1170.0</td>\n", "      <td>1284.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>2023-12-22 10:22:22.548644+00:00</td>\n", "      <td>11069</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>6.0</td>\n", "      <td>510.0</td>\n", "      <td>990.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2023-10-25 03:03:44.099643+00:00</td>\n", "      <td>16696</td>\n", "      <td>53776</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>11.0</td>\n", "      <td>990.0</td>\n", "      <td>990.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>2023-12-22 10:21:30.193631+00:00</td>\n", "      <td>11069</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>3.0</td>\n", "      <td>480.0</td>\n", "      <td>990.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>2023-12-03 09:05:05.998765+00:00</td>\n", "      <td>106</td>\n", "      <td>53776</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>2.0</td>\n", "      <td>180.0</td>\n", "      <td>830.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>2023-12-14 10:10:16.085369+00:00</td>\n", "      <td>106</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>2.0</td>\n", "      <td>170.0</td>\n", "      <td>830.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>2023-12-22 10:18:45.677896+00:00</td>\n", "      <td>106</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>3.0</td>\n", "      <td>480.0</td>\n", "      <td>830.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>2023-11-21 08:59:51.322493+00:00</td>\n", "      <td>38374</td>\n", "      <td>53776</td>\n", "      <td>8.0</td>\n", "      <td>90.0</td>\n", "      <td>8.0</td>\n", "      <td>720.0</td>\n", "      <td>805.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>2023-11-24 10:15:36.666926+00:00</td>\n", "      <td>38374</td>\n", "      <td>46805</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>805.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>2023-11-22 09:57:30.023252+00:00</td>\n", "      <td>36768</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>790.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-10-08 08:44:55.059104+00:00</td>\n", "      <td>36768</td>\n", "      <td>53776</td>\n", "      <td>7.0</td>\n", "      <td>90.0</td>\n", "      <td>7.0</td>\n", "      <td>630.0</td>\n", "      <td>790.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>2023-11-22 09:55:56.849648+00:00</td>\n", "      <td>36230</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>790.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-10-16 10:32:04.998098+00:00</td>\n", "      <td>36230</td>\n", "      <td>53776</td>\n", "      <td>7.0</td>\n", "      <td>90.0</td>\n", "      <td>7.0</td>\n", "      <td>630.0</td>\n", "      <td>790.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-10-19 06:39:49.227802+00:00</td>\n", "      <td>20141</td>\n", "      <td>53776</td>\n", "      <td>8.0</td>\n", "      <td>90.0</td>\n", "      <td>8.0</td>\n", "      <td>720.0</td>\n", "      <td>720.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2023-10-25 03:02:53.012144+00:00</td>\n", "      <td>43887</td>\n", "      <td>53776</td>\n", "      <td>7.0</td>\n", "      <td>90.0</td>\n", "      <td>7.0</td>\n", "      <td>630.0</td>\n", "      <td>715.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>2023-11-10 08:41:55.506504+00:00</td>\n", "      <td>43887</td>\n", "      <td>46805</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>715.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>2023-11-13 04:05:06.635620+00:00</td>\n", "      <td>37710</td>\n", "      <td>46805</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>2.0</td>\n", "      <td>170.0</td>\n", "      <td>595.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2023-10-22 02:38:55.575841+00:00</td>\n", "      <td>37710</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>5.0</td>\n", "      <td>425.0</td>\n", "      <td>595.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-11-03 10:29:04.543319+00:00</td>\n", "      <td>48294</td>\n", "      <td>1108</td>\n", "      <td>2.0</td>\n", "      <td>29.0</td>\n", "      <td>20.0</td>\n", "      <td>580.0</td>\n", "      <td>580.0</td>\n", "      <td>drive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2023-11-12 09:49:24.545043+00:00</td>\n", "      <td>40658</td>\n", "      <td>53776</td>\n", "      <td>6.0</td>\n", "      <td>90.0</td>\n", "      <td>6.0</td>\n", "      <td>540.0</td>\n", "      <td>540.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>2023-12-15 10:23:44.940796+00:00</td>\n", "      <td>40659</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>4.0</td>\n", "      <td>340.0</td>\n", "      <td>529.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>2023-11-13 04:01:34.970029+00:00</td>\n", "      <td>40659</td>\n", "      <td>1108</td>\n", "      <td>1.0</td>\n", "      <td>29.0</td>\n", "      <td>1.0</td>\n", "      <td>29.0</td>\n", "      <td>529.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>2023-12-25 09:40:32.111632+00:00</td>\n", "      <td>40659</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>529.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2023-11-01 07:25:11.428019+00:00</td>\n", "      <td>36555</td>\n", "      <td>53776</td>\n", "      <td>5.0</td>\n", "      <td>90.0</td>\n", "      <td>5.0</td>\n", "      <td>450.0</td>\n", "      <td>450.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>2023-12-16 07:52:04.632219+00:00</td>\n", "      <td>26594</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>445.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>2023-11-21 08:57:50.493893+00:00</td>\n", "      <td>26594</td>\n", "      <td>53776</td>\n", "      <td>2.0</td>\n", "      <td>90.0</td>\n", "      <td>4.0</td>\n", "      <td>360.0</td>\n", "      <td>445.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-10-08 08:45:53.639898+00:00</td>\n", "      <td>25370</td>\n", "      <td>53776</td>\n", "      <td>3.0</td>\n", "      <td>90.0</td>\n", "      <td>3.0</td>\n", "      <td>270.0</td>\n", "      <td>430.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>2023-11-30 07:37:46.957713+00:00</td>\n", "      <td>25370</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>430.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-10-19 06:41:28.230772+00:00</td>\n", "      <td>37620</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>3.0</td>\n", "      <td>255.0</td>\n", "      <td>415.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>2023-12-22 10:24:24.682869+00:00</td>\n", "      <td>37620</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>415.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>2023-12-30 10:20:55.144481+00:00</td>\n", "      <td>19937</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>369.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2023-11-08 03:32:44.696545+00:00</td>\n", "      <td>19937</td>\n", "      <td>1108</td>\n", "      <td>1.0</td>\n", "      <td>29.0</td>\n", "      <td>1.0</td>\n", "      <td>29.0</td>\n", "      <td>369.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>2023-11-12 09:50:09.551262+00:00</td>\n", "      <td>19937</td>\n", "      <td>53776</td>\n", "      <td>2.0</td>\n", "      <td>90.0</td>\n", "      <td>2.0</td>\n", "      <td>180.0</td>\n", "      <td>369.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-10-07 10:09:13.790258+00:00</td>\n", "      <td>31838</td>\n", "      <td>53776</td>\n", "      <td>2.0</td>\n", "      <td>90.0</td>\n", "      <td>4.0</td>\n", "      <td>360.0</td>\n", "      <td>360.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>2023-12-27 10:12:44.078889+00:00</td>\n", "      <td>22046</td>\n", "      <td>53776</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>4.0</td>\n", "      <td>360.0</td>\n", "      <td>360.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2023-11-18 07:41:28.351902+00:00</td>\n", "      <td>37650</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>340.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>2023-11-18 07:42:45.731021+00:00</td>\n", "      <td>37650</td>\n", "      <td>53776</td>\n", "      <td>2.0</td>\n", "      <td>90.0</td>\n", "      <td>2.0</td>\n", "      <td>180.0</td>\n", "      <td>340.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2023-11-10 08:39:26.498813+00:00</td>\n", "      <td>24968</td>\n", "      <td>53776</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>3.0</td>\n", "      <td>270.0</td>\n", "      <td>270.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>2023-11-24 10:14:45.629213+00:00</td>\n", "      <td>46814</td>\n", "      <td>53776</td>\n", "      <td>3.0</td>\n", "      <td>90.0</td>\n", "      <td>3.0</td>\n", "      <td>270.0</td>\n", "      <td>270.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>2023-12-24 09:07:41.156083+00:00</td>\n", "      <td>2290</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>245.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>2023-12-26 09:58:34.962427+00:00</td>\n", "      <td>2290</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>245.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2023-11-01 07:27:06.154368+00:00</td>\n", "      <td>105</td>\n", "      <td>1108</td>\n", "      <td>1.0</td>\n", "      <td>29.0</td>\n", "      <td>1.0</td>\n", "      <td>29.0</td>\n", "      <td>209.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2023-11-01 07:26:26.855882+00:00</td>\n", "      <td>105</td>\n", "      <td>53776</td>\n", "      <td>2.0</td>\n", "      <td>90.0</td>\n", "      <td>2.0</td>\n", "      <td>180.0</td>\n", "      <td>209.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>2023-11-18 07:43:55.232014+00:00</td>\n", "      <td>37441</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>160.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>2023-11-30 07:37:02.898146+00:00</td>\n", "      <td>2343</td>\n", "      <td>61350</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>1.0</td>\n", "      <td>160.0</td>\n", "      <td>160.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-10-14 02:29:30.890915+00:00</td>\n", "      <td>26042</td>\n", "      <td>1108</td>\n", "      <td>2.0</td>\n", "      <td>29.0</td>\n", "      <td>4.0</td>\n", "      <td>116.0</td>\n", "      <td>116.0</td>\n", "      <td>drive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>2023-11-18 07:44:57.647077+00:00</td>\n", "      <td>43889</td>\n", "      <td>1108</td>\n", "      <td>1.0</td>\n", "      <td>29.0</td>\n", "      <td>1.0</td>\n", "      <td>29.0</td>\n", "      <td>114.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2023-11-08 03:36:36.090419+00:00</td>\n", "      <td>43889</td>\n", "      <td>46805</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>114.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>2023-12-05 09:37:24.767131+00:00</td>\n", "      <td>38201</td>\n", "      <td>53776</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>90.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>2023-12-27 10:11:45.494288+00:00</td>\n", "      <td>20455</td>\n", "      <td>53776</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>90.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>2023-11-24 10:18:58.117165+00:00</td>\n", "      <td>7565</td>\n", "      <td>46805</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>85.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime  che_liang_id  product_id  qty   price   \n", "98 2023-12-31 10:07:34.180036+00:00           194         801  1.0    85.0  \\\n", "27 2023-11-10 08:38:14.557475+00:00           194       53776  1.0    90.0   \n", "96 2023-12-29 10:08:32.252846+00:00           194        9463  1.0  1800.0   \n", "83 2023-12-17 09:56:46.873931+00:00         18576       71277  3.0   862.0   \n", "25 2023-11-10 08:40:40.996206+00:00         46723       46805  2.0    85.0   \n", "11 2023-10-22 02:37:59.145813+00:00         46723       53776  3.0    90.0   \n", "12 2023-10-22 02:42:25.076246+00:00         46723       61350  2.0   160.0   \n", "40 2023-11-21 09:01:21.725281+00:00           104        1108  1.0    29.0   \n", "43 2023-11-21 09:04:39.308762+00:00           104       46805  1.0    85.0   \n", "82 2023-12-17 09:57:42.516680+00:00           104       53776  6.0    90.0   \n", "58 2023-12-22 10:22:22.548644+00:00         11069         801  1.0    85.0   \n", "16 2023-10-25 03:03:44.099643+00:00         16696       53776  1.0    90.0   \n", "66 2023-12-22 10:21:30.193631+00:00         11069       61350  1.0   160.0   \n", "63 2023-12-03 09:05:05.998765+00:00           106       53776  1.0    90.0   \n", "70 2023-12-14 10:10:16.085369+00:00           106         801  1.0    85.0   \n", "84 2023-12-22 10:18:45.677896+00:00           106       61350  1.0   160.0   \n", "45 2023-11-21 08:59:51.322493+00:00         38374       53776  8.0    90.0   \n", "51 2023-11-24 10:15:36.666926+00:00         38374       46805  1.0    85.0   \n", "46 2023-11-22 09:57:30.023252+00:00         36768       61350  1.0   160.0   \n", "1  2023-10-08 08:44:55.059104+00:00         36768       53776  7.0    90.0   \n", "48 2023-11-22 09:55:56.849648+00:00         36230       61350  1.0   160.0   \n", "6  2023-10-16 10:32:04.998098+00:00         36230       53776  7.0    90.0   \n", "9  2023-10-19 06:39:49.227802+00:00         20141       53776  8.0    90.0   \n", "14 2023-10-25 03:02:53.012144+00:00         43887       53776  7.0    90.0   \n", "34 2023-11-10 08:41:55.506504+00:00         43887       46805  1.0    85.0   \n", "31 2023-11-13 04:05:06.635620+00:00         37710       46805  1.0    85.0   \n", "10 2023-10-22 02:38:55.575841+00:00         37710         801  1.0    85.0   \n", "0  2023-11-03 10:29:04.543319+00:00         48294        1108  2.0    29.0   \n", "29 2023-11-12 09:49:24.545043+00:00         40658       53776  6.0    90.0   \n", "78 2023-12-15 10:23:44.940796+00:00         40659         801  1.0    85.0   \n", "32 2023-11-13 04:01:34.970029+00:00         40659        1108  1.0    29.0   \n", "91 2023-12-25 09:40:32.111632+00:00         40659       61350  1.0   160.0   \n", "18 2023-11-01 07:25:11.428019+00:00         36555       53776  5.0    90.0   \n", "81 2023-12-16 07:52:04.632219+00:00         26594         801  1.0    85.0   \n", "44 2023-11-21 08:57:50.493893+00:00         26594       53776  2.0    90.0   \n", "4  2023-10-08 08:45:53.639898+00:00         25370       53776  3.0    90.0   \n", "61 2023-11-30 07:37:46.957713+00:00         25370       61350  1.0   160.0   \n", "8  2023-10-19 06:41:28.230772+00:00         37620         801  1.0    85.0   \n", "87 2023-12-22 10:24:24.682869+00:00         37620       61350  1.0   160.0   \n", "97 2023-12-30 10:20:55.144481+00:00         19937       61350  1.0   160.0   \n", "23 2023-11-08 03:32:44.696545+00:00         19937        1108  1.0    29.0   \n", "35 2023-11-12 09:50:09.551262+00:00         19937       53776  2.0    90.0   \n", "3  2023-10-07 10:09:13.790258+00:00         31838       53776  2.0    90.0   \n", "93 2023-12-27 10:12:44.078889+00:00         22046       53776  1.0    90.0   \n", "24 2023-11-18 07:41:28.351902+00:00         37650       61350  1.0   160.0   \n", "37 2023-11-18 07:42:45.731021+00:00         37650       53776  2.0    90.0   \n", "33 2023-11-10 08:39:26.498813+00:00         24968       53776  1.0    90.0   \n", "49 2023-11-24 10:14:45.629213+00:00         46814       53776  3.0    90.0   \n", "88 2023-12-24 09:07:41.156083+00:00          2290         801  1.0    85.0   \n", "92 2023-12-26 09:58:34.962427+00:00          2290       61350  1.0   160.0   \n", "17 2023-11-01 07:27:06.154368+00:00           105        1108  1.0    29.0   \n", "20 2023-11-01 07:26:26.855882+00:00           105       53776  2.0    90.0   \n", "38 2023-11-18 07:43:55.232014+00:00         37441       61350  1.0   160.0   \n", "59 2023-11-30 07:37:02.898146+00:00          2343       61350  1.0   160.0   \n", "5  2023-10-14 02:29:30.890915+00:00         26042        1108  2.0    29.0   \n", "39 2023-11-18 07:44:57.647077+00:00         43889        1108  1.0    29.0   \n", "22 2023-11-08 03:36:36.090419+00:00         43889       46805  1.0    85.0   \n", "71 2023-12-05 09:37:24.767131+00:00         38201       53776  1.0    90.0   \n", "95 2023-12-27 10:11:45.494288+00:00         20455       53776  1.0    90.0   \n", "52 2023-11-24 10:18:58.117165+00:00          7565       46805  1.0    85.0   \n", "\n", "    sum_qty  sum_price  total_price used_type  \n", "98      1.0       85.0       2605.0    change  \n", "27      8.0      720.0       2605.0  add_more  \n", "96      1.0     1800.0       2605.0    change  \n", "83      3.0     2586.0       2586.0    change  \n", "25      4.0      340.0       2280.0    change  \n", "11     18.0     1620.0       2280.0  add_more  \n", "12      2.0      320.0       2280.0    change  \n", "40      1.0       29.0       1284.0     clean  \n", "43      1.0       85.0       1284.0    change  \n", "82     13.0     1170.0       1284.0    change  \n", "58      6.0      510.0        990.0    change  \n", "16     11.0      990.0        990.0  add_more  \n", "66      3.0      480.0        990.0    change  \n", "63      2.0      180.0        830.0  add_more  \n", "70      2.0      170.0        830.0    change  \n", "84      3.0      480.0        830.0    change  \n", "45      8.0      720.0        805.0    change  \n", "51      1.0       85.0        805.0    change  \n", "46      1.0      160.0        790.0    change  \n", "1       7.0      630.0        790.0    change  \n", "48      1.0      160.0        790.0    change  \n", "6       7.0      630.0        790.0    change  \n", "9       8.0      720.0        720.0    change  \n", "14      7.0      630.0        715.0    change  \n", "34      1.0       85.0        715.0    change  \n", "31      2.0      170.0        595.0    change  \n", "10      5.0      425.0        595.0  add_more  \n", "0      20.0      580.0        580.0     drive  \n", "29      6.0      540.0        540.0    change  \n", "78      4.0      340.0        529.0  add_more  \n", "32      1.0       29.0        529.0     clean  \n", "91      1.0      160.0        529.0    change  \n", "18      5.0      450.0        450.0    change  \n", "81      1.0       85.0        445.0  add_more  \n", "44      4.0      360.0        445.0  add_more  \n", "4       3.0      270.0        430.0  add_more  \n", "61      1.0      160.0        430.0    change  \n", "8       3.0      255.0        415.0    change  \n", "87      1.0      160.0        415.0    change  \n", "97      1.0      160.0        369.0    change  \n", "23      1.0       29.0        369.0     clean  \n", "35      2.0      180.0        369.0  add_more  \n", "3       4.0      360.0        360.0  add_more  \n", "93      4.0      360.0        360.0  add_more  \n", "24      1.0      160.0        340.0    change  \n", "37      2.0      180.0        340.0  add_more  \n", "33      3.0      270.0        270.0  add_more  \n", "49      3.0      270.0        270.0  add_more  \n", "88      1.0       85.0        245.0  add_more  \n", "92      1.0      160.0        245.0    change  \n", "17      1.0       29.0        209.0     clean  \n", "20      2.0      180.0        209.0  add_more  \n", "38      1.0      160.0        160.0    change  \n", "59      1.0      160.0        160.0    change  \n", "5       4.0      116.0        116.0     drive  \n", "39      1.0       29.0        114.0     clean  \n", "22      1.0       85.0        114.0    change  \n", "71      1.0       90.0         90.0  add_more  \n", "95      1.0       90.0         90.0  add_more  \n", "52      1.0       85.0         85.0    change  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["A10001 = SQLcall(\n", "                    \"\"\"   SELECT *\n", "                        FROM item_insert\n", "                        WHERE datetime >= NOW() - INTERVAL '90 days' \n", "                        AND status = 'success'  \n", "                    \"\"\"\n", "                , engine)\n", "\n", "A10001 = A10001[[\"datetime\", \"che_liang_id\", \"product_id\", \"qty\", \"price\", \"used_type\"]]\n", "A10001['sum_qty'] = A10001.groupby(['che_liang_id', 'product_id'])['qty'].transform('sum')\n", "A10001 = A10001.sort_values(by=['che_liang_id', 'product_id', 'qty'])\n", "A10001 = A10001.drop_duplicates(subset=['che_liang_id', 'product_id'])\n", "A10001['sum_price'] = A10001['sum_qty'] * A10001['price']\n", "A10001['total_price'] = A10001.groupby('che_liang_id')['sum_price'].transform('sum')\n", "A10001 = A10001.sort_values(by='total_price', ascending=False)\n", "A10001 = A10001[[\"datetime\", \"che_liang_id\", \"product_id\", \"qty\", \"price\", \"sum_qty\", \"sum_price\", \"total_price\", \"used_type\"]]\n", "A10001.head(60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}