{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Similarity: 68.99%\n"]}], "source": ["import cv2\n", "import numpy as np\n", "import urllib.request\n", "from io import BytesIO\n", "from PIL import Image\n", "\n", "# Image URLs\n", "image_url1 = 'http://************:9000/personal/personalFolder/d9901183a12a4.jpg'\n", "image_url2 = 'http://************:9000/personal/personalFolder/2fb1203dbadb4.jpg'\n", "\n", "# Load images from URLs\n", "with urllib.request.urlopen(image_url1) as response:\n", "    image1 = Image.open(BytesIO(response.read()))\n", "\n", "with urllib.request.urlopen(image_url2) as response:\n", "    image2 = Image.open(BytesIO(response.read()))\n", "\n", "# Convert images to numpy arrays\n", "image1_np = np.array(image1)\n", "image2_np = np.array(image2)\n", "\n", "# Convert images to grayscale\n", "gray1 = cv2.cvtColor(image1_np, cv2.COLOR_BGR2GRAY)\n", "gray2 = cv2.cvtColor(image2_np, cv2.COLOR_BGR2GRAY)\n", "\n", "# Compute histograms\n", "hist1 = cv2.calcHist([gray1], [0], None, [256], [0, 256])\n", "hist2 = cv2.calcHist([gray2], [0], None, [256], [0, 256])\n", "\n", "# Normalize histograms\n", "hist1 /= hist1.sum()\n", "hist2 /= hist2.sum()\n", "\n", "# Compare histograms using <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> distance\n", "bhat<PERSON><PERSON><PERSON><PERSON>_distance = cv2.compareHist(hist1, hist2, cv2.HISTCMP_BHATTACHARYYA)\n", "\n", "# Convert <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> distance to similarity percentage\n", "similarity_percentage = (1 - bhatta<PERSON><PERSON><PERSON>_distance) * 100\n", "\n", "print(f\"Similarity: {similarity_percentage:.2f}%\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fffffffffff\n"]}], "source": ["print(\"fffffffffff\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'skimage'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/shwethe_order_in_fast_api/app/src/shwethe_miniapp_carItemChange/src/jupyter/ocr2.ipynb Cell 3\u001b[0m line \u001b[0;36m6\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2B192.168.1.11/home/<USER>/shwethe_order_in_fast_api/app/src/shwethe_miniapp_carItemChange/src/jupyter/ocr2.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=3'>4</a>\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mio\u001b[39;00m \u001b[39mimport\u001b[39;00m BytesIO\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2B192.168.1.11/home/<USER>/shwethe_order_in_fast_api/app/src/shwethe_miniapp_carItemChange/src/jupyter/ocr2.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=4'>5</a>\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mPIL\u001b[39;00m \u001b[39mimport\u001b[39;00m Image\n\u001b[0;32m----> <a href='vscode-notebook-cell://ssh-remote%2B192.168.1.11/home/<USER>/shwethe_order_in_fast_api/app/src/shwethe_miniapp_carItemChange/src/jupyter/ocr2.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=5'>6</a>\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mskimage\u001b[39;00m \u001b[39mimport\u001b[39;00m feature\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2B192.168.1.11/home/<USER>/shwethe_order_in_fast_api/app/src/shwethe_miniapp_carItemChange/src/jupyter/ocr2.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=7'>8</a>\u001b[0m \u001b[39m# Image URLs\u001b[39;00m\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2B192.168.1.11/home/<USER>/shwethe_order_in_fast_api/app/src/shwethe_miniapp_carItemChange/src/jupyter/ocr2.ipynb#W2sdnNjb2RlLXJlbW90ZQ%3D%3D?line=8'>9</a>\u001b[0m image_url1 \u001b[39m=\u001b[39m \u001b[39m'\u001b[39m\u001b[39mhttp://************:9000/personal/personalFolder/d9901183a12a4.jpg\u001b[39m\u001b[39m'\u001b[39m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'skimage'"]}], "source": ["import cv2\n", "import numpy as np\n", "import urllib.request\n", "from io import BytesIO\n", "from PIL import Image\n", "from skimage import feature\n", "\n", "# Image URLs\n", "image_url1 = 'http://************:9000/personal/personalFolder/d9901183a12a4.jpg'\n", "image_url2 = 'http://************:9000/personal/personalFolder/d9901183a12a4.jpg'\n", "\n", "# Load images from URLs\n", "with urllib.request.urlopen(image_url1) as response:\n", "    image1 = Image.open(BytesIO(response.read()))\n", "\n", "with urllib.request.urlopen(image_url2) as response:\n", "    image2 = Image.open(BytesIO(response.read()))\n", "\n", "# Convert images to numpy arrays\n", "image1_np = np.array(image1)\n", "image2_np = np.array(image2)\n", "\n", "# Convert images to grayscale\n", "gray1 = cv2.cvtColor(image1_np, cv2.COLOR_BGR2GRAY)\n", "gray2 = cv2.cvtColor(image2_np, cv2.COLOR_BGR2GRAY)\n", "\n", "# Extract Local Binary Pattern features\n", "lbp1 = feature.local_binary_pattern(gray1, P=8, R=1, method=\"uniform\")\n", "lbp2 = feature.local_binary_pattern(gray2, P=8, R=1, method=\"uniform\")\n", "\n", "# Flatten the LBP features\n", "lbp1_flat = lbp1.flatten()\n", "lbp2_flat = lbp2.flatten()\n", "\n", "# Calculate the L1 distance between the flattened LBP features\n", "distance = np.sum(np.abs(lbp1_flat - lbp2_flat))\n", "\n", "# Set a threshold for similarity to determine if the faces are similar\n", "threshold = 2000  # Adjust as needed\n", "if distance < threshold:\n", "    print(f\"Faces are similar, potential double registration. Distance: {distance:.2f}\")\n", "else:\n", "    print(f\"Faces are not similar. Distance: {distance:.2f}\")\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}