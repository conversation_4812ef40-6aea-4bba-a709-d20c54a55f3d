{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"cccccccccccc\")\n", "# !pip install opencv-python-headless\n", "!pip install Pillow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "import urllib.request\n", "from io import BytesIO\n", "from PIL import Image\n", "\n", "# Image URLs\n", "image_url1 = 'http://************:9000/im-entry/product/28fed1f45b314.jpg'\n", "image_url2 = 'http://************:9000/im-entry/product/47aadfd565114.jpg'\n", "\n", "# Load images from URLs\n", "with urllib.request.urlopen(image_url1) as response:\n", "    image1 = Image.open(BytesIO(response.read()))\n", "\n", "with urllib.request.urlopen(image_url2) as response:\n", "    image2 = Image.open(BytesIO(response.read()))\n", "\n", "# Convert images to numpy arrays\n", "image1_np = np.array(image1)\n", "image2_np = np.array(image2)\n", "\n", "# Convert images to grayscale\n", "gray1 = cv2.cvtColor(image1_np, cv2.COLOR_BGR2GRAY)\n", "gray2 = cv2.cvtColor(image2_np, cv2.COLOR_BGR2GRAY)\n", "\n", "# Compute histograms\n", "hist1 = cv2.calcHist([gray1], [0], None, [256], [0, 256])\n", "hist2 = cv2.calcHist([gray2], [0], None, [256], [0, 256])\n", "\n", "# Normalize histograms\n", "hist1 /= hist1.sum()\n", "hist2 /= hist2.sum()\n", "\n", "# Compare histograms using <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> distance\n", "similarity = cv2.compareHist(hist1, hist2, cv2.HISTCMP_BHATTACHARYYA)\n", "\n", "# Set a threshold for similarity to determine if the faces are similar\n", "threshold = 0.2  # Adjust as needed\n", "if similarity < threshold:\n", "    print(\"Faces are similar, potential double registration.\")\n", "else:\n", "    print(\"Faces are not similar.\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "import urllib.request\n", "from io import BytesIO\n", "from PIL import Image\n", "\n", "# Image URLs\n", "image_url1 = 'http://************:9000/personal/personalFolder/d9901183a12a4.jpg'\n", "image_url2 = 'http://************:9000/personal/personalFolder/2fb1203dbadb4.jpg'\n", "\n", "# Load images from URLs\n", "with urllib.request.urlopen(image_url1) as response:\n", "    image1 = Image.open(BytesIO(response.read()))\n", "\n", "with urllib.request.urlopen(image_url2) as response:\n", "    image2 = Image.open(BytesIO(response.read()))\n", "\n", "# Convert images to numpy arrays\n", "image1_np = np.array(image1)\n", "image2_np = np.array(image2)\n", "\n", "# Convert images to grayscale\n", "gray1 = cv2.cvtColor(image1_np, cv2.COLOR_BGR2GRAY)\n", "gray2 = cv2.cvtColor(image2_np, cv2.COLOR_BGR2GRAY)\n", "\n", "# Compute histograms\n", "hist1 = cv2.calcHist([gray1], [0], None, [256], [0, 256])\n", "hist2 = cv2.calcHist([gray2], [0], None, [256], [0, 256])\n", "\n", "# Normalize histograms\n", "hist1 /= hist1.sum()\n", "hist2 /= hist2.sum()\n", "\n", "# Compare histograms using <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> distance\n", "bhat<PERSON><PERSON><PERSON><PERSON>_distance = cv2.compareHist(hist1, hist2, cv2.HISTCMP_BHATTACHARYYA)\n", "\n", "# Convert <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> distance to similarity percentage\n", "similarity_percentage = (1 - bhatta<PERSON><PERSON><PERSON>_distance) * 100\n", "\n", "print(f\"Similarity: {similarity_percentage:.2f}%\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "import urllib.request\n", "from io import BytesIO\n", "from PIL import Image\n", "from skimage import feature\n", "\n", "# Image URLs\n", "image_url1 = 'http://************:9000/personal/personalFolder/d9901183a12a4.jpg'\n", "image_url2 = 'http://************:9000/personal/personalFolder/d9901183a12a4.jpg'\n", "\n", "# Load images from URLs\n", "with urllib.request.urlopen(image_url1) as response:\n", "    image1 = Image.open(BytesIO(response.read()))\n", "\n", "with urllib.request.urlopen(image_url2) as response:\n", "    image2 = Image.open(BytesIO(response.read()))\n", "\n", "# Convert images to numpy arrays\n", "image1_np = np.array(image1)\n", "image2_np = np.array(image2)\n", "\n", "# Convert images to grayscale\n", "gray1 = cv2.cvtColor(image1_np, cv2.COLOR_BGR2GRAY)\n", "gray2 = cv2.cvtColor(image2_np, cv2.COLOR_BGR2GRAY)\n", "\n", "# Extract Local Binary Pattern features\n", "lbp1 = feature.local_binary_pattern(gray1, P=8, R=1, method=\"uniform\")\n", "lbp2 = feature.local_binary_pattern(gray2, P=8, R=1, method=\"uniform\")\n", "\n", "# Flatten the LBP features\n", "lbp1_flat = lbp1.flatten()\n", "lbp2_flat = lbp2.flatten()\n", "\n", "# Calculate the L1 distance between the flattened LBP features\n", "distance = np.sum(np.abs(lbp1_flat - lbp2_flat))\n", "\n", "# Set a threshold for similarity to determine if the faces are similar\n", "threshold = 2000  # Adjust as needed\n", "if distance < threshold:\n", "    print(f\"Faces are similar, potential double registration. Distance: {distance:.2f}\")\n", "else:\n", "    print(f\"Faces are not similar. Distance: {distance:.2f}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}