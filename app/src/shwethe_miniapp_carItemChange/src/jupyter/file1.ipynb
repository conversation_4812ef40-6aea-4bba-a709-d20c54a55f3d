{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-07-23 06:44:38,178 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2023-07-23 06:44:38,181 INFO sqlalchemy.engine.Engine SELECT check_stock.record_id, check_stock.pd_image, check_stock.sub, check_stock.datetime, check_stock.warehouse_id, check_stock.che_liang_id, check_stock.jia_yi_fang_a, check_stock.jia_yi_fang_b, check_stock.lei_a, check_stock.lei_b, check_stock.product_id, check_stock.qty, check_stock.price, check_stock.bi_zhi, check_stock.group_id, check_stock.old_qty, check_stock.amount_in_de, check_stock.status, check_stock.auto_id \n", "FROM check_stock\n", "2023-07-23 06:44:38,185 INFO sqlalchemy.engine.Engine [cached since 30.14s ago] {}\n", "2023-07-23 06:44:38,205 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>warehouse_id</th>\n", "      <th>price</th>\n", "      <th>che_liang_id</th>\n", "      <th>bi_zhi</th>\n", "      <th>jia_yi_fang_a</th>\n", "      <th>group_id</th>\n", "      <th>record_id</th>\n", "      <th>jia_yi_fang_b</th>\n", "      <th>old_qty</th>\n", "      <th>pd_image</th>\n", "      <th>lei_a</th>\n", "      <th>amount_in_de</th>\n", "      <th>sub</th>\n", "      <th>lei_b</th>\n", "      <th>status</th>\n", "      <th>datetime</th>\n", "      <th>product_id</th>\n", "      <th>auto_id</th>\n", "      <th>qty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>793.0</td>\n", "      <td>50.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>793.0</td>\n", "      <td>78f35a11</td>\n", "      <td>CS00000281</td>\n", "      <td>793.0</td>\n", "      <td>52.0</td>\n", "      <td>[192.168.1.12:9000/personal/workshopCheckStock...</td>\n", "      <td>37.0</td>\n", "      <td>900.0</td>\n", "      <td>{}</td>\n", "      <td>22.0</td>\n", "      <td>waiting</td>\n", "      <td>2023-07-09 11:41:30.812967+06:30</td>\n", "      <td>536.0</td>\n", "      <td>282</td>\n", "      <td>952.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>CS00000275</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>276</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   warehouse_id  price  che_liang_id  bi_zhi  jia_yi_fang_a  group_id   \n", "0         793.0   50.0           0.0     0.0          793.0  78f35a11  \\\n", "1           0.0    0.0           0.0     0.0            0.0         0   \n", "\n", "    record_id  jia_yi_fang_b  old_qty   \n", "0  CS00000281          793.0     52.0  \\\n", "1  CS00000275            0.0      0.0   \n", "\n", "                                            pd_image  lei_a  amount_in_de sub   \n", "0  [192.168.1.12:9000/personal/workshopCheckStock...   37.0         900.0  {}  \\\n", "1                                                  0    0.0           0.0   0   \n", "\n", "   lei_b   status                          datetime  product_id  auto_id   \n", "0   22.0  waiting  2023-07-09 11:41:30.812967+06:30       536.0      282  \\\n", "1    0.0        0                                 0         0.0      276   \n", "\n", "     qty  \n", "0  952.0  \n", "1    0.0  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "from src.shwethe_miniapp_carItemChange.database import get_session\n", "from src.shwethe_miniapp_carItemChange.src.models.models import check_stock\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def dataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "def getCarAtive100():\n", "    with get_session_dependency() as db:\n", "        mergeDF = dataframe(db.exec(select(check_stock)).all(), to_dict=False)\n", "    return mergeDF\n", "\n", "getCarAtive100()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}