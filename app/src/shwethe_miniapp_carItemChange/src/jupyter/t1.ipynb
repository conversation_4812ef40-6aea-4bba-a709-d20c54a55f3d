{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-02-23 04:30:48,994 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-02-23 04:30:48,998 INFO sqlalchemy.engine.Engine SELECT item_insert.datetime, item_insert.product_id, item_insert.qty, item_insert.price \n", "FROM item_insert \n", "WHERE item_insert.datetime > %(datetime_1)s AND item_insert.status = %(status_1)s\n", "2025-02-23 04:30:49,004 INFO sqlalchemy.engine.Engine [cached since 4464s ago] {'datetime_1': datetime.date(2024, 2, 24), 'status_1': 'success'}\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>product_id</th>\n", "      <th>sum_qty</th>\n", "      <th>total_price</th>\n", "      <th>mean_day_diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-02-02 16:52:26.243055+06:30</td>\n", "      <td>53776</td>\n", "      <td>507.00</td>\n", "      <td>54794.0000</td>\n", "      <td>2.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-02-21 17:07:39.668542+06:30</td>\n", "      <td>801</td>\n", "      <td>122.00</td>\n", "      <td>10395.0000</td>\n", "      <td>3.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-02-14 16:57:28.617856+06:30</td>\n", "      <td>81744</td>\n", "      <td>23.00</td>\n", "      <td>2208.0000</td>\n", "      <td>0.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-02-08 16:57:58.191345+06:30</td>\n", "      <td>315</td>\n", "      <td>12.00</td>\n", "      <td>1080.0000</td>\n", "      <td>3.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-12-04 17:07:05.475126+06:30</td>\n", "      <td>10730</td>\n", "      <td>8.00</td>\n", "      <td>1920.0000</td>\n", "      <td>135.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2025-01-14 16:56:52.259135+06:30</td>\n", "      <td>7743</td>\n", "      <td>7.00</td>\n", "      <td>315.0000</td>\n", "      <td>2.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-12-25 16:34:51.726669+06:30</td>\n", "      <td>13528</td>\n", "      <td>6.70</td>\n", "      <td>402.0000000000000106581410364</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-11-29 16:52:50.796587+06:30</td>\n", "      <td>10175</td>\n", "      <td>6.00</td>\n", "      <td>1170.0000</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-12-24 17:04:22.135798+06:30</td>\n", "      <td>596</td>\n", "      <td>5.00</td>\n", "      <td>8000.0000</td>\n", "      <td>53.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2025-02-01 17:00:56.349511+06:30</td>\n", "      <td>432</td>\n", "      <td>4.00</td>\n", "      <td>2000.0000</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-12-25 16:33:04.765572+06:30</td>\n", "      <td>13527</td>\n", "      <td>3.50</td>\n", "      <td>455.00000</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2025-01-07 16:40:53.965247+06:30</td>\n", "      <td>9957</td>\n", "      <td>3.00</td>\n", "      <td>1020.0000</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-06-30 16:53:36.062696+06:30</td>\n", "      <td>79858</td>\n", "      <td>2.00</td>\n", "      <td>560.0000</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-11-28 17:20:37.430802+06:30</td>\n", "      <td>52371</td>\n", "      <td>1.00</td>\n", "      <td>100.0000</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime  product_id  sum_qty   \n", "0  2025-02-02 16:52:26.243055+06:30       53776   507.00  \\\n", "1  2025-02-21 17:07:39.668542+06:30         801   122.00   \n", "2  2025-02-14 16:57:28.617856+06:30       81744    23.00   \n", "3  2025-02-08 16:57:58.191345+06:30         315    12.00   \n", "4  2024-12-04 17:07:05.475126+06:30       10730     8.00   \n", "5  2025-01-14 16:56:52.259135+06:30        7743     7.00   \n", "6  2024-12-25 16:34:51.726669+06:30       13528     6.70   \n", "7  2024-11-29 16:52:50.796587+06:30       10175     6.00   \n", "8  2024-12-24 17:04:22.135798+06:30         596     5.00   \n", "9  2025-02-01 17:00:56.349511+06:30         432     4.00   \n", "10 2024-12-25 16:33:04.765572+06:30       13527     3.50   \n", "11 2025-01-07 16:40:53.965247+06:30        9957     3.00   \n", "12 2024-06-30 16:53:36.062696+06:30       79858     2.00   \n", "13 2024-11-28 17:20:37.430802+06:30       52371     1.00   \n", "\n", "                      total_price  mean_day_diff  \n", "0                      54794.0000           2.40  \n", "1                      10395.0000           3.50  \n", "2                       2208.0000           0.20  \n", "3                       1080.0000           3.00  \n", "4                       1920.0000         135.00  \n", "5                        315.0000           2.50  \n", "6   402.0000000000000106581410364           0.00  \n", "7                       1170.0000           0.00  \n", "8                       8000.0000          53.00  \n", "9                       2000.0000           0.00  \n", "10                      455.00000           0.00  \n", "11                      1020.0000           0.00  \n", "12                       560.0000           0.00  \n", "13                       100.0000           0.00  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import datetime as DT\n", "from decimal import Decimal\n", "import pandas as pd\n", "import requests\n", "import json\n", "from sqlmodel import Session, select\n", "from src.shwethe_miniapp_carItemChange.database import get_session\n", "from src.shwethe_miniapp_carItemChange.src.models.models import item_insert\n", "from src.Connect.https_connect import mongodb_data_api\n", "\n", "def product_id_rank100(db: Session):  # Removed Depends(get_session)\n", "    try:\n", "        def getCheckStockReport():\n", "            today = DT.date.today()\n", "            week_ago = today - DT.<PERSON><PERSON><PERSON>(days=365)\n", "            heroesPersonal = db.exec(select(item_insert.datetime, item_insert.product_id, item_insert.qty, item_insert.price).where(item_insert.datetime > week_ago, item_insert.status == 'success')).all()\n", "            A10001 = pd.DataFrame([dict(row) for row in heroesPersonal])\n", "\n", "            A10001 = A10001.sort_values(by=['datetime', 'product_id'], ascending=[False, False])\n", "            A10001['sum_qty'] = A10001.groupby('product_id')['qty'].transform('sum')\n", "            A10001['sum_price'] = A10001['qty'].apply(Decimal) * A10001['price']\n", "            A10001['total_price'] = A10001.groupby('product_id')['sum_price'].transform('sum')\n", "\n", "            A10001['datetime'] = pd.to_datetime(A10001['datetime'])\n", "            A10001 = A10001.sort_values(by=['product_id', 'datetime'])\n", "            A10001['day_diff'] = A10001.groupby('product_id')['datetime'].diff().dt.days\n", "            A10001['day_diff'] = A10001['day_diff'].fillna(0)\n", "\n", "            A10001['mean_day_diff'] = A10001.groupby('product_id')['day_diff'].transform('mean')\n", "            A10001 = A10001.round(1)\n", "            \n", "            df_sorted = A10001.sort_values(by=['sum_qty', 'datetime'], ascending=[False, True])\n", "            A10001 = df_sorted.drop_duplicates(subset=['sum_qty'], keep='last')\n", "\n", "            return A10001\n", "\n", "        def getName():\n", "            fgetCheckStockReport = getCheckStockReport()\n", "            df = fgetCheckStockReport.rename(columns={\"product_id\": 'product_id'})\n", "            url = mongodb_data_api + '/api/v2/search/product_list_id'\n", "            to_dict = df[['product_id']].to_dict('records')\n", "            body_raw = {\"data_api\": to_dict}\n", "            getProduct = requests.get(url=url, json=body_raw).json()\n", "            getProduct = pd.DataFrame(getProduct)\n", "            return getProduct, fgetCheckStockReport\n", "\n", "        def fusion():\n", "            getProduct, fgetCheckStockReport = getName()\n", "            mergeProduct = fgetCheckStockReport.merge(getProduct, left_on='product_id', right_on='product_id', how='left')\n", "            # mergeProduct = mergeProduct[['datetime', 'product_id', 'price', 'sum_qty', 'total_price', 'mean_day_diff', 'product_idname', 'product_mm_name', 'product_d_name', 'th_name']]\n", "            mergeProduct = mergeProduct[['datetime', 'product_id', 'sum_qty', 'total_price', 'mean_day_diff']]\n", "            return mergeProduct\n", "\n", "        ddd = fusion()\n", "        # mergeDF = ddd.to_dict(\"records\")\n", "        mergeDF = ddd\n", "    except:\n", "        mergeDF = []\n", "    return mergeDF\n", "\n", "# Test the function\n", "db = next(get_session())\n", "result = product_id_rank100(db)\n", "result"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-02-23 04:42:05,149 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2025-02-23 04:42:05,153 INFO sqlalchemy.engine.Engine SELECT item_insert.datetime, item_insert.product_id, item_insert.qty, item_insert.price \n", "FROM item_insert \n", "WHERE item_insert.datetime > %(datetime_1)s AND item_insert.status = %(status_1)s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-02-23 04:42:05,154 INFO sqlalchemy.engine.Engine [cached since 5141s ago] {'datetime_1': datetime.date(2024, 2, 24), 'status_1': 'success'}\n", "\n", "Product Analysis with Days Since Last Transaction:\n", "Total Products: 14\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>product_id</th>\n", "      <th>sum_qty</th>\n", "      <th>total_price</th>\n", "      <th>mean_day_diff</th>\n", "      <th>days_since_last</th>\n", "      <th>status</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-02-21 10:37:39.668542+00:00</td>\n", "      <td>801</td>\n", "      <td>122.00</td>\n", "      <td>10395.0000</td>\n", "      <td>3.50</td>\n", "      <td>1</td>\n", "      <td>Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-02-14 10:27:28.617856+00:00</td>\n", "      <td>81744</td>\n", "      <td>23.00</td>\n", "      <td>2208.0000</td>\n", "      <td>0.20</td>\n", "      <td>8</td>\n", "      <td>Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-02-08 10:27:58.191345+00:00</td>\n", "      <td>315</td>\n", "      <td>12.00</td>\n", "      <td>1080.0000</td>\n", "      <td>3.00</td>\n", "      <td>14</td>\n", "      <td>Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-02-02 10:22:26.243055+00:00</td>\n", "      <td>53776</td>\n", "      <td>507.00</td>\n", "      <td>54794.0000</td>\n", "      <td>2.40</td>\n", "      <td>20</td>\n", "      <td>Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2025-02-01 10:30:56.349511+00:00</td>\n", "      <td>432</td>\n", "      <td>4.00</td>\n", "      <td>2000.0000</td>\n", "      <td>0.00</td>\n", "      <td>21</td>\n", "      <td>Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2025-01-14 10:26:52.259135+00:00</td>\n", "      <td>7743</td>\n", "      <td>7.00</td>\n", "      <td>315.0000</td>\n", "      <td>2.50</td>\n", "      <td>39</td>\n", "      <td>Semi-Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2025-01-07 10:10:53.965247+00:00</td>\n", "      <td>9957</td>\n", "      <td>3.00</td>\n", "      <td>1020.0000</td>\n", "      <td>0.00</td>\n", "      <td>46</td>\n", "      <td>Semi-Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-12-25 10:04:51.726669+00:00</td>\n", "      <td>13528</td>\n", "      <td>6.70</td>\n", "      <td>402.0000000000000106581410364</td>\n", "      <td>0.00</td>\n", "      <td>59</td>\n", "      <td>Semi-Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-12-25 10:03:04.765572+00:00</td>\n", "      <td>13527</td>\n", "      <td>3.50</td>\n", "      <td>455.00000</td>\n", "      <td>0.00</td>\n", "      <td>59</td>\n", "      <td>Semi-Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-12-24 10:34:22.135798+00:00</td>\n", "      <td>596</td>\n", "      <td>5.00</td>\n", "      <td>8000.0000</td>\n", "      <td>53.00</td>\n", "      <td>60</td>\n", "      <td>Semi-Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-12-04 10:37:05.475126+00:00</td>\n", "      <td>10730</td>\n", "      <td>8.00</td>\n", "      <td>1920.0000</td>\n", "      <td>135.00</td>\n", "      <td>80</td>\n", "      <td>Semi-Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-11-29 10:22:50.796587+00:00</td>\n", "      <td>10175</td>\n", "      <td>6.00</td>\n", "      <td>1170.0000</td>\n", "      <td>0.00</td>\n", "      <td>85</td>\n", "      <td>Semi-Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-11-28 10:50:37.430802+00:00</td>\n", "      <td>52371</td>\n", "      <td>1.00</td>\n", "      <td>100.0000</td>\n", "      <td>0.00</td>\n", "      <td>86</td>\n", "      <td>Semi-Active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-06-30 10:23:36.062696+00:00</td>\n", "      <td>79858</td>\n", "      <td>2.00</td>\n", "      <td>560.0000</td>\n", "      <td>0.00</td>\n", "      <td>237</td>\n", "      <td>Inactive</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime  product_id  sum_qty   \n", "1  2025-02-21 10:37:39.668542+00:00         801   122.00  \\\n", "2  2025-02-14 10:27:28.617856+00:00       81744    23.00   \n", "3  2025-02-08 10:27:58.191345+00:00         315    12.00   \n", "0  2025-02-02 10:22:26.243055+00:00       53776   507.00   \n", "9  2025-02-01 10:30:56.349511+00:00         432     4.00   \n", "5  2025-01-14 10:26:52.259135+00:00        7743     7.00   \n", "11 2025-01-07 10:10:53.965247+00:00        9957     3.00   \n", "6  2024-12-25 10:04:51.726669+00:00       13528     6.70   \n", "10 2024-12-25 10:03:04.765572+00:00       13527     3.50   \n", "8  2024-12-24 10:34:22.135798+00:00         596     5.00   \n", "4  2024-12-04 10:37:05.475126+00:00       10730     8.00   \n", "7  2024-11-29 10:22:50.796587+00:00       10175     6.00   \n", "13 2024-11-28 10:50:37.430802+00:00       52371     1.00   \n", "12 2024-06-30 10:23:36.062696+00:00       79858     2.00   \n", "\n", "                      total_price  mean_day_diff  days_since_last       status  \n", "1                      10395.0000           3.50                1       Active  \n", "2                       2208.0000           0.20                8       Active  \n", "3                       1080.0000           3.00               14       Active  \n", "0                      54794.0000           2.40               20       Active  \n", "9                       2000.0000           0.00               21       Active  \n", "5                        315.0000           2.50               39  Semi-Active  \n", "11                      1020.0000           0.00               46  Semi-Active  \n", "6   402.0000000000000106581410364           0.00               59  Semi-Active  \n", "10                      455.00000           0.00               59  Semi-Active  \n", "8                       8000.0000          53.00               60  Semi-Active  \n", "4                       1920.0000         135.00               80  Semi-Active  \n", "7                       1170.0000           0.00               85  Semi-Active  \n", "13                       100.0000           0.00               86  Semi-Active  \n", "12                       560.0000           0.00              237     Inactive  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import datetime as DT\n", "from decimal import Decimal\n", "import pandas as pd\n", "import requests\n", "import json\n", "from sqlmodel import Session, select\n", "from src.shwethe_miniapp_carItemChange.database import get_session\n", "from src.shwethe_miniapp_carItemChange.src.models.models import item_insert\n", "from src.Connect.https_connect import mongodb_data_api\n", "\n", "def product_id_rank100(db: Session):  # Removed Depends(get_session)\n", "    try:\n", "        def getCheckStockReport():\n", "            today = DT.date.today()\n", "            week_ago = today - DT.<PERSON><PERSON><PERSON>(days=365)\n", "            heroesPersonal = db.exec(select(item_insert.datetime, item_insert.product_id, item_insert.qty, item_insert.price).where(item_insert.datetime > week_ago, item_insert.status == 'success')).all()\n", "            A10001 = pd.DataFrame([dict(row) for row in heroesPersonal])\n", "\n", "            # Convert datetime string to pandas datetime\n", "            A10001['datetime'] = pd.to_datetime(A10001['datetime'], utc=True)\n", "            \n", "            A10001 = A10001.sort_values(by=['datetime', 'product_id'], ascending=[False, False])\n", "            A10001['sum_qty'] = A10001.groupby('product_id')['qty'].transform('sum')\n", "            A10001['sum_price'] = A10001['qty'].apply(Decimal) * A10001['price']\n", "            A10001['total_price'] = A10001.groupby('product_id')['sum_price'].transform('sum')\n", "\n", "            A10001 = A10001.sort_values(by=['product_id', 'datetime'])\n", "            A10001['day_diff'] = A10001.groupby('product_id')['datetime'].diff().dt.days\n", "            A10001['day_diff'] = A10001['day_diff'].fillna(0)\n", "\n", "            A10001['mean_day_diff'] = A10001.groupby('product_id')['day_diff'].transform('mean')\n", "            \n", "            # Calculate days since last transaction\n", "            today_utc = pd.Timestamp.now(tz='UTC')\n", "            A10001['days_since_last'] = (today_utc - A10001['datetime']).dt.days\n", "            \n", "            # Add status based on days_since_last\n", "            A10001['status'] = A10001['days_since_last'].apply(\n", "                lambda x: 'Active' if x <= 30 else \n", "                         'Semi-Active' if x <= 90 else \n", "                         'Inactive'\n", "            )\n", "            \n", "            A10001 = A10001.round(1)\n", "            \n", "            df_sorted = A10001.sort_values(by=['sum_qty', 'datetime'], ascending=[False, True])\n", "            A10001 = df_sorted.drop_duplicates(subset=['sum_qty'], keep='last')\n", "\n", "            return A10001\n", "\n", "        def getName():\n", "            fgetCheckStockReport = getCheckStockReport()\n", "            df = fgetCheckStockReport.rename(columns={\"product_id\": 'product_id'})\n", "            url = mongodb_data_api + '/api/v2/search/product_list_id'\n", "            to_dict = df[['product_id']].to_dict('records')\n", "            body_raw = {\"data_api\": to_dict}\n", "            getProduct = requests.get(url=url, json=body_raw).json()\n", "            getProduct = pd.DataFrame(getProduct)\n", "            return getProduct, fgetCheckStockReport\n", "\n", "        def fusion():\n", "            getProduct, fgetCheckStockReport = getName()\n", "            mergeProduct = fgetCheckStockReport.merge(getProduct, left_on='product_id', right_on='product_id', how='left')\n", "            mergeProduct = mergeProduct[['datetime', 'product_id', 'sum_qty', 'total_price', \n", "                                       'mean_day_diff', 'days_since_last', 'status']]\n", "            return mergeProduct\n", "\n", "        ddd = fusion()\n", "        mergeDF = ddd\n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        mergeDF = pd.DataFrame()\n", "    return mergeDF\n", "\n", "# Test the function\n", "db = next(get_session())\n", "result = product_id_rank100(db)\n", "\n", "# Format the display\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_rows', None)\n", "pd.options.display.float_format = '{:.2f}'.format\n", "\n", "# Sort by days_since_last to see most recent first\n", "if not result.empty:\n", "    result_sorted = result.sort_values('days_since_last')\n", "    print(\"\\nProduct Analysis with Days Since Last Transaction:\")\n", "    print(f\"Total Products: {len(result_sorted)}\")\n", "    display(result_sorted)\n", "else:\n", "    print(\"No data found\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}