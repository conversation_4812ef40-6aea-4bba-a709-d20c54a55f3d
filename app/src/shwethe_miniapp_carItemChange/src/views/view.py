from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_miniapp_carItemChange.database import get_session
from src.shwethe_miniapp_carItemChange.src.crud.crud import (
    getCarAtive100,
    MAddItem100,
    MGetListItem100,
    MUseItem100,
    MSearchItem100,
    delItem100,
    MSearchItem2100,
    MGetListCar100,
    MGetListStore100,
    MList100,
    sendToBigTable100,
    MGetPrice100,
    MGetStoreByProduct_id100,
    MUpdateStatus100,
    updateFirst100,
    updateRealtimeQty100,
    product_id_rank100
)
from src.shwethe_miniapp_carItemChange.src.models.models import item_insert, item_insert_post, item_select, item_select_post
 
from fastapi import APIRouter, Depends, Query, Body

router = APIRouter()


@router.get("/getCarAtive")
def getCarAtive(db: Session = Depends(get_session)):
    return getCarAtive100(db=db)

@router.get("/MSearchItem")
def MSearchItem(getApiItem: str, db: Session = Depends(get_session)):
    return MSearchItem100(getApiItem=getApiItem, db=db)

@router.post("/MAddItem")
def MAddItem(hero : item_select_post , db: Session = Depends(get_session)):
    return MAddItem100(hero=hero,db=db)
    
@router.delete("/delItem")
def delItem(jia_yi_id: int, db: Session = Depends(get_session)):
    return delItem100(jia_yi_id=jia_yi_id, db=db)

@router.get("/MGetListItem")
def MGetListItem(db: Session = Depends(get_session)):
    return MGetListItem100(db=db)

@router.get("/MGetListCar")
def MGetListCar(db: Session = Depends(get_session)):
    return MGetListCar100(db=db)

@router.get("/MGetListStore")
def MGetListStore(db: Session = Depends(get_session)):
    return MGetListStore100(db=db)

@router.post("/MUseItem")
def MUseItem(hero : List[item_insert_post] , db: Session = Depends(get_session)):
    return MUseItem100(hero=hero,db=db)

@router.get("/MSearchItem2")
def MSearchItem2(getApiItem: str, db: Session = Depends(get_session)):
    return MSearchItem2100(getApiItem=getApiItem, db=db)

# @router.get("/MList")
# def MList(day: str, db: Session = Depends(get_session)):
#     return MList100(day=day, db=db)
@router.get("/MList")
def MList(day: str, days_ago: int = 3, db: Session = Depends(get_session)):
    return MList100(day=day, days_ago=days_ago, db=db)

@router.get("/MGetPrice")
def MGetPrice(product_id: int, db: Session = Depends(get_session)):
    return MGetPrice100(product_id=product_id, db=db)

@router.get("/MGetStoreByProduct_id")
def MGetStoreByProduct_id(product_id: int, db: Session = Depends(get_session)):
    return MGetStoreByProduct_id100(product_id=product_id, db=db)

@router.put("/MUpdateStatus")
def MUpdateStatus(record_id: str, statusText: str, db: Session = Depends(get_session)):
    return MUpdateStatus100(record_id=record_id, statusText=statusText, db=db)

@router.put("/updateFirst")
def updateFirst(db: Session = Depends(get_session)):
    return updateFirst100(db=db)

@router.put("/updateRealtimeQty")
def updateRealtimeQty(db: Session = Depends(get_session)):
    return updateRealtimeQty100(db=db)

@router.get("/product_id_rank")
def product_id_rank(db: Session = Depends(get_session)):
    return product_id_rank100(db=db)

@router.post("/sendToBigTable")
def sendToBigTable(db: Session = Depends(get_session)):
    return sendToBigTable100(db=db)