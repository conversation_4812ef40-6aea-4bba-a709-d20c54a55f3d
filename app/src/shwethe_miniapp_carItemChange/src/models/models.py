from typing import Dict
from xmlrpc.client import DateTime

from click import option
from pydantic.types import Optional, List
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel, Column, TEXT, text, JSON
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id



class item_insert_base(SQLModel):
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'carItemChange'::text || lpad(nextval('caritemchange_sequence'::regclass)::text, 8, '0'::text)")))
    datetime: Optional[datetime]
    warehouse_id: Optional[int]
    che_liang_id: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    product_id: Optional[int]
    qty: Optional[float]
    price: Optional[int]
    bi_zhi: Optional[int]
    group_id: Optional[str]
    status: Optional[str]
    used_type: Optional[str]
    old_qty: Optional[float]
    amount_in_de: Optional[float]

class item_insert(item_insert_base, table=True):
    __tablename__ = "item_insert"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'carItemChange'::text || lpad(nextval('caritemchange_sequence'::regclass)::text, 8, '0'::text)")))
    datetime: Optional[datetime]
    warehouse_id: Optional[int]
    che_liang_id: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    product_id: Optional[int]
    qty: Optional[float]
    price: Optional[int]
    bi_zhi: Optional[int]
    group_id: Optional[str]
    status: Optional[str]
    used_type: Optional[str]
    old_qty: Optional[float]
    amount_in_de: Optional[float]


class item_insert_read(BaseModel):
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'CIC'::text || lpad(nextval('caritemchange_sequence'::regclass)::text, 8, '0'::text)")))
    datetime: Optional[datetime]
    warehouse_id: Optional[int]
    che_liang_id: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    product_id: Optional[int]
    qty: Optional[float]
    price: Optional[int]
    bi_zhi: Optional[int]
    group_id: Optional[str]
    status: Optional[str]
    used_type: Optional[str]
    old_qty: Optional[float]
    amount_in_de: Optional[float]

class item_insert_post(BaseModel):
    datetime: Optional[datetime]
    warehouse_id: Optional[int]
    che_liang_id: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    product_id: Optional[int]
    qty: Optional[float]
    price: Optional[int]
    bi_zhi: Optional[int]
    group_id: Optional[str]
    status: Optional[str]
    used_type: Optional[str]
    old_qty: Optional[float]
    amount_in_de: Optional[float]

# -----------------------------------------

class item_select_base(SQLModel):
    jia_yi_id: Optional[int]
    type_item: Optional[str]

class item_select(item_select_base, table=True):
    __tablename__ = "item_select"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    jia_yi_id: Optional[int]
    type_item: Optional[str]

class item_select_read(BaseModel):
    jia_yi_id: Optional[int]
    type_item: Optional[str]

class item_select_post(BaseModel):
    jia_yi_id: Optional[int]
    type_item: Optional[str]


# -----------------------------------------

class check_stock_base(SQLModel):
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'CS'::text || lpad(nextval('check_stock_seq'::regclass)::text, 8, '0'::text)")))
    datetime: Optional[datetime]
    warehouse_id: Optional[int]
    che_liang_id: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    product_id: Optional[int]
    qty: Optional[float]
    price: Optional[int]
    bi_zhi: Optional[int]
    group_id: Optional[str]
    old_qty: Optional[float]
    amount_in_de: Optional[float]
    # pd_image: dict = Field(sa_column=Column(JSONB), default={})
    pd_image: list = Field(sa_column=Column(JSONB), default=[])
    status: Optional[str]
    sub: dict = Field(sa_column=Column(JSONB), default={})

class check_stock(check_stock_base, table=True):
    __tablename__ = "check_stock"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'CS'::text || lpad(nextval('check_stock_seq'::regclass)::text, 8, '0'::text)")))
    datetime: Optional[datetime]
    warehouse_id: Optional[int]
    che_liang_id: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    product_id: Optional[int]
    qty: Optional[float]
    price: Optional[int]
    bi_zhi: Optional[int]
    group_id: Optional[str]
    old_qty: Optional[float]
    amount_in_de: Optional[float]
    pd_image: list = Field(sa_column=Column(JSONB), default=[])
    status: Optional[str]
    sub: dict = Field(sa_column=Column(JSONB), default={})


class check_stock_read(BaseModel):
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'CS'::text || lpad(nextval('check_stock_seq'::regclass)::text, 8, '0'::text)")))
    datetime: Optional[datetime]
    warehouse_id: Optional[int]
    che_liang_id: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    product_id: Optional[int]
    qty: Optional[float]
    price: Optional[int]
    bi_zhi: Optional[int]
    group_id: Optional[str]
    old_qty: Optional[float]
    amount_in_de: Optional[float]
    pd_image: list = Field(sa_column=Column(JSONB), default=[])
    status: Optional[str]
    sub: dict = Field(sa_column=Column(JSONB), default={})

class check_stock_post(BaseModel):
    datetime: Optional[datetime]
    warehouse_id: Optional[int]
    che_liang_id: Optional[int]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    product_id: Optional[int]
    qty: Optional[float]
    price: Optional[int]
    bi_zhi: Optional[int]
    group_id: Optional[str]
    old_qty: Optional[float]
    amount_in_de: Optional[float]
    pd_image: list = Field(sa_column=Column(JSONB), default=[])
    status: Optional[str]
    sub: dict = Field(sa_column=Column(JSONB), default={})

# -----------------------------------------

class workshop_all_product_base(SQLModel):
    product_list: list = Field(sa_column=Column(JSONB), default=[])
    datetime: Optional[datetime]

class workshop_all_product(workshop_all_product_base, table=True):
    __tablename__ = "workshop_all_product"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    product_list: list = Field(sa_column=Column(JSONB), default=[])
    datetime: Optional[datetime]

class workshop_all_product_read(BaseModel):
    product_list: list = Field(sa_column=Column(JSONB), default=[])
    datetime: Optional[datetime]

class workshop_all_product_post(BaseModel):
    product_list: list = Field(sa_column=Column(JSONB), default=[])
    datetime: Optional[datetime]

    
# # # -----------------------------------------

class chart_item_use_base(SQLModel):
    type: Optional[str]
    period: Optional[int]
    detail_json: list = Field(sa_column=Column(JSONB), default=[])
    datetime: Optional[datetime]

class chart_item_use(chart_item_use_base, table=True):
    __tablename__ = "chart_item_use"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    type: Optional[str]
    period: Optional[int]
    detail_json: list = Field(sa_column=Column(JSONB), default=[])
    datetime: Optional[datetime]

class chart_item_use_read(BaseModel):
    type: Optional[str]
    period: Optional[int]
    detail_json: list = Field(sa_column=Column(JSONB), default=[])
    datetime: Optional[datetime]

class chart_item_use_post(BaseModel):
    type: Optional[str]
    period: Optional[int]
    detail_json: list = Field(sa_column=Column(JSONB), default=[])
    datetime: Optional[datetime]

