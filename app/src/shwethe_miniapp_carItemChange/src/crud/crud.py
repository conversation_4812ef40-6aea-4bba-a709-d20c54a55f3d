from sqlite3 import dbapi2
import requests
from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,and_
from typing import List, Optional

from datetime import datetime, timedelta
from src.shwethe_miniapp_carItemChange.database import get_session
from helper import generate_datetime_id
from src.shwethe_miniapp_carItemChange.src.models.models import item_insert, item_insert_post, item_select, item_select_post
import json
import pandas as pd
import calendar
from src.time_zone.time_zone_function import get_datetime
from helper import generate_id
from sqlalchemy import and_, or_, not_, func, literal_column, desc
from fastapi import APIRouter, Depends, Query, Body
# from sqlmodel.sql.expression import literal_column
from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api

pd.set_option('display.max_columns', None)

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def dataframe(sqlModel, to_dict=False):
    records = [i.dict() for i in sqlModel]
    mergeDF = pd.DataFrame.from_records(records).fillna(0)
    if to_dict:
        mergeDF = mergeDF.to_dict("records")
    return mergeDF


def getCarAtive100(db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(item_insert)).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records).fillna(0)

        mergeDF = df.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def MAddItem100(hero: item_select_post, db: Session = Depends(get_session)):
    
    hero_to_db = item_select.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)

    return hero_to_db


def delItem100(jia_yi_id: int, db: Session = Depends(get_session)):
    statement = select(item_select).where(item_select.jia_yi_id == jia_yi_id)
    results = db.exec(statement)
    heroes = results.all()  # Use .all() to retrieve all matching items

    for hero in heroes:
        db.delete(hero)

    db.commit()

    return heroes


# def MSearchItem100(getApiItem: str, db: Session = Depends(get_session)):
#     # print(getApiItem)
#     # print(type(getApiItem))

#     try:
#         getApiItem = int(getApiItem)
#         print(getApiItem)
#         print(type(getApiItem))

#         url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
#         # url = Arter_api + 'jia_yi_name_list_id'
#         body_raw = {"data_api": [{"product_id": getApiItem}]}
#         df2 = requests.get(url=url, json=body_raw)
#         df2 = df2.json()
#         df2 = pd.DataFrame(df2)
#         df2 = df2.to_dict('records')
#         # print(df2)
#     except:
#         print('The provided value is not an integer')
#         print(getApiItem)
#         print(type(getApiItem))

#         url = f'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_search_text?text={getApiItem}&pageNumber=0&nPerPage=0'
#         # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_idname?ID={getApiItem}'
#         # url = Arter_api + f'jia_yi_search_text?text={getApiItem}'
#         df2 = requests.get(url=url)
#         df2 = df2.json()
#         df2 = pd.DataFrame(df2)
#         df2 = df2.to_dict('records')
#         # print(df2)
#     return df2

def MSearchItem100(getApiItem: str, db: Session = Depends(get_session)):
    # print(getApiItem)
    # print(type(getApiItem))

    try:
        if getApiItem.isdigit():
            getApiItem10 = int(getApiItem)
            # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
            url = mongodb_data_api + '/api/v2/search/product_list_id'
            body_raw = {"data_api": [{"product_id": getApiItem10}]}
            df2 = requests.get(url=url, json=body_raw)
            df2 = df2.json()
            df2 = pd.DataFrame(df2).fillna(0)
            df2 = df2.to_dict('records')
            print('aaaaaaaaaaaaaaa', df2)
        else:
            df2 = []
            print('aaaaaaaaaaaaaaa', df2)


        getApiItem20 = getApiItem
        # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_search_text?text={getApiItem20}&pageNumber=0&nPerPage=0'
        url = f'{mongodb_data_api}/api/v2/search/product_search_text?text={getApiItem20}&pageNumber=0&nPerPage=0'
        df3 = requests.get(url=url)
        df3 = df3.json()
        df3 = pd.DataFrame(df3).fillna(0)
        df3 = df3.to_dict('records')
        print('bbbbbbbbbbbbbbb', df3)

        new_list = []
        new_list.extend(df2)
        new_list.extend(df3)

    except:
        new_list = []
    return new_list


def MGetListItem100(db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(item_select).where(item_select.type_item == 'item')).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records).fillna(0)

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
        url = mongodb_data_api + '/api/v2/search/product_list_id'
        dfRename = df.rename(columns={'jia_yi_id': 'product_id'})
        to_dict = dfRename[['product_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getPerson = requests.get(url=url, json=body_raw).json()
        getPerson = pd.DataFrame(getPerson)
        print(getPerson)

        mergeDF = getPerson.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def MGetListCar100(db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(item_select).where(item_select.type_item == 'car')).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records).fillna(0)

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        to_dict = df.to_dict('records')
        body_raw = {"data_api": to_dict}
        mergeDF = requests.get(url=url, json=body_raw).json()
        mergeDF = pd.DataFrame(mergeDF)

        mergeDF = mergeDF.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def MGetListStore100(db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(item_select).where(item_select.type_item == 'store')).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records).fillna(0)

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        to_dict = df.to_dict('records')
        body_raw = {"data_api": to_dict}
        mergeDF = requests.get(url=url, json=body_raw).json()
        mergeDF = pd.DataFrame(mergeDF)

        mergeDF = mergeDF.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def MUseItem100(hero: List[item_insert_post], db: Session = Depends(get_session)):

    print(hero)
    
    group_id = generate_id()

    for hero2 in hero:
        print(hero2)
        print(hero2.qty)
        if hero2.qty == 0:
            print("no 0")
        else:
            hero_to_db = item_insert.from_orm(hero2)
            hero_to_db.datetime = datetime.now()
            # hero_to_db.lei_a = 22
            # hero_to_db.lei_b = 34
            hero_to_db.bi_zhi = 138
            hero_to_db.group_id = group_id
            hero_to_db.status = "waiting"
            db.add(hero_to_db)
            db.commit()
            db.refresh(hero_to_db)

    return hero


def MSearchItem2100(getApiItem: str, db: Session = Depends(get_session)):
    # print(getApiItem)
    # print(type(getApiItem))

    try:
        getApiItem20 = getApiItem
        # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v1/search/jia_yi_search_text?text={getApiItem20}'
        url = f'{mongodb_data_api}/api/v1/search/jia_yi_search_text?text={getApiItem20}'
        df3 = requests.get(url=url)
        df3 = df3.json()
        df3 = pd.DataFrame(df3).fillna(0)
        df3 = df3.to_dict('records')
        print('bbbbbbbbbbbbbbb', df3)

    except:
        df3 = []
    return df3


# def MList100(day: str, db: Session = Depends(get_session)):
#     try:
#         import datetime as DT
#         today = DT.date.today()
        
#         if day == 'today':
#             week_ago = today - DT.timedelta(days=0)
#             heroesPersonal = db.exec(select(item_insert).where(item_insert.datetime > week_ago).order_by(item_insert.datetime.desc())).all()
#             records = [i.dict() for i in heroesPersonal]   
            
#         else:
#             week_ago = today - DT.timedelta(days=90)
#             heroesPersonal = db.exec(select(item_insert).where(item_insert.datetime > week_ago).order_by(item_insert.datetime.desc())).all()
#             records = [i.dict() for i in heroesPersonal]   


#         df = pd.DataFrame.from_records(records).fillna(0)
#         df['total_qty'] = df.groupby(['group_id', 'product_id'])['qty'].transform('sum')


#         # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         df1 = df
#         df1['jia_yi_id'] = df1['warehouse_id']
#         df1 = df1[['jia_yi_id']]
#         to_dict = df1.to_dict('records')
#         body_raw = {"data_api": to_dict}
#         warehouse_df = requests.get(url=url, json=body_raw).json()
#         warehouse_df = pd.DataFrame(warehouse_df)
#         warehouse_df = warehouse_df.rename(columns={"jia_yi_id": "warehouse_id", "jia_yi_idname": "warehouse_idname", "jia_yi_mm_name": "warehouse_mm_name"})

#         # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         df2 = df
#         df2['jia_yi_id'] = df2['che_liang_id']
#         df2 = df2[['jia_yi_id']]
#         to_dict = df2.to_dict('records')
#         body_raw = {"data_api": to_dict}
#         che_liang_id_df = requests.get(url=url, json=body_raw).json()
#         che_liang_id_df = pd.DataFrame(che_liang_id_df)
#         che_liang_id_df = che_liang_id_df.rename(columns={"jia_yi_id": "che_liang_id", "jia_yi_idname": "che_liang_idname", "jia_yi_mm_name": "che_liang_mm_name"})

#         # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
#         url = mongodb_data_api + '/api/v2/search/product_list_id'
#         df3 = df
#         df3 = df3[['product_id']]
#         to_dict = df3.to_dict('records')
#         body_raw = {"data_api": to_dict}
#         product_id_df = requests.get(url=url, json=body_raw).json()
#         product_id_df = pd.DataFrame(product_id_df)

#         mergeDF = pd.merge(df, warehouse_df, on="warehouse_id", how="left")
#         mergeDF = pd.merge(mergeDF, che_liang_id_df, on="che_liang_id", how="left")
#         mergeDF = pd.merge(mergeDF, product_id_df, on="product_id", how="left")
#         # mergeDF = mergeDF.sort_values(by='datetime', ascending=False)

#         mergeDF = mergeDF.to_dict("records")
#     except:
#         mergeDF = []
#     return mergeDF
def MList100(day: str, days_ago: int, db: Session = Depends(get_session)):
    try:
        import datetime as DT
        today = DT.date.today()
        
        if day == 'today':
            week_ago = today - DT.timedelta(days=0)
            heroesPersonal = db.exec(select(item_insert).where(item_insert.datetime > week_ago).order_by(item_insert.datetime.desc())).all()
            records = [i.dict() for i in heroesPersonal]   
            
        else:
            # Fetch the maximum datetime value from the table
            max_date_query = select(func.max(item_insert.datetime))
            max_date_result = db.execute(max_date_query).scalar()
            if max_date_result:
                max_date = max_date_result.date()
                two_days_ago = max_date - DT.timedelta(days=days_ago)
            else:
                return {"error": "No data available in the table"}

            # week_ago = today - DT.timedelta(days=90)
            heroesPersonal = db.exec(select(item_insert).where(item_insert.datetime >= two_days_ago).order_by(item_insert.datetime.desc())).all()
            records = [i.dict() for i in heroesPersonal]   


        df = pd.DataFrame.from_records(records).fillna(0)
        df['total_qty'] = df.groupby(['group_id', 'product_id'])['qty'].transform('sum')


        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        df1 = df
        df1['jia_yi_id'] = df1['warehouse_id']
        df1 = df1[['jia_yi_id']]
        to_dict = df1.to_dict('records')
        body_raw = {"data_api": to_dict}
        warehouse_df = requests.get(url=url, json=body_raw).json()
        warehouse_df = pd.DataFrame(warehouse_df)
        warehouse_df = warehouse_df.rename(columns={"jia_yi_id": "warehouse_id", "jia_yi_idname": "warehouse_idname", "jia_yi_mm_name": "warehouse_mm_name"})

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        df2 = df
        df2['jia_yi_id'] = df2['che_liang_id']
        df2 = df2[['jia_yi_id']]
        to_dict = df2.to_dict('records')
        body_raw = {"data_api": to_dict}
        che_liang_id_df = requests.get(url=url, json=body_raw).json()
        che_liang_id_df = pd.DataFrame(che_liang_id_df)
        che_liang_id_df = che_liang_id_df.rename(columns={"jia_yi_id": "che_liang_id", "jia_yi_idname": "che_liang_idname", "jia_yi_mm_name": "che_liang_mm_name"})

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
        url = mongodb_data_api + '/api/v2/search/product_list_id'
        df3 = df
        df3 = df3[['product_id']]
        to_dict = df3.to_dict('records')
        body_raw = {"data_api": to_dict}
        product_id_df = requests.get(url=url, json=body_raw).json()
        product_id_df = pd.DataFrame(product_id_df)

        mergeDF = pd.merge(df, warehouse_df, on="warehouse_id", how="left")
        mergeDF = pd.merge(mergeDF, che_liang_id_df, on="che_liang_id", how="left")
        mergeDF = pd.merge(mergeDF, product_id_df, on="product_id", how="left")
        # mergeDF = mergeDF.sort_values(by='datetime', ascending=False)

        mergeDF = mergeDF.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def MGetPrice100(product_id: int, db: Session = Depends(get_session)):
    try:
        # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/price?ID={product_id}'
        url = f'{mongodb_data_api}/api/v2/search/price?ID={product_id}'
        df3 = requests.get(url=url)
        df3 = df3.json()
        print('bbbbbbbbbbbbbbb', df3)

    except:
        df3 = []
    return df3


def MGetStoreByProduct_id100(product_id: int, db: Session = Depends(get_session)):
    try:
        # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v1/search/qty/{product_id}'
        url = f'{mongodb_data_api}/api/v1/search/qty/{product_id}'
        df = requests.get(url=url)
        df = df.json()
        df = pd.DataFrame(df).fillna(0)
        df = df[df['fen'] == 4]


        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        df1 = df
        df1['jia_yi_id'] = df1['lei']
        df1 = df1[['jia_yi_id']]
        to_dict = df1.to_dict('records')
        body_raw = {"data_api": to_dict}
        warehouse_df = requests.get(url=url, json=body_raw).json()
        warehouse_df = pd.DataFrame(warehouse_df).fillna(0)
        warehouse_df = warehouse_df[warehouse_df['jia_yi_idname'].str.startswith('E')]
        # warehouse_df = warehouse_df.rename(columns={"jia_yi_id": "warehouse_id", "jia_yi_idname": "warehouse_idname", "jia_yi_mm_name": "warehouse_mm_name"})

        dataframeOutput = pd.merge(df, warehouse_df, left_on='lei', right_on='jia_yi_id')

        out = dataframeOutput.to_dict('records')

    except:
        out = []
    return out


# def MGetStoreByProduct_id100(product_id: int, db: Session = Depends(get_session)):
#     try:
#         def getOldQty():
#             # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v1/search/qty/{product_id}'
#             url = f'{mongodb_data_api}/api/v1/search/qty/{product_id}'
#             df = requests.get(url=url)
#             df = df.json()
#             df = pd.DataFrame(df).fillna(0)
#             df = df[df['fen'] == 4]

#             # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#             url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#             df1 = df
#             df1['jia_yi_id'] = df1['lei']
#             df1 = df1[['jia_yi_id']]
#             to_dict = df1.to_dict('records')
#             body_raw = {"data_api": to_dict}
#             warehouse_df = requests.get(url=url, json=body_raw).json()
#             warehouse_df = pd.DataFrame(warehouse_df).fillna(0)
#             warehouse_df = warehouse_df[warehouse_df['jia_yi_idname'].str.startswith('E')]
#             # warehouse_df = warehouse_df.rename(columns={"jia_yi_id": "warehouse_id", "jia_yi_idname": "warehouse_idname", "jia_yi_mm_name": "warehouse_mm_name"})

#             dataframeOutput = pd.merge(df, warehouse_df, left_on='lei', right_on='jia_yi_id')

#             return dataframeOutput


#         def getLocalQty():
#             import datetime as DT
#             today = DT.date.today()
#             week_ago = today - DT.timedelta(days=0)
#             print(week_ago)     
#             statement_titleData = select(item_insert).where(item_insert.datetime > week_ago, item_insert.product_id == product_id).order_by(item_insert.product_id, item_insert.datetime.desc()).distinct(item_insert.product_id)  
#             results_titleData = db.exec(statement_titleData).first()
#             print(results_titleData)

#             return results_titleData
        
#         # getOldQtyV = getOldQty()
#         getLocalQtyV = getLocalQty()
#         print(getLocalQtyV)

#         # out = getLocalQtyV.to_dict('records')
#         out = getLocalQtyV

#     except:
#         out = []
#     return out


def MUpdateStatus100(record_id: str, statusText: str, db: Session = Depends(get_session)):
    
    print(record_id)

    # UPDATE TO title_data
    statement_titleData = select(item_insert).where(item_insert.record_id == record_id)
    results_titleData = db.exec(statement_titleData).first()
    results_titleData.status = statusText
    db.add(results_titleData)  # 
    db.commit()  # 
    db.refresh(results_titleData)  #

    sendToBigTable100()

    return record_id


def updateFirst100(db: Session = Depends(get_session)):

    from src.Connect.postgresql_nern import postgresql_shwethe_carItemChange
    import pandas as pd

    A10001 = pd.read_sql("""
                            SELECT *
                            FROM item_insert
                            WHERE datetime >= NOW() - INTERVAL '10 days' 
                            AND price = 0
                        """, postgresql_shwethe_carItemChange)
    
    for index, row in A10001.iterrows():
        print(row['record_id'], row['product_id'])
        url = f'{mongodb_data_api}/api/v2/search/price?ID={row.product_id}'
        new_price = requests.get(url).json()
        new_price_rounded = round(new_price, 2)
        print(new_price_rounded)

        # Update the price for the current record_id
        cursor = postgresql_shwethe_carItemChange.cursor()
        update_query = """
            UPDATE item_insert
            SET price = %s
            WHERE record_id = %s
        """
        cursor.execute(update_query, (new_price, row['record_id']))
        postgresql_shwethe_carItemChange.commit()
        cursor.close()

    json_nor = A10001.to_dict("records")

    return json_nor


# def updateRealtimeQty100(db: Session = Depends(get_session)):
#     try:
#         import datetime as DT
#         today = DT.date.today()
#         week_ago = today - DT.timedelta(days=0)
#         heroesPersonal = db.exec(select(item_insert).where(item_insert.datetime > week_ago, item_insert.used_type == 'test').order_by(item_insert.datetime.desc())).all()
#         records = [i.dict() for i in heroesPersonal]   
#         df = pd.DataFrame.from_records(records).fillna(0)

#         mergeDF = df.to_dict("records")
#     except:
#         mergeDF = []
#     return mergeDF
def get_item_insert100(db: Session = Depends(get_session)):
    import datetime as DT
    today = DT.date.today()
    # GET TODAY DATA
    week_ago = today - DT.timedelta(days=3)
    heroesPersonal = db.exec(
        select(
            item_insert.auto_id,
            item_insert.datetime,
            item_insert.product_id,
            item_insert.qty,
            item_insert.old_qty,
            item_insert.amount_in_de
        ).where(item_insert.datetime > week_ago)
    ).all()
    mergeDF = pd.DataFrame([dict(row) for row in heroesPersonal])
    return mergeDF
def updateRealtimeQty100(db: Session = Depends(get_session)):

    print('updateRealtimeQty100 updateRealtimeQty100 updateRealtimeQty100')
    # Get the data from the item_insert table
    data = get_item_insert100(db=db)

    # Group the data by product_id to process each product separately
    grouped_data = data.groupby("product_id")

    for product_id, group in grouped_data:
        # Sort the group by datetime in ascending order
        sorted_group = group.sort_values(by="datetime", ascending=True)

        # Initialize the initial values for old_qty and amount_in_de
        old_qty = None
        amount_in_de = None

        for _, row in sorted_group.iterrows():
            qty = row["qty"]

            # Check if old_qty and amount_in_de need to be updated
            if old_qty is not None and amount_in_de is not None:
                old_qty = amount_in_de
                amount_in_de = old_qty - qty
            else:
                old_qty = row["old_qty"]
                amount_in_de = old_qty - qty

            # Update the row with the new values
            row["old_qty"] = old_qty
            row["amount_in_de"] = amount_in_de

            # Get the auto_id and update the row in the database
            auto_id = row["auto_id"]
            statement_item_insert = select(item_insert).where(item_insert.auto_id == auto_id)
            result_item_insert = db.exec(statement_item_insert).first()
            result_item_insert.old_qty = old_qty
            result_item_insert.amount_in_de = amount_in_de
            db.add(result_item_insert)

    # Commit the changes to the database
    db.commit()

    return "data"


def product_id_rank100(db: Session = Depends(get_session)):
    try:
        def getCheckStockReport():
            import datetime as DT
            from decimal import Decimal
            today = DT.date.today()
            week_ago = today - DT.timedelta(days=365)
            heroesPersonal = db.exec(select(item_insert.datetime, item_insert.product_id, item_insert.qty, item_insert.price).where(item_insert.datetime > week_ago, item_insert.status == 'success')).all()
            A10001 = pd.DataFrame([dict(row) for row in heroesPersonal])

            # A10001 = A10001.head(40)
            A10001 = A10001.sort_values(by=['datetime', 'product_id'], ascending=[False, False])
            A10001['sum_qty'] = A10001.groupby('product_id')['qty'].transform('sum')
            A10001['sum_price'] = A10001['qty'].apply(Decimal) * A10001['price']
            A10001['total_price'] = A10001.groupby('product_id')['sum_price'].transform('sum')

            A10001['datetime'] = pd.to_datetime(A10001['datetime'])  # Convert to datetime
            A10001 = A10001.sort_values(by=['product_id', 'datetime'])
            A10001['day_diff'] = A10001.groupby('product_id')['datetime'].diff().dt.days
            A10001['day_diff'] = A10001['day_diff'].fillna(0)

            A10001['mean_day_diff'] = A10001.groupby('product_id')['day_diff'].transform('mean')
            A10001 = A10001.round(1)
            
            df_sorted = A10001.sort_values(by=['sum_qty', 'datetime'], ascending=[False, True])
            A10001 = df_sorted.drop_duplicates(subset=['sum_qty'], keep='last')

            return A10001

        def getName():
            fgetCheckStockReport = getCheckStockReport()

            df = fgetCheckStockReport.rename(columns={"product_id": 'product_id'})
            url = mongodb_data_api + '/api/v2/search/product_list_id'
            to_dict = df[['product_id']].to_dict('records')
            body_raw = {"data_api": to_dict}
            getProduct = requests.get(url=url, json=body_raw).json()
            getProduct = pd.DataFrame(getProduct)

            # url = f'{mongodb_data_api}/api/v2/search/jia_yi_name_list_id'
            # changename = fgetCheckStockReport.rename(columns={'che_liang_id': 'jia_yi_id'})
            # to_dict = changename[['jia_yi_id']].to_dict('records')
            # body_raw = {"data_api": to_dict}
            # getCar = requests.get(url=url, json=body_raw).json()
            # getCar = pd.DataFrame(getCar)

            # output = getProduct, getCar, fgetCheckStockReport
            output = getProduct, fgetCheckStockReport
            # output = fgetCheckStockReport
            return output

        def fusion():
            # getProduct, getCar, fgetCheckStockReport = getName()
            getProduct, fgetCheckStockReport = getName()
            mergeProduct = fgetCheckStockReport.merge(getProduct, left_on='product_id', right_on='product_id', how='left')
            # mergeCar = mergeProduct.merge(getCar, left_on='che_liang_id', right_on='jia_yi_id', how='left')
            # df = mergeCar.applymap(lambda x: None if pd.isna(x) or x == '' else x)
            mergeProduct = mergeProduct[['datetime', 'product_id', 'price', 'sum_qty', 'total_price', 'mean_day_diff', 'product_idname', 'product_mm_name', 'product_d_name', 'th_name']]
            return mergeProduct

        ddd = fusion()
        # ddd

        mergeDF = ddd.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def sendToBigTable100(db: Session = Depends(get_session)):
    # try:
    from src.Connect.postgresql_nern import postgresql_shwethe_carItemChange
    import pandas as pd


    # A10001 = pd.read_sql(""" select * from item_insert WHERE datetime >= NOW() - INTERVAL '2 days' """, postgresql_shwethe_carItemChange)
    A10001 = pd.read_sql("""
                            SELECT *
                            FROM item_insert
                            WHERE datetime >= NOW() - INTERVAL '4 days' 
                            AND status = 'success'
                        """, postgresql_shwethe_carItemChange)
    json_nor = A10001.to_dict("records")


    # INSERT TO ARTER DATABASE
    aaa = []
    for ioo in json_nor:
        d = {
            'a_id': ioo['auto_id'],
            'b_id': ioo['auto_id'],
            'product_id': ioo['product_id'],
            'product_qty': ioo['qty'],
            'product_price_a' : ioo['price'],
            'product_price_b' : ioo['price'],
            'ke_bian': 0,
            'jia_yi_fang_a': ioo['jia_yi_fang_a'],
            'jia_yi_fang_b': ioo['jia_yi_fang_b'],
            'lei_a': ioo['lei_a'],
            'lei_b': ioo['lei_b'],
            'bu_bian': 1005,
            'jin_huo_bian': 0,   
            'jin_huo_dang': ioo['record_id'],   
            'ci_bian': 0,
            'u_id': 0,
            'shu_riqi_datetime': str(ioo['datetime']),
            'riqi_datetime': str(ioo['datetime']),
            'che_liang': ioo['che_liang_id'],
            'kind': 11234,
            'product_qtyp': 0,
            'product_qtyn': 0,
            'bi_zhi': ioo['bi_zhi']
            }
        aaa.append(d)
    df1 = pd.DataFrame(aaa) 
    mata = {
    'data' : df1.to_dict(orient='records')
    }
        
    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v2/table/mysql', data= json.dumps(mata))
    url = shwethe_mysql_api + '/api/v1/table/mysql_big_table'
    dd = requests.post(url, data= json.dumps(mata))
    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v1/table/mysql_big_table', data= json.dumps(mata))
    out = dd.status_code
    print(out)

    return out


# def sendToBigTable100(db: Session = Depends(get_session)):
#     # try:
#     from src.Connect.postgresql_nern import postgresql_shwethe_carItemChange
#     import pandas as pd


#     # A10001 = pd.read_sql(""" select * from item_insert WHERE datetime >= NOW() - INTERVAL '2 days' """, postgresql_shwethe_carItemChange)
#     A10001 = pd.read_sql("""
#                             SELECT *
#                             FROM item_insert
#                             WHERE datetime >= NOW() - INTERVAL '2 days' 
#                             AND status = 'success'
#                         """, postgresql_shwethe_carItemChange)
#     json_nor = A10001.to_dict("records")


#     # INSERT TO ARTER DATABASE
#     aaa = []
#     for ioo in json_nor:
#         d = {
#             'a_id': ioo['auto_id'],
#             'b_id': ioo['auto_id'],
#             'datetime': str(ioo['datetime']),
#             'jin_huo_dang': ioo['record_id'],   
#             'uid': 0,
#             'product_id': ioo['product_id'],
#             'product_qty': ioo['qty'],
#             'product_price_x': ioo['price'],
#             'product_price_y': ioo['price'],
#             'jia_yi_fang_a': ioo['jia_yi_fang_a'],
#             'jia_yi_fang_b': ioo['jia_yi_fang_b'],
#             'lei_a': ioo['lei_a'],
#             'lei_b': ioo['lei_b'],
#             'line': 1005,
#             'ke_bian': 0,
#             'che_liang': ioo['che_liang_id'],
#             'kind': 11234,
#             'bi_zhi': ioo['bi_zhi']
#             }
#         aaa.append(d)
#     df1 = pd.DataFrame(aaa) 
#     mata = {
#     'data' : df1.to_dict(orient='records')
#     }

#     dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v2/table/mysql', data= json.dumps(mata))
#     print(dd)

#     # postgresql_shwethe_carItemChange.close()

#     # vvv = {"out": dd}

#     # except:
#     #     mergeDF = []
#     return dd.status_code

