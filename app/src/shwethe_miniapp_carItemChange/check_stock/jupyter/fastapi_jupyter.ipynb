{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'Depends' object has no attribute 'exec'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[6], line 25\u001b[0m\n\u001b[1;32m     22\u001b[0m     \u001b[39m# mergeDF = df.to_dict(\"records\")\u001b[39;00m\n\u001b[1;32m     23\u001b[0m     \u001b[39mreturn\u001b[39;00m mergeDF\n\u001b[0;32m---> 25\u001b[0m getCarAtive100()\n", "Cell \u001b[0;32mIn[6], line 18\u001b[0m, in \u001b[0;36mgetCarAtive100\u001b[0;34m(db)\u001b[0m\n\u001b[1;32m     16\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mgetCarAtive100\u001b[39m(db: Session \u001b[39m=\u001b[39m Depends(get_session)):\n\u001b[0;32m---> 18\u001b[0m     heroesPersonal \u001b[39m=\u001b[39m db\u001b[39m.\u001b[39;49mexec(select(check_stock))\u001b[39m.\u001b[39mall()\n\u001b[1;32m     19\u001b[0m     records \u001b[39m=\u001b[39m [i\u001b[39m.\u001b[39mdict() \u001b[39mfor\u001b[39;00m i \u001b[39min\u001b[39;00m heroesPersonal]   \n\u001b[1;32m     20\u001b[0m     mergeDF \u001b[39m=\u001b[39m pd\u001b[39m.\u001b[39mDataFrame\u001b[39m.\u001b[39mfrom_records(records)\u001b[39m.\u001b[39mfillna(\u001b[39m0\u001b[39m)\n", "\u001b[0;31mAttributeError\u001b[0m: 'Depends' object has no attribute 'exec'"]}], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select,SQLModel, and_\n", "from src.shwethe_miniapp_carItemChange.database import get_session\n", "from src.shwethe_miniapp_carItemChange.src.models.models import check_stock, check_stock_post, item_select, item_select_post\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_, or_, not_, func, literal_column, desc\n", "from fastapi import APIRouter, Depends, Query, Body\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "\n", "\n", "pd.set_option('display.max_columns', None)\n", "\n", "def getCarAtive100(db: Session = Depends(get_session)):\n", " \n", "    heroesPersonal = db.exec(select(check_stock)).all()\n", "    records = [i.dict() for i in heroesPersonal]   \n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "\n", "    # mergeDF = df.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "getCarAtive100()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-07-17 07:52:22,675 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2023-07-17 07:52:22,682 INFO sqlalchemy.engine.Engine SELECT check_stock.record_id, check_stock.pd_image, check_stock.sub, check_stock.datetime, check_stock.warehouse_id, check_stock.che_liang_id, check_stock.jia_yi_fang_a, check_stock.jia_yi_fang_b, check_stock.lei_a, check_stock.lei_b, check_stock.product_id, check_stock.qty, check_stock.price, check_stock.bi_zhi, check_stock.group_id, check_stock.old_qty, check_stock.amount_in_de, check_stock.status, check_stock.auto_id \n", "FROM check_stock\n", "2023-07-17 07:52:22,685 INFO sqlalchemy.engine.Engine [cached since 2575s ago] {}\n", "2023-07-17 07:52:22,709 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>qty</th>\n", "      <th>auto_id</th>\n", "      <th>warehouse_id</th>\n", "      <th>price</th>\n", "      <th>che_liang_id</th>\n", "      <th>bi_zhi</th>\n", "      <th>jia_yi_fang_a</th>\n", "      <th>group_id</th>\n", "      <th>record_id</th>\n", "      <th>jia_yi_fang_b</th>\n", "      <th>old_qty</th>\n", "      <th>pd_image</th>\n", "      <th>lei_a</th>\n", "      <th>amount_in_de</th>\n", "      <th>sub</th>\n", "      <th>lei_b</th>\n", "      <th>status</th>\n", "      <th>product_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-07-09 11:41:30.812967+06:30</td>\n", "      <td>952.0</td>\n", "      <td>282</td>\n", "      <td>793.0</td>\n", "      <td>50.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>793.0</td>\n", "      <td>78f35a11</td>\n", "      <td>CS00000281</td>\n", "      <td>793.0</td>\n", "      <td>52.0</td>\n", "      <td>[192.168.1.12:9000/personal/workshopCheckStock...</td>\n", "      <td>37.0</td>\n", "      <td>900.0</td>\n", "      <td>{}</td>\n", "      <td>22.0</td>\n", "      <td>waiting</td>\n", "      <td>536.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>276</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>CS00000275</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime    qty  auto_id  warehouse_id  price   \n", "0  2023-07-09 11:41:30.812967+06:30  952.0      282         793.0   50.0  \\\n", "1                                 0    0.0      276           0.0    0.0   \n", "\n", "   che_liang_id  bi_zhi  jia_yi_fang_a  group_id   record_id  jia_yi_fang_b   \n", "0           0.0     0.0          793.0  78f35a11  CS00000281          793.0  \\\n", "1           0.0     0.0            0.0         0  CS00000275            0.0   \n", "\n", "   old_qty                                           pd_image  lei_a   \n", "0     52.0  [192.168.1.12:9000/personal/workshopCheckStock...   37.0  \\\n", "1      0.0                                                  0    0.0   \n", "\n", "   amount_in_de sub  lei_b   status  product_id  \n", "0         900.0  {}   22.0  waiting       536.0  \n", "1           0.0   0    0.0        0         0.0  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import select, SQLModel\n", "from src.shwethe_miniapp_carItemChange.database import get_session\n", "from src.shwethe_miniapp_carItemChange.src.models.models import check_stock\n", "import json\n", "import pandas as pd\n", "from fastapi import APIRouter\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "\n", "pd.set_option('display.max_columns', None)\n", "\n", "def getCarAtive100():\n", "    session = next(get_session())\n", "    heroesPersonal = session.exec(select(check_stock)).all()\n", "    records = [i.dict() for i in heroesPersonal]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    session.close()\n", "    return mergeDF\n", "\n", "getCarAtive100()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-07-17 07:46:52,065 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2023-07-17 07:46:52,076 INFO sqlalchemy.engine.Engine SELECT check_stock.record_id, check_stock.pd_image, check_stock.sub, check_stock.datetime, check_stock.warehouse_id, check_stock.che_liang_id, check_stock.jia_yi_fang_a, check_stock.jia_yi_fang_b, check_stock.lei_a, check_stock.lei_b, check_stock.product_id, check_stock.qty, check_stock.price, check_stock.bi_zhi, check_stock.group_id, check_stock.old_qty, check_stock.amount_in_de, check_stock.status, check_stock.auto_id \n", "FROM check_stock\n", "2023-07-17 07:46:52,079 INFO sqlalchemy.engine.Engine [cached since 2244s ago] {}\n", "2023-07-17 07:46:52,106 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>qty</th>\n", "      <th>auto_id</th>\n", "      <th>warehouse_id</th>\n", "      <th>price</th>\n", "      <th>che_liang_id</th>\n", "      <th>bi_zhi</th>\n", "      <th>jia_yi_fang_a</th>\n", "      <th>group_id</th>\n", "      <th>record_id</th>\n", "      <th>jia_yi_fang_b</th>\n", "      <th>old_qty</th>\n", "      <th>pd_image</th>\n", "      <th>lei_a</th>\n", "      <th>amount_in_de</th>\n", "      <th>sub</th>\n", "      <th>lei_b</th>\n", "      <th>status</th>\n", "      <th>product_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-07-09 11:41:30.812967+06:30</td>\n", "      <td>952.0</td>\n", "      <td>282</td>\n", "      <td>793.0</td>\n", "      <td>50.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>793.0</td>\n", "      <td>78f35a11</td>\n", "      <td>CS00000281</td>\n", "      <td>793.0</td>\n", "      <td>52.0</td>\n", "      <td>[192.168.1.12:9000/personal/workshopCheckStock...</td>\n", "      <td>37.0</td>\n", "      <td>900.0</td>\n", "      <td>{}</td>\n", "      <td>22.0</td>\n", "      <td>waiting</td>\n", "      <td>536.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>276</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>CS00000275</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime    qty  auto_id  warehouse_id  price   \n", "0  2023-07-09 11:41:30.812967+06:30  952.0      282         793.0   50.0  \\\n", "1                                 0    0.0      276           0.0    0.0   \n", "\n", "   che_liang_id  bi_zhi  jia_yi_fang_a  group_id   record_id  jia_yi_fang_b   \n", "0           0.0     0.0          793.0  78f35a11  CS00000281          793.0  \\\n", "1           0.0     0.0            0.0         0  CS00000275            0.0   \n", "\n", "   old_qty                                           pd_image  lei_a   \n", "0     52.0  [192.168.1.12:9000/personal/workshopCheckStock...   37.0  \\\n", "1      0.0                                                  0    0.0   \n", "\n", "   amount_in_de sub  lei_b   status  product_id  \n", "0         900.0  {}   22.0  waiting       536.0  \n", "1           0.0   0    0.0        0         0.0  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "from src.shwethe_miniapp_carItemChange.database import get_session\n", "from src.shwethe_miniapp_carItemChange.src.models.models import check_stock\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "\n", "pd.set_option('display.max_columns', None)\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def getCarAtive100():\n", "    with get_session_dependency() as db:\n", "        heroesPersonal = db.exec(select(check_stock)).all()\n", "        records = [i.dict() for i in heroesPersonal]\n", "        mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    return mergeDF\n", "\n", "getCarAtive100()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "from src.shwethe_miniapp_carItemChange.database import get_session\n", "from src.shwethe_miniapp_carItemChange.src.models.models import check_stock\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "import logging\n", "\n", "\n", "# pd.set_option('display.max_columns', None)\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "\n", "def makeDataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "def getCarAtive100():\n", "    with get_session_dependency() as db:\n", "        mergeDF = makeDataframe(db.exec(select(check_stock)).all(), to_dict=False)\n", "    return mergeDF\n", "\n", "getCarAtive100()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}