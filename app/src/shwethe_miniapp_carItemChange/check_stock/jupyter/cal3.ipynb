{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# !pip install pandas\n", "# !pip install psycopg2-binary"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-11-02 07:36:20,875 INFO sqlalchemy.engine.Engine select pg_catalog.version()\n", "2024-11-02 07:36:20,877 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-11-02 07:36:20,883 INFO sqlalchemy.engine.Engine select current_schema()\n", "2024-11-02 07:36:20,888 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-11-02 07:36:20,892 INFO sqlalchemy.engine.Engine show standard_conforming_strings\n", "2024-11-02 07:36:20,894 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-11-02 07:36:20,899 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-11-02 07:36:20,920 INFO sqlalchemy.engine.Engine SELECT item_insert.record_id, item_insert.datetime, item_insert.warehouse_id, item_insert.che_liang_id, item_insert.jia_yi_fang_a, item_insert.jia_yi_fang_b, item_insert.lei_a, item_insert.lei_b, item_insert.product_id, item_insert.qty, item_insert.price, item_insert.bi_zhi, item_insert.group_id, item_insert.status, item_insert.used_type, item_insert.old_qty, item_insert.amount_in_de, item_insert.auto_id \n", "FROM item_insert \n", "WHERE item_insert.datetime > %(datetime_1)s AND item_insert.status = %(status_1)s\n", "2024-11-02 07:36:20,923 INFO sqlalchemy.engine.Engine [generated in 0.00384s] {'datetime_1': datetime.date(2024, 6, 5), 'status_1': 'success'}\n", "2024-11-02 07:36:21,026 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/plain": ["(    jia_yi_id jia_yi_idname                             jia_yi_mm_name   \n", " 0       46723          FT69      အုတ်ရိုက်စက်(6)လုံးထိုး ဝမ်လိသုံး (1)  \\\n", " 1       46814          FT70      အုတ်ရိုက်စက်(4)လုံးထိုး ဝမ်လိသုံး (2)   \n", " 2       48548          V.23              ဘိလပ်တင်မစက် 4မစ် 3 Ton  အသစ်   \n", " 3         104          W01A    နှစ်တန်ကားတင်ဘာပါ  2A-6114/ 1988/3431CC   \n", " 4         106          W03A     နှစ်တန်ကားတင်ဘာပါ 3A-9900 /1985/3660CC   \n", " 5         194          W06A      နှစ်တန်ကားတင်ဘာပါ P-6308 /1986/2779CC   \n", " 6        2290          W07A     နှစ်တန်ကားတင်ဘာပါ 1R-1730 /2019/2771CC   \n", " 7       11069          W16B     နှစ်တန်ကားရိုးရိုး J-7681 /1979/3431CC   \n", " 8       20141          W13C    သုံးတန်ကားရိုးရိုး 4F-5207 /2005/4009CC   \n", " 9       20455          W04A     နှစ်တန်ကားတင်ဘာပါ 9F-7235 /2013/2999CC   \n", " 10      23021          W15E             12ဘီးကား 7I-9141 /2002/13074CC   \n", " 11      36555          W10A     နှစ်တန်ကားတင်ဘာပါ 7R-6894 /2020/2771CC   \n", " 12      37620          W09A     နှစ်တန်ကားတင်ဘာပါ 1S-1776 /2020/2771CC   \n", " 13      37710          W05B    နှစ်တန်ကားရိုးရိုး 7R-2956 /2020/2672CC   \n", " 14      38201          W17D  နှစ်တန်ခွဲကားတင်ဘာပါ 2R-1964 /2019/3612CC   \n", " 15      40658          W18C    သုံးတန်ကားရိုးရိုး 6R-8220 /2020/4100CC   \n", " 16      40659          W19B    နှစ်တန်ကားရိုးရိုး 7R-2942 /2020/2672CC   \n", " 17      41085          W20D  နှစ်တန်ခွဲကားတင်ဘာပါ 2R-1959 /2019/3012CC   \n", " 18       2343           V.3                     ဘိလပ်တင်မစက် 4မစ် 3Ton   \n", " 19       7565          W14F      လေးတန်ကားတင်ဘာပါ 9H-3468 /2003/5307CC   \n", " 20      24968          V.16                   ဘိလပ်တင်မစက် 3မစ် 3.5Ton   \n", " 21      25370           V.4                     ဘိလပ်တင်မစက် 4မစ် 3Ton   \n", " 22      26594          V.18                     ဘိလပ်တင်မစက် 4မစ် 3Ton   \n", " 23      31838          V.20                     ဘိလပ်တင်မစက် 4မစ် 3Ton   \n", " 24      36230          V.22                     ဘိလပ်တင်မစက် 4မစ် 3Ton   \n", " 25      36768           V.5                     ဘိလပ်တင်မစက် 4မစ် 3Ton   \n", " 26      38374           V.9                     ဘိလပ်တင်မစက် 4မစ် 3Ton   \n", " 27      42401          W21E       12ဘီးကရိန်းကား 4Q-5836 /2007/13074CC   \n", " 28      43162          V.10                     ဘိလပ်တင်မစက် 5မစ် 5Ton   \n", " 29      43887          V.11                     ဘိလပ်တင်မစက် 4မစ် 3Ton   \n", " 30       5636           U3G           လိုက်ထရပ်ကား8E-2435 /2008/1974CC   \n", " 31      19937           U4G          လိုက်ထရပ်ကား 2F-4084 /2006/2977CC   \n", " \n", "                       ri_qi  \n", " 0   2023-06-02 00:00:00.000  \n", " 1   2023-06-08 00:00:00.000  \n", " 2   2023-11-12 00:00:00.000  \n", " 3   2002-01-11 00:00:00.000  \n", " 4   2002-01-11 00:00:00.000  \n", " 5   2002-01-11 00:00:00.000  \n", " 6   2006-04-24 00:00:00.000  \n", " 7   2010-03-11 00:00:00.000  \n", " 8   2013-07-06 00:00:00.000  \n", " 9   2013-10-23 00:00:00.000  \n", " 10  2015-02-14 00:00:00.000  \n", " 11  2021-07-11 00:00:00.000  \n", " 12  2022-01-17 00:00:00.000  \n", " 13  2022-01-24 00:00:00.000  \n", " 14  2022-02-25 00:00:00.000  \n", " 15  2022-06-27 00:00:00.000  \n", " 16  2022-06-27 00:00:00.000  \n", " 17  2022-07-18 00:00:00.000  \n", " 18  2006-05-07 00:00:00.000  \n", " 19  2015-09-18 00:00:00.000  \n", " 20  2015-11-21 00:00:00.000  \n", " 21  2016-01-17 00:00:00.000  \n", " 22  2016-06-09 00:00:00.000  \n", " 23  2019-05-09 00:00:00.000  \n", " 24  2021-05-22 00:00:00.000  \n", " 25  2021-08-30 00:00:00.000  \n", " 26  2022-03-08 00:00:00.000  \n", " 27  2022-09-27 00:00:00.000  \n", " 28  2022-11-09 00:00:00.000  \n", " 29  2022-12-19 00:00:00.000  \n", " 30  2008-08-19 00:00:00.000  \n", " 31  2013-05-16 00:00:00.000  ,\n", "         datetime  che_liang_id  sum_price  total_price\n", " 0     2024-06-08           104        NaN          NaN\n", " 32    2024-06-09           104        NaN          NaN\n", " 64    2024-06-10           104        NaN          NaN\n", " 96    2024-06-11           104        NaN          NaN\n", " 128   2024-06-12           104        NaN          NaN\n", " ...          ...           ...        ...          ...\n", " 4575  2024-10-28         48548        NaN          NaN\n", " 4607  2024-10-29         48548        NaN          NaN\n", " 4639  2024-10-30         48548        NaN          NaN\n", " 4671  2024-10-31         48548        NaN          NaN\n", " 4703  2024-11-01         48548        NaN          NaN\n", " \n", " [4704 rows x 4 columns])"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "from src.shwethe_miniapp_carItemChange.database import get_session\n", "from src.shwethe_miniapp_carItemChange.src.models.models import item_insert\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "from src.Connect.postgresql_nern import real_backend_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "from pandas import json_normalize\n", "import requests\n", "\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def dataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "def getCheckStockReport():\n", "    with get_session_dependency() as db:\n", "        \n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        week_ago = today - DT.<PERSON><PERSON><PERSON>(days=150)\n", "        heroesPersonal = db.exec(select(item_insert).where(item_insert.datetime > week_ago, item_insert.status == 'success')).all()\n", "        A10001 = dataframe(heroesPersonal, to_dict=False)\n", "        A10001['price'] = A10001['price'].astype(float)\n", "        A10001 = A10001[[\"datetime\", \"che_liang_id\", \"product_id\", \"qty\", \"price\", \"used_type\"]]\n", "\n", "        A10001['datetime'] = pd.to_datetime(A10001['datetime'])\n", "        A10001['datetime'] = A10001['datetime'].dt.strftime('%Y-%m-%d')\n", "        A10001.sort_values(by=['che_liang_id', 'datetime'], inplace=True)\n", "        A10001['sum_price'] = A10001['qty'] * A10001['price']\n", "        A10001 = A10001.groupby(['datetime', 'che_liang_id'])['sum_price'].sum().reset_index()\n", "        A10001 = A10001.sort_values(by=['che_liang_id', 'datetime'])\n", "        A10001['total_price'] = A10001.groupby('che_liang_id')['sum_price'].cumsum()\n", "    return A10001\n", "\n", "def add_missing_date():\n", "        df = getCheckStockReport()\n", "\n", "        df['datetime'] = pd.to_datetime(df['datetime'])\n", "        # Generate a date range that covers the entire period\n", "        date_range = pd.date_range(start=df['datetime'].min(), end=df['datetime'].max())\n", "\n", "        # Create a new dataframe with all possible combinations of date and che_liang_id\n", "        che_liang_ids = df['che_liang_id'].unique()\n", "        expanded_data = []\n", "\n", "        for date in date_range:\n", "            for che_liang_id in che_liang_ids:\n", "                expanded_data.append({'datetime': date, 'che_liang_id': che_liang_id})\n", "\n", "        expanded_df = pd.DataFrame(expanded_data)\n", "\n", "        # Merge the expanded dataframe with the original dataframe\n", "        final_df = pd.merge(expanded_df, df, on=['datetime', 'che_liang_id'], how='left')\n", "\n", "        # Sort the final dataframe\n", "        final_df = final_df.sort_values(by=['che_liang_id', 'datetime'])\n", "        final_df['datetime'] = final_df['datetime'].dt.strftime('%Y-%m-%d')\n", "\n", "        return final_df\n", "\n", "def getName():\n", "    fgetCheckStockReport = add_missing_date()\n", "\n", "    url = f'{mongodb_data_api}/api/v2/search/jia_yi_name_list_id'\n", "    changename = fgetCheckStockReport.rename(columns={'che_liang_id': 'jia_yi_id'})\n", "    to_dict = changename[['jia_yi_id']].to_dict('records')\n", "    body_raw = {\"data_api\": to_dict}\n", "    getCar = requests.get(url=url, json=body_raw).json()\n", "    getCar = pd.DataFrame(getCar)\n", "    output = getCar, fgetCheckStockReport\n", "    return output\n", "\n", "def fusion():\n", "    getProduct, getCar, fgetCheckStockReport = getName()\n", "    mergeProduct = fgetCheckStockReport.merge(getProduct, left_on='product_id', right_on='product_id', how='left')\n", "    mergeCar = mergeProduct.merge(getCar, left_on='che_liang_id', right_on='jia_yi_id', how='left')\n", "    df = mergeCar.applymap(lambda x: None if pd.isna(x) or x == '' else x)\n", "    df = df[['datetime', 'che_liang_id', 'jia_yi_idname', 'product_id', 'product_idname', 'product_mm_name', 'product_d_name', 'qty', 'price', 'sum_qty', 'sum_price', 'total_price', 'used_type']]\n", "    return df\n", "\n", "ddd = getName()\n", "ddd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}