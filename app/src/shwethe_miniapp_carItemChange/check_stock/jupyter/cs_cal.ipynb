{"cells": [{"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item</th>\n", "      <th>stock</th>\n", "      <th>defineDay</th>\n", "      <th>checkPerDay</th>\n", "      <th>product_id</th>\n", "      <th>today</th>\n", "      <th>last_fill</th>\n", "      <th>CheckDay</th>\n", "      <th>dayWait</th>\n", "      <th>dayWait2</th>\n", "      <th>status</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>old</td>\n", "      <td>6</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>801</td>\n", "      <td>2024-11-02</td>\n", "      <td>2024-11-02</td>\n", "      <td>2024-11-05</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>old</td>\n", "      <td>6</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>1109</td>\n", "      <td>2024-11-02</td>\n", "      <td>2024-10-29</td>\n", "      <td>2024-11-01</td>\n", "      <td>4.0</td>\n", "      <td>-1.0</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>old</td>\n", "      <td>6</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>42076</td>\n", "      <td>2024-11-02</td>\n", "      <td>2024-11-02</td>\n", "      <td>2024-11-05</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>new</td>\n", "      <td>6</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>73981</td>\n", "      <td>2024-11-02</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>new</td>\n", "      <td>6</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>76130</td>\n", "      <td>2024-11-02</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>new</td>\n", "      <td>6</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>1108</td>\n", "      <td>2024-11-02</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  item  stock  defineDay  checkPerDay  product_id       today   last_fill   \n", "0  old      6          3            2         801  2024-11-02  2024-11-02  \\\n", "1  old      6          3            2        1109  2024-11-02  2024-10-29   \n", "2  old      6          3            2       42076  2024-11-02  2024-11-02   \n", "3  new      6          3            2       73981  2024-11-02         NaT   \n", "4  new      6          3            2       76130  2024-11-02         NaT   \n", "5  new      6          3            2        1108  2024-11-02         NaT   \n", "\n", "     CheckDay  dayWait  dayWait2  status  \n", "0  2024-11-05      0.0       3.0   False  \n", "1  2024-11-01      4.0      -1.0    True  \n", "2  2024-11-05      0.0       3.0   False  \n", "3         NaT      0.0       0.0    True  \n", "4         NaT      0.0       0.0    True  \n", "5         NaT      0.0       0.0    True  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "from datetime import datetime, date\n", "\n", "last_fill_product_dataframe = pd.DataFrame([\n", " {\n", " \"datetime\": \"2024-11-02T11:42:06.749991+06:30\",\n", " \"product_id\": 801,\n", " \"record_id\": \"CS00000032\",\n", " },\n", " {\n", " \"datetime\": \"2024-10-29T11:42:20.366979+06:30\",\n", " \"product_id\": 1109,\n", " \"record_id\": \"CS00000033\",\n", " },\n", " {\n", " \"datetime\": \"2024-11-02T11:42:32.403000+06:30\",\n", " \"product_id\": 42076,\n", " \"record_id\": \"CS00000035\",\n", " }\n", "])\n", "\n", "warehouse_product_dataframe = pd.DataFrame([\n", " {\n", " \"product_id\": 42076,\n", " \"product_idname\": \"EW1249\",\n", " },\n", " {\n", " \"product_id\": 73981,\n", " \"product_idname\": \"EW1250\",\n", " },\n", " {\n", " \"product_id\": 801,\n", " \"product_idname\": \"EW2190\",\n", " },\n", " {\n", " \"product_id\": 76130,\n", " \"product_idname\": \"EW2284\",\n", " },\n", " {\n", " \"product_id\": 1109,\n", " \"product_idname\": \"GV7401\",\n", " },\n", " {\n", " \"product_id\": 1108,\n", " \"product_idname\": \"GV7445\",\n", " }\n", "])\n", "\n", "merged_df = pd.merge(last_fill_product_dataframe, warehouse_product_dataframe, on='product_id', how='outer')\n", "\n", "defineDay = 3\n", "merged_df.loc[~merged_df['product_id'].isin(last_fill_product_dataframe['product_id']), 'item'] = 'new'\n", "merged_df.loc[(~merged_df['item'].eq('new')) & (merged_df['datetime'].notna()), 'item'] = 'old'\n", "merged_df['stock'] = len(warehouse_product_dataframe)\n", "merged_df['defineDay'] = defineDay\n", "merged_df['checkPerDay'] = (merged_df['stock'] / merged_df['defineDay']).astype(int)\n", "merged_df['today'] = pd.to_datetime('today').date().strftime('%Y-%m-%d')\n", "merged_df['last_fill'] = pd.to_datetime(merged_df['datetime']).dt.date.astype(str)\n", "merged_df['CheckDay'] = (pd.to_datetime(merged_df['last_fill']) + pd.DateOffset(days=defineDay)).astype(str)\n", "merged_df['dayWait'] = (pd.to_datetime(merged_df['today']) - pd.to_datetime(merged_df['last_fill'])).dt.days\n", "merged_df['dayWait2'] = (pd.to_datetime(merged_df['CheckDay']) - pd.to_datetime(merged_df['today'])).dt.days\n", "merged_df['status'] = merged_df['dayWait'].apply(lambda x: False if x < defineDay else True)\n", "merged_df.fillna(0, inplace=True)\n", "# merged_df = merged_df.sort_values(by=['status', 'dayWait'], ascending=[False, False])\n", "\n", "\n", "desired_output_dataframe = merged_df[['item', 'stock', 'defineDay', 'checkPerDay', 'product_id',  'today', 'last_fill', 'CheckDay', 'dayWait', 'dayWait2', 'status']]\n", "desired_output_dataframe"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "logging.getLogger().setLevel(logging.WARNING)\n", "logging.disable(logging.CRITICAL)\n", "\n", "import sys\n", "import requests\n", "import pandas as pd\n", "sys.path.append('../../../../../app/')\n", "from src.Connect.postgresql_nern import postgresql_shwethe_carItemChange\n", "from src.shwethe_miniapp_carItemChange.database import get_session \n", "from src.common.withEngineFunc import SQLcall\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "\n", "session_generator = get_session()\n", "session = next(session_generator)  # 获取生成器中的第一个元素，即 session 对象\n", "engine = session.get_bind()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_id</th>\n", "      <th>jia_yi_idname</th>\n", "      <th>jia_yi_mm_name</th>\n", "      <th>ri_qi</th>\n", "      <th>product_id</th>\n", "      <th>fen_dian</th>\n", "      <th>product_qty</th>\n", "      <th>type</th>\n", "      <th>product_idname</th>\n", "      <th>product_mm_name</th>\n", "      <th>product_d_name</th>\n", "      <th>th_name</th>\n", "      <th>price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>793</td>\n", "      <td>E1</td>\n", "      <td>ကားပစ္စည်းဂိုဒေါင်</td>\n", "      <td>2010-06-06 00:00:00.000</td>\n", "      <td>536</td>\n", "      <td>4</td>\n", "      <td>52.0</td>\n", "      <td>0</td>\n", "      <td>EX3102</td>\n", "      <td>ဘရိတ်ဝါရှာ</td>\n", "      <td>SC-80207R  SEIKEN</td>\n", "      <td>0</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>793</td>\n", "      <td>E1</td>\n", "      <td>ကားပစ္စည်းဂိုဒေါင်</td>\n", "      <td>2010-06-06 00:00:00.000</td>\n", "      <td>1425</td>\n", "      <td>4</td>\n", "      <td>46.0</td>\n", "      <td>0</td>\n", "      <td>GW9411</td>\n", "      <td>ဝရိန်မီးကြည့်မှန် (ဖြူ)</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>793</td>\n", "      <td>E1</td>\n", "      <td>ကားပစ္စည်းဂိုဒေါင်</td>\n", "      <td>2010-06-06 00:00:00.000</td>\n", "      <td>6430</td>\n", "      <td>4</td>\n", "      <td>6.0</td>\n", "      <td>0</td>\n", "      <td>EX4313</td>\n", "      <td>ကလပ်ဖင်းဂါးဖေါ့ဝါရှာ(ထွန်စက်)</td>\n", "      <td>887910   12\"</td>\n", "      <td>0</td>\n", "      <td>60.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>793</td>\n", "      <td>E1</td>\n", "      <td>ကားပစ္စည်းဂိုဒေါင်</td>\n", "      <td>2010-06-06 00:00:00.000</td>\n", "      <td>6500</td>\n", "      <td>4</td>\n", "      <td>140.0</td>\n", "      <td>0</td>\n", "      <td>EX1107</td>\n", "      <td>သတ္တုဝါရှာ</td>\n", "      <td>NO.12</td>\n", "      <td>0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>793</td>\n", "      <td>E1</td>\n", "      <td>ကားပစ္စည်းဂိုဒေါင်</td>\n", "      <td>2010-06-06 00:00:00.000</td>\n", "      <td>6501</td>\n", "      <td>4</td>\n", "      <td>73.0</td>\n", "      <td>0</td>\n", "      <td>EX1108</td>\n", "      <td>သတ္တုဝါရှာ</td>\n", "      <td>NO-19</td>\n", "      <td>0</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>151</th>\n", "      <td>1081</td>\n", "      <td>E3</td>\n", "      <td>စန်းကော (စက်ပစ္စည်းထားခန်း)</td>\n", "      <td>2010-06-06 00:00:00.000</td>\n", "      <td>28436</td>\n", "      <td>4</td>\n", "      <td>2.0</td>\n", "      <td>0</td>\n", "      <td>GW1654</td>\n", "      <td>ထွန်စက်ဘရိတ်ရွှံကာ</td>\n", "      <td>MF275</td>\n", "      <td>0</td>\n", "      <td>120.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td>1081</td>\n", "      <td>E3</td>\n", "      <td>စန်းကော (စက်ပစ္စည်းထားခန်း)</td>\n", "      <td>2010-06-06 00:00:00.000</td>\n", "      <td>28620</td>\n", "      <td>4</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>EX4338</td>\n", "      <td>ပလပ်ဘော (ထွန်စက်)</td>\n", "      <td>MF-390</td>\n", "      <td>0</td>\n", "      <td>650.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>153</th>\n", "      <td>1081</td>\n", "      <td>E3</td>\n", "      <td>စန်းကော (စက်ပစ္စည်းထားခန်း)</td>\n", "      <td>2010-06-06 00:00:00.000</td>\n", "      <td>35069</td>\n", "      <td>4</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>EY1139</td>\n", "      <td>ဂျစ်ကားဘီး</td>\n", "      <td>SENCEN</td>\n", "      <td>0</td>\n", "      <td>6700.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154</th>\n", "      <td>1081</td>\n", "      <td>E3</td>\n", "      <td>စန်းကော (စက်ပစ္စည်းထားခန်း)</td>\n", "      <td>2010-06-06 00:00:00.000</td>\n", "      <td>40730</td>\n", "      <td>4</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>GW7552</td>\n", "      <td>ထွန်စက်ဝိုက်စီး (ဘား)</td>\n", "      <td>275</td>\n", "      <td>0</td>\n", "      <td>250.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>155</th>\n", "      <td>1081</td>\n", "      <td>E3</td>\n", "      <td>စန်းကော (စက်ပစ္စည်းထားခန်း)</td>\n", "      <td>2010-06-06 00:00:00.000</td>\n", "      <td>13528</td>\n", "      <td>4</td>\n", "      <td>6.7</td>\n", "      <td>0</td>\n", "      <td>EX3606</td>\n", "      <td>ကြေးပိုက်(မစ်နူန်း)</td>\n", "      <td>3/16''</td>\n", "      <td>0</td>\n", "      <td>60.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>156 rows × 13 columns</p>\n", "</div>"], "text/plain": ["     jia_yi_id jia_yi_idname               jia_yi_mm_name   \n", "0          793            E1           ကားပစ္စည်းဂိုဒေါင်  \\\n", "1          793            E1           ကားပစ္စည်းဂိုဒေါင်   \n", "2          793            E1           ကားပစ္စည်းဂိုဒေါင်   \n", "3          793            E1           ကားပစ္စည်းဂိုဒေါင်   \n", "4          793            E1           ကားပစ္စည်းဂိုဒေါင်   \n", "..         ...           ...                          ...   \n", "151       1081            E3  စန်းကော (စက်ပစ္စည်းထားခန်း)   \n", "152       1081            E3  စန်းကော (စက်ပစ္စည်းထားခန်း)   \n", "153       1081            E3  စန်းကော (စက်ပစ္စည်းထားခန်း)   \n", "154       1081            E3  စန်းကော (စက်ပစ္စည်းထားခန်း)   \n", "155       1081            E3  စန်းကော (စက်ပစ္စည်းထားခန်း)   \n", "\n", "                       ri_qi  product_id  fen_dian  product_qty  type   \n", "0    2010-06-06 00:00:00.000         536         4         52.0     0  \\\n", "1    2010-06-06 00:00:00.000        1425         4         46.0     0   \n", "2    2010-06-06 00:00:00.000        6430         4          6.0     0   \n", "3    2010-06-06 00:00:00.000        6500         4        140.0     0   \n", "4    2010-06-06 00:00:00.000        6501         4         73.0     0   \n", "..                       ...         ...       ...          ...   ...   \n", "151  2010-06-06 00:00:00.000       28436         4          2.0     0   \n", "152  2010-06-06 00:00:00.000       28620         4          1.0     0   \n", "153  2010-06-06 00:00:00.000       35069         4          1.0     0   \n", "154  2010-06-06 00:00:00.000       40730         4          1.0     0   \n", "155  2010-06-06 00:00:00.000       13528         4          6.7     0   \n", "\n", "    product_idname                product_mm_name     product_d_name th_name   \n", "0           EX3102                     ဘရိတ်ဝါရှာ  SC-80207R  SEIKEN       0  \\\n", "1           GW9411        ဝရိန်မီးကြည့်မှန် (ဖြူ)                  0       0   \n", "2           EX4313  ကလပ်ဖင်းဂါးဖေါ့ဝါရှာ(ထွန်စက်)       887910   12\"       0   \n", "3           EX1107                     သတ္တုဝါရှာ              NO.12       0   \n", "4           EX1108                     သတ္တုဝါရှာ              NO-19       0   \n", "..             ...                            ...                ...     ...   \n", "151         GW1654             ထွန်စက်ဘရိတ်ရွှံကာ              MF275       0   \n", "152         EX4338              ပလပ်ဘော (ထွန်စက်)             MF-390       0   \n", "153         EY1139                     ဂျစ်ကားဘီး             SENCEN       0   \n", "154         GW7552          ထွန်စက်ဝိုက်စီး (ဘား)                275       0   \n", "155         EX3606            ကြေးပိုက်(မစ်နူန်း)             3/16''       0   \n", "\n", "      price  \n", "0      50.0  \n", "1       5.0  \n", "2      60.0  \n", "3       2.0  \n", "4       4.0  \n", "..      ...  \n", "151   120.0  \n", "152   650.0  \n", "153  6700.0  \n", "154   250.0  \n", "155    60.0  \n", "\n", "[156 rows x 13 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["def MListProductNotCheck100():\n", "    \n", "    def warehouseProduct():\n", "        warehouseId = ['793', '4612', '1081']\n", "        list_product = []\n", "        for id in warehouseId:\n", "            url = f\"{mongodb_data_api}/api/v2/search/warehouse_product/?warehouse_id={id}\"\n", "            response = requests.get(url)\n", "            list_product.append(response.json())\n", "        fusion_warehouse = []\n", "        for inner_list in list_product:\n", "            for item in inner_list:\n", "                fusion_warehouse.append(item)\n", "\n", "        df = pd.DataFrame(fusion_warehouse).fillna(0)\n", "        df.drop_duplicates(subset=[\"product_id\"], keep=\"first\", inplace=True)\n", "\n", "        url = f'{mongodb_data_api}/api/v2/search/product_list_id'\n", "        to_dict = df[['product_id']].to_dict('records')\n", "        body_raw = {\"data_api\": to_dict}\n", "        getProduct = requests.get(url=url, json=body_raw).json()\n", "        getProduct = pd.DataFrame(getProduct)\n", "\n", "        dataframeOutput = pd.merge(df, getProduct, left_on='product_id', right_on='product_id').fillna(0)\n", "\n", "        return dataframeOutput\n", "\n", "\n", "    def priceEachProduct():\n", "        product_id = warehouseProduct()\n", "        product_ids = product_id['product_id'].tolist()\n", "        # product_ids = [536, 1425, 6430, 6500, 1108]\n", "        prices = []\n", "\n", "        for product_id in product_ids:\n", "            url = f'{mongodb_data_api}/api/v2/search/price?ID={product_id}'\n", "            response = requests.get(url).json()\n", "            prices.append({'product_id': product_id, 'price': response})\n", "\n", "        df = pd.DataFrame(prices)\n", "        return df\n", "\n", "\n", "    warehouse_product_dataframe = warehouseProduct()\n", "    ppriceEachProduct = priceEachProduct()\n", "\n", "    dataframeOutput = pd.merge(warehouse_product_dataframe, ppriceEachProduct, left_on='product_id', right_on='product_id').fillna(0)\n", "\n", "    return dataframeOutput\n", "\n", "MListProductNotCheck100()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}