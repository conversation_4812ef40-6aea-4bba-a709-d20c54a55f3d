{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# !pip install pandas\n", "# !pip install psycopg2-binary"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-10-28 06:47:13,301 INFO sqlalchemy.engine.Engine select pg_catalog.version()\n", "2023-10-28 06:47:13,309 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2023-10-28 06:47:13,316 INFO sqlalchemy.engine.Engine select current_schema()\n", "2023-10-28 06:47:13,318 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2023-10-28 06:47:13,323 INFO sqlalchemy.engine.Engine show standard_conforming_strings\n", "2023-10-28 06:47:13,329 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2023-10-28 06:47:13,336 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2023-10-28 06:47:13,372 INFO sqlalchemy.engine.Engine SELECT item_insert.record_id, item_insert.datetime, item_insert.warehouse_id, item_insert.che_liang_id, item_insert.jia_yi_fang_a, item_insert.jia_yi_fang_b, item_insert.lei_a, item_insert.lei_b, item_insert.product_id, item_insert.qty, item_insert.price, item_insert.bi_zhi, item_insert.group_id, item_insert.status, item_insert.used_type, item_insert.old_qty, item_insert.amount_in_de, item_insert.auto_id \n", "FROM item_insert \n", "WHERE item_insert.datetime > %(datetime_1)s AND item_insert.status = %(status_1)s\n", "2023-10-28 06:47:13,392 INFO sqlalchemy.engine.Engine [generated in 0.01991s] {'datetime_1': datetime.date(2022, 10, 28), 'status_1': 'success'}\n", "2023-10-28 06:47:13,681 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>che_liang_id</th>\n", "      <th>product_id</th>\n", "      <th>qty</th>\n", "      <th>price</th>\n", "      <th>sum_qty</th>\n", "      <th>sum_price</th>\n", "      <th>total_price</th>\n", "      <th>used_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>2023-09-15 08:24:34.847189+06:30</td>\n", "      <td>194</td>\n", "      <td>53776</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>14.0</td>\n", "      <td>1260.0</td>\n", "      <td>16160.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>2023-07-09 15:48:18.015110+06:30</td>\n", "      <td>194</td>\n", "      <td>76130</td>\n", "      <td>5.0</td>\n", "      <td>180.0</td>\n", "      <td>5.0</td>\n", "      <td>900.0</td>\n", "      <td>16160.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>2023-06-20 10:22:07.226217+06:30</td>\n", "      <td>194</td>\n", "      <td>28997</td>\n", "      <td>1.0</td>\n", "      <td>14000.0</td>\n", "      <td>1.0</td>\n", "      <td>14000.0</td>\n", "      <td>16160.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>103</th>\n", "      <td>2023-07-09 15:51:09.687519+06:30</td>\n", "      <td>104</td>\n", "      <td>801</td>\n", "      <td>2.0</td>\n", "      <td>85.0</td>\n", "      <td>2.0</td>\n", "      <td>170.0</td>\n", "      <td>10671.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-05-20 09:32:36.775647+06:30</td>\n", "      <td>104</td>\n", "      <td>1108</td>\n", "      <td>2.0</td>\n", "      <td>28.0</td>\n", "      <td>2.0</td>\n", "      <td>56.0</td>\n", "      <td>10671.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>118</th>\n", "      <td>2023-06-26 08:58:34.137369+06:30</td>\n", "      <td>104</td>\n", "      <td>1109</td>\n", "      <td>3.0</td>\n", "      <td>27.0</td>\n", "      <td>7.0</td>\n", "      <td>189.0</td>\n", "      <td>10671.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>2023-06-22 09:20:51.227272+06:30</td>\n", "      <td>104</td>\n", "      <td>10894</td>\n", "      <td>1.0</td>\n", "      <td>8000.0</td>\n", "      <td>1.0</td>\n", "      <td>8000.0</td>\n", "      <td>10671.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2023-05-18 10:20:25.182988+06:30</td>\n", "      <td>104</td>\n", "      <td>53776</td>\n", "      <td>3.0</td>\n", "      <td>90.0</td>\n", "      <td>20.0</td>\n", "      <td>1800.0</td>\n", "      <td>10671.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109</th>\n", "      <td>2023-07-11 08:43:05.549109+06:30</td>\n", "      <td>104</td>\n", "      <td>61350</td>\n", "      <td>3.0</td>\n", "      <td>152.0</td>\n", "      <td>3.0</td>\n", "      <td>456.0</td>\n", "      <td>10671.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>2023-06-12 08:52:31.067967+06:30</td>\n", "      <td>23021</td>\n", "      <td>801</td>\n", "      <td>3.0</td>\n", "      <td>85.0</td>\n", "      <td>3.0</td>\n", "      <td>255.0</td>\n", "      <td>5245.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>2023-08-18 09:33:54.567977+06:30</td>\n", "      <td>23021</td>\n", "      <td>23515</td>\n", "      <td>1.0</td>\n", "      <td>2500.0</td>\n", "      <td>1.0</td>\n", "      <td>2500.0</td>\n", "      <td>5245.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>2023-07-06 08:46:17.850234+06:30</td>\n", "      <td>23021</td>\n", "      <td>28991</td>\n", "      <td>2.0</td>\n", "      <td>300.0</td>\n", "      <td>2.0</td>\n", "      <td>600.0</td>\n", "      <td>5245.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161</th>\n", "      <td>2023-07-29 08:27:59.254570+06:30</td>\n", "      <td>23021</td>\n", "      <td>53776</td>\n", "      <td>6.0</td>\n", "      <td>90.0</td>\n", "      <td>21.0</td>\n", "      <td>1890.0</td>\n", "      <td>5245.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>218</th>\n", "      <td>2023-09-19 08:31:24.123940+06:30</td>\n", "      <td>11069</td>\n", "      <td>53776</td>\n", "      <td>3.0</td>\n", "      <td>90.0</td>\n", "      <td>11.0</td>\n", "      <td>990.0</td>\n", "      <td>4771.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>2023-07-19 08:49:39.672467+06:30</td>\n", "      <td>11069</td>\n", "      <td>9399</td>\n", "      <td>4.0</td>\n", "      <td>825.0</td>\n", "      <td>4.0</td>\n", "      <td>3300.0</td>\n", "      <td>4771.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2023-05-30 09:28:16.808499+06:30</td>\n", "      <td>11069</td>\n", "      <td>1108</td>\n", "      <td>2.0</td>\n", "      <td>28.0</td>\n", "      <td>2.0</td>\n", "      <td>56.0</td>\n", "      <td>4771.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>146</th>\n", "      <td>2023-07-22 13:27:27.195882+06:30</td>\n", "      <td>11069</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>5.0</td>\n", "      <td>425.0</td>\n", "      <td>4771.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>2023-07-04 08:26:50.029522+06:30</td>\n", "      <td>26042</td>\n", "      <td>1108</td>\n", "      <td>1.0</td>\n", "      <td>27.0</td>\n", "      <td>132.0</td>\n", "      <td>3564.0</td>\n", "      <td>3564.0</td>\n", "      <td>drive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-05-19 12:30:03.907007+06:30</td>\n", "      <td>97</td>\n", "      <td>53776</td>\n", "      <td>10.0</td>\n", "      <td>90.0</td>\n", "      <td>25.0</td>\n", "      <td>2250.0</td>\n", "      <td>2635.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>2023-06-04 09:05:06.167071+06:30</td>\n", "      <td>97</td>\n", "      <td>1109</td>\n", "      <td>3.0</td>\n", "      <td>27.0</td>\n", "      <td>3.0</td>\n", "      <td>81.0</td>\n", "      <td>2635.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>2023-05-30 09:20:39.383716+06:30</td>\n", "      <td>97</td>\n", "      <td>61350</td>\n", "      <td>2.0</td>\n", "      <td>152.0</td>\n", "      <td>2.0</td>\n", "      <td>304.0</td>\n", "      <td>2635.0</td>\n", "      <td>other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2023-05-20 09:53:28.697766+06:30</td>\n", "      <td>106</td>\n", "      <td>1109</td>\n", "      <td>3.0</td>\n", "      <td>30.0</td>\n", "      <td>3.0</td>\n", "      <td>90.0</td>\n", "      <td>2507.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>2023-07-06 08:40:10.023334+06:30</td>\n", "      <td>106</td>\n", "      <td>46805</td>\n", "      <td>2.0</td>\n", "      <td>81.0</td>\n", "      <td>2.0</td>\n", "      <td>162.0</td>\n", "      <td>2507.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>2023-06-22 09:22:27.991644+06:30</td>\n", "      <td>106</td>\n", "      <td>317</td>\n", "      <td>5.0</td>\n", "      <td>150.0</td>\n", "      <td>5.0</td>\n", "      <td>750.0</td>\n", "      <td>2507.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>151</th>\n", "      <td>2023-07-22 13:28:13.482334+06:30</td>\n", "      <td>106</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>5.0</td>\n", "      <td>425.0</td>\n", "      <td>2507.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>2023-07-31 08:30:29.508930+06:30</td>\n", "      <td>106</td>\n", "      <td>53776</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>12.0</td>\n", "      <td>1080.0</td>\n", "      <td>2507.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>173</th>\n", "      <td>2023-08-15 08:33:46.918094+06:30</td>\n", "      <td>43162</td>\n", "      <td>76130</td>\n", "      <td>1.0</td>\n", "      <td>180.0</td>\n", "      <td>1.0</td>\n", "      <td>180.0</td>\n", "      <td>2250.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174</th>\n", "      <td>2023-08-15 08:27:10.222348+06:30</td>\n", "      <td>43162</td>\n", "      <td>53776</td>\n", "      <td>11.0</td>\n", "      <td>90.0</td>\n", "      <td>23.0</td>\n", "      <td>2070.0</td>\n", "      <td>2250.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>2023-06-28 09:24:12.740714+06:30</td>\n", "      <td>46723</td>\n", "      <td>61350</td>\n", "      <td>2.0</td>\n", "      <td>152.0</td>\n", "      <td>4.0</td>\n", "      <td>608.0</td>\n", "      <td>2249.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>240</th>\n", "      <td>2023-10-22 09:07:59.145813+06:30</td>\n", "      <td>46723</td>\n", "      <td>53776</td>\n", "      <td>3.0</td>\n", "      <td>90.0</td>\n", "      <td>7.0</td>\n", "      <td>630.0</td>\n", "      <td>2249.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154</th>\n", "      <td>2023-07-26 08:46:30.784530+06:30</td>\n", "      <td>46723</td>\n", "      <td>11873</td>\n", "      <td>3.0</td>\n", "      <td>170.0</td>\n", "      <td>3.0</td>\n", "      <td>510.0</td>\n", "      <td>2249.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>2023-09-09 08:31:49.219071+06:30</td>\n", "      <td>46723</td>\n", "      <td>9555</td>\n", "      <td>2.0</td>\n", "      <td>75.0</td>\n", "      <td>2.0</td>\n", "      <td>150.0</td>\n", "      <td>2249.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>2023-07-27 08:46:36.540953+06:30</td>\n", "      <td>46723</td>\n", "      <td>8072</td>\n", "      <td>6.0</td>\n", "      <td>45.0</td>\n", "      <td>6.0</td>\n", "      <td>270.0</td>\n", "      <td>2249.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>2023-07-04 08:28:25.643975+06:30</td>\n", "      <td>46723</td>\n", "      <td>1109</td>\n", "      <td>3.0</td>\n", "      <td>27.0</td>\n", "      <td>3.0</td>\n", "      <td>81.0</td>\n", "      <td>2249.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-05-18 10:27:42.152927+06:30</td>\n", "      <td>2290</td>\n", "      <td>1108</td>\n", "      <td>2.0</td>\n", "      <td>28.0</td>\n", "      <td>2.0</td>\n", "      <td>56.0</td>\n", "      <td>2025.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>2023-05-21 09:55:45.939026+06:30</td>\n", "      <td>2290</td>\n", "      <td>1109</td>\n", "      <td>3.0</td>\n", "      <td>28.0</td>\n", "      <td>3.0</td>\n", "      <td>84.0</td>\n", "      <td>2025.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2023-05-17 09:24:19.653289+06:30</td>\n", "      <td>2290</td>\n", "      <td>53776</td>\n", "      <td>6.0</td>\n", "      <td>90.0</td>\n", "      <td>20.0</td>\n", "      <td>1800.0</td>\n", "      <td>2025.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>206</th>\n", "      <td>2023-09-05 09:39:32.887651+06:30</td>\n", "      <td>2290</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>2025.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>2023-07-13 08:26:46.231379+06:30</td>\n", "      <td>31838</td>\n", "      <td>53776</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>21.0</td>\n", "      <td>1890.0</td>\n", "      <td>1998.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>2023-06-15 09:08:56.968119+06:30</td>\n", "      <td>31838</td>\n", "      <td>1109</td>\n", "      <td>4.0</td>\n", "      <td>27.0</td>\n", "      <td>4.0</td>\n", "      <td>108.0</td>\n", "      <td>1998.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2023-05-23 11:03:40.640241+06:30</td>\n", "      <td>43889</td>\n", "      <td>76130</td>\n", "      <td>1.0</td>\n", "      <td>180.0</td>\n", "      <td>2.0</td>\n", "      <td>360.0</td>\n", "      <td>1890.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176</th>\n", "      <td>2023-08-15 08:29:23.936975+06:30</td>\n", "      <td>43889</td>\n", "      <td>53776</td>\n", "      <td>2.0</td>\n", "      <td>90.0</td>\n", "      <td>17.0</td>\n", "      <td>1530.0</td>\n", "      <td>1890.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>2023-05-30 09:18:28.779746+06:30</td>\n", "      <td>7565</td>\n", "      <td>53776</td>\n", "      <td>3.0</td>\n", "      <td>91.0</td>\n", "      <td>9.0</td>\n", "      <td>819.0</td>\n", "      <td>1712.0</td>\n", "      <td>other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>171</th>\n", "      <td>2023-08-14 10:46:46.464925+06:30</td>\n", "      <td>7565</td>\n", "      <td>46805</td>\n", "      <td>1.0</td>\n", "      <td>81.0</td>\n", "      <td>3.0</td>\n", "      <td>243.0</td>\n", "      <td>1712.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>2023-08-18 09:41:59.453509+06:30</td>\n", "      <td>7565</td>\n", "      <td>13915</td>\n", "      <td>1.0</td>\n", "      <td>650.0</td>\n", "      <td>1.0</td>\n", "      <td>650.0</td>\n", "      <td>1712.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>2023-07-09 15:47:02.935619+06:30</td>\n", "      <td>37710</td>\n", "      <td>53776</td>\n", "      <td>8.0</td>\n", "      <td>90.0</td>\n", "      <td>8.0</td>\n", "      <td>720.0</td>\n", "      <td>1651.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>116</th>\n", "      <td>2023-07-13 08:31:55.476820+06:30</td>\n", "      <td>37710</td>\n", "      <td>46805</td>\n", "      <td>1.0</td>\n", "      <td>81.0</td>\n", "      <td>1.0</td>\n", "      <td>81.0</td>\n", "      <td>1651.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>2023-07-23 08:22:27.542741+06:30</td>\n", "      <td>37710</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>10.0</td>\n", "      <td>850.0</td>\n", "      <td>1651.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-05-18 10:01:51.998094+06:30</td>\n", "      <td>2343</td>\n", "      <td>53776</td>\n", "      <td>8.0</td>\n", "      <td>90.0</td>\n", "      <td>16.0</td>\n", "      <td>1440.0</td>\n", "      <td>1620.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>2023-08-29 09:27:08.461483+06:30</td>\n", "      <td>2343</td>\n", "      <td>76130</td>\n", "      <td>1.0</td>\n", "      <td>180.0</td>\n", "      <td>1.0</td>\n", "      <td>180.0</td>\n", "      <td>1620.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2023-05-22 10:14:01.304185+06:30</td>\n", "      <td>25371</td>\n", "      <td>46805</td>\n", "      <td>1.0</td>\n", "      <td>81.0</td>\n", "      <td>1.0</td>\n", "      <td>81.0</td>\n", "      <td>1611.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>2023-06-19 08:57:34.476174+06:30</td>\n", "      <td>25371</td>\n", "      <td>53776</td>\n", "      <td>2.0</td>\n", "      <td>90.0</td>\n", "      <td>17.0</td>\n", "      <td>1530.0</td>\n", "      <td>1611.0</td>\n", "      <td>other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>117</th>\n", "      <td>2023-07-13 08:30:09.668344+06:30</td>\n", "      <td>105</td>\n", "      <td>46805</td>\n", "      <td>1.0</td>\n", "      <td>81.0</td>\n", "      <td>1.0</td>\n", "      <td>81.0</td>\n", "      <td>1592.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>112</th>\n", "      <td>2023-07-13 08:24:04.458559+06:30</td>\n", "      <td>105</td>\n", "      <td>53776</td>\n", "      <td>8.0</td>\n", "      <td>90.0</td>\n", "      <td>8.0</td>\n", "      <td>720.0</td>\n", "      <td>1592.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>224</th>\n", "      <td>2023-09-22 09:12:38.562873+06:30</td>\n", "      <td>105</td>\n", "      <td>801</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>1.0</td>\n", "      <td>85.0</td>\n", "      <td>1592.0</td>\n", "      <td>add_more</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2023-05-16 09:25:21.464112+06:30</td>\n", "      <td>105</td>\n", "      <td>1108</td>\n", "      <td>2.0</td>\n", "      <td>28.0</td>\n", "      <td>2.0</td>\n", "      <td>56.0</td>\n", "      <td>1592.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>208</th>\n", "      <td>2023-09-07 13:10:53.368927+06:30</td>\n", "      <td>105</td>\n", "      <td>13915</td>\n", "      <td>1.0</td>\n", "      <td>650.0</td>\n", "      <td>1.0</td>\n", "      <td>650.0</td>\n", "      <td>1592.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>2023-06-15 09:11:04.933490+06:30</td>\n", "      <td>42401</td>\n", "      <td>801</td>\n", "      <td>2.0</td>\n", "      <td>85.0</td>\n", "      <td>2.0</td>\n", "      <td>170.0</td>\n", "      <td>1475.0</td>\n", "      <td>other</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>2023-06-03 09:01:23.327813+06:30</td>\n", "      <td>42401</td>\n", "      <td>1109</td>\n", "      <td>3.0</td>\n", "      <td>27.0</td>\n", "      <td>12.0</td>\n", "      <td>324.0</td>\n", "      <td>1475.0</td>\n", "      <td>clean</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>2023-08-25 15:11:53.799653+06:30</td>\n", "      <td>42401</td>\n", "      <td>46805</td>\n", "      <td>1.0</td>\n", "      <td>81.0</td>\n", "      <td>1.0</td>\n", "      <td>81.0</td>\n", "      <td>1475.0</td>\n", "      <td>change</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                            datetime  che_liang_id  product_id   qty    price   \n", "213 2023-09-15 08:24:34.847189+06:30           194       53776   1.0     90.0  \\\n", "107 2023-07-09 15:48:18.015110+06:30           194       76130   5.0    180.0   \n", "79  2023-06-20 10:22:07.226217+06:30           194       28997   1.0  14000.0   \n", "103 2023-07-09 15:51:09.687519+06:30           104         801   2.0     85.0   \n", "0   2023-05-20 09:32:36.775647+06:30           104        1108   2.0     28.0   \n", "118 2023-06-26 08:58:34.137369+06:30           104        1109   3.0     27.0   \n", "85  2023-06-22 09:20:51.227272+06:30           104       10894   1.0   8000.0   \n", "13  2023-05-18 10:20:25.182988+06:30           104       53776   3.0     90.0   \n", "109 2023-07-11 08:43:05.549109+06:30           104       61350   3.0    152.0   \n", "68  2023-06-12 08:52:31.067967+06:30         23021         801   3.0     85.0   \n", "180 2023-08-18 09:33:54.567977+06:30         23021       23515   1.0   2500.0   \n", "100 2023-07-06 08:46:17.850234+06:30         23021       28991   2.0    300.0   \n", "161 2023-07-29 08:27:59.254570+06:30         23021       53776   6.0     90.0   \n", "218 2023-09-19 08:31:24.123940+06:30         11069       53776   3.0     90.0   \n", "137 2023-07-19 08:49:39.672467+06:30         11069        9399   4.0    825.0   \n", "26  2023-05-30 09:28:16.808499+06:30         11069        1108   2.0     28.0   \n", "146 2023-07-22 13:27:27.195882+06:30         11069         801   1.0     85.0   \n", "98  2023-07-04 08:26:50.029522+06:30         26042        1108   1.0     27.0   \n", "2   2023-05-19 12:30:03.907007+06:30            97       53776  10.0     90.0   \n", "58  2023-06-04 09:05:06.167071+06:30            97        1109   3.0     27.0   \n", "46  2023-05-30 09:20:39.383716+06:30            97       61350   2.0    152.0   \n", "33  2023-05-20 09:53:28.697766+06:30           106        1109   3.0     30.0   \n", "101 2023-07-06 08:40:10.023334+06:30           106       46805   2.0     81.0   \n", "87  2023-06-22 09:22:27.991644+06:30           106         317   5.0    150.0   \n", "151 2023-07-22 13:28:13.482334+06:30           106         801   1.0     85.0   \n", "163 2023-07-31 08:30:29.508930+06:30           106       53776   1.0     90.0   \n", "173 2023-08-15 08:33:46.918094+06:30         43162       76130   1.0    180.0   \n", "174 2023-08-15 08:27:10.222348+06:30         43162       53776  11.0     90.0   \n", "84  2023-06-28 09:24:12.740714+06:30         46723       61350   2.0    152.0   \n", "240 2023-10-22 09:07:59.145813+06:30         46723       53776   3.0     90.0   \n", "154 2023-07-26 08:46:30.784530+06:30         46723       11873   3.0    170.0   \n", "197 2023-09-09 08:31:49.219071+06:30         46723        9555   2.0     75.0   \n", "157 2023-07-27 08:46:36.540953+06:30         46723        8072   6.0     45.0   \n", "99  2023-07-04 08:28:25.643975+06:30         46723        1109   3.0     27.0   \n", "4   2023-05-18 10:27:42.152927+06:30          2290        1108   2.0     28.0   \n", "32  2023-05-21 09:55:45.939026+06:30          2290        1109   3.0     28.0   \n", "15  2023-05-17 09:24:19.653289+06:30          2290       53776   6.0     90.0   \n", "206 2023-09-05 09:39:32.887651+06:30          2290         801   1.0     85.0   \n", "114 2023-07-13 08:26:46.231379+06:30         31838       53776   1.0     90.0   \n", "73  2023-06-15 09:08:56.968119+06:30         31838        1109   4.0     27.0   \n", "22  2023-05-23 11:03:40.640241+06:30         43889       76130   1.0    180.0   \n", "176 2023-08-15 08:29:23.936975+06:30         43889       53776   2.0     90.0   \n", "49  2023-05-30 09:18:28.779746+06:30          7565       53776   3.0     91.0   \n", "171 2023-08-14 10:46:46.464925+06:30          7565       46805   1.0     81.0   \n", "179 2023-08-18 09:41:59.453509+06:30          7565       13915   1.0    650.0   \n", "108 2023-07-09 15:47:02.935619+06:30         37710       53776   8.0     90.0   \n", "116 2023-07-13 08:31:55.476820+06:30         37710       46805   1.0     81.0   \n", "147 2023-07-23 08:22:27.542741+06:30         37710         801   1.0     85.0   \n", "6   2023-05-18 10:01:51.998094+06:30          2343       53776   8.0     90.0   \n", "193 2023-08-29 09:27:08.461483+06:30          2343       76130   1.0    180.0   \n", "18  2023-05-22 10:14:01.304185+06:30         25371       46805   1.0     81.0   \n", "76  2023-06-19 08:57:34.476174+06:30         25371       53776   2.0     90.0   \n", "117 2023-07-13 08:30:09.668344+06:30           105       46805   1.0     81.0   \n", "112 2023-07-13 08:24:04.458559+06:30           105       53776   8.0     90.0   \n", "224 2023-09-22 09:12:38.562873+06:30           105         801   1.0     85.0   \n", "10  2023-05-16 09:25:21.464112+06:30           105        1108   2.0     28.0   \n", "208 2023-09-07 13:10:53.368927+06:30           105       13915   1.0    650.0   \n", "71  2023-06-15 09:11:04.933490+06:30         42401         801   2.0     85.0   \n", "56  2023-06-03 09:01:23.327813+06:30         42401        1109   3.0     27.0   \n", "190 2023-08-25 15:11:53.799653+06:30         42401       46805   1.0     81.0   \n", "\n", "     sum_qty  sum_price  total_price used_type  \n", "213     14.0     1260.0      16160.0  add_more  \n", "107      5.0      900.0      16160.0    change  \n", "79       1.0    14000.0      16160.0    change  \n", "103      2.0      170.0      10671.0    change  \n", "0        2.0       56.0      10671.0         0  \n", "118      7.0      189.0      10671.0     clean  \n", "85       1.0     8000.0      10671.0    change  \n", "13      20.0     1800.0      10671.0         0  \n", "109      3.0      456.0      10671.0    change  \n", "68       3.0      255.0       5245.0    change  \n", "180      1.0     2500.0       5245.0    change  \n", "100      2.0      600.0       5245.0    change  \n", "161     21.0     1890.0       5245.0  add_more  \n", "218     11.0      990.0       4771.0  add_more  \n", "137      4.0     3300.0       4771.0    change  \n", "26       2.0       56.0       4771.0     clean  \n", "146      5.0      425.0       4771.0    change  \n", "98     132.0     3564.0       3564.0     drive  \n", "2       25.0     2250.0       2635.0         0  \n", "58       3.0       81.0       2635.0     clean  \n", "46       2.0      304.0       2635.0     other  \n", "33       3.0       90.0       2507.0     clean  \n", "101      2.0      162.0       2507.0    change  \n", "87       5.0      750.0       2507.0    change  \n", "151      5.0      425.0       2507.0    change  \n", "163     12.0     1080.0       2507.0  add_more  \n", "173      1.0      180.0       2250.0    change  \n", "174     23.0     2070.0       2250.0    change  \n", "84       4.0      608.0       2249.0    change  \n", "240      7.0      630.0       2249.0  add_more  \n", "154      3.0      510.0       2249.0    change  \n", "197      2.0      150.0       2249.0    change  \n", "157      6.0      270.0       2249.0    change  \n", "99       3.0       81.0       2249.0     clean  \n", "4        2.0       56.0       2025.0         0  \n", "32       3.0       84.0       2025.0     clean  \n", "15      20.0     1800.0       2025.0         0  \n", "206      1.0       85.0       2025.0    change  \n", "114     21.0     1890.0       1998.0  add_more  \n", "73       4.0      108.0       1998.0     clean  \n", "22       2.0      360.0       1890.0         0  \n", "176     17.0     1530.0       1890.0  add_more  \n", "49       9.0      819.0       1712.0     other  \n", "171      3.0      243.0       1712.0    change  \n", "179      1.0      650.0       1712.0    change  \n", "108      8.0      720.0       1651.0    change  \n", "116      1.0       81.0       1651.0    change  \n", "147     10.0      850.0       1651.0    change  \n", "6       16.0     1440.0       1620.0         0  \n", "193      1.0      180.0       1620.0    change  \n", "18       1.0       81.0       1611.0         0  \n", "76      17.0     1530.0       1611.0     other  \n", "117      1.0       81.0       1592.0    change  \n", "112      8.0      720.0       1592.0    change  \n", "224      1.0       85.0       1592.0  add_more  \n", "10       2.0       56.0       1592.0         0  \n", "208      1.0      650.0       1592.0    change  \n", "71       2.0      170.0       1475.0     other  \n", "56      12.0      324.0       1475.0     clean  \n", "190      1.0       81.0       1475.0    change  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "from src.shwethe_miniapp_carItemChange.database import get_session\n", "from src.shwethe_miniapp_carItemChange.src.models.models import item_insert\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "from src.Connect.postgresql_nern import real_backend_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "from pandas import json_normalize\n", "import requests\n", "\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def dataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "def getCheckStockReport():\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        week_ago = today - DT.<PERSON><PERSON><PERSON>(days=365)\n", "        heroesPersonal = db.exec(select(item_insert).where(item_insert.datetime > week_ago, item_insert.status == 'success')).all()\n", "        A10001 = dataframe(heroesPersonal, to_dict=False)\n", "\n", "        A10001['price'] = A10001['price'].astype(float)\n", "        A10001 = A10001[[\"datetime\", \"che_liang_id\", \"product_id\", \"qty\", \"price\", \"used_type\"]]\n", "        A10001['sum_qty'] = A10001.groupby(['che_liang_id', 'product_id'])['qty'].transform('sum')\n", "        A10001 = A10001.sort_values(by=['che_liang_id', 'product_id', 'qty'])\n", "        A10001 = A10001.drop_duplicates(subset=['che_liang_id', 'product_id'])\n", "        A10001['sum_price'] = A10001['sum_qty'] * A10001['price']\n", "        A10001['total_price'] = A10001.groupby('che_liang_id')['sum_price'].transform('sum')\n", "        A10001 = A10001.sort_values(by='total_price', ascending=False)\n", "        A10001 = A10001[[\"datetime\", \"che_liang_id\", \"product_id\", \"qty\", \"price\", \"sum_qty\", \"sum_price\", \"total_price\", \"used_type\"]]\n", "        A10001 = A10001.head(60)\n", "    return A10001\n", "\n", "def getName():\n", "    fgetCheckStockReport = getCheckStockReport()\n", "\n", "    df = fgetCheckStockReport.rename(columns={\"product_id\": 'product_id'})\n", "    url = mongodb_data_api + '/api/v2/search/product_list_id'\n", "    to_dict = df[['product_id']].to_dict('records')\n", "    body_raw = {\"data_api\": to_dict}\n", "    getProduct = requests.get(url=url, json=body_raw).json()\n", "    getProduct = pd.DataFrame(getProduct)\n", "\n", "    url = f'{mongodb_data_api}/api/v2/search/jia_yi_name_list_id'\n", "    changename = fgetCheckStockReport.rename(columns={'che_liang_id': 'jia_yi_id'})\n", "    to_dict = changename[['jia_yi_id']].to_dict('records')\n", "    body_raw = {\"data_api\": to_dict}\n", "    getCar = requests.get(url=url, json=body_raw).json()\n", "    getCar = pd.DataFrame(getCar)\n", "\n", "    output = getProduct, getCar, fgetCheckStockReport\n", "    # output = fgetCheckStockReport\n", "    return output\n", "\n", "def fusion():\n", "    getProduct, getCar, fgetCheckStockReport = getName()\n", "    mergeProduct = fgetCheckStockReport.merge(getProduct, left_on='product_id', right_on='product_id', how='left')\n", "    mergeCar = mergeProduct.merge(getCar, left_on='che_liang_id', right_on='jia_yi_id', how='left')\n", "    df = mergeCar.applymap(lambda x: None if pd.isna(x) or x == '' else x)\n", "    df = df[['datetime', 'che_liang_id', 'jia_yi_idname', 'product_id', 'product_idname', 'product_mm_name', 'product_d_name', 'qty', 'price', 'sum_qty', 'sum_price', 'total_price', 'used_type']]\n", "    return df\n", "\n", "ddd = getCheckStockReport()\n", "ddd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}