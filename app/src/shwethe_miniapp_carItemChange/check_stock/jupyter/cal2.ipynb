{"cells": [{"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-08-21 07:02:02,273 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2023-08-21 07:02:02,287 INFO sqlalchemy.engine.Engine SELECT item_insert.record_id, item_insert.datetime, item_insert.warehouse_id, item_insert.che_liang_id, item_insert.jia_yi_fang_a, item_insert.jia_yi_fang_b, item_insert.lei_a, item_insert.lei_b, item_insert.product_id, item_insert.qty, item_insert.price, item_insert.bi_zhi, item_insert.group_id, item_insert.status, item_insert.used_type, item_insert.old_qty, item_insert.amount_in_de, item_insert.auto_id \n", "FROM item_insert \n", "WHERE item_insert.datetime > %(datetime_1)s AND item_insert.status = %(status_1)s\n", "2023-08-21 07:02:02,292 INFO sqlalchemy.engine.Engine [cached since 1030s ago] {'datetime_1': datetime.date(2023, 8, 14), 'status_1': 'success'}\n", "2023-08-21 07:02:02,346 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>che_liang_id</th>\n", "      <th>sum_price</th>\n", "      <th>total_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-08-14</td>\n", "      <td>7565</td>\n", "      <td>81.0</td>\n", "      <td>81.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2023-08-15</td>\n", "      <td>7565</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2023-08-16</td>\n", "      <td>7565</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>2023-08-17</td>\n", "      <td>7565</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>2023-08-18</td>\n", "      <td>7565</td>\n", "      <td>650.0</td>\n", "      <td>731.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>2023-08-16</td>\n", "      <td>46723</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>2023-08-17</td>\n", "      <td>46723</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>2023-08-18</td>\n", "      <td>46723</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>2023-08-19</td>\n", "      <td>46723</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>2023-08-20</td>\n", "      <td>46723</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>98 rows × 4 columns</p>\n", "</div>"], "text/plain": ["     datetime  che_liang_id  sum_price  total_price\n", "0  2023-08-14          7565       81.0         81.0\n", "14 2023-08-15          7565        NaN          NaN\n", "28 2023-08-16          7565        NaN          NaN\n", "42 2023-08-17          7565        NaN          NaN\n", "56 2023-08-18          7565      650.0        731.0\n", "..        ...           ...        ...          ...\n", "41 2023-08-16         46723        NaN          NaN\n", "55 2023-08-17         46723        NaN          NaN\n", "69 2023-08-18         46723        NaN          NaN\n", "83 2023-08-19         46723        NaN          NaN\n", "97 2023-08-20         46723        NaN          NaN\n", "\n", "[98 rows x 4 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import psycopg2\n", "import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "from src.shwethe_miniapp_carItemChange.database import get_session\n", "from src.shwethe_miniapp_carItemChange.src.models.models import item_insert\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "from src.Connect.postgresql_nern import real_backend_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "from pandas import json_normalize\n", "import requests\n", "import numpy as np\n", "\n", "\n", "# Assuming you have the connection details\n", "postgresql_shwethe_carItemChange = psycopg2.connect(\n", "    host=\"************\",\n", "    database=\"shwethe_carItemChange\",\n", "    user=\"postgres\",\n", "    password=\"0818822095\",\n", "    port=\"5436\"\n", ")\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def dataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "\n", "def getCheckStockReport():\n", "    # # Use a context manager to execute the query\n", "    # with postgresql_shwethe_carItemChange:\n", "    #     A10001 = pd.read_sql(\"\"\"\n", "    #         SELECT *\n", "    #         FROM item_insert \n", "    #         WHERE datetime > (CURRENT_DATE - INTERVAL '30 days')\n", "    #         AND status = 'success'\n", "    #     \"\"\", postgresql_shwethe_carItemChange)\n", "    #     A10001 = A10001[[\"datetime\", \"che_liang_id\", \"product_id\", \"qty\", \"price\", \"used_type\"]]\n", "    #     A10001['datetime'] = pd.to_datetime(A10001['datetime'])  # Convert 'datetime' column to datetime type\n", "    #     A10001 = A10001.sort_values(by=['che_liang_id', 'datetime'])\n", "\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        week_ago = today - DT.<PERSON><PERSON><PERSON>(days=7)\n", "        heroesPersonal = db.exec(select(item_insert).where(item_insert.datetime > week_ago, item_insert.status == 'success')).all()\n", "        A10001 = dataframe(heroesPersonal, to_dict=False)\n", "        A10001['price'] = A10001['price'].astype(float)\n", "        A10001 = A10001[[\"datetime\", \"che_liang_id\", \"product_id\", \"qty\", \"price\", \"used_type\"]]\n", "\n", "        A10001['datetime'] = pd.to_datetime(A10001['datetime'])\n", "        A10001['datetime'] = A10001['datetime'].dt.strftime('%Y-%m-%d')\n", "        A10001.sort_values(by=['che_liang_id', 'datetime'], inplace=True)\n", "        A10001['sum_price'] = A10001['qty'] * A10001['price']\n", "        A10001 = A10001.groupby(['datetime', 'che_liang_id'])['sum_price'].sum().reset_index()\n", "        A10001 = A10001.sort_values(by=['che_liang_id', 'datetime'])\n", "        A10001['total_price'] = A10001.groupby('che_liang_id')['sum_price'].cumsum()\n", "        # A10001['total_price'] = A10001.groupby('che_liang_id')['sum_price'].transform('sum')\n", "        # A10001 = A10001.sort_values(by=['total_price'])\n", "        return A10001\n", "\n", "\n", "def add_missing_date():\n", "    df = getCheckStockReport()\n", "\n", "    df['datetime'] = pd.to_datetime(df['datetime'])\n", "    # Generate a date range that covers the entire period\n", "    date_range = pd.date_range(start=df['datetime'].min(), end=df['datetime'].max())\n", "\n", "    # Create a new dataframe with all possible combinations of date and che_liang_id\n", "    che_liang_ids = df['che_liang_id'].unique()\n", "    expanded_data = []\n", "\n", "    for date in date_range:\n", "        for che_liang_id in che_liang_ids:\n", "            expanded_data.append({'datetime': date, 'che_liang_id': che_liang_id})\n", "\n", "    expanded_df = pd.DataFrame(expanded_data)\n", "\n", "    # Merge the expanded dataframe with the original dataframe\n", "    final_df = pd.merge(expanded_df, df, on=['datetime', 'che_liang_id'], how='left')\n", "\n", "    # Sort the final dataframe\n", "    final_df = final_df.sort_values(by=['che_liang_id', 'datetime'])\n", "\n", "    # final_df.replace(np.nan, None, inplace=True)\n", "    # final_df.replace(float('nan'), None, inplace=True)\n", "    final_df = final_df.applymap(lambda x: None if pd.isna(x) else x)\n", "\n", "\n", "    return final_df\n", "    \n", "    \n", "def getName():\n", "    fgetCheckStockReport = add_missing_date()\n", "\n", "    url = f'{mongodb_data_api}/api/v2/search/jia_yi_name_list_id'\n", "    changename = fgetCheckStockReport.rename(columns={'che_liang_id': 'jia_yi_id'})\n", "    to_dict = changename[['jia_yi_id']].to_dict('records')\n", "    body_raw = {\"data_api\": to_dict}\n", "    getCar = requests.get(url=url, json=body_raw).json()\n", "    getCar = pd.DataFrame(getCar)\n", "    output = getCar, fgetCheckStockReport\n", "    return output\n", "\n", "\n", "def fusion():\n", "    getCar, fgetCheckStockReport = getName()\n", "    mergeCar = fgetCheckStockReport.merge(getCar, left_on='che_liang_id', right_on='jia_yi_id', how='left')\n", "    df = mergeCar.applymap(lambda x: None if pd.isna(x) or x == '' else x)\n", "    df = df[['datetime', 'che_liang_id', 'jia_yi_idname', 'sum_price', 'total_price']]\n", "    return df\n", "\n", "\n", "# getProduct, getCar, fgetCheckStockReport = getName()\n", "# df = getCheckStockReport()\n", "df = add_missing_date()\n", "postgresql_shwethe_carItemChange.close()\n", "\n", "# df = df[df['che_liang_id'] == 26042]\n", "df\n", "\n", "# # Group by 'jia_yi_idname' and aggregate 'total_price'\n", "# grouped = df.groupby('jia_yi_idname')['total_price'].apply(list).reset_index(name='data')\n", "\n", "# # Create the series for the JSON format\n", "# series = []\n", "# for index, row in grouped.iterrows():\n", "#     series_data = {\n", "#         'name': row['jia_yi_idname'],\n", "#         'data': row['data'],\n", "#         'type': 'line'\n", "#     }\n", "#     series.append(series_data)\n", "\n", "\n", "# sorted_indices = sorted(range(len(df['datetime'])), key=lambda k: df['datetime'][k])\n", "# sorted_datetime = [df['datetime'][i] for i in sorted_indices]\n", "# sorted_datetime = list(dict.fromkeys(sorted_datetime))\n", "\n", "# # Prepare the final JSON structure\n", "# json_data = {\n", "#     'datetime': sorted_datetime,\n", "#     'series': series\n", "# }\n", "\n", "# # Convert dictionary to JSON string\n", "# # json_string = json.dumps(json_data, indent=4)\n", "\n", "# print(json_data)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>che_liang_id</th>\n", "      <th>total_price_x</th>\n", "      <th>total_price_y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-08-14</td>\n", "      <td>7565</td>\n", "      <td>None</td>\n", "      <td>81.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-08-15</td>\n", "      <td>7565</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-08-16</td>\n", "      <td>7565</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-08-17</td>\n", "      <td>7565</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-08-18</td>\n", "      <td>7565</td>\n", "      <td>None</td>\n", "      <td>731.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-08-14</td>\n", "      <td>43162</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-08-15</td>\n", "      <td>43162</td>\n", "      <td>None</td>\n", "      <td>180.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-08-15</td>\n", "      <td>43162</td>\n", "      <td>None</td>\n", "      <td>1170.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-08-16</td>\n", "      <td>43162</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-08-17</td>\n", "      <td>43162</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2023-08-18</td>\n", "      <td>43162</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     datetime  che_liang_id total_price_x  total_price_y\n", "0  2023-08-14          7565          None           81.0\n", "2  2023-08-15          7565          None            NaN\n", "5  2023-08-16          7565          None            NaN\n", "7  2023-08-17          7565          None            NaN\n", "9  2023-08-18          7565          None          731.0\n", "1  2023-08-14         43162          None            NaN\n", "3  2023-08-15         43162          None          180.0\n", "4  2023-08-15         43162          None         1170.0\n", "6  2023-08-16         43162          None            NaN\n", "8  2023-08-17         43162          None            NaN\n", "10 2023-08-18         43162          None            NaN"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# Original dataframe\n", "data = {'datetime': ['2023-08-14', '2023-08-18', '2023-08-15', '2023-08-15'],\n", "        'che_liang_id': [7565, 7565, 43162, 43162],\n", "        'total_price': [81.0, 731.0, 180.0, 1170.0]}\n", "\n", "df = pd.DataFrame(data)\n", "df['datetime'] = pd.to_datetime(df['datetime'])\n", "\n", "# Generate a date range that covers the entire period\n", "date_range = pd.date_range(start=df['datetime'].min(), end=df['datetime'].max())\n", "\n", "# Create a new dataframe with all possible combinations of date and che_liang_id\n", "che_liang_ids = df['che_liang_id'].unique()\n", "expanded_data = []\n", "\n", "for date in date_range:\n", "    for che_liang_id in che_liang_ids:\n", "        expanded_data.append({'datetime': date, 'che_liang_id': che_liang_id, 'total_price': None})\n", "\n", "expanded_df = pd.DataFrame(expanded_data)\n", "\n", "# Merge the expanded dataframe with the original dataframe\n", "final_df = pd.merge(expanded_df, df, on=['datetime', 'che_liang_id'], how='left')\n", "\n", "# Sort the final dataframe\n", "final_df = final_df.sort_values(by=['che_liang_id', 'datetime'])\n", "\n", "final_df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>che_liang_id</th>\n", "      <th>sum_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-08-14</td>\n", "      <td>7565</td>\n", "      <td>81.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-08-18</td>\n", "      <td>7565</td>\n", "      <td>650.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-08-15</td>\n", "      <td>43162</td>\n", "      <td>1170.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    datetime  che_liang_id  sum_price\n", "0 2023-08-14          7565       81.0\n", "2 2023-08-18          7565      650.0\n", "1 2023-08-15         43162     1170.0"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# Sample data\n", "data = {\n", "    'datetime': ['2023-08-14', '2023-08-18', '2023-08-15', '2023-08-15'],\n", "    'che_liang_id': [7565, 7565, 43162, 43162],\n", "    'sum_price': [81.0, 650.0, 180.0, 990.0],\n", "    'ddd': ['c', 'd', 'd', 'r']\n", "}\n", "\n", "# Create a DataFrame\n", "df = pd.DataFrame(data)\n", "\n", "# Convert 'datetime' column to datetime format\n", "df['datetime'] = pd.to_datetime(df['datetime'])\n", "\n", "# Group by 'datetime' and 'che_liang_id', then sum the 'sum_price'\n", "grouped = df.groupby(['datetime', 'che_liang_id'])['sum_price'].sum().reset_index()\n", "# Group by 'datetime' and 'che_liang_id', then apply sum to 'sum_price' and 'che_liang_id', and use 'first' for other columns\n", "grouped = grouped.sort_values(by=['che_liang_id', 'datetime'])\n", "grouped"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>che_liang_id</th>\n", "      <th>total_price_x</th>\n", "      <th>sum_price</th>\n", "      <th>total_price_y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-08-14</td>\n", "      <td>7565</td>\n", "      <td>None</td>\n", "      <td>81.0</td>\n", "      <td>81.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-08-15</td>\n", "      <td>7565</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-08-16</td>\n", "      <td>7565</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-08-17</td>\n", "      <td>7565</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-08-18</td>\n", "      <td>7565</td>\n", "      <td>None</td>\n", "      <td>650.0</td>\n", "      <td>731.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-08-14</td>\n", "      <td>43162</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-08-15</td>\n", "      <td>43162</td>\n", "      <td>None</td>\n", "      <td>1170.0</td>\n", "      <td>1170.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-08-16</td>\n", "      <td>43162</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-08-17</td>\n", "      <td>43162</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-08-18</td>\n", "      <td>43162</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    datetime  che_liang_id total_price_x  sum_price  total_price_y\n", "0 2023-08-14          7565          None       81.0           81.0\n", "2 2023-08-15          7565          None        NaN            NaN\n", "4 2023-08-16          7565          None        NaN            NaN\n", "6 2023-08-17          7565          None        NaN            NaN\n", "8 2023-08-18          7565          None      650.0          731.0\n", "1 2023-08-14         43162          None        NaN            NaN\n", "3 2023-08-15         43162          None     1170.0         1170.0\n", "5 2023-08-16         43162          None        NaN            NaN\n", "7 2023-08-17         43162          None        NaN            NaN\n", "9 2023-08-18         43162          None        NaN            NaN"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# Original dataframe\n", "data = {'datetime': ['2023-08-14', '2023-08-18', '2023-08-15', '2023-08-15'],\n", "        'che_liang_id': [7565, 7565, 43162, 43162],\n", "        'sum_price': [81.0, 650.0, 180.0, 990.0]\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "df = df.groupby(['datetime', 'che_liang_id'])['sum_price'].sum().reset_index()\n", "df = df.sort_values(by=['che_liang_id', 'datetime'])\n", "df['total_price'] = df.groupby('che_liang_id')['sum_price'].cumsum()\n", "df\n", "\n", "df['datetime'] = pd.to_datetime(df['datetime'])\n", "# Generate a date range that covers the entire period\n", "date_range = pd.date_range(start=df['datetime'].min(), end=df['datetime'].max())\n", "\n", "# Create a new dataframe with all possible combinations of date and che_liang_id\n", "che_liang_ids = df['che_liang_id'].unique()\n", "expanded_data = []\n", "\n", "for date in date_range:\n", "    for che_liang_id in che_liang_ids:\n", "        expanded_data.append({'datetime': date, 'che_liang_id': che_liang_id, 'total_price': None})\n", "\n", "expanded_df = pd.DataFrame(expanded_data)\n", "\n", "# Merge the expanded dataframe with the original dataframe\n", "final_df = pd.merge(expanded_df, df, on=['datetime', 'che_liang_id'], how='left')\n", "\n", "# Sort the final dataframe\n", "final_df = final_df.sort_values(by=['che_liang_id', 'datetime'])\n", "\n", "final_df"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    datetime  che_liang_id  sum_price ddd\n", "0 2023-08-14          7565       81.0   c\n", "2 2023-08-18          7565      650.0   d\n", "1 2023-08-15         43162     1170.0   d\n"]}], "source": ["import pandas as pd\n", "\n", "# Sample data\n", "data = {\n", "    'datetime': ['2023-08-14', '2023-08-18', '2023-08-15', '2023-08-15'],\n", "    'che_liang_id': [7565, 7565, 43162, 43162],\n", "    'sum_price': [81.0, 650.0, 180.0, 990.0],\n", "    'ddd': ['c', 'd', 'd', 'r']\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "\n", "df['datetime'] = pd.to_datetime(df['datetime'])\n", "\n", "grouped = df.groupby(['datetime', 'che_liang_id']).agg({'sum_price': 'sum', 'ddd': 'first'}).reset_index()\n", "grouped = grouped.sort_values(by=['che_liang_id', 'datetime'])\n", "\n", "print(grouped)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}