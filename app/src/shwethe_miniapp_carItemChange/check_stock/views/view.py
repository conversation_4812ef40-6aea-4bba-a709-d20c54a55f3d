from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_miniapp_carItemChange.database import get_session
from src.shwethe_miniapp_carItemChange.check_stock.crud.crud import (
    getCarAtive100,
    MAddItem100,
    MUseItem100,
    delItem100,
    MSearchItem2100,
    sendToBigTable100,
    MGetStoreByProduct_id100,
    MUpdateStatus100,
    MListProductNotCheck100,
    MTodayProductCheck100,
    MUseItemCheckStock100,
    MCheckListPerDay100,
    checkStockTable100,
    detailCheckStockTable100,
    MgetListByGroupId100,
    MUpdateQty100,
    MCheckQtyWarehouse100,
    delMinio100,
    chart100
)
from src.shwethe_miniapp_carItemChange.src.models.models import check_stock, check_stock_post, item_select, item_select_post
 
from fastapi import APIRouter, Depends, Query, Body

router = APIRouter()


@router.get("/getCarAtive")
def getCarAtive(db: Session = Depends(get_session)):
    return getCarAtive100(db=db)

@router.post("/MAddItem")
def MAddItem(hero : item_select_post , db: Session = Depends(get_session)):
    return MAddItem100(hero=hero,db=db)
    
@router.delete("/delItem")
def delItem(jia_yi_id: int, db: Session = Depends(get_session)):
    return delItem100(jia_yi_id=jia_yi_id, db=db)

@router.post("/MUseItem")
def MUseItem(hero : List[check_stock_post] , db: Session = Depends(get_session)):
    return MUseItem100(hero=hero,db=db)

@router.post("/MUseItemCheckStock")
def MUseItemCheckStock(hero : List[check_stock_post] , db: Session = Depends(get_session)):
    return MUseItemCheckStock100(hero=hero,db=db)

@router.get("/MSearchItem2")
def MSearchItem2(getApiItem: str, db: Session = Depends(get_session)):
    return MSearchItem2100(getApiItem=getApiItem, db=db)

@router.get("/MGetStoreByProduct_id")
def MGetStoreByProduct_id(product_id: int, db: Session = Depends(get_session)):
    return MGetStoreByProduct_id100(product_id=product_id, db=db)

@router.put("/MUpdateStatus")
def MUpdateStatus(record_id: str, statusText: str, db: Session = Depends(get_session)):
    return MUpdateStatus100(record_id=record_id, statusText=statusText, db=db)

# @router.get("/MListProductNotCheck")
# def MListProductNotCheck(warehouse_id: int = 4612, db: Session = Depends(get_session)):
#     return MListProductNotCheck100(warehouse_id=warehouse_id, db=db)

@router.get("/MListProductNotCheck")
def MListProductNotCheck(db: Session = Depends(get_session)):
    return MListProductNotCheck100(db=db)

@router.get("/MTodayProductCheck")
def MTodayProductCheck(db: Session = Depends(get_session)):
    return MTodayProductCheck100(db=db)

@router.get("/MCheckListPerDay")
def MCheckListPerDay(db: Session = Depends(get_session)):
    return MCheckListPerDay100(db=db)

@router.get("/checkStockTable")
def checkStockTable(db: Session = Depends(get_session)):
    return checkStockTable100(db=db)

@router.get("/detailCheckStockTable")
def detailCheckStockTable(product_id: str, db: Session = Depends(get_session)):
    return detailCheckStockTable100(product_id=product_id, db=db)

@router.get("/MgetListByGroupId")
def MgetListByGroupId(group_id: str, db: Session = Depends(get_session)):
    return MgetListByGroupId100(group_id=group_id, db=db)

@router.put("/MUpdateQty")
def MUpdateQty(record_id: str, sub: dict, db: Session = Depends(get_session)):
    return MUpdateQty100(record_id=record_id, sub=sub, db=db)

@router.get("/MCheckQtyWarehouse")
def MCheckQtyWarehouse(db: Session = Depends(get_session)):
    return MCheckQtyWarehouse100(db=db)

@router.delete("/delMinio")
def delMinio(db: Session = Depends(get_session)):
    return delMinio100(db=db)

@router.post("/sendToBigTable")
def sendToBigTable(db: Session = Depends(get_session)):
    return sendToBigTable100(db=db)

@router.get("/chart")
def chart(db: Session = Depends(get_session)):
    return chart100(db=db)
