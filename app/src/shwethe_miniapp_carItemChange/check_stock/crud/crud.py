from sqlite3 import dbapi2
import requests
from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,and_
from typing import List, Optional

from datetime import datetime, timedelta, date
from src.shwethe_miniapp_carItemChange.database import get_session
from helper import generate_datetime_id
from src.shwethe_miniapp_carItemChange.src.models.models import check_stock, check_stock_post, item_insert, item_select, item_select_post, workshop_all_product, chart_item_use
import json
import pandas as pd
import calendar
from src.time_zone.time_zone_function import get_datetime, get_date, my_timezone
from helper import generate_id
from sqlalchemy import and_, or_, not_, func, literal_column, desc
from fastapi import APIRouter, Depends, Query, Body
# from sqlmodel.sql.expression import literal_column
from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api
import pytz
import os
import numpy as np



pd.set_option('display.max_columns', None)

# def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
#         """Convert a SQLModel objects into a pandas DataFrame."""
#         records = [i.dict() for i in objs]
#         df = pd.DataFrame.from_records(records)
#         return df


def dataframe(sqlModel, to_dict=False):
    records = [i.dict() for i in sqlModel]
    mergeDF = pd.DataFrame.from_records(records).fillna(0)
    if to_dict:
        mergeDF = mergeDF.to_dict("records")
    return mergeDF

# def getCarAtive100(db: Session = Depends(get_session)):
#     try:
#         heroesPersonal = db.exec(select(check_stock)).all()
#         records = [i.dict() for i in heroesPersonal]   
#         df = pd.DataFrame.from_records(records).fillna(0)

#         mergeDF = df.to_dict("records")
#     except:
#         mergeDF = []
#     return mergeDF

def getCarAtive100(db: Session = Depends(get_session)):
    sql = db.exec(select(workshop_all_product)).all()
    mergeDF = dataframe(sql, to_dict=True)
    return mergeDF


def MAddItem100(hero: item_select_post, db: Session = Depends(get_session)):
    hero_to_db = item_select.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)

    return hero_to_db


def delItem100(jia_yi_id: int, db: Session = Depends(get_session)):
    statement = select(item_select).where(item_select.jia_yi_id == jia_yi_id)
    results = db.exec(statement)
    heroes = results.all()  # Use .all() to retrieve all matching items

    for hero in heroes:
        db.delete(hero)

    db.commit()

    return heroes


def MUseItem100(hero: List[check_stock_post], db: Session = Depends(get_session)):

    print(hero)
    
    group_id = generate_id()

    for hero2 in hero:
        print(hero2)
        print(hero2.qty)
        if hero2.qty == 0:
            print("no 0")
        else:
            hero_to_db = check_stock.from_orm(hero2)
            hero_to_db.datetime = datetime.now(pytz.utc)
            hero_to_db.lei_a = 37
            hero_to_db.lei_b = 22
            hero_to_db.bi_zhi = 0
            hero_to_db.group_id = group_id
            db.add(hero_to_db)
            db.commit()
            db.refresh(hero_to_db)

    return hero


# def MUseItemCheckStock100(hero: List[check_stock_post], db: Session = Depends(get_session)):
    
#     print("ewrw[peoirwpoeripoweirpoweirpoweripwierpw]")
#     print(hero[0].product_id)
    
#     group_id = generate_id()
    

#     import datetime as DT
#     today = DT.date.today()
#     week_ago = today - DT.timedelta(days=7)
#     statement_titleData = select(check_stock).where(check_stock.datetime > week_ago, check_stock.status == 'waiting', check_stock.product_id == hero[0].product_id)
#     results_titleData = db.exec(statement_titleData).all()
#     print(results_titleData)
     

#     # if results_titleData is not None and results_titleData[0].product_id is not None:
#     if results_titleData is not None and len(results_titleData) > 0 and results_titleData[0].product_id is not None:
#         print('yessssssssssss')

#     else:
#         print('nooooooooooooo')
#         # Code to be executed if results_titleData does not have a value

#     for hero2 in hero:
#         print(hero2)
#         print(hero2.qty)

#         if hero2.qty != hero2.old_qty:
#             hero_to_db = check_stock.from_orm(hero2)
#             hero_to_db.datetime = datetime.now(pytz.utc)
#             hero_to_db.lei_a = 37
#             hero_to_db.lei_b = 22
#             hero_to_db.bi_zhi = 0
#             hero_to_db.che_liang_id = 0
#             hero_to_db.group_id = group_id
#             hero_to_db.status = 'waiting'
#             db.add(hero_to_db)
#             db.commit()
#             db.refresh(hero_to_db)
        
#         if hero2.qty == hero2.old_qty:
#             hero_to_db = check_stock.from_orm(hero2)
#             hero_to_db.datetime = datetime.now(pytz.utc)
#             hero_to_db.lei_a = 37
#             hero_to_db.lei_b = 22
#             hero_to_db.bi_zhi = 0
#             hero_to_db.che_liang_id = 0
#             hero_to_db.group_id = group_id
#             hero_to_db.status = 'success'
#             db.add(hero_to_db)
#             db.commit()
#             db.refresh(hero_to_db)

#     return hero


def MUseItemCheckStock100(hero: List[check_stock_post], db: Session = Depends(get_session)):

    group_id = generate_id()

    for hero2 in hero:
        print(hero2)
        print(hero2.qty)

        if hero2.qty != hero2.old_qty:
            hero_to_db = check_stock.from_orm(hero2)
            hero_to_db.datetime = datetime.now(pytz.utc)
            hero_to_db.lei_a = 37
            hero_to_db.lei_b = 22
            hero_to_db.bi_zhi = 0
            hero_to_db.che_liang_id = 0
            hero_to_db.group_id = group_id
            hero_to_db.status = 'waiting'
            db.add(hero_to_db)
            db.commit()
            db.refresh(hero_to_db)
        
        if hero2.qty == hero2.old_qty:
            hero_to_db = check_stock.from_orm(hero2)
            hero_to_db.datetime = datetime.now(pytz.utc)
            hero_to_db.lei_a = 37
            hero_to_db.lei_b = 22
            hero_to_db.bi_zhi = 0
            hero_to_db.che_liang_id = 0
            hero_to_db.group_id = group_id
            hero_to_db.status = 'success'
            db.add(hero_to_db)
            db.commit()
            db.refresh(hero_to_db)

    return hero


def MSearchItem2100(getApiItem: str, db: Session = Depends(get_session)):
    # print(getApiItem)
    # print(type(getApiItem))

    try:
        getApiItem20 = getApiItem
        # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v1/search/jia_yi_search_text?text={getApiItem20}'
        url = f'{mongodb_data_api}/api/v1/search/jia_yi_search_text?text={getApiItem20}'
        df3 = requests.get(url=url)
        df3 = df3.json()
        df3 = pd.DataFrame(df3).fillna(0)
        df3 = df3.to_dict('records')
        print('bbbbbbbbbbbbbbb', df3)

    except:
        df3 = []
    return df3


def MGetStoreByProduct_id100(product_id: int, db: Session = Depends(get_session)):
    try:
        # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/qty_h/{product_id}'
        url = f'{mongodb_data_api}/api/v2/search/qty_h/{product_id}'
        df = requests.get(url=url)
        df = df.json()
        df = pd.DataFrame(df).fillna(0)
        df = df[df['fen_dian'] == 4]

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = f'{mongodb_data_api}/api/v2/search/jia_yi_name_list_id'
        df1 = df
        df1['jia_yi_id'] = df1['jia_yi_id']
        df1 = df1[['jia_yi_id']]
        to_dict = df1.to_dict('records')
        body_raw = {"data_api": to_dict}
        warehouse_df = requests.get(url=url, json=body_raw).json()
        warehouse_df = pd.DataFrame(warehouse_df).fillna(0)
        warehouse_df = warehouse_df[warehouse_df['jia_yi_idname'].str.startswith('E')]
        # warehouse_df = warehouse_df.rename(columns={"jia_yi_id": "warehouse_id", "jia_yi_idname": "warehouse_idname", "jia_yi_mm_name": "warehouse_mm_name"})

        dataframeOutput = pd.merge(df, warehouse_df, left_on='jia_yi_id', right_on='jia_yi_id')
        dataframeOutput['lei'] = dataframeOutput['jia_yi_id']
        dataframeOutput['fen'] = dataframeOutput['fen_dian']
        dataframeOutput['jia_yi_id_x'] = dataframeOutput['jia_yi_id']
        dataframeOutput['jia_yi_id_y'] = dataframeOutput['jia_yi_id']
        dataframeOutput['jia_yi_idname'] = dataframeOutput['jia_yi_idname_x']
        dataframeOutput['jia_yi_mm_name'] = dataframeOutput['jia_yi_mm_name_x']

        dataframeOutput = dataframeOutput[['product_id', 'lei', 'fen', 'product_qty', 'type', 'jia_yi_id_x', 'jia_yi_id_y', 'jia_yi_idname', 'jia_yi_mm_name']]

        out = dataframeOutput.to_dict('records')

    except:
        out = []
    return out


def MUpdateStatus100(record_id: str, statusText: str, db: Session = Depends(get_session)):
    
    print(record_id)

    # UPDATE TO title_data
    statement_titleData = select(check_stock).where(check_stock.record_id == record_id)
    results_titleData = db.exec(statement_titleData).first()
    results_titleData.status = statusText
    db.add(results_titleData)  # 
    db.commit()  # 
    db.refresh(results_titleData)  #

    sendToBigTable100()

    return record_id


# def MListProductNotCheck100(warehouse_id: int, db: Session = Depends(get_session)):

#     def lastFillProduct():
#         heroesPersonal = db.exec(select(check_stock).order_by(check_stock.product_id, check_stock.datetime.desc()).distinct(check_stock.product_id)).all()        
#         records = [i.dict() for i in heroesPersonal]   
#         df = pd.DataFrame.from_records(records).fillna(0)
#         # df = df.to_dict('records')
#         return df
    
#     def warehouseProduct():
#         # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/warehouse_product/?warehouse_id={warehouse_id}'
#         url = f'{mongodb_data_api}/api/v2/search/warehouse_product/?warehouse_id={warehouse_id}'
#         df = requests.get(url=url)
#         df = df.json()
#         df = pd.DataFrame(df).fillna(0)

#         # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
#         url = f'{mongodb_data_api}/api/v2/search/product_list_id'
#         to_dict = df[['product_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getProduct = requests.get(url=url, json=body_raw).json()
#         getProduct = pd.DataFrame(getProduct)

#         dataframeOutput = pd.merge(df, getProduct, left_on='product_id', right_on='product_id').fillna(0)
#         # df = dataframeOutput.to_dict('records')
#         return dataframeOutput

#     last_fill_product_dataframe = lastFillProduct()
#     warehouse_product_dataframe = warehouseProduct()

#     merged_df = pd.merge(last_fill_product_dataframe, warehouse_product_dataframe, on='product_id', how='outer').fillna(0)

#     defineDay = 30
#     merged_df.loc[~merged_df['product_id'].isin(last_fill_product_dataframe['product_id']), 'item'] = 'new'
#     merged_df.loc[(~merged_df['item'].eq('new')) & (merged_df['datetime'].notna()), 'item'] = 'old'
#     merged_df['stock'] = len(warehouse_product_dataframe)
#     merged_df['defineDay'] = defineDay
#     merged_df['checkPerDay'] = (merged_df['stock'] / merged_df['defineDay']).round().astype(int)
#     merged_df['last_fill'] = merged_df['datetime'].apply(lambda x: x.strftime('%Y-%m-%d') if x != 0 else 0)
#     merged_df['today'] = pd.to_datetime('today').date().strftime('%Y-%m-%d')
#     merged_df['CheckDay'] = merged_df['last_fill'].apply(lambda x: (pd.to_datetime(x) + pd.DateOffset(days=defineDay)).strftime('%Y-%m-%d') if x != 0 else 0)
#     merged_df['dayWait'] = merged_df.apply(lambda row: (pd.to_datetime(row['today']) - pd.to_datetime(row['last_fill'])).days if row['last_fill'] != 0 else 0, axis=1)
#     merged_df['dayWait2'] = merged_df.apply(lambda row: (pd.to_datetime(row['CheckDay']) - pd.to_datetime(row['today'])).days if row['last_fill'] != 0 else 0, axis=1)
#     merged_df['status'] = merged_df['dayWait2'].apply(lambda x: True if x <= 0 else False)
#     merged_df = merged_df.sort_values(by=["item", "last_fill"], ascending=[True, True])
#     desired_output_dataframe = merged_df[['item', 'stock', 'defineDay', 'checkPerDay', 'product_id', 'product_idname', 'product_mm_name', 'product_d_name', 'last_fill', 'today', 'CheckDay', 'dayWait', 'dayWait2', 'status']]

#     out = desired_output_dataframe.to_dict('records')

#     return out


def MListProductNotCheck100(db: Session = Depends(get_session)):
    
    def lastFillProduct():
        heroesPersonal = db.exec(select(check_stock).order_by(check_stock.product_id, check_stock.datetime.desc()).distinct(check_stock.product_id)).all()        
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records).fillna(0)
        return df
    
    def warehouseProduct():
        warehouseId = ['793', '4612', '1081']
        list_product = []
        for id in warehouseId:
            url = f"{mongodb_data_api}/api/v2/search/warehouse_product/?warehouse_id={id}"
            response = requests.get(url)
            list_product.append(response.json())
        fusion_warehouse = []
        for inner_list in list_product:
            for item in inner_list:
                fusion_warehouse.append(item)

        df = pd.DataFrame(fusion_warehouse).fillna(0)
        df.drop_duplicates(subset=["product_id"], keep="first", inplace=True)

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
        url = f'{mongodb_data_api}/api/v2/search/product_list_id'
        to_dict = df[['product_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getProduct = requests.get(url=url, json=body_raw).json()
        getProduct = pd.DataFrame(getProduct)

        dataframeOutput = pd.merge(df, getProduct, left_on='product_id', right_on='product_id').fillna(0)

        return dataframeOutput

    last_fill_product_dataframe = lastFillProduct()
    warehouse_product_dataframe = warehouseProduct()

    merged_df = pd.merge(last_fill_product_dataframe, warehouse_product_dataframe, on='product_id', how='outer').fillna(0)

    defineDay = 60
    merged_df.loc[~merged_df['product_id'].isin(last_fill_product_dataframe['product_id']), 'item'] = 'new'
    merged_df.loc[(~merged_df['item'].eq('new')) & (merged_df['datetime'].notna()), 'item'] = 'old'
    merged_df['stock'] = len(warehouse_product_dataframe)
    merged_df['defineDay'] = defineDay
    merged_df['checkPerDay'] = (merged_df['stock'] / merged_df['defineDay']).round().astype(int)
    merged_df['last_fill'] = merged_df['datetime'].apply(lambda x: x.strftime('%Y-%m-%d') if x != 0 else 0)
    merged_df['today'] = pd.to_datetime('today').date().strftime('%Y-%m-%d')
    merged_df['CheckDay'] = merged_df['last_fill'].apply(lambda x: (pd.to_datetime(x) + pd.DateOffset(days=defineDay)).strftime('%Y-%m-%d') if x != 0 else 0)
    merged_df['dayWait'] = merged_df.apply(lambda row: (pd.to_datetime(row['today']) - pd.to_datetime(row['last_fill'])).days if row['last_fill'] != 0 else 0, axis=1)
    merged_df['dayWait2'] = merged_df.apply(lambda row: (pd.to_datetime(row['CheckDay']) - pd.to_datetime(row['today'])).days if row['last_fill'] != 0 else 0, axis=1)
    merged_df['status'] = merged_df['dayWait2'].apply(lambda x: True if x <= 0 else False)
    merged_df = merged_df.sort_values(by=["item", "last_fill"], ascending=[True, True])
    desired_output_dataframe = merged_df[['item', 'stock', 'defineDay', 'checkPerDay', 'product_id', 'product_idname', 'product_mm_name', 'product_d_name', 'last_fill', 'today', 'CheckDay', 'dayWait', 'dayWait2', 'status']]

    out = desired_output_dataframe.to_dict('records')

    return out


def MTodayProductCheck100(db: Session = Depends(get_session)):
    
    def lastFillProduct():
        import datetime as DT
        today = DT.date.today()
        week_ago = today - DT.timedelta(days=0)
        print(week_ago)
        try:
            # heroesPersonal = db.exec(select(check_stock).where(check_stock.datetime > week_ago).order_by(check_stock.product_id, check_stock.datetime.desc()).distinct(check_stock.product_id)).all()      
            heroesPersonal = db.exec(select(check_stock).where(check_stock.datetime > week_ago).order_by(check_stock.datetime.desc())).all()  
            records = [i.dict() for i in heroesPersonal]   
            df = pd.DataFrame.from_records(records).fillna(0)
            df['total_qty'] = df.groupby(['group_id', 'product_id'])['qty'].transform('sum')
            df = df.groupby('product_id').first().reset_index()

            # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
            url = f'{mongodb_data_api}/api/v2/search/product_list_id'
            to_dict = df[['product_id']].to_dict('records')
            body_raw = {"data_api": to_dict}
            getProduct = requests.get(url=url, json=body_raw).json()
            getProduct = pd.DataFrame(getProduct)

            # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
            url = f'{mongodb_data_api}/api/v2/search/jia_yi_name_list_id'
            changename = df.rename(columns={'warehouse_id': 'jia_yi_id'})
            to_dict = changename[['jia_yi_id']].to_dict('records')
            body_raw = {"data_api": to_dict}
            getWarehouse = requests.get(url=url, json=body_raw).json()
            getWarehouse = pd.DataFrame(getWarehouse)

            dataframeProduct = pd.merge(df, getProduct, left_on='product_id', right_on='product_id').fillna(0)
            dataframeOutput = pd.merge(dataframeProduct, getWarehouse, left_on='warehouse_id', right_on='jia_yi_id').fillna(0)
        except:
            dataframeOutput = []
        return dataframeOutput

    today_fill = lastFillProduct()
    all_warehouse_product = MListProductNotCheck100(db)
    all_warehouse_product = pd.DataFrame(all_warehouse_product)
    all_warehouse_product = all_warehouse_product.head(all_warehouse_product['checkPerDay'].iloc[0])

    try:
        concatenated_df = pd.concat([today_fill, all_warehouse_product], axis=0).fillna(0)
        concatenated_df['status'] = concatenated_df['status'].replace(0, True)
        # concatenated_df.loc[1, 'status'] = False
        concatenated_df = concatenated_df[concatenated_df['status'] != False]

        concatenated_df['record_count'] = concatenated_df['record_id'].apply(lambda x: len(x) if x else 0)
        concatenated_df['record_count'] = concatenated_df['record_count'].astype(bool).sum()
        last_nonzero_index = concatenated_df.loc[concatenated_df['record_count'].ne(0)].index[-1]
        concatenated_df = concatenated_df.iloc[:last_nonzero_index+1]


        out = concatenated_df.to_dict('records')
    except:
        # all_warehouse_product.loc[1, 'status'] = False
        all_warehouse_product = all_warehouse_product[all_warehouse_product['status'] != False]
        out = all_warehouse_product.to_dict('records')

    return out


# def MCheckListPerDay100(db: Session = Depends(get_session)):
    
#     def lastFillProduct():
#         import datetime as DT
#         today = DT.date.today()
#         week_ago = today - DT.timedelta(days=0)
#         print(week_ago)
#         try:
#             # heroesPersonal = db.exec(select(check_stock).where(check_stock.datetime > week_ago).order_by(check_stock.product_id, check_stock.datetime.desc()).distinct(check_stock.product_id)).all()      
#             heroesPersonal = db.exec(select(check_stock).where(check_stock.datetime > week_ago).order_by(check_stock.datetime.desc())).all()  
#             records = [i.dict() for i in heroesPersonal]   
#             df = pd.DataFrame.from_records(records).fillna(0)
#             df['total_qty'] = df.groupby(['group_id', 'product_id'])['qty'].transform('sum')
#             df = df.groupby('product_id').first().reset_index()

#             # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
#             url = f'{mongodb_data_api}/api/v2/search/product_list_id'
#             to_dict = df[['product_id']].to_dict('records')
#             body_raw = {"data_api": to_dict}
#             getProduct = requests.get(url=url, json=body_raw).json()
#             getProduct = pd.DataFrame(getProduct)

#             dataframeOutput = pd.merge(df, getProduct, left_on='product_id', right_on='product_id').fillna(0)
#         except:
#             dataframeOutput = []
#         return dataframeOutput

#     today_fill = lastFillProduct()
#     all_warehouse_product = MListProductNotCheck100(db)
#     all_warehouse_product = pd.DataFrame(all_warehouse_product)
#     all_warehouse_product = all_warehouse_product.head(all_warehouse_product['checkPerDay'].iloc[0])

#     try:
#         concatenated_df = pd.concat([today_fill, all_warehouse_product], axis=0).fillna(0)
#         concatenated_df = concatenated_df.head(all_warehouse_product['checkPerDay'].iloc[0])
#         out = concatenated_df.to_dict('records')
#     except:
#         out = all_warehouse_product.to_dict('records')

#     return out

def MCheckListPerDay100(db: Session = Depends(get_session)):
    
    def lastFillProduct():
        import datetime as DT
        today = DT.date.today()
        week_ago = today - DT.timedelta(days=0)
        print(week_ago)
        try:
            # heroesPersonal = db.exec(select(check_stock).where(check_stock.datetime > week_ago).order_by(check_stock.product_id, check_stock.datetime.desc()).distinct(check_stock.product_id)).all()      
            heroesPersonal = db.exec(select(check_stock).where(check_stock.datetime > week_ago).order_by(check_stock.datetime.desc())).all()  
            records = [i.dict() for i in heroesPersonal]   
            df = pd.DataFrame.from_records(records).fillna(0)
            df['total_qty'] = df.groupby(['group_id', 'product_id'])['qty'].transform('sum')
            df = df.groupby('product_id').first().reset_index()

            # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
            url = f'{mongodb_data_api}/api/v2/search/product_list_id'
            to_dict = df[['product_id']].to_dict('records')
            body_raw = {"data_api": to_dict}
            getProduct = requests.get(url=url, json=body_raw).json()
            getProduct = pd.DataFrame(getProduct)

            dataframeOutput = pd.merge(df, getProduct, left_on='product_id', right_on='product_id').fillna(0)
        except:
            dataframeOutput = []
        return dataframeOutput

    today_fill = lastFillProduct()
    print(len(today_fill))
    all_warehouse_product = MListProductNotCheck100(db)
    all_warehouse_product = pd.DataFrame(all_warehouse_product)
    showCheckToday = all_warehouse_product['checkPerDay'].iloc[0] - len(today_fill)
    print(showCheckToday)
    all_warehouse_product = all_warehouse_product.head(showCheckToday)

    out = all_warehouse_product.to_dict('records')

    return out


def checkStockTable100(db: Session = Depends(get_session)):
    # try:

    def lastFillProduct():
        import datetime as DT
        today = DT.date.today()
        week_ago = today - DT.timedelta(days=40)
        print(week_ago)
        try:  
            statuses = ['waiting', 'update', 'success', 'old']
            statuses20 = ['waiting', 'update', 'success']
            heroesPersonal = db.exec(select(check_stock).where(check_stock.status.in_(statuses), check_stock.datetime > week_ago).order_by(check_stock.datetime.desc())).all() 
            records = [i.dict() for i in heroesPersonal]   
            df = pd.DataFrame.from_records(records).fillna(0)
            df['total_qty'] = df[df['status'].isin(statuses20)].groupby(['group_id', 'product_id'])['qty'].transform('sum')
            df['total_old_qty'] = df[df['status'].isin(statuses20)].groupby(['group_id', 'product_id'])['old_qty'].transform('sum')
            df['total_amount_in_de'] = df[df['status'].isin(statuses20)].groupby(['group_id', 'product_id'])['amount_in_de'].transform('sum')
            df['count_warehouse'] = df[df['status'].isin(statuses20)].groupby(['group_id', 'product_id'])['product_id'].transform('count')
            df['count_record'] = df.groupby(['product_id'])['record_id'].transform('count')
            df['status_product'] = df[df['status'].isin(statuses20)].groupby(['group_id', 'product_id'])['status'].transform(lambda x: 'waiting' if set(x) == {'waiting'}
                                                   else 'update' if set(x) == {'update'}
                                                   else 'success' if set(x) == {'success'}
                                                   else 'update' if {'waiting', 'update'}.issubset(set(x))
                                                   else 'waiting' if {'waiting', 'success'}.issubset(set(x))
                                                   else 'update' if {'update', 'success'}.issubset(set(x))
                                                   else 'update')
            df = df.groupby('product_id').first().reset_index()

            # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
            url = f'{mongodb_data_api}/api/v2/search/product_list_id'
            to_dict = df[['product_id']].to_dict('records')
            body_raw = {"data_api": to_dict}
            getProduct = requests.get(url=url, json=body_raw).json()
            getProduct = pd.DataFrame(getProduct)

            dataframeOutput = pd.merge(df, getProduct, left_on='product_id', right_on='product_id').fillna(0)
        except:
            dataframeOutput = []
        return dataframeOutput

    today_fill = lastFillProduct()
    all_warehouse_product = MListProductNotCheck100(db)
    all_warehouse_product = pd.DataFrame(all_warehouse_product)
    # all_warehouse_product = all_warehouse_product.head(all_warehouse_product['checkPerDay'].iloc[0])

    try:
        concatenated_df = pd.concat([today_fill, all_warehouse_product], axis=0).fillna(0)
        out = concatenated_df.to_dict('records')
    except:
        out = all_warehouse_product.to_dict('records')

    # out = today_fill.to_dict('records')

    # except:
    #     mergeDF = []
    return out


# def checkStockTable100(db: Session = Depends(get_session)):

#     all_warehouse_product = MListProductNotCheck100(db)
#     all_warehouse_product = pd.DataFrame(all_warehouse_product)

#     out = all_warehouse_product.to_dict('records')

#     bbb = []
#     for row in out:
#         url = f"http://192.168.1.11:8200/mongodb_data_api/api/v2/search/qty_h/{int(row['product_id'])}"
#         # print(url)
#         response = requests.get(url)
#         # print(response.json())    
#         bbb.append(response.json())

#     return bbb


def detailCheckStockTable100(product_id: int, db: Session = Depends(get_session)):
    # try:

    def lastFillProduct():
        try:  
            heroesPersonal = db.exec(select(check_stock).where(check_stock.product_id == product_id).order_by(check_stock.datetime.desc())).all()  
            records = [i.dict() for i in heroesPersonal]   
            df = pd.DataFrame.from_records(records).fillna(0)

            # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
            url = f'{mongodb_data_api}/api/v2/search/product_list_id'
            to_dict = df[['product_id']].to_dict('records')
            body_raw = {"data_api": to_dict}
            getProduct = requests.get(url=url, json=body_raw).json()
            getProduct = pd.DataFrame(getProduct)

            # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
            url = f'{mongodb_data_api}/api/v2/search/jia_yi_name_list_id'
            changename = df.rename(columns={'warehouse_id': 'jia_yi_id'})
            to_dict = changename[['jia_yi_id']].to_dict('records')
            body_raw = {"data_api": to_dict}
            getWarehouse = requests.get(url=url, json=body_raw).json()
            getWarehouse = pd.DataFrame(getWarehouse)
            print(getWarehouse)

            dataframeProduct = pd.merge(df, getProduct, left_on='product_id', right_on='product_id').fillna(0)
            dataframeOutput = pd.merge(dataframeProduct, getWarehouse, left_on='warehouse_id', right_on='jia_yi_id').fillna(0)
        except:
            dataframeOutput = []
        return dataframeOutput

    today_fill = lastFillProduct()
    out = today_fill.to_dict('records')

    # except:
    #     mergeDF = []
    return out


def MgetListByGroupId100(group_id: str, db: Session = Depends(get_session)):
    # try:

    def lastFillProduct():
        try:  
            heroesPersonal = db.exec(select(check_stock).where(check_stock.group_id == group_id).order_by(check_stock.datetime.desc())).all()  
            records = [i.dict() for i in heroesPersonal]   
            df = pd.DataFrame.from_records(records).fillna(0)

            # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
            url = f'{mongodb_data_api}/api/v2/search/product_list_id'
            to_dict = df[['product_id']].to_dict('records')
            body_raw = {"data_api": to_dict}
            getProduct = requests.get(url=url, json=body_raw).json()
            getProduct = pd.DataFrame(getProduct)

            # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
            url = f'{mongodb_data_api}/api/v2/search/jia_yi_name_list_id'
            changename = df.rename(columns={'warehouse_id': 'jia_yi_id'})
            to_dict = changename[['jia_yi_id']].to_dict('records')
            body_raw = {"data_api": to_dict}
            getWarehouse = requests.get(url=url, json=body_raw).json()
            getWarehouse = pd.DataFrame(getWarehouse)
            print(getWarehouse)

            dataframeProduct = pd.merge(df, getProduct, left_on='product_id', right_on='product_id').fillna(0)
            dataframeOutput = pd.merge(dataframeProduct, getWarehouse, left_on='warehouse_id', right_on='jia_yi_id').fillna(0)
        except:
            dataframeOutput = []
        return dataframeOutput

    today_fill = lastFillProduct()
    out = today_fill.to_dict('records')

    # except:
    #     mergeDF = []
    return out


def MUpdateQty100(record_id: str, sub: dict, db: Session = Depends(get_session)):
    
    # group_id = generate_id()

    # UPDATE TO by record_id
    statement_titleData = select(check_stock).where(check_stock.record_id == record_id)
    results_titleData = db.exec(statement_titleData).first()
    results_titleData.status = "old"
    db.add(results_titleData)  
    db.commit()  
    db.refresh(results_titleData) 

    # GET PRODUCT PRICE
    url = f'{mongodb_data_api}/api/v2/search/qty_h/{results_titleData.product_id}'
    response = requests.get(url).json()
    filtered_response = [item for item in response if item['jia_yi_id'] == results_titleData.warehouse_id]
    qty_now = filtered_response[0]['product_qty']
    print(filtered_response)
 
    # COPY ABOVE record_id DATA AND INSERT NEW ROW WITH NEW QTY
    new_entry = check_stock(
        datetime = datetime.now(pytz.utc),
        warehouse_id = results_titleData.warehouse_id,
        che_liang_id = results_titleData.che_liang_id,
        jia_yi_fang_a = results_titleData.jia_yi_fang_a,
        jia_yi_fang_b=  results_titleData.jia_yi_fang_b,
        lei_a = results_titleData.lei_a,
        lei_b = results_titleData.lei_b,
        product_id = results_titleData.product_id,
        qty = sub['qty'],
        price = results_titleData.price,
        bi_zhi = results_titleData.bi_zhi,
        group_id = results_titleData.group_id,
        old_qty = qty_now,
        amount_in_de = sub['qty'] - qty_now,
        pd_image = sub['pd_image'],
        status = "success" if sub['qty'] - qty_now == 0 else "update",
        sub = sub['sub']
    )
    db.add(new_entry)  
    db.commit()
    db.refresh(new_entry)

    return record_id


def MCheckQtyWarehouse100(db: Session = Depends(get_session)):
    
    try:
        import datetime as DT
        today = DT.date.today()
        week_ago = today - DT.timedelta(days=7)
        statuses = ['waiting', 'update']
        # heroesPersonal = db.exec(select(check_stock).where(check_stock.status.in_(statuses)).order_by(check_stock.product_id, check_stock.datetime.desc()).distinct(check_stock.product_id)).all()
        heroesPersonal = db.exec(select(check_stock).where(check_stock.status.in_(statuses), check_stock.datetime > week_ago).order_by(check_stock.datetime.desc())).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records).fillna(0)
        df = df.groupby(['product_id', 'warehouse_id']).first().reset_index()


        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        df1 = df
        df1['jia_yi_id'] = df1['warehouse_id']
        df1 = df1[['jia_yi_id']]
        to_dict = df1.to_dict('records')
        body_raw = {"data_api": to_dict}
        warehouse_df = requests.get(url=url, json=body_raw).json()
        warehouse_df = pd.DataFrame(warehouse_df)
        warehouse_df = warehouse_df.rename(columns={"jia_yi_id": "warehouse_id", "jia_yi_idname": "warehouse_idname", "jia_yi_mm_name": "warehouse_mm_name"})

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
        url = mongodb_data_api + '/api/v2/search/product_list_id'
        df3 = df
        df3 = df3[['product_id']]
        to_dict = df3.to_dict('records')
        body_raw = {"data_api": to_dict}
        product_id_df = requests.get(url=url, json=body_raw).json()
        product_id_df = pd.DataFrame(product_id_df)

        mergeDF = pd.merge(df, warehouse_df, on="warehouse_id", how="left")
        mergeDF = pd.merge(mergeDF, product_id_df, on="product_id", how="left")
        # mergeDF = mergeDF.sort_values(by='datetime', ascending=False)

        mergeDF = mergeDF.to_dict("records")
    except:
        mergeDF = []

    return mergeDF


# def delMinio100(db: Session = Depends(get_session)):

#     def getImageList():
#         try:
#             import datetime as DT
#             today = DT.date.today()
#             week_ago = today - DT.timedelta(days=90)
#             heroesPersonal = db.exec(select(check_stock).where(check_stock.datetime > week_ago)).all()
#             records = [i.dict() for i in heroesPersonal]   
#             df = pd.DataFrame.from_records(records).fillna(0)
#             df = df['pd_image'].tolist()
#             flat_list = [item for sublist in df if isinstance(sublist, list) for item in sublist]
#         except:
#             flat_list = []
#         return flat_list
    
#     def deleteImage(getImageList):
#         try:
#             oneItem = getImageList[0]
#             backet = oneItem.split("/")[-3]
#             folder_path = oneItem.split("/")[-2]
#             print(backet, folder_path)

#             getFileName = [os.path.basename(filename) for filename in getImageList]

#             url = f'http://192.168.1.11:8091/shwethe_file_path/api/v1/file_path/multiDelPath/{backet}/{folder_path}'
#             data = getFileName

#             response = requests.delete(url, json=data)

#             if response.status_code == 200:
#                 print("Request successful!")
#             else:
#                 print("Request failed with status code:", response.status_code)

#             ccc = response.json()
#         except:
#             ccc = "Error can't get data from database"
#         return ccc
    
#     getImageList = getImageList()
#     deleteImage = deleteImage(getImageList)

#     return deleteImage


def delMinio100(db: Session = Depends(get_session)):
    
    def getImageList():
        try:
            import datetime as DT
            today = DT.date.today()
            week_ago = today - DT.timedelta(days=90)
            heroesPersonal = db.exec(select(check_stock).where(check_stock.datetime > week_ago)).all()
            records = [i.dict() for i in heroesPersonal]   
            df = pd.DataFrame.from_records(records).fillna(0)
            df = df['pd_image'].tolist()
            flat_list = [item for sublist in df if isinstance(sublist, list) for item in sublist]
        except:
            flat_list = []
        return flat_list
    
    # def deleteImage(getImageList):
    #     try:
    #         oneItem = getImageList[0]
    #         backet = oneItem.split("/")[-3]
    #         folder_path = oneItem.split("/")[-2]
    #         print(backet, folder_path)

    #         getFileName = [os.path.basename(filename) for filename in getImageList]

    #         url = f'http://192.168.1.11:8091/shwethe_file_path/api/v1/file_path/multiDelPath/{backet}/{folder_path}'
    #         print(url)
    #         data = getFileName

    #         response = requests.delete(url, json=data)

    #         if response.status_code == 200:
    #             print("Request successful!")
    #         else:
    #             print("Request failed with status code:", response.status_code)

    #         ccc = response.json()
    #     except:
    #         ccc = "Error can't get data from database"
    #     return ccc
    
    getImageList = getImageList()
    # deleteImage = deleteImage(getImageList)
    return getImageList


def sendToBigTable100(db: Session = Depends(get_session)):
    # try:
    from src.Connect.postgresql_nern import postgresql_shwethe_carItemChange
    import pandas as pd


    # A10001 = pd.read_sql(""" select * from item_insert WHERE datetime >= NOW() - INTERVAL '2 days' """, postgresql_shwethe_carItemChange)
    A10001 = pd.read_sql("""
                            SELECT *
                            FROM item_insert
                            WHERE datetime >= NOW() - INTERVAL '4 days' 
                            AND status = 'success'
                        """, postgresql_shwethe_carItemChange)
    json_nor = A10001.to_dict("records")


    # INSERT TO ARTER DATABASE
    aaa = []
    for ioo in json_nor:
        d = {
            'a_id': ioo['auto_id'],
            'b_id': ioo['auto_id'],
            'product_id': ioo['product_id'],
            'product_qty': ioo['qty'],
            'product_price_a' : ioo['price'],
            'product_price_b' : ioo['price'],
            'ke_bian': 0,
            'jia_yi_fang_a': ioo['jia_yi_fang_a'],
            'jia_yi_fang_b': ioo['jia_yi_fang_b'],
            'lei_a': ioo['lei_a'],
            'lei_b': ioo['lei_b'],
            'bu_bian': 1005,
            'jin_huo_bian': 0,   
            'jin_huo_dang': ioo['record_id'],   
            'ci_bian': 0,
            'u_id': 0,
            'shu_riqi_datetime': str(ioo['datetime']),
            'riqi_datetime': str(ioo['datetime']),
            'che_liang': ioo['che_liang_id'],
            'kind': 11234,
            'product_qtyp': 0,
            'product_qtyn': 0,
            'bi_zhi': ioo['bi_zhi']
            }
        aaa.append(d)
    df1 = pd.DataFrame(aaa) 
    mata = {
    'data' : df1.to_dict(orient='records')
    }
        
    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v2/table/mysql', data= json.dumps(mata))
    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v1/table/mysql_big_table', data= json.dumps(mata))
    url = f'{shwethe_mysql_api}/api/v1/table/mysql_big_table'
    dd = requests.post(url, data= json.dumps(mata))
    out = dd.status_code
    print(out)

    return out


# def chart100(db: Session = Depends(get_session)):
    
#     def getCheckStockReport():
#         # # Use a context manager to execute the query
#         # with postgresql_shwethe_carItemChange:
#         #     A10001 = pd.read_sql("""
#         #         SELECT *
#         #         FROM item_insert 
#         #         WHERE datetime > (CURRENT_DATE - INTERVAL '30 days')
#         #         AND status = 'success'
#         #     """, postgresql_shwethe_carItemChange)
#         #     A10001 = A10001[["datetime", "che_liang_id", "product_id", "qty", "price", "used_type"]]
#         #     A10001['datetime'] = pd.to_datetime(A10001['datetime'])  # Convert 'datetime' column to datetime type
#         #     A10001 = A10001.sort_values(by=['che_liang_id', 'datetime'])

   
#         import datetime as DT
#         today = DT.date.today()
#         week_ago = today - DT.timedelta(days=10)
#         heroesPersonal = db.exec(select(item_insert).where(item_insert.datetime > week_ago, item_insert.status == 'success')).all()
#         A10001 = dataframe(heroesPersonal, to_dict=False)
#         A10001['price'] = A10001['price'].astype(float)
#         A10001 = A10001[["datetime", "che_liang_id", "product_id", "qty", "price", "used_type"]]

#         A10001['datetime'] = pd.to_datetime(A10001['datetime'])
#         A10001['datetime'] = A10001['datetime'].dt.strftime('%Y-%m-%d')
#         A10001.sort_values(by=['che_liang_id', 'datetime'], inplace=True)
#         A10001['sum_price'] = A10001['qty'] * A10001['price']
#         A10001 = A10001.groupby(['datetime', 'che_liang_id'])['sum_price'].sum().reset_index()
#         A10001 = A10001.sort_values(by=['che_liang_id', 'datetime'])
#         A10001['total_price'] = A10001.groupby('che_liang_id')['sum_price'].cumsum()
#         return A10001
    
#     def add_missing_date():
#         df = getCheckStockReport()

#         df['datetime'] = pd.to_datetime(df['datetime'])
#         # Generate a date range that covers the entire period
#         date_range = pd.date_range(start=df['datetime'].min(), end=df['datetime'].max())

#         # Create a new dataframe with all possible combinations of date and che_liang_id
#         che_liang_ids = df['che_liang_id'].unique()
#         expanded_data = []

#         for date in date_range:
#             for che_liang_id in che_liang_ids:
#                 expanded_data.append({'datetime': date, 'che_liang_id': che_liang_id})

#         expanded_df = pd.DataFrame(expanded_data)

#         # Merge the expanded dataframe with the original dataframe
#         final_df = pd.merge(expanded_df, df, on=['datetime', 'che_liang_id'], how='left')

#         # Sort the final dataframe
#         final_df = final_df.sort_values(by=['che_liang_id', 'datetime'])
#         final_df['datetime'] = final_df['datetime'].dt.strftime('%Y-%m-%d')

#         # final_df.replace(np.nan, None, inplace=True)
#         # final_df.replace(float('nan'), None, inplace=True)
#         # final_df = final_df.applymap(lambda x: None if pd.isna(x) else x)
#         # final_df['sum_price'] = final_df['sum_price'].replace(np.nan, None, inplace=True)
#         # final_df['total_price'] = final_df['total_price'].replace(np.nan, None, inplace=True)
    
#         return final_df
        
        
#     def getName():
#         fgetCheckStockReport = add_missing_date()

#         url = f'{mongodb_data_api}/api/v2/search/jia_yi_name_list_id'
#         changename = fgetCheckStockReport.rename(columns={'che_liang_id': 'jia_yi_id'})
#         to_dict = changename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         output = getCar, fgetCheckStockReport
#         return output


#     def fusion():
#         getCar, fgetCheckStockReport = getName()
#         mergeCar = fgetCheckStockReport.merge(getCar, left_on='che_liang_id', right_on='jia_yi_id', how='left')
#         df = mergeCar.applymap(lambda x: None if pd.isna(x) or x == '' else x)
#         df = df[['datetime', 'che_liang_id', 'jia_yi_idname', 'sum_price', 'total_price']]

#         replacement_map = {col: {np.nan: None} for col in ['sum_price', 'total_price']}
#         df.replace(replacement_map, inplace=True)
#         return df


#     df = fusion()
#     # Group by 'jia_yi_idname' and aggregate 'total_price'
#     grouped = df.groupby('jia_yi_idname')['total_price'].apply(list).reset_index(name='data')

#     # Create the series for the JSON format
#     series = []
#     for index, row in grouped.iterrows():
#         series_data = {
#             'name': row['jia_yi_idname'],
#             'data': row['data'],
#             'type': 'line',
#             'smooth': True,
#             # 'showSymbol': False,
#             # 'symbol': 'circle',
#             'symbolSize': 5,
#             'emphasis': {
#                 'focus': 'series',
#                 'itemStyle': {
#                     'borderWidth': 2,
#                     'borderColor': '#E0E3FF',
#                 },
#             },
#         }
#         series.append(series_data)

#     sorted_indices = sorted(range(len(df['datetime'])), key=lambda k: df['datetime'][k])
#     sorted_datetime = [df['datetime'][i] for i in sorted_indices]
#     sorted_datetime = list(dict.fromkeys(sorted_datetime))

#     # Prepare the final JSON structure
#     json_data = {
#         'datetime': sorted_datetime,
#         'series': series
#     }
#     print(json_data)

#     # Convert dictionary to JSON string
#     # json_string = json.dumps(json_data, indent=4)

#     print(df)
    
#     # df = df.to_dict(orient='records')
#     return json_data


# def chart100(db: Session = Depends(get_session)):

#     import psycopg2
#     from psycopg2.extras import Json

#     # Assuming you have the connection details
#     postgresql_shwethe_carItemChange = psycopg2.connect(
#         host="************",
#         database="shwethe_carItemChange",
#         user="postgres",
#         password="0818822095",
#         port="5436"
#     )
    
#     def getCheckStockReport():

#         with postgresql_shwethe_carItemChange:
#             A10001 = pd.read_sql("""
#                 SELECT *
#                 FROM item_insert 
#                 WHERE datetime > (CURRENT_DATE - INTERVAL '365 days')
#                 AND status = 'success'
#             """, postgresql_shwethe_carItemChange)
#             A10001['price'] = A10001['price'].astype(float)
#             A10001 = A10001[["datetime", "che_liang_id", "product_id", "qty", "price", "used_type"]]

#         A10001['datetime'] = pd.to_datetime(A10001['datetime'])
#         A10001['datetime'] = A10001['datetime'].dt.strftime('%Y-%m-%d')
#         A10001.sort_values(by=['che_liang_id', 'datetime'], inplace=True)
#         A10001['sum_price'] = A10001['qty'] * A10001['price']
#         A10001 = A10001.groupby(['datetime', 'che_liang_id'])['sum_price'].sum().reset_index()
#         A10001 = A10001.sort_values(by=['che_liang_id', 'datetime'])
#         A10001['total_price'] = A10001.groupby('che_liang_id')['sum_price'].cumsum()
#         return A10001
    
#     def add_missing_date():
#         df = getCheckStockReport()

#         df['datetime'] = pd.to_datetime(df['datetime'])
#         # Generate a date range that covers the entire period
#         date_range = pd.date_range(start=df['datetime'].min(), end=df['datetime'].max())

#         # Create a new dataframe with all possible combinations of date and che_liang_id
#         che_liang_ids = df['che_liang_id'].unique()
#         expanded_data = []

#         for date in date_range:
#             for che_liang_id in che_liang_ids:
#                 expanded_data.append({'datetime': date, 'che_liang_id': che_liang_id})

#         expanded_df = pd.DataFrame(expanded_data)

#         # Merge the expanded dataframe with the original dataframe
#         final_df = pd.merge(expanded_df, df, on=['datetime', 'che_liang_id'], how='left')

#         # Sort the final dataframe
#         final_df = final_df.sort_values(by=['che_liang_id', 'datetime'])
#         final_df['datetime'] = final_df['datetime'].dt.strftime('%Y-%m-%d')

#         return final_df
        
        
#     def getName():
#         fgetCheckStockReport = add_missing_date()

#         # url = f'{mongodb_data_api}/api/v2/search/jia_yi_name_list_id'
#         url = f'http://pv-api.shwethe.com/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         changename = fgetCheckStockReport.rename(columns={'che_liang_id': 'jia_yi_id'})
#         to_dict = changename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         output = getCar, fgetCheckStockReport
#         return output


#     def fusion():
#         getCar, fgetCheckStockReport = getName()
#         mergeCar = fgetCheckStockReport.merge(getCar, left_on='che_liang_id', right_on='jia_yi_id', how='left')
#         df = mergeCar.applymap(lambda x: None if pd.isna(x) or x == '' else x)
#         df = df[['datetime', 'che_liang_id', 'jia_yi_idname', 'sum_price', 'total_price']]

#         replacement_map = {col: {np.nan: None} for col in ['sum_price', 'total_price']}
#         df.replace(replacement_map, inplace=True)
#         return df

#     def result():
#         df = fusion()
#         # print(df)
#         # Group by 'jia_yi_idname' and aggregate 'total_price'
#         grouped = df.groupby('jia_yi_idname')['total_price'].apply(list).reset_index(name='data')

#         # Create the series for the JSON format
#         series = []
#         for index, row in grouped.iterrows():
#             series_data = {
#                 'name': row['jia_yi_idname'],
#                 'data': row['data'],
#                 'type': 'line',
#                 'smooth': True,
#                 # 'showSymbol': False,
#                 # 'symbol': 'circle',
#                 'symbolSize': 5,
#                 'emphasis': {
#                     'focus': 'series',
#                     'itemStyle': {
#                         'borderWidth': 2,
#                         'borderColor': '#E0E3FF',
#                     },
#                 },
#             }
#             series.append(series_data)

#         sorted_indices = sorted(range(len(df['datetime'])), key=lambda k: df['datetime'][k])
#         sorted_datetime = [df['datetime'][i] for i in sorted_indices]
#         sorted_datetime = list(dict.fromkeys(sorted_datetime))

#         # Prepare the final JSON structure
#         json_data = {
#             'datetime': sorted_datetime,
#             'series': series
#         }
#         print(json_data)
#         return json_data
    
#     def airflow():
#         # Sample JSON data
#         detail_data = {
#                         "datetime": [
#                             "2023-10-19",
#                             "2023-10-20",
#                             "2023-10-21",
#                             "2023-10-22",
#                             "2023-10-23",
#                             "2023-10-24",
#                             "2023-10-25"
#                         ],
#                         "series": [
#                             {
#                             "name": "FT69",
#                             "data": [
#                                 None,
#                                 None,
#                                 None,
#                                 590,
#                                 None,
#                                 None,
#                                 None
#                             ],
#                             "type": "line",
#                             "smooth": True,
#                             "symbolSize": 5,
#                             "emphasis": {
#                                 "focus": "series",
#                                 "itemStyle": {
#                                 "borderWidth": 2,
#                                 "borderColor": "#E0E3FF"
#                                 }
#                             }
#                             }
#                         ]
#                      }
                     
        
#         # detail_data = result()


#         # SQL query to insert data into the "expirevitus" table
#         insert_query = """
#             INSERT INTO chart_item_use (auto_id, type, period, json, datetime)
#             VALUES (DEFAULT, %s, %s, %s, %s);
#         """

#         # Use a cursor to execute the insert query
#         with postgresql_shwethe_carItemChange.cursor() as cursor:
#             # Convert the detail data to JSONB format
#             detail_jsonb = Json(detail_data)

#             # Get the current timestamp for the "datetime" column
#             current_datetime = datetime.now()

#             # Execute the insert query with the JSONB data and current timestamp
#             cursor.execute(insert_query, ('item_change_chart', 10, detail_jsonb, current_datetime))

#             # Commit the changes to the database
#             postgresql_shwethe_carItemChange.commit()
#         # print("55555555555555555555555555555555555555555555")
#         # print(detail_data)
#         return detail_data
    

#     json_data = airflow()

#     # print(df)
    
#     # json_data = df.to_dict(orient='recor  ds')
#     return json_data


def chart100(db: Session = Depends(get_session)):
    
    def getCheckStockReport():
        # import datetime as DT
        # today = DT.date.today()
        heroesPersonal = db.exec(select(chart_item_use).where(chart_item_use.type == 'item_change_chart').order_by(chart_item_use.type, chart_item_use.datetime.desc()).distinct(chart_item_use.type)).all()        
        # A10001 = dataframe(heroesPersonal, to_dict=False)
        return heroesPersonal

    json_data = getCheckStockReport()

    # print(json_data)
    
    # df = df.to_dict(orient='records')
    return json_data[0].detail_json