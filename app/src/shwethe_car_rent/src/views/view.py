from fastapi import APIRouter, Depends, Query
from typing import Optional
from pydantic.types import List
from sqlmodel import Session
from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_car_rent.database import get_session
from src.shwethe_car_rent.src.crud.crud import (
    get_jia_yi100,
    # receive_deposit_worker_insert100,
    car_rent_insert_data,
    get_car_rent_data,
    car_rent_update100,
    get_descript_option_data,
    delete_descript_option,
    insert_descript_option,
    login100,
    update_car_rent_status_complete,
    update_car_rent_receive_info,
    sendToBigTable100
)
from src.shwethe_car_rent.src.models.models import(
    receive_deposit_worker_post,
    car_rent_insert_post,
    car_rent_update_post,
    descript_option_post,
    car_rent_receive_info_post
)
router = APIRouter()


@router.get("/getCarAtive")
def getCarAtive(db: Session = Depends(get_session)):
    return "getCarAtive100()"


@router.get("/get_jia_yi/{item}")
def get_jia_yi(item: str, db: Session = Depends(get_session)):
    return get_jia_yi100(item=item,db=db)


# @router.post("/receive_deposit_worker_insert")
# def receive_deposit_worker_insert(hero: receive_deposit_worker_post, db: Session = Depends(get_session)):
#     return receive_deposit_worker_insert100(hero=hero,db=db)

@router.post("/car_rent_insert")
def car_rent_insert(data: car_rent_insert_post, db: Session = Depends(get_session)):
    return car_rent_insert_data(data=data, db=db)

@router.get("/car_rent_data")
def get_car_rent(db: Session = Depends(get_session), fen: Optional[int] = None):
    """
    Get car_rent_insert data from the last 30 days, sorted by date in descending order (newest first)

    Query parameters:
    - fen: Optional[int] - Filter results by fen value

    Returns:
    - List of car_rent_insert records from the last 30 days, sorted by datetime in descending order (newest first)
    """
    return get_car_rent_data(db=db, fen=fen)

@router.put("/car_rent_update/{record_id}")
def car_rent_update(record_id: str, data: car_rent_update_post, db: Session = Depends(get_session)):
    return car_rent_update100(record_id=record_id, data=data, db=db)

# Endpoints for descript_option table

@router.get("/descript_option")
def get_descript_option(db: Session = Depends(get_session), fen: Optional[int] = None):
    """
    Get all descript_option data sorted by date in descending order (newest first) with optional fen parameter

    Query parameters:
    - fen: Optional[int] - Filter results by fen value

    Returns:
    - List of descript_option records sorted by datetime in descending order (newest first)
    """
    return get_descript_option_data(db=db, fen=fen)

@router.delete("/descript_option")
def delete_descript_option_endpoint(auto_id: int, db: Session = Depends(get_session)):
    """Delete descript_option data by auto_id"""
    return delete_descript_option(auto_id=auto_id, db=db)

@router.post("/descript_option")
def insert_descript_option_endpoint(data: descript_option_post, db: Session = Depends(get_session)):
    """Insert new data into descript_option table"""
    return insert_descript_option(data=data, db=db)


@router.post("/login/{item}")
def login(item: str, fen: int, db: Session = Depends(get_session)):
    """Login API that requires both item (jia_yi_id) and fen parameters"""
    return login100(item=item, fen=fen, db=db)


@router.put("/car_rent_complete/{record_id}")
def complete_car_rent(record_id: str, db: Session = Depends(get_session)):
    """Update car_rent_insert status to 'complete' by record_id"""
    return update_car_rent_status_complete(record_id=record_id, db=db)


@router.put("/receive_info/{record_id}")
def receive_info(record_id: str, data: car_rent_receive_info_post, db: Session = Depends(get_session)):
    """
    Update car_rent_insert record with receive info

    Path parameter:
    - record_id: str - The record_id of the car_rent_insert record to update

    Required fields in request body:
    - jia_yi_fang_a: int
    - jia_yi_fang_b: int
    - deposit_worker: str (max length 9)
    - receiver_worker: str (max length 16)
    - deposit_store: str (max length 50)

    This will also set:
    - lei_a to 'L' (ASCII 24)
    - lei_b to 'P' (ASCII 32)

    The API will format the worker data as "jia_yi_id | jia_yi_idname | jia_yi_mm_name"
    before storing it in the database.
    """
    return update_car_rent_receive_info(record_id=record_id, data=data, db=db)



@router.post("/sendToBigTable")
def sendToBigTable(db: Session = Depends(get_session)):
    return sendToBigTable100(db=db)