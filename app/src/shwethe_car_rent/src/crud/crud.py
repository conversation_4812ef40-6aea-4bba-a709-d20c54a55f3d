from sqlite3 import dbapi2
import requests
from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,and_, join
from sqlmodel import select, func, and_, distinct, extract
from typing import List, Optional
from sqlalchemy.orm import joinedload
from datetime import datetime, date
from sqlalchemy.sql import func
import datetime
from sqlalchemy.sql import cast
from sqlalchemy import Date
from src.shwethe_car_rent.database import get_session
from helper import generate_datetime_id
from src.shwethe_car_rent.src.models.models import (
receive_deposit_worker, receive_deposit_worker_post,
car_rent_insert, car_rent_insert_post, car_rent_update_post,
descript_option, descript_option_post, car_rent_receive_info_post
)
from sqlalchemy import text
import json
import pandas as pd
import urllib.request
import cv2
import numpy as np
from src.time_zone.time_zone_function import get_datetime
from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api
from src.Connect.postgresql_nern import real_backend_api, petrol_api
from sqlalchemy import and_, or_, not_
from pandas import json_normalize
from pprint import pprint

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel items into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

def dataframe(sqlModel, to_dict=False):
    records = [i.dict() for i in sqlModel]
    mergeDF = pd.DataFrame.from_records(records).fillna(0)
    if to_dict:
        mergeDF = mergeDF.to_dict("records")
    return mergeDF


def get_jia_yi100(item: str, db: Session = Depends(get_session)):
    # print(item)
    # print(type(item))

    try:
        item = int(item)
        print(item)
        print(type(item))

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        # url = Arter_api + 'jia_yi_name_list_id'
        # body_raw = {"data_api": [{"jia_yi_id": 36557}]}
        body_raw = {"data_api": [{"jia_yi_id": item}]}
        # body_raw = {"data_api": df}
        df2 = requests.get(url=url, json=body_raw)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')

        print(df2)

        # print(df2)
    except:
        print('The provided value is not an integer')
        print(item)
        print(type(item))

        # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={item}'
        url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={item}'
        df2 = requests.get(url=url)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')

        print(df2)
        # print(df2)
    return df2


# def receive_deposit_worker_insert100(hero: receive_deposit_worker_post, db: Session = Depends(get_session)):

#     hero_to_db = receive_deposit_worker.from_orm(hero)
#     hero_to_db.datetime = get_datetime()
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)

#     return hero_to_db

def car_rent_insert_data(data: car_rent_insert_post, db: Session = Depends(get_session)):
    data_to_db = car_rent_insert.from_orm(data)
    data_to_db.datetime = get_datetime()
    # data_to_db.record_id = generate_datetime_id()
    db.add(data_to_db)
    db.commit()
    db.refresh(data_to_db)

    # Using pprint for prettier output
    print("\n=== Car Rent Data ===")
    pprint(data.dict(), indent=2, width=120)
    print("==================\n")
    return data


def get_car_rent_data(db: Session = Depends(get_session), fen: Optional[int] = None):
    # Calculate the date 30 days ago from today
    thirty_days_ago = datetime.datetime.now() - datetime.timedelta(days=30)

    # Create a query to select records from car_rent_insert table from the last 30 days
    # and order by datetime in descending order
    statement = select(car_rent_insert).where(
        car_rent_insert.datetime >= thirty_days_ago
    ).order_by(car_rent_insert.datetime.desc())

    # Filter by fen if provided
    if fen is not None:
        statement = statement.where(car_rent_insert.fen == fen)

    # Execute the query and return all results
    results = db.exec(statement).all()
    return results


def car_rent_update100(record_id: str, data: car_rent_update_post, db: Session = Depends(get_session)):
    # Find the record by record_id
    statement = select(car_rent_insert).where(car_rent_insert.record_id == record_id)
    result = db.exec(statement).first()

    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Car rent record with id {record_id} not found"
        )

    # Update only the status and detail_json fields
    result.price = data.detail_json['price']

    if data.status is not None:
        result.status = data.status

    if data.detail_json is not None:
        result.detail_json = data.detail_json

    # Commit the changes
    db.commit()
    db.refresh(result)

    # Using pprint for prettier output
    print("\n=== Updated Car Rent Data ===")
    pprint({
        "record_id": result.record_id,
        "status": result.status,
        "detail_json": result.detail_json
    }, indent=2, width=120)
    print("==================\n")

    return result


# CRUD operations for descript_option table

def get_descript_option_data(db: Session = Depends(get_session), fen: Optional[int] = None):
    # Create a query to select all records from descript_option table
    statement = select(descript_option).order_by(descript_option.datetime.desc())

    # Filter by fen if provided
    if fen is not None:
        statement = statement.where(descript_option.fen == fen)

    # Execute the query and return all results
    results = db.exec(statement).all()
    return results


def delete_descript_option(auto_id: int, db: Session = Depends(get_session)):
    # Find the record by auto_id
    statement = select(descript_option).where(descript_option.auto_id == auto_id)
    result = db.exec(statement).first()

    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Descript option with id {auto_id} not found"
        )

    # Delete the record
    db.delete(result)
    db.commit()

    return {"message": f"Descript option with id {auto_id} deleted successfully"}


def insert_descript_option(data: descript_option_post, db: Session = Depends(get_session)):
    # Create a new descript_option record
    new_record = descript_option(
        datetime=get_datetime(),
        fen=data.fen,
        descript=data.descript
    )

    # Add the record to the database
    db.add(new_record)
    db.commit()
    db.refresh(new_record)

    return new_record


def login100(item: str, fen: int, db: Session = Depends(get_session)):
    # Get item details from jia_yi API
    item_detail = get_jia_yi100(item=item, db=db)

    # Check if we got valid data
    if item_detail and len(item_detail) > 0:
        try:
            # Extract jia_yi_id from the response
            jia_yi_id = item_detail[0].get('jia_yi_id')

            # Check if a record with this jia_yi_id already exists
            existing_record = db.exec(
                select(receive_deposit_worker).where(
                    receive_deposit_worker.jia_yi_id == jia_yi_id
                )
            ).first()

            if existing_record:
                # Record already exists, just return a message
                print(f"\n=== User with jia_yi_id: {jia_yi_id} already exists ===")
                print(f"Existing record found with auto_id: {existing_record.auto_id}")
                print("==================\n")

                # Add a message to the item_detail
                item_detail[0]['message'] = f"User already exists with auto_id: {existing_record.auto_id}"
            else:
                # Create a new record in receive_deposit_worker table
                new_worker = receive_deposit_worker(
                    datetime=get_datetime(),
                    jia_yi_id=jia_yi_id,
                    fen=fen,  # Use the provided fen parameter
                    type_worker="receive"  # Default value as requested
                )

                # Add the record to the database
                db.add(new_worker)
                db.commit()
                db.refresh(new_worker)

                print(f"\n=== Login Successful for jia_yi_id: {jia_yi_id}, fen: {fen} ===")
                print(f"Worker record created with auto_id: {new_worker.auto_id}")
                print("==================\n")

                # Add a message to the item_detail
                item_detail[0]['message'] = f"New user created with auto_id: {new_worker.auto_id}"

                # Add fen to the response for clarity
                item_detail[0]['fen'] = fen
        except Exception as e:
            print(f"Error processing login: {str(e)}")
            # Continue even if there's an error, as we still want to return the item_detail
            if item_detail and len(item_detail) > 0:
                item_detail[0]['message'] = f"Error: {str(e)}"

    return item_detail


def update_car_rent_status_complete(record_id: str, db: Session = Depends(get_session)):
    """
    Update the status field of a car_rent_insert record to 'complete' based on record_id

    Args:
        record_id: The record_id of the car_rent_insert record to update
        db: Database session

    Returns:
        The updated record or an error message if the record is not found
    """
    # Find the record by record_id
    statement = select(car_rent_insert).where(car_rent_insert.record_id == record_id)
    result = db.exec(statement).first()

    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Car rent record with id {record_id} not found"
        )

    # Update the status field to 'complete'
    result.status = "complete"

    # Commit the changes
    db.commit()
    db.refresh(result)

    # Using pprint for prettier output
    print(f"\n=== Updated Car Rent Status to 'complete' for record_id: {record_id} ===")
    pprint({
        "record_id": result.record_id,
        "status": result.status
    }, indent=2, width=120)
    print("==================\n")

    return result


def update_car_rent_receive_info(record_id: str, data: car_rent_receive_info_post, db: Session = Depends(get_session)):
    """
    Update car_rent_insert record with receive info

    Args:
        record_id: The record_id of the car_rent_insert record to update
        data: The data containing jia_yi_fang_a, jia_yi_fang_b, deposit_worker, receiver_worker, and deposit_store
        db: Database session

    Returns:
        The updated record or an error message if the record is not found
    """
    # Find the record by record_id
    statement = select(car_rent_insert).where(car_rent_insert.record_id == record_id)
    result = db.exec(statement).first()

    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Car rent record with id {record_id} not found"
        )

    # Update the fields
    result.jia_yi_fang_a = data.jia_yi_fang_a
    result.jia_yi_fang_b = data.jia_yi_fang_b
    result.lei_a = 24  # ASCII value for 'L'
    result.lei_b = 32  # ASCII value for 'P'
    result.deposit_worker = data.deposit_worker
    result.receiver_worker = data.receiver_worker
    result.deposit_store = data.deposit_store

    # Commit the changes
    db.commit()
    db.refresh(result)

    # Using pprint for prettier output
    print(f"\n=== Updated Car Rent Receive Info for record_id: {record_id} ===")
    pprint({
        "record_id": result.record_id,
        "jia_yi_fang_a": result.jia_yi_fang_a,
        "jia_yi_fang_b": result.jia_yi_fang_b,
        "lei_a": result.lei_a,
        "lei_b": result.lei_b,
        "deposit_worker": result.deposit_worker,
        "receiver_worker": result.receiver_worker,
        "deposit_store": result.deposit_store
    }, indent=2, width=120)
    print("==================\n")

    return result


def sendToBigTable100(db: Session = Depends(get_session)):
    # try:
    from src.Connect.postgresql_nern import postgresql_shwethe_car_rent
    import pandas as pd


    # A10001 = pd.read_sql(""" select * from item_insert WHERE datetime >= NOW() - INTERVAL '2 days' """, postgresql_shwethe_car_rent)
    A10001 = pd.read_sql("""
                            SELECT *
                            FROM car_rent_insert
                            WHERE datetime >= NOW() - INTERVAL '4 days' 
                            AND status = 'complete'
                        """, postgresql_shwethe_car_rent)
    json_nor = A10001.to_dict("records")


    # INSERT TO ARTER DATABASE
    aaa = []
    for ioo in json_nor:
        d = {
            'a_id': ioo['auto_id'],
            'b_id': ioo['auto_id'],
            'product_id': 47775,
            'product_qty': -1,
            'product_price_a' : ioo['price'],
            'product_price_b' : ioo['price'],
            'ke_bian': ioo['jia_yi_fang_a'],
            'jia_yi_fang_a': ioo['jia_yi_fang_a'],
            'jia_yi_fang_b': ioo['jia_yi_fang_b'],
            'lei_a': ioo['lei_a'],
            'lei_b': ioo['lei_b'],
            'bu_bian': 1005,
            'jin_huo_bian': 0,   
            'jin_huo_dang': ioo['record_id'],   
            'ci_bian': 0,
            'u_id': 0,
            'shu_riqi_datetime': str(ioo['datetime']),
            'riqi_datetime': str(ioo['datetime']),
            'che_liang': ioo['detail_json']['jia_yi_id_vehicle'],
            'kind': 11237,
            'product_qtyp': 0,
            'product_qtyn': 0,
            'bi_zhi': ioo['bi_zhi']
            }
        aaa.append(d)
    df1 = pd.DataFrame(aaa) 
    mata = {
    'data' : df1.to_dict(orient='records')
    }
        
    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v2/table/mysql', data= json.dumps(mata))
    url = shwethe_mysql_api + '/api/v1/table/mysql_big_table'
    dd = requests.post(url, data= json.dumps(mata))
    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v1/table/mysql_big_table', data= json.dumps(mata))
    out = dd.status_code
    print(out)

    return out