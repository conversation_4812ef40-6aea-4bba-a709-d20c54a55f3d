from typing import Dict
from xmlrpc.client import DateTime
from click import option
from pydantic.types import Optional, List
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel, Column, JSON, TEXT, text
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id


class car_rent_insert(SQLModel, table=True):
    __tablename__ = "car_rent_insert"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    datetime: Optional[datetime]
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'carrent'::text || lpad(nextval('carrent_sequence'::regclass)::text, 6, '0'::text)")))
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    fen: Optional[int]
    deposit_worker: Optional[str] = Field(max_length=9)
    receiver_worker: Optional[str] = Field(max_length=16)
    deposit_store: Optional[str] = Field(max_length=50)
    price: Optional[float]
    bi_zhi: Optional[int]
    status: Optional[str]
    detail_json: dict = Field(sa_column=Column(JSONB), default={})
class car_rent_insert_post(BaseModel):
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int]
    lei_a: Optional[int]
    lei_b: Optional[int]
    fen: Optional[int]
    deposit_worker: Optional[str]
    receiver_worker: Optional[str]
    deposit_store: Optional[str]
    price: Optional[float]
    bi_zhi: Optional[int]
    status: Optional[str]
    detail_json: dict = Field(sa_column=Column(JSONB), default={})
class car_rent_update_post(BaseModel):
    price: Optional[int]
    status: Optional[str] = "update"
    detail_json: dict = Field(default={})

class car_rent_receive_info_post(BaseModel):
    jia_yi_fang_a: int
    jia_yi_fang_b: int
    deposit_worker: str
    receiver_worker: str
    deposit_store: str




class descript_option(SQLModel, table=True):
    __tablename__ = "descript_option"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    datetime: Optional[datetime]
    fen: Optional[int]
    descript: Optional[str]

class descript_option_post(BaseModel):
    fen: Optional[int]
    descript: str


class receive_deposit_worker(SQLModel, table=True):
    __tablename__ = "receive_deposit_worker"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    datetime: Optional[datetime]
    fen: Optional[int]
    jia_yi_id: Optional[int]
    type_worker: Optional[str] = Field(max_length=20)
class receive_deposit_worker_post(BaseModel):
    datetime: Optional[datetime]
    fen: Optional[int]
    jia_yi_id: Optional[int]
    type_worker: Optional[str] = Field(max_length=20)

