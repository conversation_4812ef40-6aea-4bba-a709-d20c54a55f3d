from dateutil.parser import parse
from typing import Optional
from pydantic import BaseModel, validator
from datetime import datetime
import json


class pos_head_post(BaseModel):
    ke_bian: int 
    status_code: Optional[int] = 300 
    jia_yi_fang: Optional[int] = 0
    create_datetime: Optional[datetime] = None
    order_id : int 


# ----------------------------------------------------   
class color_device_tb_read(BaseModel):
    auto_id: Optional[int]
    device_id: Optional[int]
    device_fen_dian: Optional[int]
    type_device_id: Optional[str]

class color_device_tb_post(BaseModel):
    device_id: Optional[int]
    device_fen_dian: Optional[int]
    type_device_id: Optional[str]

# ----------------------------------------------------
class type_device_tb_read(BaseModel):
    auto_id: Optional[int]
    name: Optional[str]
    brand_id: Optional[int]

class type_device_tb_post(BaseModel):
    name: Optional[str]
    brand_id: Optional[int]

# ----------------------------------------------------
class color_cai_liao_tb_read(BaseModel):
    auto_id: Optional[int]
    cai_liao_id: Optional[int]
    cai_liao_qty: Optional[float]
    qty_lei: Optional[str]
    color_base_id: Optional[int]

class color_cai_liao_tb_post(BaseModel):
    cai_liao_id: Optional[int]
    cai_liao_qty: Optional[float]
    qty_lei: Optional[str]
    color_base_id: Optional[int]

# -------------------------------------------------------
class color_insert_tb_post(BaseModel):
    # datetime: Optional[int] = Field(default=..., title="cai_liao_id")
    # record_id: Optional[str]
    group_id: Optional[str]
    jia_yi_fang_a: Optional[int]
    jia_yi_fang_b: Optional[int] 
    lei_a: Optional[int] 
    lei_b: Optional[int] 
    device_id: Optional[int] 
    product_id: Optional[int] 
    product_qty: Optional[int] 
    product_price: Optional[float] 
    bi_zhi: Optional[int] 
    fen: Optional[int] 

