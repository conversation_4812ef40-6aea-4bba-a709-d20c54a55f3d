from datetime import datetime
from typing import Optional, Dict
from sqlmodel import SQLModel, <PERSON>
from sqlalchemy import Column, Integer, JSON
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import TIMESTAMP, JSONB
from sqlalchemy import TEXT
from sqlalchemy.sql.expression import table, text




class cement_insert(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    # datetime: Optional[int] = Field(default=..., title="cai_liao_id")
    datetime: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="datetime"
    )  
    # record_id: Optional[str]
    record_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'COLOROA'::text || lpad(nextval('color_insertoa_sequence'::regclass)::text, 8, '0'::text)")))
    group_id: Optional[str] = Field(default=..., title="group_id")
    jia_yi_fang_a: Optional[int] = Field(default=..., title="jia_yi_fang_a")
    jia_yi_fang_b: Optional[int] = Field(default=..., title="jia_yi_fang_b")
    lei_a: Optional[int] = Field(default=..., title="lei_a")
    lei_b: Optional[int] = Field(default=..., title="lei_b")
    machine_id: Optional[int] = Field(default=..., title="machine_id")
    product_id: Optional[int] = Field(default=..., title="product_id")
    product_all_qty: Optional[float] = Field(default=..., title="product_all_qty")
    product_price: Optional[float] = Field(default=..., title="product_price")
    bi_zhi: Optional[int] = Field(default=..., title="bi_zhi")
    fen: Optional[int] = Field(default=..., title="fen")
    detail_json: Optional[Dict] = Field(default={}, sa_column=Column(JSON))
    status: Optional[int] = Field(default=..., title="status")
    sum_price: Optional[float] = Field(default=..., title="sum_price")
    sum_all_price: Optional[float] = Field(default=..., title="sum_all_price")
    price_a: Optional[float] = Field(default=..., title="price_a")
    price_b: Optional[float] = Field(default=..., title="price_b")
    user_id: Optional[int] = Field(default=..., title="user_id")
    

class cement_product_produce(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    # machine_id: Optional[int] = Field(default=..., title="machine_id")
    product_id: Optional[int] = Field(default=..., title="product_id")
    product_name: Optional[str] = Field(default=..., title="product_name")


class cement_product_mix_ratio(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    product_id: Optional[int] = Field(default=..., title="product_id")
    material_id: Optional[str] = Field(default=..., title="material_id")


class cement_machine(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    machine_id: Optional[int] = Field(default=..., title="machine_id")
    machine: Optional[str] = Field(default=..., title="machine")
    fen: Optional[int] = Field(default=..., title="fen")


class employee_name(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    employee_id: Optional[int] = Field(default=..., title="employee_id")
    employee_show: Optional[str] = Field(default=..., title="employee_show")
    fen: Optional[int] = Field(default=..., title="fen")
    datetime: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="datetime"
    )  
    status: Optional[bool] = Field(default=..., title="status")


class employee_insert(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    employee_id: Optional[int] = Field(default=..., title="employee_id")
    employee_show: Optional[str] = Field(default=..., title="employee_show")
    fen: Optional[int] = Field(default=..., title="fen")
    datetime: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="datetime"
    )  
    jia_yi_fang_a: Optional[int] = Field(default=..., title="jia_yi_fang_a")
    jia_yi_fang_b: Optional[int] = Field(default=..., title="jia_yi_fang_b")
    lei_a: Optional[int] = Field(default=..., title="lei_a")
    lei_b: Optional[int] = Field(default=..., title="lei_b")