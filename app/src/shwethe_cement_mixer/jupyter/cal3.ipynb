{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# import sys\n", "# # sys.path.append('../../../../../app/')\n", "# sys.path.append('../../../../app/')\n", "# import requests\n", "# from sqlmodel import Session, select, SQLModel\n", "# # from src.shwethe_color.database import get_session\n", "# from src.config.shwethe_color.database import get_session\n", "# from src.shwethe_color.models.model import color_insert_tb, color_insert_oa_tb\n", "# import json\n", "# import pandas as pd\n", "# import numpy as np\n", "# from sqlalchemy import and_\n", "# from contextlib import contextmanager\n", "# from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "# import logging\n", "# from datetime import datetime, timedelta, date\n", "# import pytz\n", "# from collections import defaultdict\n", "# from sqlmodel import update\n", "# from sqlalchemy import distinct\n", "\n", "\n", "\n", "# @contextmanager\n", "# def get_session_dependency():\n", "#     session = next(get_session())\n", "#     try:\n", "#         yield session\n", "#     finally:\n", "#         session.close()\n", "\n", "# def dataframe(sqlModel, to_dict=False):\n", "#     records = [i.dict() for i in sqlModel]\n", "#     mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "#     if to_dict:\n", "#         mergeDF = mergeDF.to_dict(\"records\")\n", "#     return mergeDF\n", "\n", "# def get_color_insert_tb():\n", "#     with get_session_dependency() as db:\n", "#         import datetime as DT\n", "#         today = DT.date.today()\n", "#         # GET TODAY DATA\n", "#         week_ago = today - DT.<PERSON><PERSON>(days=1)\n", "\n", "#         # First, create a subquery for SuccessGroups\n", "#         success_groups_subquery = select(color_insert_tb.group_id).where(\n", "#             color_insert_tb.status[(\"status_big_table\")].as_string() == \"success\"\n", "#         ).subquery()\n", "#         # Now, create the main query\n", "#         query = select(color_insert_tb).where(\n", "#             color_insert_tb.group_id.in_(success_groups_subquery),\n", "#             color_insert_tb.datetime >= week_ago\n", "#         ).order_by(color_insert_tb.datetime.desc())\n", "#         # Execute the query\n", "#         heroesPersonal = db.exec(query).all()\n", "\n", "#         mergeDF = dataframe(heroesPersonal, to_dict=False)\n", "#         # mergeDF = mergeDF[['datetime', 'fen', 'device_id', 'group_id', 'product_id', 'product_qty', 'product_qty_sum']]\n", "#         mergeDF = mergeDF[['datetime', 'jia_yi_fang_a', 'jia_yi_fang_b', 'lei_a', 'fen', 'group_id', 'device_id', 'product_id', 'product_qty_sum']]\n", "#         # mergeDF = mergeDF.head(60)\n", "#         # mergeDF = mergeDF[60:120]\n", "#     return mergeDF\n", "\n", "\n", "# def calOne():\n", "#     df = get_color_insert_tb()\n", "\n", "#     # Create a combination key of jia_yi_fang_a and group_id where lei_a is 31\n", "#     code_combination = df[df['lei_a'] == 31][['jia_yi_fang_a', 'group_id']]\n", "#     code_combination['key'] = code_combination['jia_yi_fang_a'].astype(str) + '-' + code_combination['group_id']\n", "#     code_set = set(code_combination['key'])\n", "#     # Assign 'type_lei' based on conditions\n", "#     df['type_lei'] = 'oa'  # Default value\n", "#     df.loc[df['lei_a'] == 31, 'type_lei'] = 'code'  # Set 'code' where lei_a is 31\n", "#     # Set 'base' where combination of jia_yi_fang_a and group_id matches the code_set and lei_a is not 31\n", "#     df['combination_key'] = df['jia_yi_fang_a'].astype(str) + '-' + df['group_id']\n", "#     df.loc[df['combination_key'].isin(code_set) & (df['lei_a'] != 31), 'type_lei'] = 'base'\n", "#     # Drop the temporary combination_key column\n", "#     df.drop('combination_key', axis=1, inplace=True)\n", "\n", "#     # Filter rows where 'type_lei' is either 'code' or 'base'\n", "#     df = df[df['type_lei'].isin(['oa'])]\n", "\n", "#     df['type'] = 'mix'\n", "\n", "#     df = df[['datetime', 'fen', 'type', 'type_lei', 'group_id', 'device_id', 'product_id', 'product_qty_sum']]\n", "\n", "#     df['product_qty_sum'] = -df['product_qty_sum']\n", "\n", "#     return df\n", "\n", "\n", "# def get_color_insert_oa_tb():\n", "#     with get_session_dependency() as db:\n", "#         import datetime as DT\n", "#         today = DT.date.today()\n", "#         # GET TODAY DATA\n", "#         week_ago = today - DT.<PERSON><PERSON>(days=2)\n", "\n", "#         # First, create a subquery for SuccessGroups\n", "#         success_groups_subquery = select(color_insert_oa_tb.group_id).where(\n", "#             color_insert_oa_tb.status[(\"status_big_table\")].as_string() == \"success\"\n", "#         ).subquery()\n", "#         # Now, create the main query\n", "#         query = select(color_insert_oa_tb).where(\n", "#             color_insert_oa_tb.group_id.in_(success_groups_subquery),\n", "#             color_insert_oa_tb.datetime >= week_ago\n", "#         ).order_by(color_insert_oa_tb.datetime.desc())\n", "#         # Execute the query\n", "#         heroesPersonal = db.exec(query).all()\n", "\n", "#         mergeDF = dataframe(heroesPersonal, to_dict=False)\n", "\n", "#         mergeDF = mergeDF[mergeDF['product_qty'].isin([-1000])]\n", "#         mergeDF = mergeDF[['datetime', 'fen', 'group_id', 'device_id', 'product_id']]\n", "#         mergeDF['type'] = 'add'\n", "#         mergeDF['product_qty_sum'] = 1000\n", "#         mergeDF = mergeDF[['datetime', 'fen', 'type', 'group_id', 'device_id', 'product_id', 'product_qty_sum']]\n", "\n", "#     return mergeDF\n", "\n", "\n", "# def joinDF():\n", "#     color_insert = calOne()\n", "#     color_insert_oa = get_color_insert_oa_tb()\n", "\n", "#     # Concatenate the two DataFrames\n", "#     concatenated_df = pd.concat([color_insert, color_insert_oa], ignore_index=True)\n", "\n", "#     # Convert the 'datetime_column' to datetime format if it's not already\n", "#     concatenated_df['datetime'] = pd.to_datetime(concatenated_df['datetime'])\n", "\n", "#     # Sort the DataFrame in descending order based on the 'datetime_column'\n", "#     df = concatenated_df.sort_values(by='datetime', ascending=False)\n", "\n", "#     # # Initialize a dictionary to store the balances for each product_id\n", "#     # balances = {}\n", "\n", "#     # # Iterate over the rows starting from the tail\n", "#     # for index, row in df[::-1].iterrows():\n", "#     #     product_id = row['product_id']\n", "#     #     product_qty_sum = row['product_qty_sum']\n", "#     #     transaction_type = row['type']\n", "\n", "#     #     # balance = \"\" + str(product_id) + \", \" + str(product_qty_sum) + \", \" + transaction_type\n", "\n", "#     #     # if product_id not in balances:\n", "#     #     #     # A. assign default product_qty_sum value if this product_id has no previous row with the same product_id\n", "#     #     #     balance = product_qty_sum\n", "#     #     # else:\n", "#     #     #     previous_balance = balances[product_id]\n", "#     #     #     balance = previous_balance\n", "\n", "#     #     if product_id not in balances:\n", "#     #         # A. assign default product_qty_sum value if this product_id has no previous row with the same product_id\n", "#     #         balance = product_qty_sum\n", "#     #     else:\n", "#     #         previous_balance = balances[product_id]\n", "\n", "#     #     #     if product_id not in balances:\n", "#     #     #     # A. assign default product_qty_sum value if this product_id has no previous row with the same product_id\n", "#     #     #         balance = product_qty_sum\n", "#     #     #     else:\n", "#     #     #         previous_balance = balances[product_id]\n", "\n", "#     #     #         if transaction_type == 'add':\n", "#     #     #             if previous_transaction_type == 'mix':\n", "#     #     #                 # B. if this product_id type add has previous row type mix with the same product_id, it will (product_qty_sum - balance)\n", "#     #     #                 balance = previous_balance - product_qty_sum\n", "#     #     #             else:\n", "#     #     #                 # E. if this product_id type add has previous row type add with the same product_id, it will (product_qty_sum + balance)\n", "#     #     #                 balance = previous_balance + product_qty_sum\n", "#     #     #         else:  # transaction_type == 'mix'\n", "#     #     #             if previous_transaction_type == 'add':\n", "#     #     #                 # C. if this product_id type mix has previous row type add with the same product_id, it will (- product_qty_sum + balance)\n", "#     #     #                 balance = previous_balance - product_qty_sum\n", "#     #     #             else:  # previous_transaction_type == 'mix'\n", "#     #     #                 # D. if this product_id type mix has previous row type mix with the same product_id, it will (- product_qty_sum - balance)\n", "#     #     #                 balance = previous_balance - product_qty_sum\n", "\n", "\n", "#     #         if transaction_type == 'add' and previous_transaction_type == 'mix':\n", "#     #             # balance = \"product_qty_sum - balance\"\n", "#     #             # balance = product_qty_sum - previous_balance\n", "#     #             balance = transaction_type + '|' + previous_transaction_type\n", "#     #         if transaction_type == 'add' and previous_transaction_type == 'add':\n", "#     #             # balance = \"(product_qty_sum + balance)\"\n", "#     #             # balance = product_qty_sum + previous_balance\n", "#     #             balance = transaction_type + '|' + previous_transaction_type\n", "#     #         if transaction_type == 'mix' and previous_transaction_type == 'mix':\n", "#     #             # balance = \"(- product_qty_sum - balance)\"\n", "#     #             # balance = - product_qty_sum - previous_balance\n", "#     #             balance = transaction_type + '|' + previous_transaction_type\n", "#     #         if transaction_type == 'mix' and previous_transaction_type == 'add':\n", "#     #             # balance = \"(- product_qty_sum + balance)\"\n", "#     #             # balance = - product_qty_sum + previous_balance\n", "#     #             balance = transaction_type + '|' + previous_transaction_type\n", "#     #             # balance = \"\" + str(product_id) + \", \" + str(product_qty_sum) + \", \" + str(previous_balance)\n", "\n", "#     #         # if product_id not in balances:\n", "#     #         # # A. assign default product_qty_sum value if this product_id has no previous row with the same product_id\n", "#     #         #     balance = product_qty_sum\n", "#     #         # else:\n", "#     #         #     previous_balance = balances[product_id]\n", "\n", "#     #         #     if transaction_type == 'add':\n", "#     #         #         # E. if this product_id type add has previous row type add with the same product_id, it will (product_qty_sum + balance)\n", "#     #         #         balance = previous_balance + product_qty_sum\n", "#     #         #     else:  # transaction_type == 'mix'\n", "#     #         #         # D. if this product_id type mix has previous row type mix with the same product_id, it will (balance - product_qty_sum)\n", "#     #         #         balance = previous_balance - product_qty_sum\n", "        \n", "\n", "\n", "            \n", "#     #     # Update the balances dictionary with the current balance for the product_id\n", "#     #     balances[product_id] = balance\n", "\n", "#     #     # Assign the calculated balance to the 'balance' column\n", "#     #     df.at[index, 'balance'] = balance\n", "\n", "#     #     # Store the current transaction type for the next iteration\n", "#     #     previous_transaction_type = transaction_type\n", "\n", "#     # return df\n", "\n", "\n", "#     # Initialize a dictionary to store the balances for each product_id\n", "#     balances = {}\n", "\n", "#     # Initialize previous_transaction_type\n", "#     previous_transaction_type = None\n", "\n", "#     # Iterate over the rows starting from the tail\n", "#     for index, row in df[::-1].iterrows():\n", "#         product_id = row['product_id']\n", "#         product_qty_sum = row['product_qty_sum']\n", "#         transaction_type = row['type']\n", "\n", "#         if product_id not in balances:\n", "#             # A. assign default product_qty_sum value if this product_id has no previous row with the same product_id\n", "#             balance = product_qty_sum\n", "#         else:\n", "#             previous_balance = balances[product_id]\n", "\n", "#             # display(product_qty_sum, type(product_qty_sum))\n", "#             # display('-----------------------------------------------------')\n", "#             # display(previous_balance, type(previous_balance))\n", "\n", "#             if transaction_type == 'add' and previous_transaction_type == 'mix':\n", "#                 balance = product_qty_sum + previous_balance\n", "#                 # balance = transaction_type + '|' + previous_transaction_type + '|' + str(product_qty_sum) + '|' + str(previous_balance)\n", "#             if transaction_type == 'add' and previous_transaction_type == 'add':\n", "#                 balance = product_qty_sum + previous_balance\n", "#                 # balance = product_qty_sum + previous_balance\n", "#                 # balance = transaction_type + '|' + previous_transaction_type + '|' + str(product_qty_sum) + '|' + str(previous_balance)\n", "#             if transaction_type == 'mix' and previous_transaction_type == 'mix':\n", "#                 # balance = - product_qty_sum - previous_balance\n", "#                 balance = previous_balance + product_qty_sum\n", "#                 # balance = f\"{-product_qty_sum - previous_balance}sdcsdcsdc\"\n", "#                 # balance = transaction_type + '|' + previous_transaction_type + '|' + str(product_qty_sum) + '|' + str(previous_balance)\n", "#             if transaction_type == 'mix' and previous_transaction_type == 'add':\n", "#                 balance = product_qty_sum + previous_balance\n", "#                 # balance = str(product_qty_sum) + '|' + str(previous_balance) + '|' + transaction_type + '|' + previous_transaction_type + '|' + str((- product_qty_sum + previous_balance))\n", "#                 # balance = transaction_type + '|' + previous_transaction_type + '|' + str(product_qty_sum) + '|' + str(previous_balance)\n", "\n", "#         # Store the current transaction type for the next iteration\n", "#         previous_transaction_type = transaction_type\n", "                \n", "#         # Update the balances dictionary with the current balance for the product_id\n", "#         balances[product_id] = balance\n", "\n", "#         # Assign the calculated balance to the 'balance' column\n", "#         df.at[index, 'balance'] = balance\n", "\n", "#     return df\n", "\n", "\n", "# # display(calOne())\n", "# # display(get_color_insert_oa_tb())\n", "# display(joinDF())\n", "\n", "\n", "# # aaa = 10\n", "# # bbb = 20\n", "# # ccc = - aaa - bbb\n", "# # print(ccc)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-04-24 10:21:33,240 INFO sqlalchemy.engine.Engine select pg_catalog.version()\n", "2024-04-24 10:21:33,259 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-04-24 10:21:33,265 INFO sqlalchemy.engine.Engine select current_schema()\n", "2024-04-24 10:21:33,267 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-04-24 10:21:33,272 INFO sqlalchemy.engine.Engine show standard_conforming_strings\n", "2024-04-24 10:21:33,277 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-04-24 10:21:33,289 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-04-24 10:21:33,316 INFO sqlalchemy.engine.Engine SELECT color_insert_tb.record_id, color_insert_tb.detail_json, color_insert_tb.status, color_insert_tb.auto_id, color_insert_tb.datetime, color_insert_tb.group_id, color_insert_tb.jia_yi_fang_a, color_insert_tb.jia_yi_fang_b, color_insert_tb.lei_a, color_insert_tb.lei_b, color_insert_tb.device_id, color_insert_tb.product_id, color_insert_tb.product_qty, color_insert_tb.color_price, color_insert_tb.unit_price, color_insert_tb.bi_zhi, color_insert_tb.fen, color_insert_tb.sum_price, color_insert_tb.product_qty_sum, color_insert_tb.unit_price_sum \n", "FROM color_insert_tb \n", "WHERE color_insert_tb.group_id IN (SELECT anon_1.group_id \n", "FROM (SELECT color_insert_tb.group_id AS group_id \n", "FROM color_insert_tb \n", "WHERE CAST((color_insert_tb.status ->> %(status_1)s) AS VARCHAR) = %(param_1)s) AS anon_1) AND color_insert_tb.datetime >= %(datetime_1)s ORDER BY color_insert_tb.datetime DESC\n", "2024-04-24 10:21:33,327 INFO sqlalchemy.engine.Engine [generated in 0.01074s] {'status_1': 'status_big_table', 'param_1': 'success', 'datetime_1': datetime.date(2024, 4, 23)}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_23224/3439404420.py:52: SAWarning: Coercing Subquery object into a select() for use in IN(); please pass a select() construct explicitly\n", "  color_insert_tb.group_id.in_(success_groups_subquery),\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024-04-24 10:21:33,523 INFO sqlalchemy.engine.Engine ROLLBACK\n", "2024-04-24 10:21:33,567 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-04-24 10:21:33,580 INFO sqlalchemy.engine.Engine SELECT color_insert_oa_tb.record_id, color_insert_oa_tb.detail_json, color_insert_oa_tb.status, color_insert_oa_tb.auto_id, color_insert_oa_tb.datetime, color_insert_oa_tb.group_id, color_insert_oa_tb.jia_yi_fang_a, color_insert_oa_tb.jia_yi_fang_b, color_insert_oa_tb.lei_a, color_insert_oa_tb.lei_b, color_insert_oa_tb.device_id, color_insert_oa_tb.product_id, color_insert_oa_tb.product_qty, color_insert_oa_tb.fen, color_insert_oa_tb.color_price, color_insert_oa_tb.unit_price \n", "FROM color_insert_oa_tb \n", "WHERE color_insert_oa_tb.group_id IN (SELECT anon_1.group_id \n", "FROM (SELECT color_insert_oa_tb.group_id AS group_id \n", "FROM color_insert_oa_tb \n", "WHERE CAST((color_insert_oa_tb.status ->> %(status_1)s) AS VARCHAR) = %(param_1)s) AS anon_1) AND color_insert_oa_tb.datetime >= %(datetime_1)s ORDER BY color_insert_oa_tb.datetime DESC\n", "2024-04-24 10:21:33,587 INFO sqlalchemy.engine.Engine [generated in 0.00667s] {'status_1': 'status_big_table', 'param_1': 'success', 'datetime_1': datetime.date(2024, 3, 25)}\n", "2024-04-24 10:21:33,696 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_23224/3439404420.py:107: SAWarning: Coercing Subquery object into a select() for use in IN(); please pass a select() construct explicitly\n", "  color_insert_oa_tb.group_id.in_(success_groups_subquery),\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>fen</th>\n", "      <th>type</th>\n", "      <th>type_lei</th>\n", "      <th>group_id</th>\n", "      <th>device_id</th>\n", "      <th>product_id</th>\n", "      <th>product_qty_sum</th>\n", "      <th>balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-04-24 14:10:33.995849+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0296a1cb</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>-12.30</td>\n", "      <td>2987.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-04-24 08:27:04.626442+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>aabe7e1c</td>\n", "      <td>49019</td>\n", "      <td>76273</td>\n", "      <td>-150.00</td>\n", "      <td>1850.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-04-24 14:10:33.996366+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0296a1cb</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>-271.10</td>\n", "      <td>728.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-04-24 14:10:33.996749+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0296a1cb</td>\n", "      <td>49019</td>\n", "      <td>76277</td>\n", "      <td>-10.80</td>\n", "      <td>-10.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2024-04-24 08:27:04.626992+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>aabe7e1c</td>\n", "      <td>49019</td>\n", "      <td>76278</td>\n", "      <td>-21.00</td>\n", "      <td>-21.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-04-24 14:10:33.997087+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0296a1cb</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-64.10</td>\n", "      <td>935.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-04-24 08:27:04.627571+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>aabe7e1c</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>-15.00</td>\n", "      <td>-15.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-04-24 16:41:58.134290+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>83d043eb</td>\n", "      <td>49020</td>\n", "      <td>76273</td>\n", "      <td>-0.28</td>\n", "      <td>-0.28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-04-24 16:41:58.134982+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>83d043eb</td>\n", "      <td>49020</td>\n", "      <td>76274</td>\n", "      <td>-3.00</td>\n", "      <td>-32.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-04-24 16:41:58.135630+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>83d043eb</td>\n", "      <td>49020</td>\n", "      <td>76280</td>\n", "      <td>-2.80</td>\n", "      <td>978.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2024-04-23 10:59:16.279879+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>244f1506</td>\n", "      <td>49021</td>\n", "      <td>76270</td>\n", "      <td>-70.00</td>\n", "      <td>930.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-04-24 09:23:42.203406+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>a7dfb231</td>\n", "      <td>49021</td>\n", "      <td>76271</td>\n", "      <td>-1.50</td>\n", "      <td>989.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2024-04-23 10:59:16.280561+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>244f1506</td>\n", "      <td>49021</td>\n", "      <td>76273</td>\n", "      <td>-98.40</td>\n", "      <td>-98.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-04-24 09:23:42.204016+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>a7dfb231</td>\n", "      <td>49021</td>\n", "      <td>76274</td>\n", "      <td>-10.10</td>\n", "      <td>1983.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-04-24 09:12:39.917105+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>017adcc8</td>\n", "      <td>49021</td>\n", "      <td>76277</td>\n", "      <td>-26.00</td>\n", "      <td>-26.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-04-24 13:08:58.229119+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>c194c828</td>\n", "      <td>49021</td>\n", "      <td>76278</td>\n", "      <td>-35.10</td>\n", "      <td>964.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-04-24 13:08:58.229603+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>c194c828</td>\n", "      <td>49021</td>\n", "      <td>76279</td>\n", "      <td>-19.80</td>\n", "      <td>603.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2024-04-23 13:40:58.493786+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>1c67fd53</td>\n", "      <td>49021</td>\n", "      <td>76280</td>\n", "      <td>-509.60</td>\n", "      <td>4096.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2024-04-23 13:40:58.494022+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>1c67fd53</td>\n", "      <td>49021</td>\n", "      <td>76281</td>\n", "      <td>-4.90</td>\n", "      <td>870.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>2024-03-28 08:45:35.090170+06:30</td>\n", "      <td>1</td>\n", "      <td>add</td>\n", "      <td>NaN</td>\n", "      <td>af30d210</td>\n", "      <td>49023</td>\n", "      <td>77976</td>\n", "      <td>1000.00</td>\n", "      <td>1000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2024-04-23 08:38:45.603648+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0e7ead0a</td>\n", "      <td>49023</td>\n", "      <td>77979</td>\n", "      <td>-55.62</td>\n", "      <td>-55.62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2024-04-23 08:38:45.604716+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0e7ead0a</td>\n", "      <td>49023</td>\n", "      <td>77980</td>\n", "      <td>-11.20</td>\n", "      <td>-11.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2024-04-23 08:38:45.605400+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0e7ead0a</td>\n", "      <td>49023</td>\n", "      <td>77982</td>\n", "      <td>-132.41</td>\n", "      <td>-132.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>2024-03-28 09:24:42.677896+06:30</td>\n", "      <td>1</td>\n", "      <td>add</td>\n", "      <td>NaN</td>\n", "      <td>27111da8</td>\n", "      <td>49023</td>\n", "      <td>77983</td>\n", "      <td>1000.00</td>\n", "      <td>1000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2024-04-23 08:38:45.605993+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0e7ead0a</td>\n", "      <td>49023</td>\n", "      <td>77985</td>\n", "      <td>-64.66</td>\n", "      <td>-64.66</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>2024-04-05 09:00:38.921178+06:30</td>\n", "      <td>2</td>\n", "      <td>add</td>\n", "      <td>NaN</td>\n", "      <td>c8aba2fb</td>\n", "      <td>49024</td>\n", "      <td>77975</td>\n", "      <td>1000.00</td>\n", "      <td>1000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>2024-03-31 13:06:35.044504+06:30</td>\n", "      <td>2</td>\n", "      <td>add</td>\n", "      <td>NaN</td>\n", "      <td>2bbaa324</td>\n", "      <td>49024</td>\n", "      <td>77980</td>\n", "      <td>1000.00</td>\n", "      <td>1000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2024-04-21 10:47:37.643796+06:30</td>\n", "      <td>2</td>\n", "      <td>add</td>\n", "      <td>NaN</td>\n", "      <td>079cc6cc</td>\n", "      <td>49024</td>\n", "      <td>77981</td>\n", "      <td>1000.00</td>\n", "      <td>3000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>2024-04-06 10:34:31.156785+06:30</td>\n", "      <td>2</td>\n", "      <td>add</td>\n", "      <td>NaN</td>\n", "      <td>591940d4</td>\n", "      <td>49024</td>\n", "      <td>77983</td>\n", "      <td>1000.00</td>\n", "      <td>3000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>2024-04-06 10:33:00.530251+06:30</td>\n", "      <td>2</td>\n", "      <td>add</td>\n", "      <td>NaN</td>\n", "      <td>2c029169</td>\n", "      <td>49024</td>\n", "      <td>77984</td>\n", "      <td>1000.00</td>\n", "      <td>2000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>2024-04-05 09:03:30.774334+06:30</td>\n", "      <td>2</td>\n", "      <td>add</td>\n", "      <td>NaN</td>\n", "      <td>27824f5e</td>\n", "      <td>49024</td>\n", "      <td>77986</td>\n", "      <td>1000.00</td>\n", "      <td>1000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>2024-03-31 13:08:02.922052+06:30</td>\n", "      <td>2</td>\n", "      <td>add</td>\n", "      <td>NaN</td>\n", "      <td>0f3e51b5</td>\n", "      <td>49024</td>\n", "      <td>77987</td>\n", "      <td>1000.00</td>\n", "      <td>1000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>2024-03-31 13:09:40.023216+06:30</td>\n", "      <td>2</td>\n", "      <td>add</td>\n", "      <td>NaN</td>\n", "      <td>e22d4bc0</td>\n", "      <td>49024</td>\n", "      <td>77989</td>\n", "      <td>1000.00</td>\n", "      <td>1000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>2024-03-31 13:11:00.540658+06:30</td>\n", "      <td>2</td>\n", "      <td>add</td>\n", "      <td>NaN</td>\n", "      <td>e952c162</td>\n", "      <td>49024</td>\n", "      <td>77991</td>\n", "      <td>1000.00</td>\n", "      <td>1000.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime  fen type type_lei  group_id  device_id   \n", "6  2024-04-24 14:10:33.995849+06:30    1  mix       oa  0296a1cb      49019  \\\n", "19 2024-04-24 08:27:04.626442+06:30    1  mix       oa  aabe7e1c      49019   \n", "5  2024-04-24 14:10:33.996366+06:30    1  mix       oa  0296a1cb      49019   \n", "4  2024-04-24 14:10:33.996749+06:30    1  mix       oa  0296a1cb      49019   \n", "18 2024-04-24 08:27:04.626992+06:30    1  mix       oa  aabe7e1c      49019   \n", "3  2024-04-24 14:10:33.997087+06:30    1  mix       oa  0296a1cb      49019   \n", "17 2024-04-24 08:27:04.627571+06:30    1  mix       oa  aabe7e1c      49019   \n", "2  2024-04-24 16:41:58.134290+06:30    2  mix       oa  83d043eb      49020   \n", "1  2024-04-24 16:41:58.134982+06:30    2  mix       oa  83d043eb      49020   \n", "0  2024-04-24 16:41:58.135630+06:30    2  mix       oa  83d043eb      49020   \n", "25 2024-04-23 10:59:16.279879+06:30    3  mix       oa  244f1506      49021   \n", "13 2024-04-24 09:23:42.203406+06:30    3  mix       oa  a7dfb231      49021   \n", "24 2024-04-23 10:59:16.280561+06:30    3  mix       oa  244f1506      49021   \n", "12 2024-04-24 09:23:42.204016+06:30    3  mix       oa  a7dfb231      49021   \n", "15 2024-04-24 09:12:39.917105+06:30    3  mix       oa  017adcc8      49021   \n", "8  2024-04-24 13:08:58.229119+06:30    3  mix       oa  c194c828      49021   \n", "7  2024-04-24 13:08:58.229603+06:30    3  mix       oa  c194c828      49021   \n", "21 2024-04-23 13:40:58.493786+06:30    3  mix       oa  1c67fd53      49021   \n", "20 2024-04-23 13:40:58.494022+06:30    3  mix       oa  1c67fd53      49021   \n", "63 2024-03-28 08:45:35.090170+06:30    1  add      NaN  af30d210      49023   \n", "29 2024-04-23 08:38:45.603648+06:30    3  mix       oa  0e7ead0a      49023   \n", "28 2024-04-23 08:38:45.604716+06:30    3  mix       oa  0e7ead0a      49023   \n", "27 2024-04-23 08:38:45.605400+06:30    3  mix       oa  0e7ead0a      49023   \n", "62 2024-03-28 09:24:42.677896+06:30    1  add      NaN  27111da8      49023   \n", "26 2024-04-23 08:38:45.605993+06:30    3  mix       oa  0e7ead0a      49023   \n", "47 2024-04-05 09:00:38.921178+06:30    2  add      NaN  c8aba2fb      49024   \n", "55 2024-03-31 13:06:35.044504+06:30    2  add      NaN  2bbaa324      49024   \n", "33 2024-04-21 10:47:37.643796+06:30    2  add      NaN  079cc6cc      49024   \n", "42 2024-04-06 10:34:31.156785+06:30    2  add      NaN  591940d4      49024   \n", "43 2024-04-06 10:33:00.530251+06:30    2  add      NaN  2c029169      49024   \n", "45 2024-04-05 09:03:30.774334+06:30    2  add      NaN  27824f5e      49024   \n", "54 2024-03-31 13:08:02.922052+06:30    2  add      NaN  0f3e51b5      49024   \n", "53 2024-03-31 13:09:40.023216+06:30    2  add      NaN  e22d4bc0      49024   \n", "52 2024-03-31 13:11:00.540658+06:30    2  add      NaN  e952c162      49024   \n", "\n", "    product_id  product_qty_sum  balance  \n", "6        76270           -12.30  2987.70  \n", "19       76273          -150.00  1850.00  \n", "5        76274          -271.10   728.90  \n", "4        76277           -10.80   -10.80  \n", "18       76278           -21.00   -21.00  \n", "3        76280           -64.10   935.90  \n", "17       76281           -15.00   -15.00  \n", "2        76273            -0.28    -0.28  \n", "1        76274            -3.00   -32.00  \n", "0        76280            -2.80   978.20  \n", "25       76270           -70.00   930.00  \n", "13       76271            -1.50   989.30  \n", "24       76273           -98.40   -98.40  \n", "12       76274           -10.10  1983.90  \n", "15       76277           -26.00   -26.00  \n", "8        76278           -35.10   964.90  \n", "7        76279           -19.80   603.50  \n", "21       76280          -509.60  4096.10  \n", "20       76281            -4.90   870.00  \n", "63       77976          1000.00  1000.00  \n", "29       77979           -55.62   -55.62  \n", "28       77980           -11.20   -11.20  \n", "27       77982          -132.41  -132.41  \n", "62       77983          1000.00  1000.00  \n", "26       77985           -64.66   -64.66  \n", "47       77975          1000.00  1000.00  \n", "55       77980          1000.00  1000.00  \n", "33       77981          1000.00  3000.00  \n", "42       77983          1000.00  3000.00  \n", "43       77984          1000.00  2000.00  \n", "45       77986          1000.00  1000.00  \n", "54       77987          1000.00  1000.00  \n", "53       77989          1000.00  1000.00  \n", "52       77991          1000.00  1000.00  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sys\n", "# sys.path.append('../../../../../app/')\n", "sys.path.append('../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "# from src.shwethe_color.database import get_session\n", "from src.config.shwethe_color.database import get_session\n", "from src.shwethe_color.models.model import color_insert_tb, color_insert_oa_tb\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "\n", "\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def dataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "def get_color_insert_tb():\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        # GET TODAY DATA\n", "        week_ago = today - DT.<PERSON><PERSON><PERSON>(days=1)\n", "\n", "        # First, create a subquery for SuccessGroups\n", "        success_groups_subquery = select(color_insert_tb.group_id).where(\n", "            color_insert_tb.status[(\"status_big_table\")].as_string() == \"success\"\n", "        ).subquery()\n", "        # Now, create the main query\n", "        query = select(color_insert_tb).where(\n", "            color_insert_tb.group_id.in_(success_groups_subquery),\n", "            color_insert_tb.datetime >= week_ago\n", "        ).order_by(color_insert_tb.datetime.desc())\n", "        # Execute the query\n", "        heroesPersonal = db.exec(query).all()\n", "\n", "        mergeDF = dataframe(heroesPersonal, to_dict=False)\n", "        # mergeDF = mergeDF[['datetime', 'fen', 'device_id', 'group_id', 'product_id', 'product_qty', 'product_qty_sum']]\n", "        mergeDF = mergeDF[['datetime', 'jia_yi_fang_a', 'jia_yi_fang_b', 'lei_a', 'fen', 'group_id', 'device_id', 'product_id', 'product_qty_sum']]\n", "        # mergeDF = mergeDF.head(60)\n", "        # mergeDF = mergeDF[60:120]\n", "    return mergeDF\n", "\n", "\n", "def calOne():\n", "    df = get_color_insert_tb()\n", "\n", "    # Create a combination key of jia_yi_fang_a and group_id where lei_a is 31\n", "    code_combination = df[df['lei_a'] == 31][['jia_yi_fang_a', 'group_id']]\n", "    code_combination['key'] = code_combination['jia_yi_fang_a'].astype(str) + '-' + code_combination['group_id']\n", "    code_set = set(code_combination['key'])\n", "    # Assign 'type_lei' based on conditions\n", "    df['type_lei'] = 'oa'  # Default value\n", "    df.loc[df['lei_a'] == 31, 'type_lei'] = 'code'  # Set 'code' where lei_a is 31\n", "    # Set 'base' where combination of jia_yi_fang_a and group_id matches the code_set and lei_a is not 31\n", "    df['combination_key'] = df['jia_yi_fang_a'].astype(str) + '-' + df['group_id']\n", "    df.loc[df['combination_key'].isin(code_set) & (df['lei_a'] != 31), 'type_lei'] = 'base'\n", "    # Drop the temporary combination_key column\n", "    df.drop('combination_key', axis=1, inplace=True)\n", "\n", "    # Filter rows where 'type_lei' is either 'code' or 'base'\n", "    df = df[df['type_lei'].isin(['oa'])]\n", "\n", "    df['type'] = 'mix'\n", "\n", "    df = df[['datetime', 'fen', 'type', 'type_lei', 'group_id', 'device_id', 'product_id', 'product_qty_sum']]\n", "\n", "    df['product_qty_sum'] = -df['product_qty_sum']\n", "\n", "    return df\n", "\n", "\n", "def get_color_insert_oa_tb():\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        # GET TODAY DATA\n", "        week_ago = today - DT.<PERSON><PERSON>(days=30)\n", "\n", "        # First, create a subquery for SuccessGroups\n", "        success_groups_subquery = select(color_insert_oa_tb.group_id).where(\n", "            color_insert_oa_tb.status[(\"status_big_table\")].as_string() == \"success\"\n", "        ).subquery()\n", "        # Now, create the main query\n", "        query = select(color_insert_oa_tb).where(\n", "            color_insert_oa_tb.group_id.in_(success_groups_subquery),\n", "            color_insert_oa_tb.datetime >= week_ago\n", "        ).order_by(color_insert_oa_tb.datetime.desc())\n", "        # Execute the query\n", "        heroesPersonal = db.exec(query).all()\n", "\n", "        mergeDF = dataframe(heroesPersonal, to_dict=False)\n", "\n", "        mergeDF = mergeDF[mergeDF['product_qty'].isin([-1000])]\n", "        mergeDF = mergeDF[['datetime', 'fen', 'group_id', 'device_id', 'product_id']]\n", "        mergeDF['type'] = 'add'\n", "        mergeDF['product_qty_sum'] = 1000\n", "        mergeDF = mergeDF[['datetime', 'fen', 'type', 'group_id', 'device_id', 'product_id', 'product_qty_sum']]\n", "\n", "    return mergeDF\n", "\n", "\n", "def joinDF():\n", "    color_insert = calOne()\n", "    color_insert_oa = get_color_insert_oa_tb()\n", "\n", "    # Concatenate the two DataFrames\n", "    concatenated_df = pd.concat([color_insert, color_insert_oa], ignore_index=True)\n", "\n", "    # Convert the 'datetime_column' to datetime format if it's not already\n", "    concatenated_df['datetime'] = pd.to_datetime(concatenated_df['datetime'])\n", "\n", "    # Sort the DataFrame in descending order based on the 'datetime_column'\n", "    concatenated_df = concatenated_df.sort_values(by='datetime', ascending=False)\n", "    \n", "    # Initialize a dictionary to store the balances for each product_id and device_id combination\n", "    balances = defaultdict(dict)\n", "\n", "    # Iterate over the grouped DataFrame by device_id\n", "    for device_id, group in concatenated_df.groupby('device_id'):\n", "        # Initialize previous_transaction_type for each group\n", "        previous_transaction_type = None\n", "\n", "        # Iterate over the rows starting from the tail within each group\n", "        for index, row in group[::-1].iterrows():\n", "            product_id = row['product_id']\n", "            product_qty_sum = row['product_qty_sum']\n", "            transaction_type = row['type']\n", "\n", "            if product_id not in balances[device_id]:\n", "                # A. assign default product_qty_sum value if this product_id has no previous row with the same product_id\n", "                balance = product_qty_sum\n", "            else:\n", "                previous_balance = balances[device_id][product_id]\n", "\n", "                if transaction_type == 'add' and previous_transaction_type == 'mix':\n", "                    balance = product_qty_sum + previous_balance\n", "                if transaction_type == 'add' and previous_transaction_type == 'add':\n", "                    balance = product_qty_sum + previous_balance\n", "                if transaction_type == 'mix' and previous_transaction_type == 'mix':\n", "                    balance = previous_balance + product_qty_sum\n", "                if transaction_type == 'mix' and previous_transaction_type == 'add':\n", "                    balance = product_qty_sum + previous_balance\n", "\n", "            # Store the current transaction type for the next iteration\n", "            previous_transaction_type = transaction_type\n", "\n", "            # Update the balances dictionary with the current balance for the product_id and device_id combination\n", "            balances[device_id][product_id] = balance\n", "\n", "            # Assign the calculated balance to the 'balance' column\n", "            concatenated_df.at[index, 'balance'] = balance\n", "\n", "    # Group by 'device_id' and 'product_id' and get the index of the row with the maximum datetime within each group\n", "    last_datetime_indices = concatenated_df.groupby(['device_id', 'product_id'])['datetime'].idxmax()\n", "\n", "    # Extract the rows corresponding to the last datetime within each group\n", "    last_datetime_rows = concatenated_df.loc[last_datetime_indices]\n", "\n", "    return last_datetime_rows\n", "\n", "\n", "# display(calOne())\n", "# display(get_color_insert_oa_tb())\n", "display(joinDF())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-04-24 10:26:21,525 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-04-24 10:26:21,559 INFO sqlalchemy.engine.Engine SELECT color_insert_tb.record_id, color_insert_tb.detail_json, color_insert_tb.status, color_insert_tb.auto_id, color_insert_tb.datetime, color_insert_tb.group_id, color_insert_tb.jia_yi_fang_a, color_insert_tb.jia_yi_fang_b, color_insert_tb.lei_a, color_insert_tb.lei_b, color_insert_tb.device_id, color_insert_tb.product_id, color_insert_tb.product_qty, color_insert_tb.color_price, color_insert_tb.unit_price, color_insert_tb.bi_zhi, color_insert_tb.fen, color_insert_tb.sum_price, color_insert_tb.product_qty_sum, color_insert_tb.unit_price_sum \n", "FROM color_insert_tb \n", "WHERE color_insert_tb.group_id IN (SELECT anon_1.group_id \n", "FROM (SELECT color_insert_tb.group_id AS group_id \n", "FROM color_insert_tb \n", "WHERE CAST((color_insert_tb.status ->> %(status_1)s) AS VARCHAR) = %(param_1)s) AS anon_1) AND color_insert_tb.datetime >= %(datetime_1)s ORDER BY color_insert_tb.datetime DESC\n", "2024-04-24 10:26:21,565 INFO sqlalchemy.engine.Engine [cached since 288.2s ago] {'status_1': 'status_big_table', 'param_1': 'success', 'datetime_1': datetime.date(2024, 3, 25)}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_23224/977521823.py:59: SAWarning: Coercing Subquery object into a select() for use in IN(); please pass a select() construct explicitly\n", "  color_insert_tb.group_id.in_(success_groups_subquery),\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024-04-24 10:26:22,545 INFO sqlalchemy.engine.Engine ROLLBACK\n", "2024-04-24 10:26:22,591 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-04-24 10:26:22,595 INFO sqlalchemy.engine.Engine SELECT color_insert_oa_tb.record_id, color_insert_oa_tb.detail_json, color_insert_oa_tb.status, color_insert_oa_tb.auto_id, color_insert_oa_tb.datetime, color_insert_oa_tb.group_id, color_insert_oa_tb.jia_yi_fang_a, color_insert_oa_tb.jia_yi_fang_b, color_insert_oa_tb.lei_a, color_insert_oa_tb.lei_b, color_insert_oa_tb.device_id, color_insert_oa_tb.product_id, color_insert_oa_tb.product_qty, color_insert_oa_tb.fen, color_insert_oa_tb.color_price, color_insert_oa_tb.unit_price \n", "FROM color_insert_oa_tb \n", "WHERE color_insert_oa_tb.group_id IN (SELECT anon_1.group_id \n", "FROM (SELECT color_insert_oa_tb.group_id AS group_id \n", "FROM color_insert_oa_tb \n", "WHERE CAST((color_insert_oa_tb.status ->> %(status_1)s) AS VARCHAR) = %(param_1)s) AS anon_1) AND color_insert_oa_tb.datetime >= %(datetime_1)s ORDER BY color_insert_oa_tb.datetime DESC\n", "2024-04-24 10:26:22,598 INFO sqlalchemy.engine.Engine [cached since 289s ago] {'status_1': 'status_big_table', 'param_1': 'success', 'datetime_1': datetime.date(2024, 3, 25)}\n", "2024-04-24 10:26:22,704 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_23224/977521823.py:114: SAWarning: Coercing Subquery object into a select() for use in IN(); please pass a select() construct explicitly\n", "  color_insert_oa_tb.group_id.in_(success_groups_subquery),\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>fen</th>\n", "      <th>type</th>\n", "      <th>type_lei</th>\n", "      <th>group_id</th>\n", "      <th>device_id</th>\n", "      <th>product_id</th>\n", "      <th>product_qty_sum</th>\n", "      <th>balance</th>\n", "      <th>product_d_name</th>\n", "      <th>jia_yi_id</th>\n", "      <th>jia_yi_mmname</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-04-24 14:10:33.995849+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0296a1cb</td>\n", "      <td>49019</td>\n", "      <td>76270</td>\n", "      <td>-12.3</td>\n", "      <td>730.5</td>\n", "      <td><PERSON><PERSON> (NO-1)</td>\n", "      <td>49019</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-04-01 15:55:06.047762+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>a8290e0a</td>\n", "      <td>49020</td>\n", "      <td>76270</td>\n", "      <td>-150.0</td>\n", "      <td>-324.5</td>\n", "      <td><PERSON><PERSON> (NO-1)</td>\n", "      <td>49020</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-04-23 10:59:16.279879+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>244f1506</td>\n", "      <td>49021</td>\n", "      <td>76270</td>\n", "      <td>-70.0</td>\n", "      <td>127.7</td>\n", "      <td><PERSON><PERSON> (NO-1)</td>\n", "      <td>49021</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-04-06 11:51:27.582743+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>3f081f78</td>\n", "      <td>49019</td>\n", "      <td>76271</td>\n", "      <td>-8.0</td>\n", "      <td>-25.3</td>\n", "      <td><PERSON><PERSON> (NO-2)</td>\n", "      <td>49019</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-04-19 16:32:29.528681+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>c93e91bd</td>\n", "      <td>49020</td>\n", "      <td>76271</td>\n", "      <td>-2.1</td>\n", "      <td>-89.0</td>\n", "      <td><PERSON><PERSON> (NO-2)</td>\n", "      <td>49020</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-04-24 09:23:42.203406+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>a7dfb231</td>\n", "      <td>49021</td>\n", "      <td>76271</td>\n", "      <td>-1.5</td>\n", "      <td>692.3</td>\n", "      <td><PERSON><PERSON> (NO-2)</td>\n", "      <td>49021</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-03-27 15:12:21.013473+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0b6dda64</td>\n", "      <td>49019</td>\n", "      <td>76272</td>\n", "      <td>-13.6</td>\n", "      <td>-13.6</td>\n", "      <td><PERSON><PERSON> (NO-3)</td>\n", "      <td>49019</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-04-19 11:06:24.189986+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>26b23b63</td>\n", "      <td>49021</td>\n", "      <td>76272</td>\n", "      <td>-1.2</td>\n", "      <td>-61.8</td>\n", "      <td><PERSON><PERSON> (NO-3)</td>\n", "      <td>49021</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-04-24 08:27:04.626442+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>aabe7e1c</td>\n", "      <td>49019</td>\n", "      <td>76273</td>\n", "      <td>-150.0</td>\n", "      <td>-837.9</td>\n", "      <td><PERSON><PERSON> (NO-4)</td>\n", "      <td>49019</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-04-24 16:41:58.134290+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>83d043eb</td>\n", "      <td>49020</td>\n", "      <td>76273</td>\n", "      <td>-0.3</td>\n", "      <td>-306.0</td>\n", "      <td><PERSON><PERSON> (NO-4)</td>\n", "      <td>49020</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-04-23 10:59:16.280561+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>244f1506</td>\n", "      <td>49021</td>\n", "      <td>76273</td>\n", "      <td>-98.4</td>\n", "      <td>-392.5</td>\n", "      <td><PERSON><PERSON> (NO-4)</td>\n", "      <td>49021</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-04-24 14:10:33.996366+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0296a1cb</td>\n", "      <td>49019</td>\n", "      <td>76274</td>\n", "      <td>-271.1</td>\n", "      <td>-894.1</td>\n", "      <td><PERSON><PERSON> (NO-5)</td>\n", "      <td>49019</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-04-24 16:41:58.134982+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>83d043eb</td>\n", "      <td>49020</td>\n", "      <td>76274</td>\n", "      <td>-3.0</td>\n", "      <td>-552.6</td>\n", "      <td><PERSON><PERSON> (NO-5)</td>\n", "      <td>49020</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-04-24 09:23:42.204016+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>a7dfb231</td>\n", "      <td>49021</td>\n", "      <td>76274</td>\n", "      <td>-10.1</td>\n", "      <td>377.8</td>\n", "      <td><PERSON><PERSON> (NO-5)</td>\n", "      <td>49021</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-03-27 15:28:30.138589+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>5d5b03f5</td>\n", "      <td>49019</td>\n", "      <td>76275</td>\n", "      <td>-16.8</td>\n", "      <td>-28.9</td>\n", "      <td><PERSON><PERSON> (NO-6)</td>\n", "      <td>49019</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-04-22 13:55:54.158824+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>077a7620</td>\n", "      <td>49020</td>\n", "      <td>76275</td>\n", "      <td>-1.2</td>\n", "      <td>-12.2</td>\n", "      <td><PERSON><PERSON> (NO-6)</td>\n", "      <td>49020</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-04-22 15:54:05.827082+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>8cadcfb6</td>\n", "      <td>49021</td>\n", "      <td>76275</td>\n", "      <td>-36.4</td>\n", "      <td>-54.1</td>\n", "      <td><PERSON><PERSON> (NO-6)</td>\n", "      <td>49021</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-04-19 12:26:59.419615+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>ee99513d</td>\n", "      <td>49019</td>\n", "      <td>76276</td>\n", "      <td>-10.2</td>\n", "      <td>-28.4</td>\n", "      <td><PERSON><PERSON> (NO-7)</td>\n", "      <td>49019</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2024-04-10 14:49:21.800009+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>edf90eda</td>\n", "      <td>49021</td>\n", "      <td>76276</td>\n", "      <td>-68.0</td>\n", "      <td>-68.0</td>\n", "      <td><PERSON><PERSON> (NO-7)</td>\n", "      <td>49021</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-04-24 14:10:33.996749+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0296a1cb</td>\n", "      <td>49019</td>\n", "      <td>76277</td>\n", "      <td>-10.8</td>\n", "      <td>-134.3</td>\n", "      <td><PERSON><PERSON> (NO-8)</td>\n", "      <td>49019</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2024-03-25 14:25:36.764582+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>94f5617d</td>\n", "      <td>49020</td>\n", "      <td>76277</td>\n", "      <td>-5.6</td>\n", "      <td>-5.6</td>\n", "      <td><PERSON><PERSON> (NO-8)</td>\n", "      <td>49020</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2024-04-24 09:12:39.917105+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>017adcc8</td>\n", "      <td>49021</td>\n", "      <td>76277</td>\n", "      <td>-26.0</td>\n", "      <td>-199.2</td>\n", "      <td><PERSON><PERSON> (NO-8)</td>\n", "      <td>49021</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2024-04-24 08:27:04.626992+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>aabe7e1c</td>\n", "      <td>49019</td>\n", "      <td>76278</td>\n", "      <td>-21.0</td>\n", "      <td>-193.2</td>\n", "      <td><PERSON><PERSON> (NO-9)</td>\n", "      <td>49019</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2024-04-19 10:17:17.859035+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>7e967a07</td>\n", "      <td>49020</td>\n", "      <td>76278</td>\n", "      <td>-8.0</td>\n", "      <td>-19.1</td>\n", "      <td><PERSON><PERSON> (NO-9)</td>\n", "      <td>49020</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2024-04-24 13:08:58.229119+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>c194c828</td>\n", "      <td>49021</td>\n", "      <td>76278</td>\n", "      <td>-35.1</td>\n", "      <td>578.6</td>\n", "      <td><PERSON><PERSON> (NO-9)</td>\n", "      <td>49021</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2024-04-21 10:19:03.030345+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>5c671518</td>\n", "      <td>49019</td>\n", "      <td>76279</td>\n", "      <td>-40.0</td>\n", "      <td>-140.9</td>\n", "      <td><PERSON><PERSON> (NO-10)</td>\n", "      <td>49019</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2024-04-19 15:16:53.729667+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>e738f5dd</td>\n", "      <td>49020</td>\n", "      <td>76279</td>\n", "      <td>-12.0</td>\n", "      <td>-79.5</td>\n", "      <td><PERSON><PERSON> (NO-10)</td>\n", "      <td>49020</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2024-04-24 13:08:58.229603+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>c194c828</td>\n", "      <td>49021</td>\n", "      <td>76279</td>\n", "      <td>-19.8</td>\n", "      <td>-768.1</td>\n", "      <td><PERSON><PERSON> (NO-10)</td>\n", "      <td>49021</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2024-04-24 14:10:33.997087+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0296a1cb</td>\n", "      <td>49019</td>\n", "      <td>76280</td>\n", "      <td>-64.1</td>\n", "      <td>-216.7</td>\n", "      <td><PERSON><PERSON> (NO-11)</td>\n", "      <td>49019</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2024-04-24 16:41:58.135630+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>83d043eb</td>\n", "      <td>49020</td>\n", "      <td>76280</td>\n", "      <td>-2.8</td>\n", "      <td>-1434.1</td>\n", "      <td><PERSON><PERSON> (NO-11)</td>\n", "      <td>49020</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>2024-04-23 13:40:58.493786+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>1c67fd53</td>\n", "      <td>49021</td>\n", "      <td>76280</td>\n", "      <td>-509.6</td>\n", "      <td>-263.0</td>\n", "      <td><PERSON><PERSON> (NO-11)</td>\n", "      <td>49021</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>2024-04-24 08:27:04.627571+06:30</td>\n", "      <td>1</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>aabe7e1c</td>\n", "      <td>49019</td>\n", "      <td>76281</td>\n", "      <td>-15.0</td>\n", "      <td>-284.4</td>\n", "      <td><PERSON><PERSON> (NO-12)</td>\n", "      <td>49019</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>2024-04-22 13:55:54.159131+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>077a7620</td>\n", "      <td>49020</td>\n", "      <td>76281</td>\n", "      <td>-11.2</td>\n", "      <td>-203.5</td>\n", "      <td><PERSON><PERSON> (NO-12)</td>\n", "      <td>49020</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2024-04-23 13:40:58.494022+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>1c67fd53</td>\n", "      <td>49021</td>\n", "      <td>76281</td>\n", "      <td>-4.9</td>\n", "      <td>-114.5</td>\n", "      <td><PERSON><PERSON> (NO-12)</td>\n", "      <td>49021</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (Beger)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>2024-04-10 09:27:43.838386+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>9c85729a</td>\n", "      <td>49023</td>\n", "      <td>77976</td>\n", "      <td>-86.6</td>\n", "      <td>653.6</td>\n", "      <td>Captain  LS02</td>\n", "      <td>49023</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>2024-04-08 12:36:04.331742+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>2fe21506</td>\n", "      <td>49024</td>\n", "      <td>77976</td>\n", "      <td>-53.0</td>\n", "      <td>-332.3</td>\n", "      <td>Captain  LS02</td>\n", "      <td>49024</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>2024-04-23 08:38:45.603648+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0e7ead0a</td>\n", "      <td>49023</td>\n", "      <td>77979</td>\n", "      <td>-55.6</td>\n", "      <td>-55.6</td>\n", "      <td>Captain  RS05</td>\n", "      <td>49023</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>2024-04-23 08:38:45.604716+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0e7ead0a</td>\n", "      <td>49023</td>\n", "      <td>77980</td>\n", "      <td>-11.2</td>\n", "      <td>-11.2</td>\n", "      <td>Captain  US06</td>\n", "      <td>49023</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>2024-04-10 09:27:43.838801+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>9c85729a</td>\n", "      <td>49023</td>\n", "      <td>77981</td>\n", "      <td>-13.5</td>\n", "      <td>-54.0</td>\n", "      <td>Captain  TT07</td>\n", "      <td>49023</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>2024-04-22 16:34:12.357895+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>93232467</td>\n", "      <td>49024</td>\n", "      <td>77981</td>\n", "      <td>-209.8</td>\n", "      <td>406.6</td>\n", "      <td>Captain  TT07</td>\n", "      <td>49024</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>2024-04-23 08:38:45.605400+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0e7ead0a</td>\n", "      <td>49023</td>\n", "      <td>77982</td>\n", "      <td>-132.4</td>\n", "      <td>-132.4</td>\n", "      <td>Captain   MS08</td>\n", "      <td>49023</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>2024-04-22 16:34:12.358222+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>93232467</td>\n", "      <td>49024</td>\n", "      <td>77982</td>\n", "      <td>-13.0</td>\n", "      <td>-223.8</td>\n", "      <td>Captain   MS08</td>\n", "      <td>49024</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>2024-04-10 09:27:43.839135+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>9c85729a</td>\n", "      <td>49023</td>\n", "      <td>77983</td>\n", "      <td>-143.9</td>\n", "      <td>424.2</td>\n", "      <td>Captain   <PERSON>9</td>\n", "      <td>49023</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>2024-04-19 10:18:54.462813+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>b1e64a87</td>\n", "      <td>49024</td>\n", "      <td>77983</td>\n", "      <td>-89.9</td>\n", "      <td>157.0</td>\n", "      <td>Captain   <PERSON>9</td>\n", "      <td>49024</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>2024-04-23 08:38:45.605993+06:30</td>\n", "      <td>3</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>0e7ead0a</td>\n", "      <td>49023</td>\n", "      <td>77985</td>\n", "      <td>-64.7</td>\n", "      <td>-64.7</td>\n", "      <td>Captain   VI11</td>\n", "      <td>49023</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>2024-04-19 10:18:54.463475+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>b1e64a87</td>\n", "      <td>49024</td>\n", "      <td>77985</td>\n", "      <td>-15.7</td>\n", "      <td>-54.0</td>\n", "      <td>Captain   VI11</td>\n", "      <td>49024</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>2024-04-22 16:35:18.372964+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>7ba5169a</td>\n", "      <td>49024</td>\n", "      <td>77975</td>\n", "      <td>-19.9</td>\n", "      <td>303.3</td>\n", "      <td>Captain  FT01</td>\n", "      <td>49024</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>2024-04-22 16:35:18.373400+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>7ba5169a</td>\n", "      <td>49024</td>\n", "      <td>77978</td>\n", "      <td>-16.6</td>\n", "      <td>-16.6</td>\n", "      <td>Captain  MT04</td>\n", "      <td>49024</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>2024-04-06 10:39:40.422944+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>d60db7dd</td>\n", "      <td>49024</td>\n", "      <td>77984</td>\n", "      <td>-502.8</td>\n", "      <td>-11.1</td>\n", "      <td>Captain   LT10</td>\n", "      <td>49024</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>2024-04-19 10:18:54.464147+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>b1e64a87</td>\n", "      <td>49024</td>\n", "      <td>77986</td>\n", "      <td>-133.2</td>\n", "      <td>-19.6</td>\n", "      <td>Captain   XT12</td>\n", "      <td>49024</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>2024-04-06 10:39:40.423209+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>d60db7dd</td>\n", "      <td>49024</td>\n", "      <td>77988</td>\n", "      <td>-32.4</td>\n", "      <td>-129.4</td>\n", "      <td>Captain   RT14</td>\n", "      <td>49024</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>2024-04-22 16:35:18.373629+06:30</td>\n", "      <td>2</td>\n", "      <td>mix</td>\n", "      <td>oa</td>\n", "      <td>7ba5169a</td>\n", "      <td>49024</td>\n", "      <td>77989</td>\n", "      <td>-10.9</td>\n", "      <td>989.1</td>\n", "      <td>Captain   ST15</td>\n", "      <td>49024</td>\n", "      <td>ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime  fen type type_lei  group_id  device_id   \n", "0  2024-04-24 14:10:33.995849+06:30    1  mix       oa  0296a1cb      49019  \\\n", "1  2024-04-01 15:55:06.047762+06:30    2  mix       oa  a8290e0a      49020   \n", "2  2024-04-23 10:59:16.279879+06:30    3  mix       oa  244f1506      49021   \n", "3  2024-04-06 11:51:27.582743+06:30    1  mix       oa  3f081f78      49019   \n", "4  2024-04-19 16:32:29.528681+06:30    2  mix       oa  c93e91bd      49020   \n", "5  2024-04-24 09:23:42.203406+06:30    3  mix       oa  a7dfb231      49021   \n", "6  2024-03-27 15:12:21.013473+06:30    1  mix       oa  0b6dda64      49019   \n", "7  2024-04-19 11:06:24.189986+06:30    3  mix       oa  26b23b63      49021   \n", "8  2024-04-24 08:27:04.626442+06:30    1  mix       oa  aabe7e1c      49019   \n", "9  2024-04-24 16:41:58.134290+06:30    2  mix       oa  83d043eb      49020   \n", "10 2024-04-23 10:59:16.280561+06:30    3  mix       oa  244f1506      49021   \n", "11 2024-04-24 14:10:33.996366+06:30    1  mix       oa  0296a1cb      49019   \n", "12 2024-04-24 16:41:58.134982+06:30    2  mix       oa  83d043eb      49020   \n", "13 2024-04-24 09:23:42.204016+06:30    3  mix       oa  a7dfb231      49021   \n", "14 2024-03-27 15:28:30.138589+06:30    1  mix       oa  5d5b03f5      49019   \n", "15 2024-04-22 13:55:54.158824+06:30    2  mix       oa  077a7620      49020   \n", "16 2024-04-22 15:54:05.827082+06:30    3  mix       oa  8cadcfb6      49021   \n", "17 2024-04-19 12:26:59.419615+06:30    1  mix       oa  ee99513d      49019   \n", "18 2024-04-10 14:49:21.800009+06:30    3  mix       oa  edf90eda      49021   \n", "19 2024-04-24 14:10:33.996749+06:30    1  mix       oa  0296a1cb      49019   \n", "20 2024-03-25 14:25:36.764582+06:30    2  mix       oa  94f5617d      49020   \n", "21 2024-04-24 09:12:39.917105+06:30    3  mix       oa  017adcc8      49021   \n", "22 2024-04-24 08:27:04.626992+06:30    1  mix       oa  aabe7e1c      49019   \n", "23 2024-04-19 10:17:17.859035+06:30    2  mix       oa  7e967a07      49020   \n", "24 2024-04-24 13:08:58.229119+06:30    3  mix       oa  c194c828      49021   \n", "25 2024-04-21 10:19:03.030345+06:30    1  mix       oa  5c671518      49019   \n", "26 2024-04-19 15:16:53.729667+06:30    2  mix       oa  e738f5dd      49020   \n", "27 2024-04-24 13:08:58.229603+06:30    3  mix       oa  c194c828      49021   \n", "28 2024-04-24 14:10:33.997087+06:30    1  mix       oa  0296a1cb      49019   \n", "29 2024-04-24 16:41:58.135630+06:30    2  mix       oa  83d043eb      49020   \n", "30 2024-04-23 13:40:58.493786+06:30    3  mix       oa  1c67fd53      49021   \n", "31 2024-04-24 08:27:04.627571+06:30    1  mix       oa  aabe7e1c      49019   \n", "32 2024-04-22 13:55:54.159131+06:30    2  mix       oa  077a7620      49020   \n", "33 2024-04-23 13:40:58.494022+06:30    3  mix       oa  1c67fd53      49021   \n", "34 2024-04-10 09:27:43.838386+06:30    3  mix       oa  9c85729a      49023   \n", "35 2024-04-08 12:36:04.331742+06:30    2  mix       oa  2fe21506      49024   \n", "36 2024-04-23 08:38:45.603648+06:30    3  mix       oa  0e7ead0a      49023   \n", "37 2024-04-23 08:38:45.604716+06:30    3  mix       oa  0e7ead0a      49023   \n", "38 2024-04-10 09:27:43.838801+06:30    3  mix       oa  9c85729a      49023   \n", "39 2024-04-22 16:34:12.357895+06:30    2  mix       oa  93232467      49024   \n", "40 2024-04-23 08:38:45.605400+06:30    3  mix       oa  0e7ead0a      49023   \n", "41 2024-04-22 16:34:12.358222+06:30    2  mix       oa  93232467      49024   \n", "42 2024-04-10 09:27:43.839135+06:30    3  mix       oa  9c85729a      49023   \n", "43 2024-04-19 10:18:54.462813+06:30    2  mix       oa  b1e64a87      49024   \n", "44 2024-04-23 08:38:45.605993+06:30    3  mix       oa  0e7ead0a      49023   \n", "45 2024-04-19 10:18:54.463475+06:30    2  mix       oa  b1e64a87      49024   \n", "46 2024-04-22 16:35:18.372964+06:30    2  mix       oa  7ba5169a      49024   \n", "47 2024-04-22 16:35:18.373400+06:30    2  mix       oa  7ba5169a      49024   \n", "48 2024-04-06 10:39:40.422944+06:30    2  mix       oa  d60db7dd      49024   \n", "49 2024-04-19 10:18:54.464147+06:30    2  mix       oa  b1e64a87      49024   \n", "50 2024-04-06 10:39:40.423209+06:30    2  mix       oa  d60db7dd      49024   \n", "51 2024-04-22 16:35:18.373629+06:30    2  mix       oa  7ba5169a      49024   \n", "\n", "    product_id  product_qty_sum  balance      product_d_name  jia_yi_id   \n", "0        76270            -12.3    730.5   <PERSON><PERSON> (NO-1)      49019  \\\n", "1        76270           -150.0   -324.5   <PERSON><PERSON> (NO-1)      49020   \n", "2        76270            -70.0    127.7   <PERSON><PERSON> (NO-1)      49021   \n", "3        76271             -8.0    -25.3   <PERSON><PERSON> (NO-2)      49019   \n", "4        76271             -2.1    -89.0   <PERSON><PERSON> (NO-2)      49020   \n", "5        76271             -1.5    692.3   <PERSON><PERSON> (NO-2)      49021   \n", "6        76272            -13.6    -13.6   <PERSON><PERSON> (NO-3)      49019   \n", "7        76272             -1.2    -61.8   <PERSON><PERSON> (NO-3)      49021   \n", "8        76273           -150.0   -837.9   <PERSON><PERSON> (NO-4)      49019   \n", "9        76273             -0.3   -306.0   <PERSON><PERSON> (NO-4)      49020   \n", "10       76273            -98.4   -392.5   <PERSON><PERSON> (NO-4)      49021   \n", "11       76274           -271.1   -894.1   <PERSON><PERSON> (NO-5)      49019   \n", "12       76274             -3.0   -552.6   <PERSON><PERSON> (NO-5)      49020   \n", "13       76274            -10.1    377.8   <PERSON><PERSON> (NO-5)      49021   \n", "14       76275            -16.8    -28.9   <PERSON><PERSON> (NO-6)      49019   \n", "15       76275             -1.2    -12.2   <PERSON><PERSON> (NO-6)      49020   \n", "16       76275            -36.4    -54.1   <PERSON><PERSON> (NO-6)      49021   \n", "17       76276            -10.2    -28.4   <PERSON><PERSON> (NO-7)      49019   \n", "18       76276            -68.0    -68.0   <PERSON><PERSON> (NO-7)      49021   \n", "19       76277            -10.8   -134.3   <PERSON><PERSON> (NO-8)      49019   \n", "20       76277             -5.6     -5.6   <PERSON><PERSON> (NO-8)      49020   \n", "21       76277            -26.0   -199.2   <PERSON><PERSON> (NO-8)      49021   \n", "22       76278            -21.0   -193.2   <PERSON><PERSON> (NO-9)      49019   \n", "23       76278             -8.0    -19.1   <PERSON><PERSON> (NO-9)      49020   \n", "24       76278            -35.1    578.6   <PERSON><PERSON> (NO-9)      49021   \n", "25       76279            -40.0   -140.9  <PERSON><PERSON> (NO-10)      49019   \n", "26       76279            -12.0    -79.5  <PERSON><PERSON> (NO-10)      49020   \n", "27       76279            -19.8   -768.1  <PERSON><PERSON> (NO-10)      49021   \n", "28       76280            -64.1   -216.7  <PERSON><PERSON> (NO-11)      49019   \n", "29       76280             -2.8  -1434.1  <PERSON><PERSON> (NO-11)      49020   \n", "30       76280           -509.6   -263.0  <PERSON><PERSON> (NO-11)      49021   \n", "31       76281            -15.0   -284.4  <PERSON><PERSON> (NO-12)      49019   \n", "32       76281            -11.2   -203.5  <PERSON><PERSON> (NO-12)      49020   \n", "33       76281             -4.9   -114.5  <PERSON><PERSON> (NO-12)      49021   \n", "34       77976            -86.6    653.6       Captain  LS02      49023   \n", "35       77976            -53.0   -332.3       Captain  LS02      49024   \n", "36       77979            -55.6    -55.6       Captain  RS05      49023   \n", "37       77980            -11.2    -11.2       Captain  US06      49023   \n", "38       77981            -13.5    -54.0       Captain  TT07      49023   \n", "39       77981           -209.8    406.6       Captain  TT07      49024   \n", "40       77982           -132.4   -132.4      Captain   MS08      49023   \n", "41       77982            -13.0   -223.8      Captain   MS08      49024   \n", "42       77983           -143.9    424.2      Captain   KS09      49023   \n", "43       77983            -89.9    157.0      Captain   KS09      49024   \n", "44       77985            -64.7    -64.7      Captain   VI11      49023   \n", "45       77985            -15.7    -54.0      Captain   VI11      49024   \n", "46       77975            -19.9    303.3       Captain  FT01      49024   \n", "47       77978            -16.6    -16.6       Captain  MT04      49024   \n", "48       77984           -502.8    -11.1      Captain   LT10      49024   \n", "49       77986           -133.2    -19.6      Captain   XT12      49024   \n", "50       77988            -32.4   -129.4      Captain   RT14      49024   \n", "51       77989            -10.9    989.1      Captain   ST15      49024   \n", "\n", "                                  jia_yi_mmname  \n", "0    ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (Beger)  \n", "1   ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (Beger)  \n", "2    ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "3    ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "4   ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (<PERSON><PERSON>)  \n", "5    ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "6    ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "7    ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "8    ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "9   ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (<PERSON><PERSON>)  \n", "10   ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "11   ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "12  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (<PERSON><PERSON>)  \n", "13   ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "14   ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "15  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (<PERSON><PERSON>)  \n", "16   ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "17   ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "18   ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "19   ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "20  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (<PERSON><PERSON>)  \n", "21   ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "22   ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "23  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (<PERSON><PERSON>)  \n", "24   ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "25   ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "26  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (<PERSON><PERSON>)  \n", "27   ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "28   ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "29  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (<PERSON><PERSON>)  \n", "30   ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "31   ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "32  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၂)​ရောင်  (<PERSON><PERSON>)  \n", "33   ဆေးဖျော်စက် ဆိုင်(၃) ဆေး(၁၂)​ရောင် (<PERSON><PERSON>)  \n", "34  ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)  \n", "35  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)  \n", "36  ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)  \n", "37  ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)  \n", "38  ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)  \n", "39  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)  \n", "40  ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)  \n", "41  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)  \n", "42  ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)  \n", "43  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)  \n", "44  ဆေးဖျော်စက် ဆိုင်(၁) ဆေး(၁၆)ရောင် (Captain)  \n", "45  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)  \n", "46  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)  \n", "47  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)  \n", "48  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)  \n", "49  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)  \n", "50  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)  \n", "51  ဆေးဖျော်စက် ဆိုင်(၂) ဆေး(၁၆)ရောင် (Captain)  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sys\n", "# sys.path.append('../../../../../app/')\n", "sys.path.append('../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "# from src.shwethe_color.database import get_session\n", "from src.config.shwethe_color.database import get_session\n", "from src.shwethe_color.models.model import color_insert_tb, color_insert_oa_tb\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "\n", "\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('display.max_columns', None)\n", "pd.options.display.float_format = '{:.1f}'.format\n", "\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def dataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "\n", "daySet = 30\n", "\n", "def get_color_insert_tb():\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        # GET TODAY DATA\n", "        week_ago = today - DT.<PERSON><PERSON><PERSON>(days=daySet)\n", "\n", "        # First, create a subquery for SuccessGroups\n", "        success_groups_subquery = select(color_insert_tb.group_id).where(\n", "            color_insert_tb.status[(\"status_big_table\")].as_string() == \"success\"\n", "        ).subquery()\n", "        # Now, create the main query\n", "        query = select(color_insert_tb).where(\n", "            color_insert_tb.group_id.in_(success_groups_subquery),\n", "            color_insert_tb.datetime >= week_ago\n", "        ).order_by(color_insert_tb.datetime.desc())\n", "        # Execute the query\n", "        heroesPersonal = db.exec(query).all()\n", "\n", "        mergeDF = dataframe(heroesPersonal, to_dict=False)\n", "        # mergeDF = mergeDF[['datetime', 'fen', 'device_id', 'group_id', 'product_id', 'product_qty', 'product_qty_sum']]\n", "        mergeDF = mergeDF[['datetime', 'jia_yi_fang_a', 'jia_yi_fang_b', 'lei_a', 'fen', 'group_id', 'device_id', 'product_id', 'product_qty_sum']]\n", "        # mergeDF = mergeDF.head(60)\n", "        # mergeDF = mergeDF[60:120]\n", "    return mergeDF\n", "\n", "\n", "def calOne():\n", "    df = get_color_insert_tb()\n", "\n", "    # Create a combination key of jia_yi_fang_a and group_id where lei_a is 31\n", "    code_combination = df[df['lei_a'] == 31][['jia_yi_fang_a', 'group_id']]\n", "    code_combination['key'] = code_combination['jia_yi_fang_a'].astype(str) + '-' + code_combination['group_id']\n", "    code_set = set(code_combination['key'])\n", "    # Assign 'type_lei' based on conditions\n", "    df['type_lei'] = 'oa'  # Default value\n", "    df.loc[df['lei_a'] == 31, 'type_lei'] = 'code'  # Set 'code' where lei_a is 31\n", "    # Set 'base' where combination of jia_yi_fang_a and group_id matches the code_set and lei_a is not 31\n", "    df['combination_key'] = df['jia_yi_fang_a'].astype(str) + '-' + df['group_id']\n", "    df.loc[df['combination_key'].isin(code_set) & (df['lei_a'] != 31), 'type_lei'] = 'base'\n", "    # Drop the temporary combination_key column\n", "    df.drop('combination_key', axis=1, inplace=True)\n", "\n", "    # Filter rows where 'type_lei' is either 'code' or 'base'\n", "    df = df[df['type_lei'].isin(['oa'])]\n", "\n", "    df['type'] = 'mix'\n", "\n", "    df = df[['datetime', 'fen', 'type', 'type_lei', 'group_id', 'device_id', 'product_id', 'product_qty_sum']]\n", "\n", "    df['product_qty_sum'] = -df['product_qty_sum']\n", "\n", "    return df\n", "\n", "\n", "def get_color_insert_oa_tb():\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        # GET TODAY DATA\n", "        week_ago = today - DT.<PERSON><PERSON><PERSON>(days=daySet)\n", "\n", "        # First, create a subquery for SuccessGroups\n", "        success_groups_subquery = select(color_insert_oa_tb.group_id).where(\n", "            color_insert_oa_tb.status[(\"status_big_table\")].as_string() == \"success\"\n", "        ).subquery()\n", "        # Now, create the main query\n", "        query = select(color_insert_oa_tb).where(\n", "            color_insert_oa_tb.group_id.in_(success_groups_subquery),\n", "            color_insert_oa_tb.datetime >= week_ago\n", "        ).order_by(color_insert_oa_tb.datetime.desc())\n", "        # Execute the query\n", "        heroesPersonal = db.exec(query).all()\n", "\n", "        mergeDF = dataframe(heroesPersonal, to_dict=False)\n", "\n", "        mergeDF = mergeDF[mergeDF['product_qty'].isin([-1000])]\n", "        mergeDF = mergeDF[['datetime', 'fen', 'group_id', 'device_id', 'product_id']]\n", "        mergeDF['type'] = 'add'\n", "        mergeDF['product_qty_sum'] = 1000\n", "        mergeDF = mergeDF[['datetime', 'fen', 'type', 'group_id', 'device_id', 'product_id', 'product_qty_sum']]\n", "\n", "    return mergeDF\n", "\n", "\n", "def joinDF():\n", "    color_insert = calOne()\n", "    color_insert_oa = get_color_insert_oa_tb()\n", "\n", "    # Concatenate the two DataFrames\n", "    concatenated_df = pd.concat([color_insert, color_insert_oa], ignore_index=True)\n", "\n", "    # Convert the 'datetime_column' to datetime format if it's not already\n", "    concatenated_df['datetime'] = pd.to_datetime(concatenated_df['datetime'])\n", "\n", "    # Sort the DataFrame in descending order based on the 'datetime_column'\n", "    concatenated_df = concatenated_df.sort_values(by='datetime', ascending=False)\n", "    \n", "    # Initialize a dictionary to store the balances for each product_id and device_id combination\n", "    balances = defaultdict(dict)\n", "\n", "    # Iterate over the grouped DataFrame by device_id\n", "    for device_id, group in concatenated_df.groupby('device_id'):\n", "        # Initialize previous_transaction_type for each group\n", "        previous_transaction_type = None\n", "\n", "        # Iterate over the rows starting from the tail within each group\n", "        for index, row in group[::-1].iterrows():\n", "            product_id = row['product_id']\n", "            product_qty_sum = row['product_qty_sum']\n", "            transaction_type = row['type']\n", "\n", "            if product_id not in balances[device_id]:\n", "                # A. assign default product_qty_sum value if this product_id has no previous row with the same product_id\n", "                balance = product_qty_sum\n", "            else:\n", "                previous_balance = balances[device_id][product_id]\n", "\n", "                if transaction_type == 'add' and previous_transaction_type == 'mix':\n", "                    balance = product_qty_sum + previous_balance\n", "                if transaction_type == 'add' and previous_transaction_type == 'add':\n", "                    balance = product_qty_sum + previous_balance\n", "                if transaction_type == 'mix' and previous_transaction_type == 'mix':\n", "                    balance = previous_balance + product_qty_sum\n", "                if transaction_type == 'mix' and previous_transaction_type == 'add':\n", "                    balance = product_qty_sum + previous_balance\n", "\n", "            # Store the current transaction type for the next iteration\n", "            previous_transaction_type = transaction_type\n", "\n", "            # Update the balances dictionary with the current balance for the product_id and device_id combination\n", "            balances[device_id][product_id] = balance\n", "\n", "            # Assign the calculated balance to the 'balance' column\n", "            concatenated_df.at[index, 'balance'] = balance\n", "\n", "    # Filter rows where 'type' equals 'mix'\n", "    mix_df = concatenated_df[concatenated_df['type'] == 'mix']\n", "\n", "    # Group by 'device_id' and 'product_id' and get the index of the row with the maximum datetime within each group\n", "    last_datetime_indices = mix_df.groupby(['device_id', 'product_id'])['datetime'].idxmax()\n", "\n", "    # Extract the rows corresponding to the last datetime within each group\n", "    last_datetime_rows = concatenated_df.loc[last_datetime_indices]\n", "\n", "    return last_datetime_rows\n", "\n", "\n", "def add_name():\n", "    _joinDF = joinDF()\n", "\n", "    # # Extract unique product_ids from _joinDF\n", "    unique_product_ids = _joinDF['product_id'].unique()\n", "\n", "    result_data = []\n", "    for product_id_a in unique_product_ids:\n", "        url = f\"{mongodb_data_api}/api/v1/search/product_id?ID={product_id_a}\"\n", "        response = requests.get(url)\n", "        result = response.json()\n", "        result_data.extend(result)\n", "\n", "    # Create DataFrame from result_data\n", "    result_df = pd.DataFrame(result_data)\n", "    result_df = result_df[['product_id', 'product_d_name']]\n", "\n", "    # # Merge _joinDF with result_df on 'product_id'\n", "    merged_df = pd.merge(_joinDF, result_df, left_on='product_id', right_on='product_id')\n", "\n", "\n", "    # Extract unique device_ids from merged_df\n", "    unique_device_ids = _joinDF['device_id'].unique()\n", "\n", "    result_data = []\n", "    for device_id in unique_device_ids:\n", "        url = f\"{mongodb_data_api}/api/v1/search/jia_yi_name/in?id={device_id}\"\n", "        response = requests.get(url)\n", "        result = response.json()\n", "        result_data.extend(result)\n", "\n", "    # Create DataFrame from result_data\n", "    result_df = pd.DataFrame(result_data)\n", "    result_df = result_df[['jia_yi_id', 'jia_yi_mmname']]\n", "\n", "    # # Merge merged_df with result_df on 'device_id'\n", "    merged_df2 = pd.merge(merged_df, result_df, left_on='device_id', right_on='jia_yi_id', how='left')\n", "\n", "    return merged_df2\n", "\n", "\n", "# display(calOne())\n", "# display(get_color_insert_oa_tb())\n", "display(add_name())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}