from sqlmodel import Session, select
from typing import List, Dict
from sqlalchemy import func,and_, bindparam,desc, distinct,or_,cast, String,func, text, delete, inspect
from sqlalchemy.dialects.postgresql import JSONB
import uuid
from datetime import datetime
# from src.shwethe_cement_mixer.models import (
# jia_yi_fang,product,find_new_goods,name_storage_tb,product_price_tb)
from src.shwethe_cement_mixer.models.model import cement_product_produce, cement_product_mix_ratio, cement_insert, cement_machine, employee_name, employee_insert
# from src.shwethe_cement_mixer.schemas.schemas import (
#     color_cai_liao_tb_post,
#     cement_insert_post
# )
from fastapi import HTTPException, status
from src.config.shwethe_cement_mixer.database import get_session
from fastapi import APIRouter, Depends, Query, Body

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session, Load, defer
import requests
import pandas as pd
from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api
import json
import random
from sqlalchemy import update
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy.exc import SQLAlchemyError
from helper import generate_id
import datetime as DT
from datetime import date, timedelta
import re


def random_groupID():
    import datetime
    import random

    # Get today's date in YYYYMMDD format
    today_date = datetime.datetime.now().strftime("%Y%m%d")

    # Generate a random 6-digit number
    random_number = random.randint(1000, 9999)

    # Combine the date, dead number, and random number
    result = f"{today_date}000{random_number}"

    return result


def getCarAtive100(db: Session = Depends(get_session)):
    # try:
    #     heroesPersonal = db.exec(select(item_insert)).all()
    #     records = [i.dict() for i in heroesPersonal]   
    #     df = pd.DataFrame.from_records(records).fillna(0)

    #     mergeDF = df.to_dict("records")
    # except:
    #     mergeDF = []
    return "mergeDF"


def getProductProduceItem100(item: str, db: Session = Depends(get_session)):
    # print(item)
    # print(type(item))

    try:
        item = int(item)
        print(item)
        print(type(item))

        # url = 'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/product_list_id'
        # url = Arter_api + 'jia_yi_name_list_id'
        # body_raw = {"data_api": [{"jia_yi_id": 36557}]}
        body_raw = {"data_api": [{"product_id": item}]}
        # body_raw = {"data_api": df}
        df2 = requests.get(url=url, json=body_raw)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')

        print(df2)
        # print(df2)
    except:
        print('The provided value is not an integer')
        print(item)
        print(type(item))

        # url = f'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={item}'
        url = f'{mongodb_data_api}/api/v2/search/product_idname?ID={item}'
        df2 = requests.get(url=url)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')

        print(df2)
        # print(df2)
    return df2


def getMaterialItem100(item: str, db: Session = Depends(get_session)):
    try:
        # Attempt to parse `item` as an integer
        item = int(item)
        print(item)
        print(type(item))

        # Fetch material data based on product ID
        url = mongodb_data_api + '/api/v2/search/product_list_id'
        body_raw = {"data_api": [{"product_id": item}]}
        response = requests.get(url=url, json=body_raw)
        material_data = response.json()

    except ValueError:
        # Handle `item` as a string if it's not an integer
        print('The provided value is not an integer')
        url = f'{mongodb_data_api}/api/v2/search/product_idname?ID={item}'
        response = requests.get(url=url)
        material_data = response.json()

    # Convert to DataFrame and back to dict for consistency
    material_data = pd.DataFrame(material_data).to_dict('records')

    if material_data:
        # Fetch the price for the first (or single) product ID
        product_id = material_data[0].get('product_id')
        if product_id:
            price_url = f"{mongodb_data_api}/api/v2/search/price?ID={product_id}"
            price_response = requests.get(price_url)
            price = price_response.json() if price_response.status_code == 200 else None

            # Add the price to the material data
            material_data[0]['price'] = price

    return material_data


# def getMaterialsByProductID100(product_id: int, product_name: str, db: Session = Depends(get_session)):
#     # Step 1: Query the cement_product_mix_ratio table using the product_id
#     mix_ratios = db.exec(select(cement_product_mix_ratio).where(cement_product_mix_ratio.product_id == product_id)).all()
    
#     search_type = "product_id"
#     search_value = product_id
    
#     # If no results are found, switch to searching by product_name
#     if not mix_ratios:
#         product_ids = db.exec(select(cement_product_produce.product_id).where(cement_product_produce.product_name == product_name)).all()
#         if not product_ids:
#             raise HTTPException(status_code=404, detail=f"No products found with product_name: {product_name}")
        
#         mix_ratios = db.exec(select(cement_product_mix_ratio).where(cement_product_mix_ratio.product_id.in_(product_ids))).all()
#         if not mix_ratios:
#             raise HTTPException(status_code=404, detail=f"No materials found for product_name: {product_name}")
        
#         search_type = "product_name"
#         search_value = product_name
    
#     # Convert mix_ratios to a pandas DataFrame and drop duplicates
#     df = pd.DataFrame([i.dict() for i in mix_ratios])
#     df_unique = df.drop_duplicates(subset='material_id')
#     material_ids = df_unique['material_id'].tolist()
    
#     # Fetch material names
#     url = f"{mongodb_data_api}/api/v2/search/product_list_id"
#     body_raw = {"data_api": [{"product_id": mid} for mid in material_ids]}
#     info = requests.get(url=url, json=body_raw).json()
#     info = pd.DataFrame(info).to_dict('records')
    
#     # Fetch prices for each material_id
#     price_url_template = f"{mongodb_data_api}/api/v2/search/price?ID={{}}"
#     prices = {}
#     for mid in material_ids:
#         price_url = price_url_template.format(mid)
#         price_response = requests.get(price_url)
#         prices[mid] = price_response.json() if price_response.status_code == 200 else None
    
#     # Add prices to the info dictionary
#     for item in info:
#         item['price'] = prices.get(item['product_id'], None)
    
#     return {
#         "search_type": search_type,
#         "search_value": search_value,
#         "output": info
#     }
def getMaterialsByProductID100(product_id: int, product_name: str, db: Session = Depends(get_session)):
    # Step 0: Clean the product_name by removing text after the first space and within parentheses
    cleaned_product_name = re.sub(r'\s.*', '', product_name).strip()
    
    # Step 1: Query the cement_product_mix_ratio table using the product_id
    mix_ratios = db.exec(select(cement_product_mix_ratio).where(cement_product_mix_ratio.product_id == product_id)).all()
    
    search_type = "product_id"
    search_value = product_id
    
    # If no results are found, switch to searching by product_name using similar text
    if not mix_ratios:
        # Use ilike for similar text search with cleaned product_name
        product_ids = db.exec(select(cement_product_produce.product_id).where(cement_product_produce.product_name.ilike(f"%{cleaned_product_name}%"))).all()
        if not product_ids:
            raise HTTPException(status_code=404, detail=f"No products found with similar product_name: {cleaned_product_name}")
        
        mix_ratios = db.exec(select(cement_product_mix_ratio).where(cement_product_mix_ratio.product_id.in_(product_ids))).all()
        if not mix_ratios:
            raise HTTPException(status_code=404, detail=f"No materials found for similar product_name: {cleaned_product_name}")
        
        search_type = "product_name"
        search_value = product_name
    
    # Convert mix_ratios to a pandas DataFrame and drop duplicates
    df = pd.DataFrame([i.dict() for i in mix_ratios])
    df_unique = df.drop_duplicates(subset='material_id')
    material_ids = df_unique['material_id'].tolist()
    
    # Fetch material names
    url = f"{mongodb_data_api}/api/v2/search/product_list_id"
    body_raw = {"data_api": [{"product_id": mid} for mid in material_ids]}
    info = requests.get(url=url, json=body_raw).json()
    info = pd.DataFrame(info).to_dict('records')
    
    # Fetch prices for each material_id
    price_url_template = f"{mongodb_data_api}/api/v2/search/price?ID={{}}"
    prices = {}
    for mid in material_ids:
        price_url = price_url_template.format(mid)
        price_response = requests.get(price_url)
        prices[mid] = price_response.json() if price_response.status_code == 200 else None
    
    # Add prices to the info dictionary
    for item in info:
        item['price'] = prices.get(item['product_id'], None)
    
    return {
        "search_type": search_type,
        "search_value": search_value,
        "clean_value": cleaned_product_name,
        "output": info
    }



# def insert_cement100(list_data: Dict, db: Session = Depends(get_session)):
#     # print("list_data", list_data)
#     # Extract the necessary data from the input dictionary
#     product_id = list_data["get_product_id"]
#     product_name = list_data["get_product_name"]
#     list_select_materials = list_data["list_select_materials"]
#     form_insert_send = list_data["form_insert_send"]
#     print("list_dataaaaaaaaaaaaaaa", list_data["get_machine_id"])

#     if list_data["get_machine_id"]:
#         # Query the cement_machine table to check if a record with the given machine_id exists
#         existing_machine = db.exec(select(cement_machine).where(cement_machine.machine_id == list_data["get_machine_id"])).first()
#         if existing_machine:
#             # If the record exists, update it
#             existing_machine.machine = list_data["show_machine_name"] 
#             db.add(existing_machine)
#             db.commit()
#             db.refresh(existing_machine)
#         else:
#             # If the record does not exist, insert a new record
#             new_machine = cement_machine(
#                 machine_id=list_data["get_machine_id"],
#                 machine=list_data["show_machine_name"] 
#             )
#             db.add(new_machine)
#             db.commit()
#             db.refresh(new_machine)
    

#     # Query the cement_product_produce table to check if a record with the given product_id exists
#     existing_product = db.exec(select(cement_product_produce).where(cement_product_produce.product_id == product_id)).first()
    
#     if existing_product:
#         # If the record exists, update it
#         existing_product.product_name = product_name
#         db.add(existing_product)
#         db.commit()
#         db.refresh(existing_product)
#     else:
#         # If the record does not exist, insert a new record
#         new_product = cement_product_produce(
#             product_id=product_id,
#             product_name=product_name
#         )
#         db.add(new_product)
#         db.commit()
#         db.refresh(new_product)
    

#     # Process the cement_product_mix_ratio table
#     # Delete all existing records for the given product_id
#     db.execute(delete(cement_product_mix_ratio).where(cement_product_mix_ratio.product_id == product_id))
#     db.commit()
    
#     # Insert the new records
#     for material in list_select_materials:
#         material_id = material["product_id"]
        
#         new_mix_ratio = cement_product_mix_ratio(
#             product_id=product_id,
#             material_id=material_id,
#         )
#         db.add(new_mix_ratio)
    
#     db.commit()


#     # Insert data into the cement_insert table
#     group_id = "CEMENT" + generate_id()
#     first_record = True  # Flag to identify the first record

#     for item in form_insert_send:
#         new_insert = cement_insert(
#             group_id=item.get("group_id", group_id),  # Assuming group_id is not provided in the input
#             jia_yi_fang_a=item.get("jia_yi_fang_a", 0) if item.get("jia_yi_fang_a", 0) else 0,  # Replace Ellipsis with 0
#             jia_yi_fang_b=item.get("jia_yi_fang_b", 0) if item.get("jia_yi_fang_b", 0) else 0,  # Replace Ellipsis with 0
#             lei_a=item.get("lei_a", 0),  # Replace Ellipsis with 0
#             lei_b=item.get("lei_b", 0),  # Replace Ellipsis with 0
#             machine_id=item.get("machine_id", 0) if item.get("machine_id", 0) else 0,  # Replace Ellipsis with 0
#             product_id=item.get("product_id", 0),  # Replace Ellipsis with 0
#             product_all_qty=item.get("product_all_qty", 0),  # Replace Ellipsis with 0
#             product_price=item.get("product_price", 0),  # Replace Ellipsis with 0
#             bi_zhi=item.get("bi_zhi", 138),  # Assuming bi_zhi is not provided in the input
#             fen=item.get("fen", 0),  # Replace Ellipsis with 0
#             detail_json=list_data if first_record else {},  # Use list_data only for the first record
#             status=300 if first_record else item.get("status", 0),  # Assuming status is not provided in the input
#             sum_price=item.get("sum_price", 0),  # Replace Ellipsis with 0
#             sum_all_price=item.get("sum_all_price", 0),  # Replace Ellipsis with 0
#             price_a=item.get("price_a", 0),  # Replace Ellipsis with 0
#             price_b=item.get("price_b", 0),  # Replace Ellipsis with 0
#             user_id=item.get("user_id", 0)  # Replace Ellipsis with 0
#         )
#         db.add(new_insert)

#         first_record = False  # Set the flag to False after the first iteration

#     db.commit()
    
    
#     return {"message": "Data inserted/updated successfully"}
def insert_cement100(list_data: Dict, db: Session = Depends(get_session)):
    try:
        # Start a transaction
        db.begin()

        # Extract the necessary data from the input dictionary
        product_id = list_data["get_product_id"]
        product_name = list_data["get_product_name"]
        list_select_materials = list_data["list_select_materials"]
        form_insert_send = list_data["form_insert_send"]
        print("list_dataaaaaaaaaaaaaaa", list_data["get_machine_id"])

        if list_data["get_machine_id"]:
            # Query the cement_machine table to check if a record with the given machine_id exists
            existing_machine = db.exec(select(cement_machine).where(cement_machine.machine_id == list_data["get_machine_id"])).first()
            if existing_machine:
                # If the record exists, update it
                existing_machine.machine = list_data["show_machine_name"] 
                existing_machine.fen = list_data["form_insert_send"][0]['fen'] 
                db.add(existing_machine)
            else:
                # If the record does not exist, insert a new record
                new_machine = cement_machine(
                    machine_id=list_data["get_machine_id"],
                    machine=list_data["show_machine_name"],
                    fen=list_data["form_insert_send"][0]['fen'] 
                )
                db.add(new_machine)

        # Query the cement_product_produce table to check if a record with the given product_id exists
        existing_product = db.exec(select(cement_product_produce).where(cement_product_produce.product_id == product_id)).first()
        
        if existing_product:
            # If the record exists, update it
            existing_product.product_name = product_name
            db.add(existing_product)
        else:
            # If the record does not exist, insert a new record
            new_product = cement_product_produce(
                product_id=product_id,
                product_name=product_name
            )
            db.add(new_product)

        # Process the cement_product_mix_ratio table
        # Delete all existing records for the given product_id
        db.execute(delete(cement_product_mix_ratio).where(cement_product_mix_ratio.product_id == product_id))
        
        # Insert the new records
        for material in list_select_materials:
            material_id = material["product_id"]
            
            new_mix_ratio = cement_product_mix_ratio(
                product_id=product_id,
                material_id=material_id,
            )
            db.add(new_mix_ratio)

        # Insert data into the cement_insert table
        group_id = "CEMENT" + generate_id()
        first_record = True  # Flag to identify the first record

        for item in form_insert_send:
            new_insert = cement_insert(
                group_id=item.get("group_id", group_id),  # Assuming group_id is not provided in the input
                jia_yi_fang_a=item.get("jia_yi_fang_a", 0) if item.get("jia_yi_fang_a", 0) else 0,  # Replace Ellipsis with 0
                jia_yi_fang_b=item.get("jia_yi_fang_b", 0) if item.get("jia_yi_fang_b", 0) else 0,  # Replace Ellipsis with 0
                lei_a=item.get("lei_a", 0),  # Replace Ellipsis with 0
                lei_b=item.get("lei_b", 0),  # Replace Ellipsis with 0
                machine_id=item.get("machine_id", 0) if item.get("machine_id", 0) else 0,  # Replace Ellipsis with 0
                product_id=item.get("product_id", 0),  # Replace Ellipsis with 0
                product_all_qty=item.get("product_all_qty", 0),  # Replace Ellipsis with 0
                product_price=item.get("product_price", 0),  # Replace Ellipsis with 0
                bi_zhi=item.get("bi_zhi", 138),  # Assuming bi_zhi is not provided in the input
                fen=item.get("fen", 0),  # Replace Ellipsis with 0
                detail_json=list_data if first_record else {},  # Use list_data only for the first record
                status=300 if first_record else item.get("status", 0),  # Assuming status is not provided in the input
                sum_price=item.get("sum_price", 0),  # Replace Ellipsis with 0
                sum_all_price=item.get("sum_all_price", 0),  # Replace Ellipsis with 0
                price_a=item.get("price_a", 0),  # Replace Ellipsis with 0
                price_b=item.get("price_b", 0),  # Replace Ellipsis with 0
                user_id=item.get("user_id", 0)  # Replace Ellipsis with 0
            )
            db.add(new_insert)

            first_record = False  # Set the flag to False after the first iteration

        # If everything is successful, commit the transaction
        db.commit()

        return {"message": "Data inserted/updated successfully"}

    except SQLAlchemyError as e:
        # If an error occurs, rollback the transaction
        db.rollback()
        raise e  # Re-raise the exception to propagate it



def getQrcodeMachine100(item: str, db: Session = Depends(get_session)):
    # print(item)
    # print(type(item))

    try:
        item = int(item)
        print(item)
        print(type(item))

        # url = 'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        # url = mongodb_data_api + '/api/v2/search/product_list_id'
        body_raw = {"data_api": [{"jia_yi_id": item}]}
        # body_raw = {"data_api": [{"product_id": item}]}
        # body_raw = {"data_api": df}
        df2 = requests.get(url=url, json=body_raw)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')

        print(df2)
        # print(df2)
    except:
        print('The provided value is not an integer')
        print(item)
        print(type(item))

        # url = f'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={item}'
        url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={item}'
        # url = f'{mongodb_data_api}/api/v2/search/product_idname?ID={item}'
        df2 = requests.get(url=url)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')

        print(df2)
        # print(df2)
    return df2


def get_list_produce100(fen: int, page: int = 1, per_page: int = 2, db: Session = Depends(get_session)):

    import datetime as DT
    today = DT.date.today()

    offset = (page - 1) * per_page
    
    a1 = (
        db.query(cement_insert)
        .filter(cement_insert.fen == fen, cement_insert.detail_json != {})
        # .where(cement_insert.datetime > week_ago_2)
        .order_by(cement_insert.datetime.desc())
        .offset(offset)
        .limit(per_page)
        # .options(*[defer(column) for column in columns_to_exclude])
        .all()
    )

    return a1


def update_status_cement_insert100(record_id: str, status: int, db: Session):
    try:
        # Fetch the record
        result = db.query(cement_insert).filter(cement_insert.record_id == record_id).first()

        if result:
            # Update the status field
            # result.status.update({"status_big_table": status})
            result.status = status

            # Explicitly mark the JSON field as modified
            flag_modified(result, "status")

            # Commit the changes
            db.commit()
            db.refresh(result)
            return "Success"

    except SQLAlchemyError as e:
        db.rollback()
        print(f"Error occurred: {e}")
        # Handle or re-raise the error as appropriate for your application

    return None


# def get_list_produce_web100(db: Session = Depends(get_session)):
#     import datetime as DT
#     today = DT.date.today()
#     ninety_days_ago = today - DT.timedelta(days=90)

#     a1 = (
#         db.query(cement_insert)
#         .filter(
#             cement_insert.detail_json != {},
#             cement_insert.datetime >= ninety_days_ago
#         )
#         .order_by(cement_insert.datetime.desc())
#         .all()
#     )

#     return a1
# def get_list_produce_web100(db: Session = Depends(get_session)):
#     today = date.today()
#     ninety_days_ago = today - timedelta(days=90)

#     # Extract fields from JSONB
#     show_output_product = func.jsonb_extract_path_text(cement_insert.detail_json, 'show_output_product')
#     list_select_materials_length = func.jsonb_array_length(func.jsonb_extract_path(cement_insert.detail_json, 'list_select_materials'))

#     # Query with new columns
#     a1 = (
#         db.query(
#             cement_insert,
#             show_output_product.label('show_output_product'),
#             list_select_materials_length.label('list_select_materials')
#         )
#         .filter(
#             cement_insert.detail_json != {},
#             cement_insert.datetime >= ninety_days_ago
#         )
#         .order_by(cement_insert.datetime.desc())
#         .all()
#     )

#     return a1
def get_list_produce_web100(db: Session = Depends(get_session)):
    today = date.today()
    ninety_days_ago = today - timedelta(days=10)

    # Inspect the table to get all columns
    inspector = inspect(cement_insert)
    all_columns = inspector.columns.keys()

    # Exclude the 'detail_json', 'jia_yi_fang_a', and 'jia_yi_fang_b' columns
    columns_to_select = [col for col in all_columns if col not in ['detail_json', 'jia_yi_fang_a', 'jia_yi_fang_b', 'group_id', 'lei_a', 'lei_b', 'bi_zhi']]

    # Extract fields from JSONB
    show_output_product = func.jsonb_extract_path_text(cement_insert.detail_json, 'show_output_product')
    list_select_materials_length = func.jsonb_array_length(func.jsonb_extract_path(cement_insert.detail_json, 'list_select_materials'))

    # Build the query with all columns except 'detail_json', 'jia_yi_fang_a', and 'jia_yi_fang_b' and add new columns
    query = (
        select(
            [getattr(cement_insert, col) for col in columns_to_select] +
            [show_output_product.label('show_output_product'),
             list_select_materials_length.label('list_select_materials')]
        )
        .where(
            cement_insert.detail_json != {},
            cement_insert.datetime >= ninety_days_ago
        )
        .order_by(cement_insert.datetime.desc())
    )

    # Execute the query
    result = db.execute(query).all()

    return result


def get_materials_list_web100(record_id: str, db: Session = Depends(get_session)):

    a1 = (
        db.query(cement_insert.detail_json)
        .filter(
            cement_insert.record_id == record_id,
        )
        .all()
    )

    return a1



def update_pricB100(record_id: str, price_b: int, db: Session):
    try:
        # Fetch the record
        result = db.query(cement_insert).filter(cement_insert.record_id == record_id).first()

        if result:
            # Update the status field
            # result.status.update({"status_big_table": status})
            result.price_b = price_b

            # Explicitly mark the JSON field as modified
            flag_modified(result, "status")

            # Commit the changes
            db.commit()
            db.refresh(result)
            return "Success"

    except SQLAlchemyError as e:
        db.rollback()
        print(f"Error occurred: {e}")
        # Handle or re-raise the error as appropriate for your application

    return None



def send_big_table100(db: Session = Depends(get_session)):

    from src.Connect.postgresql_nern import postgresql_shwethe_cement_mixer
    import pandas as pd

    # columns_to_exclude = ['detail_json', 'status']
    # a1 = (
    #     db.query(cement_insert)
    #     # .filter(cement_insert.product_qty == 1)
    #     .options(*[defer(column) for column in columns_to_exclude])
    #     .all()
    # )

    columns_to_exclude = ['detail_json', 'status']
    # Constructing the SELECT statement
    # select_statement = f"""
    #     SELECT * 
    #     FROM cement_insert
    #     WHERE datetime >= NOW() - INTERVAL '10 days' 
    # """
    select_statement = """
        WITH SuccessGroups AS (
            SELECT group_id
            FROM cement_insert
            WHERE status = 200
        )
        SELECT *
        FROM cement_insert
        WHERE group_id IN (SELECT group_id FROM SuccessGroups)
              AND datetime >= NOW() - INTERVAL '1 days'
    """

    df_a1 = pd.read_sql(select_statement, postgresql_shwethe_cement_mixer)
    df_a1 = df_a1.drop(columns=columns_to_exclude)
    json_nor = df_a1.to_dict("records")


    # INSERT TO ARTER DATABASE
    aaa = []
    for ioo in json_nor:
        d = {
            'a_id': ioo['auto_id'],
            'b_id': ioo['auto_id'],
            'product_id': ioo['product_id'],
            'product_qty': ioo['product_all_qty'],
            'product_price_a' : ioo['price_a'],
            'product_price_b' : ioo['price_b'],
            'ke_bian': 0,
            'jia_yi_fang_a': ioo['jia_yi_fang_a'],
            'jia_yi_fang_b': ioo['jia_yi_fang_b'],
            'lei_a': ioo['lei_a'],
            'lei_b': ioo['lei_b'],
            'bu_bian': 418,
            'jin_huo_bian': 0,   
            'jin_huo_dang': ioo['group_id'],   
            'ci_bian': 0,
            'u_id': 0,
            'shu_riqi_datetime': str(ioo['datetime']),
            'riqi_datetime': str(ioo['datetime']),
            'che_liang': 0,
            'kind': 11237,
            'product_qtyp': 0,
            'product_qtyn': 0,
            'bi_zhi': ioo['bi_zhi']
            }
        aaa.append(d)
    df1 = pd.DataFrame(aaa) 
    # df1 = df1.to_dict("records")
    mata = {
    'data' : df1.to_dict(orient='records')
    }

    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v2/table/mysql', data= json.dumps(mata))
    url = shwethe_mysql_api + '/api/v1/table/mysql_big_table'
    dd = requests.post(url, data= json.dumps(mata))
    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v1/table/mysql_big_table', data= json.dumps(mata))
    out = dd.status_code
    print(out)

    return out



# def getEmployeeSearch100(fen: int, item: str, db: Session = Depends(get_session)):
#     try:
#         # Attempt to parse `item` as an integer
#         item = int(item)
#         print(item)
#         print(type(item))

#         # Fetch material data based on product ID
#         # url = mongodb_data_api + '/api/v2/search/product_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         body_raw = {"data_api": [{"jia_yi_id": item}]}
#         response = requests.get(url=url, json=body_raw)
#         material_data = response.json()

#     except ValueError:
#         # Handle `item` as a string if it's not an integer
#         print('The provided value is not an integer')
#         url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={item}'
#         response = requests.get(url=url)
#         material_data = response.json()

#     print("material_datamaterial_datamaterial_data", material_data, fen)
 

#     existing_employee = db.exec(select(employee_name).where(employee_name.employee_id == material_data[0]['jia_yi_id'])).first()
#     if existing_employee:
#         # If the record exists, update it
#         existing_employee.employee_show = f"{material_data[0]['jia_yi_id']} | {material_data[0]['jia_yi_idname']} | {material_data[0]['jia_yi_mm_name']}",
#         existing_employee.fen=fen,
#         existing_employee.status = True
#         db.add(existing_employee)
#     else:
#         # If the record does not exist, insert a new record
#         new_product = employee_name(
#             employee_id=material_data[0]['jia_yi_id'],
#             employee_show = f"{material_data[0]['jia_yi_id']} | {material_data[0]['jia_yi_idname']} | {material_data[0]['jia_yi_mm_name']}",
#             fen=fen,
#             status=True
#         )
#         db.add(new_product)
    
#     # If everything is successful, commit the transaction
#     db.commit()

#     return material_data
def getEmployeeSearch100(fen: int, item: str, db: Session = Depends(get_session)):
    try:
        # Attempt to parse `item` as an integer
        item = int(item)
        print(item)
        print(type(item))

        # Fetch material data based on product ID
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        body_raw = {"data_api": [{"jia_yi_id": item}]}
        response = requests.get(url=url, json=body_raw)
        material_data = response.json()

    except ValueError:
        # Handle `item` as a string if it's not an integer
        print('The provided value is not an integer')
        url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={item}'
        response = requests.get(url=url)
        material_data = response.json()

    # Return empty list if no data found
    if not material_data:
        return []

    # Get only the first matching record
    first_record = material_data[0]
    
    # Convert jia_yi_id to integer to remove decimal
    jia_yi_id = int(first_record['jia_yi_id'])
    
    existing_employee = db.exec(select(employee_name).where(employee_name.employee_id == jia_yi_id)).first()
    if existing_employee:
        # If the record exists, update it
        existing_employee.employee_show = f"{jia_yi_id} | {first_record['jia_yi_idname']} | {first_record['jia_yi_mm_name']}"
        existing_employee.fen = fen
        existing_employee.status = True
        db.add(existing_employee)
    else:
        # If the record does not exist, insert a new record
        new_product = employee_name(
            employee_id=jia_yi_id,
            employee_show=f"{jia_yi_id} | {first_record['jia_yi_idname']} | {first_record['jia_yi_mm_name']}",
            fen=fen,
            status=True
        )
        db.add(new_product)
    
    # If everything is successful, commit the transaction
    db.commit()

    # Update the first_record with integer ID before returning
    first_record['jia_yi_id'] = jia_yi_id
    return [first_record]



def getEmplyeeByFen100(fen: int, db: Session = Depends(get_session)):

    # Step 1: Query the cement_product_mix_ratio table using the product_id
    
    em = db.exec(select(employee_name).where(employee_name.fen == fen, employee_name.status == True)).all()

    return em



def insert_employee100(list_data: Dict, db: Session = Depends(get_session)):
    try:
        # Start a transaction
        db.begin()

        # Extract the necessary data from the input dictionary
        product_id = list_data["get_product_id"]
        product_name = list_data["get_product_name"]
        list_select_materials = list_data["list_select_materials"]
        form_insert_send = list_data["form_insert_send"]
        print("list_dataaaaaaaaaaaaaaa", list_data["get_machine_id"])

        # Insert data into the cement_insert table
        group_id = "CEMENT" + generate_id()
        first_record = True  # Flag to identify the first record

        for item in form_insert_send:
            new_insert = cement_insert(
                group_id=item.get("group_id", group_id),  # Assuming group_id is not provided in the input
                jia_yi_fang_a=item.get("jia_yi_fang_a", 0) if item.get("jia_yi_fang_a", 0) else 0,  # Replace Ellipsis with 0
                jia_yi_fang_b=item.get("jia_yi_fang_b", 0) if item.get("jia_yi_fang_b", 0) else 0,  # Replace Ellipsis with 0
                lei_a=item.get("lei_a", 0),  # Replace Ellipsis with 0
                lei_b=item.get("lei_b", 0),  # Replace Ellipsis with 0
                bi_zhi=item.get("bi_zhi", 138),  # Assuming bi_zhi is not provided in the input
                fen=item.get("fen", 0),  # Replace Ellipsis with 0
            )
            db.add(new_insert)

            first_record = False  # Set the flag to False after the first iteration

        # If everything is successful, commit the transaction
        db.commit()

        return {"message": "Data inserted/updated successfully"}

    except SQLAlchemyError as e:
        # If an error occurs, rollback the transaction
        db.rollback()
        raise e  # Re-raise the exception to propagate it




def change_employee_status100(employee_id: int, db: Session):

    existing_employee = db.exec(select(employee_name).where(employee_name.employee_id == employee_id)).first()

    # If the record exists, update it
    existing_employee.status = False
    db.add(existing_employee)
    
    # If everything is successful, commit the transaction
    db.commit()

    return None



def getEmployeeBarcode100(item: str, db: Session = Depends(get_session)):
    try:
        # Convert item to uppercase before querying
        item = item.upper()
        
        # Split the employee_show field and get the second part
        result = db.exec(
            select(employee_name)
            .where(employee_name.employee_show.like(f"% | {item} | %"))
            .where(employee_name.status == True)
        ).first()

        if result is None:
            return None

        return {
            "auto_id": result.auto_id,
            "employee_id": result.employee_id,
            "employee_show": result.employee_show,
            "fen": result.fen,
            "datetime": result.datetime,
            "status": result.status
        }

    except Exception as e:
        print(f'Error: {str(e)}')
        return None

    
def insertEployeeDays100(list_data: List[Dict], db: Session = Depends(get_session)):
    """
    Insert employee days data into employee_insert table and return today's data
    
    Args:
        list_data: List of dictionaries containing employee data
        db: Database session
    """
    try:
        # Start a transaction
        db.begin()

        # Get today's date range
        today = datetime.now().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())

        # Process each employee record
        employee_ids = []
        for item in list_data:
            employee_id = item.get("employee_id", 0)
            employee_ids.append(employee_id)
            
            # Create new employee_insert record with all required fields
            new_insert = employee_insert(
                employee_id=employee_id,
                employee_show=item.get("employee_show", ""),
                fen=item.get("fen", 0),
                jia_yi_fang_a=0,  # Set default value
                jia_yi_fang_b=0,  # Set default value
                lei_a=0,          # Set default value
                lei_b=0           # Set default value
            )
            db.add(new_insert)

        # Commit the transaction
        db.commit()

        # Query today's data for these employees
        today_records = db.exec(
            select(employee_insert)
            .where(
                employee_insert.employee_id.in_(employee_ids),
                employee_insert.datetime >= today_start,
                employee_insert.datetime <= today_end
            )
            # .order_by(employee_insert.datetime.desc())
        ).all()

        # Convert records to dict format
        result = [
            {
                "auto_id": record.auto_id,
                "employee_id": record.employee_id,
                "employee_show": record.employee_show,
                "fen": record.fen,
                "datetime": record.datetime,
                "jia_yi_fang_a": record.jia_yi_fang_a,
                "jia_yi_fang_b": record.jia_yi_fang_b,
                "lei_a": record.lei_a,
                "lei_b": record.lei_b
            }
            for record in today_records
        ]

        return {
            "message": "Employee days data inserted successfully",
            "today_records": result
        }

    except SQLAlchemyError as e:
        # Rollback in case of error
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {str(e)}"
        )
