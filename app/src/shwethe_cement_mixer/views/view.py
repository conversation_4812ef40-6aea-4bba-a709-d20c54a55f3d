from typing import Any, List, Dict

import requests
from pydantic import BaseModel
from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from src.config.shwethe_cement_mixer.database import get_session
import os
import time
from src.shwethe_cement_mixer.crud.crud import (
    getCarAtive100,
    getProductProduceItem100,
    getMaterialsByProductID100,
    insert_cement100,
    getQrcodeMachine100,
    getMaterialItem100,
    get_list_produce100,
    update_status_cement_insert100,
    get_list_produce_web100,
    send_big_table100,
    get_materials_list_web100,
    update_pricB100,
    change_employee_status100,
    getEmployeeSearch100,
    getEmplyeeByFen100,
    insert_employee100,
    getEmployeeBarcode100,
    insertEployeeDays100
)
# from src.shwethe_cement_mixer.models import name_storage_tb
# from src.shwethe_cement_mixer.schemas.schemas import (
#     color_cai_liao_tb_post,
#     color_insert_tb_post
# )
from sqlalchemy.exc import IntegrityError
router = APIRouter()


# @router.post("/product")
# def create_product_handler(nameStorageTbPostDatag: nameStorageTbForProductPost, db: Session = Depends(get_session)):
#     A100001 = create_name_storage_tb(db=db, nameStorageTbPostData=nameStorageTbPostDatag)
#     nameStorageTbPostDatag.product_id = A100001.id
#     return create_product(db=db, nameStorageTbPostData=nameStorageTbPostDatag)


@router.post("/getCarAtive")
def getCarAtive(db: Session = Depends(get_session)):
    return getCarAtive100(db=db)

@router.get("/getProductProduceItem/item={item}")
def getProductProduceItem(item: str, db: Session = Depends(get_session)):
    return getProductProduceItem100(item=item,db=db)

@router.get("/getMaterialItem/item={item}")
def getMaterialItem(item: str, db: Session = Depends(get_session)):
    return getMaterialItem100(item=item,db=db)

# @router.get("/getMaterialsByProductID/item={item}")
# def getMaterialsByProductID(item: str, db: Session = Depends(get_session)):
#     return getMaterialsByProductID100(item=item,db=db)
@router.get("/getMaterialsByProductID/product_id={product_id}/product_name={product_name}")
def getMaterialsByProductID(product_id: int, product_name: str, db: Session = Depends(get_session)):
    return getMaterialsByProductID100(product_id=product_id, product_name=product_name, db=db)


@router.post("/insert_cement")
def insert_cement(list_data: Dict, db: Session = Depends(get_session)):
    return insert_cement100(list_data=list_data, db=db)


@router.get("/getQrcodeMachine/item={item}")
def getQrcodeMachine(item: str, db: Session = Depends(get_session)):
    return getQrcodeMachine100(item=item,db=db)


@router.get("/get_list_produce")
def get_list_produce(fen: int, page: int = 1, per_page: int = 1, db: Session = Depends(get_session)):
    return get_list_produce100(fen=fen, page=page, per_page=per_page, db=db)


@router.put("/update_status_cement_insert")
def update_status_cement_insert(record_id: str, status: int, db: Session = Depends(get_session)):
    return update_status_cement_insert100(record_id=record_id, status=status, db=db)


@router.get("/get_list_produce_web")
def get_list_produce_web(db: Session = Depends(get_session)):
    return get_list_produce_web100(db=db)


@router.get("/get_materials_list_web")
def get_materials_list_web(record_id: str, db: Session = Depends(get_session)):
    return get_materials_list_web100(record_id=record_id, db=db)


@router.put("/update_pricB")
def update_pricB(record_id: str, price_b: int, db: Session = Depends(get_session)):
    return update_pricB100(record_id=record_id, price_b=price_b, db=db)


@router.post("/send_big_table")
def send_big_table(db: Session = Depends(get_session)):
    return send_big_table100(db=db)


@router.get("/getEmployeeSearch/fen={fen}/item={item}")
def getEmployeeSearch(fen: int, item: str, db: Session = Depends(get_session)):
    return getEmployeeSearch100(fen=fen, item=item,db=db)


@router.get("/getEmployeeBarcode/item={item}")
def getEmployeeBarcode(item: str, db: Session = Depends(get_session)):
    return getEmployeeBarcode100(item=item, db=db)


@router.get("/getEmplyeeByFen/fen={fen}")
def getEmplyeeByFen(fen: int, db: Session = Depends(get_session)):
    return getEmplyeeByFen100(fen=fen, db=db)


@router.post("/insert_employee")
def insert_employee(list_data: Dict, db: Session = Depends(get_session)):
    return insert_employee100(list_data=list_data, db=db)


@router.put("/change_employee_status/employee_id={employee_id}")
def change_employee_status(employee_id: int, db: Session = Depends(get_session)):
    return change_employee_status100(employee_id=employee_id, db=db)


@router.post("/insertEployeeDays")
def insertEployeeDays(list_data: List[Dict] = Body(...), db: Session = Depends(get_session)):
    """
    API endpoint to insert employee days data
    
    Args:
        list_data: List of employee data dictionaries
        db: Database session
    """
    return insertEployeeDays100(list_data=list_data, db=db)
