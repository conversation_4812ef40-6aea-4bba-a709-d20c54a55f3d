# from sqlmodel import Session, SQLModel, create_engine

# from src.shwethe_miniapp_departScan.config import test_settings as settings

# # connect_args = {"check_same_thread": False}
# connect_args = {"options": "-c timezone=Asia/Yangon"}
# engine = create_engine(settings.DATABASE_URI, echo=True, connect_args=connect_args)


# def create_db_and_tables():
#     SQLModel.metadata.create_all(engine)


# def get_session():
#     with Session(engine) as session:
#         yield session

from src.config.shwethe_car_active.database import get_session as get_session