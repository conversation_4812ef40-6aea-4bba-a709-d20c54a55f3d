from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session
from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_car_active.database import get_session
from src.shwethe_car_active.src.crud.crud import (
    getCarAtive100,
    vehicleTypeChange100,
    imageListByID100,
    getApiCarDriver100,
    formVehicle100,
    MaddActive100,
    addVehicle100,
    MgetCarAtive100,
    getCompareVehicle100,
    carActiveSend100,
    MstatusInsert100,
    MgetcarShop100,
    MgetcarWorkShop100,
    create_vehicle_types,
    read_vehicle_types,
    create_vehicle_owners_types,
    read_vehicle_owners_types,
    create_vehicles,
    read_vehicles,
    update_vehicle,
    create_document_types,
    read_document_types,
    create_document_type_vehicle_type_relations,
    read_document_type_vehicle_type_relations,
    getDocument,
    postDocument,
    getDocumentWithParme,
    getExpireCar100,
    getSummaryCarQty100,
    get_map_test_data,
    create_map_test_data,
    delete_map_test_data
    # simpleOutputCarToday100
)
from src.shwethe_car_active.src.models.models import(
    document_types_post,
    vehicle_owners_post,
    vehicles_post,
    vehicle_read, vehicle_post,
    vehicle_insert_post,vehicle_types_post,
    document_type_vehicle_type_relations_post,
    vehicle_documents_post,
    map_test_read, map_test_post
)
router = APIRouter()


@router.get("/getCarAtive/{typeVehicle}")
def getCarAtive(typeVehicle: str, db: Session = Depends(get_session)):
    return getCarAtive100(typeVehicle=typeVehicle, db=db)

@router.put("/vehicleTypeChange/{auto_id}/{typeChange}")
def vehicleTypeChange(auto_id: int, typeChange: str, db: Session = Depends(get_session)):
    return vehicleTypeChange100(auto_id=auto_id, typeChange=typeChange, db=db)

@router.get("/imageListByID/{jia_yi_id_vehicle}")
def imageListByID(jia_yi_id_vehicle: int, db: Session = Depends(get_session)):
    return imageListByID100(jia_yi_id_vehicle=jia_yi_id_vehicle,db=db)

@router.get("/getApiCarDriver/{getApiCarDriver}")
def getApiCarDriver(getApiCarDriver: str, db: Session = Depends(get_session)):
    return getApiCarDriver100(getApiCarDriver=getApiCarDriver,db=db)

@router.post("/formVehicle")
def formVehicle(hero: vehicle_insert_post, db: Session = Depends(get_session)):
    return formVehicle100(hero=hero,db=db)

@router.post("/MaddActive")
def MaddActive(vehicle_insert_post : vehicle_insert_post, db: Session = Depends(get_session)):
    return MaddActive100(vehicle_insert_post=vehicle_insert_post, db=db)

@router.post("/addVehicle")
def addVehicle(vehicle_insert_post : vehicle_insert_post, db: Session = Depends(get_session)):
    return addVehicle100(vehicle_insert_post=vehicle_insert_post, db=db)

@router.get("/MgetCarAtive/{fen_dian_id}")
def MgetCarAtive(fen_dian_id: int, db: Session = Depends(get_session)):
    return MgetCarAtive100(fen_dian_id=fen_dian_id, db=db)

@router.get("/getCompareVehicle")
def getCompareVehicle(db: Session = Depends(get_session)):
    return getCompareVehicle100(db=db)

@router.get("/carActiveSend")
def carActiveSend(beforeDay: int, db: Session = Depends(get_session)):
    return carActiveSend100(beforeDay=beforeDay, db=db)

@router.post("/MstatusInsert")
def MstatusInsert(hero: vehicle_insert_post, db: Session = Depends(get_session)):
    return MstatusInsert100(hero=hero,db=db)

@router.get("/MgetcarWorkShop")
def MgetcarWorkShop(db: Session = Depends(get_session)):
    return MgetcarWorkShop100(db=db)

@router.get("/MgetcarShop")
def MgetcarShop(db: Session = Depends(get_session)):
    return MgetcarShop100(db=db)

@router.get("/getExpireCar")
def getExpireCar(db: Session = Depends(get_session)):
    return getExpireCar100(db=db)

@router.get("/getSummaryCarQty")
def getSummaryCarQty(db: Session = Depends(get_session)):
    return getSummaryCarQty100(db=db)

# @router.get("/simpleOutputCarToday")
# def simpleOutputCarToday(db: Session = Depends(get_session)):
#     return simpleOutputCarToday100(db=db)


# @router.get("/getBarcode")
# def getBarcode(db: Session = Depends(get_session)):
#     return getBarcode200(db=db)

# @router.get("/checkDriver/{checkDriver}")
# def checkDriver(checkDriver: str, db: Session = Depends(get_session)):
#     return checkDriver100(checkDriver=checkDriver,db=db)

# @router.post("/insertPersonForm")
# def insertPersonForm(hero: insertDepart_post, db: Session = Depends(get_session)):
#     return insertPersonForm100(hero=hero,db=db)

# @router.get("/departSend")
# def departSend(beforeDay: int, db: Session = Depends(get_session)):
#     return departSend100(beforeDay=beforeDay, db=db)

# new project for insert car info


@router.post("/vehicles_types")
def create_vehicle_api(hero:vehicle_types_post,db: Session = Depends(get_session)):
    return create_vehicle_types(hero=hero,db=db)

@router.get("/vehicles_types")
def create_vehicle_api(db: Session = Depends(get_session)):
    return read_vehicle_types(db=db)

@router.post("/vehicle_owners")
def create_vehicle_api(hero:vehicle_owners_post,db: Session = Depends(get_session)):
    return create_vehicle_owners_types(hero=hero,db=db)

@router.get("/vehicle_owners")
def create_vehicle_api(db: Session = Depends(get_session)):
    return read_vehicle_owners_types(db=db)

@router.post("/vehicles")
def create_vehicle_api(hero:vehicles_post,db: Session = Depends(get_session)):
    return create_vehicles(hero=hero,db=db)

@router.get("/vehicles")
def create_vehicle_api(db: Session = Depends(get_session)):
    return read_vehicles(db=db)

@router.put("/vehicles/{che_liang_id}")
def create_vehicle_api(che_liang_id: int,hero:vehicles_post,db: Session = Depends(get_session)):
    return update_vehicle(che_liang_id=che_liang_id,hero=hero,db=db)

@router.post("/document_types")
def document_types(hero:document_types_post,db: Session = Depends(get_session)):
    return create_document_types(hero=hero,db=db)

@router.get("/document_types")
def document_types(db: Session = Depends(get_session)):
    return read_document_types(db=db)

@router.post("/document_type_vehicle_type_relations")
def document_type_vehicle_type_relations(hero:document_type_vehicle_type_relations_post,db: Session = Depends(get_session)):
    return create_document_type_vehicle_type_relations(hero=hero,db=db)

@router.get("/document_type_vehicle_type_relations")
def document_type_vehicle_type_relations(db: Session = Depends(get_session)):
    return read_document_type_vehicle_type_relations(db=db)

# 做个一个车子 没有 装好的资料
@router.get("/document/car_cocument")
def document_type_vehicle_type_relations(months_until_expiration:int=0,db: Session = Depends(get_session)):
    if months_until_expiration == 0:
        return getDocument(db=db)

    if months_until_expiration > 0:
        return getDocumentWithParme(db=db,months_until_expiration=months_until_expiration)

# 做个一个车子 没有 装好的资料
@router.post("/document/car_cocument")
def document_type_vehicle_type_relations(hero:vehicle_documents_post,db: Session = Depends(get_session)):
    return postDocument(db=db,hero=hero)

# Map Test API endpoints
@router.get("/map_test")
def get_map_test(db: Session = Depends(get_session)):
    """
    Get all map_test data.
    Data is sorted by created_at in descending order (newest first).

    Args:
        db: Database session

    Returns:
        List of map_test records
    """
    return get_map_test_data(db=db)

@router.post("/map_test")
def create_map_test(hero: map_test_post, db: Session = Depends(get_session)):
    """
    Create a new map_test record.

    Args:
        hero: Data to insert
        db: Database session

    Returns:
        The created record
    """
    return create_map_test_data(hero=hero, db=db)

@router.delete("/map_test/{auto_id}")
def delete_map_test(auto_id: int, db: Session = Depends(get_session)):
    """
    Delete a map_test record by ID.

    Args:
        auto_id: ID of the record to delete
        db: Database session

    Returns:
        Success message
    """
    return delete_map_test_data(auto_id=auto_id, db=db)