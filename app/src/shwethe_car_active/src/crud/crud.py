from sqlite3 import dbapi2
import requests
from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,and_, join
from sqlmodel import select, func, and_, distinct, extract
from typing import List, Optional
from sqlalchemy.orm import joinedload
from datetime import datetime, date
from sqlalchemy.sql import func
import datetime
from sqlalchemy.sql import cast
from sqlalchemy import Date
from src.shwethe_car_active.database import get_session
from helper import generate_datetime_id
from src.shwethe_car_active.src.models.models import (
vehicle, vehicle_post, vehicle_insert, vehicle_insert_post,vehicle_types_post,
vehicle_types,
vehicle_owners,
vehicle_owners_post,
vehicles,
vehicle_documents,
vehicles_post,
document_types_post,
document_types,
document_type_vehicle_type_relations_post,
document_type_vehicle_type_relations,
vehicle_documents_post,
expirevitus,
map_test, map_test_post
)
from sqlalchemy import text
import json
import pandas as pd
import urllib.request
import cv2
import numpy as np
from src.time_zone.time_zone_function import get_datetime
from src.Connect.https_connect import mongodb_data_api
from src.Connect.postgresql_nern import real_backend_api, petrol_api
from sqlalchemy import and_, or_, not_
from pandas import json_normalize

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

def dataframe(sqlModel, to_dict=False):
    records = [i.dict() for i in sqlModel]
    mergeDF = pd.DataFrame.from_records(records).fillna(0)
    if to_dict:
        mergeDF = mergeDF.to_dict("records")
    return mergeDF

def getCarAtive100(typeVehicle: str, db: Session = Depends(get_session)):
    try:
        if(typeVehicle == 'all'):
            heroesPersonal = db.exec(select(vehicle)).all()
            records = [i.dict() for i in heroesPersonal]
            df = pd.DataFrame.from_records(records)

        if(typeVehicle != 'all'):
            heroesPersonal = db.exec(select(vehicle).where(vehicle.type == typeVehicle)).all()
            records = [i.dict() for i in heroesPersonal]
            df = pd.DataFrame.from_records(records)

        heroesPersonal2 = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
        records = [i.dict() for i in heroesPersonal2]
        df2 = pd.DataFrame.from_records(records)

        mergeDF = pd.merge(df, df2, left_on='jia_yi_id', right_on='jia_yi_id_vehicle').fillna(0)
        # mergeDF = mergeDF.sort_values(by=['jia_yi_id','datetime'])
        # mergeDF = mergeDF.groupby(['jia_yi_id']).agg({'auto_id_x':'first',
        #                                     'datetime':lambda x: x.iloc[-1],
        #                                     'fen_dian_id':lambda x: x.iloc[-1],
        #                                     'jia_yi_id_driver':lambda x: x.iloc[-1],
        #                                     'auto_id_y':lambda x: x.iloc[-1],
        #                                     'type':lambda x: x.iloc[-1],
        #                                     'jia_yi_id':'count'}).rename(columns={'jia_yi_id':'count'}).reset_index()

        # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        to_dict = mergeDF[['jia_yi_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getCar = requests.get(url=url, json=body_raw).json()
        getCar = pd.DataFrame(getCar)
        print(getCar)

        mergeDF2 = pd.merge(mergeDF, getCar, on='jia_yi_id').fillna(0)
        mergeDF2 = mergeDF2.sort_values(by='datetime', ascending=False)
        mergeDF2 = mergeDF2.to_dict("records")
    except:
        mergeDF2 = []
    return mergeDF2


# def getCarAtive100(typeVehicle: str, db: Session = Depends(get_session)):
#     try:
#         if(typeVehicle == 'all'):
#             heroesPersonal = db.exec(select(vehicle)).all()
#             records = [i.dict() for i in heroesPersonal]
#             df = pd.DataFrame.from_records(records)

#         if(typeVehicle != 'all'):
#             heroesPersonal = db.exec(select(vehicle).where(vehicle.type == typeVehicle)).all()
#             records = [i.dict() for i in heroesPersonal]
#             df = pd.DataFrame.from_records(records)

#         heroesPersonal2 = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#         records = [i.dict() for i in heroesPersonal2]
#         df2 = pd.DataFrame.from_records(records)

#         df2["image_2"] = df2["image"]
#         df2['image_2'] = df2['image_2'].fillna('***********2:9000/personal/carActiveFolder/ba8fb9ce5cda4.jpg', inplace=False)
#         df2["image_2"] = "http://" + df2["image_2"]

#         blur_values = []
#         for url in df2["image_2"]:
#             with urllib.request.urlopen(url) as url_response:
#                 img_array = np.array(bytearray(url_response.read()), dtype=np.uint8)
#                 image = cv2.imdecode(img_array, -1)
#                 gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
#                 grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
#                 grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
#                 grad_mag = cv2.magnitude(grad_x, grad_y)
#                 variance = np.var(grad_mag)
#                 blur_values.append(variance)
#         df2["image_2_blur"] = blur_values
#         df2["image_2_blur"] = df2["image_2_blur"].round(0)

#         mergeDF = pd.merge(df, df2, left_on='jia_yi_id', right_on='jia_yi_id_vehicle').fillna(0)

#         url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         to_dict = mergeDF[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         print(getCar)

#         mergeDF2 = pd.merge(mergeDF, getCar, on='jia_yi_id').fillna(0)
#         mergeDF2 = mergeDF2.sort_values(by='datetime', ascending=False)
#         mergeDF2 = mergeDF2.to_dict("records")
#     except:
#         mergeDF2 = []
#     return mergeDF2


# def getCarAtive100(typeVehicle: str, db: Session = Depends(get_session)):

#     if(typeVehicle == 'all'):
#         heroesPersonal = db.exec(select(vehicle)).all()
#         records = [i.dict() for i in heroesPersonal]
#         df = pd.DataFrame.from_records(records)

#     if(typeVehicle != 'all'):
#         heroesPersonal = db.exec(select(vehicle).where(vehicle.type == typeVehicle)).all()
#         records = [i.dict() for i in heroesPersonal]
#         df = pd.DataFrame.from_records(records)

#     heroesPersonal2 = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#     records = [i.dict() for i in heroesPersonal2]
#     df2 = pd.DataFrame.from_records(records)

#     df2["image_2"] = df2["image"]
#     df2['image_2'] = df2['image_2'].fillna('***********2:9000/personal/carActiveFolder/f5c8f5449b494.jpg', inplace=False)
#     df2["image_2"] = "http://" + df2["image_2"]

#     blur_values = []
#     for url in df2["image_2"]:
#         with urllib.request.urlopen(url) as url_response:
#             img_array = np.array(bytearray(url_response.read()), dtype=np.uint8)
#             image = cv2.imdecode(img_array, -1)
#             gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
#             grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
#             grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
#             grad_mag = cv2.magnitude(grad_x, grad_y)
#             variance = np.var(grad_mag)
#             blur_values.append(variance)
#     df2["image_2_blur"] = blur_values
#     df2["image_2_blur"] = df2["image_2_blur"].round(0)

#     mergeDF2 = df2.to_dict("records")

#     # mergeDF2 = []
#     return mergeDF2


def vehicleTypeChange100(typeChange : str, auto_id: int, db: Session = Depends(get_session)):

    statement = select(vehicle).where(vehicle.auto_id == auto_id)
    results = db.exec(statement).first()

    results.type = typeChange

    db.add(results)  #
    db.commit()  #
    db.refresh(results)  #
    print("Updated heroooooooooooooooooo:")

    return  results


def imageListByID100(jia_yi_id_vehicle: int, db: Session = Depends(get_session)):
    try:
        import datetime as DT
        today = DT.date.today()
        week_ago = today - DT.timedelta(days=90)
        heroesPersonal = db.exec(select(vehicle_insert).where(vehicle_insert.datetime > week_ago, vehicle_insert.jia_yi_id_vehicle == jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
        records = [i.dict() for i in heroesPersonal]
        df = pd.DataFrame.from_records(records).fillna(0)
        df = df.to_dict("records")
    except:
        df = []
    return df

def getLatComment(getCarID: int, db: Session):
    print("getCarIDgetCarIDgetCarIDgetCarIDgetCarIDgetCarIDgetCarIDgetCarID", int(getCarID))

    heroesPersonal = db.exec(select(vehicle_insert).where(vehicle_insert.jia_yi_id_vehicle == getCarID).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).first()
    print(heroesPersonal)

    return heroesPersonal
def getApiCarDriver100(getApiCarDriver: str, db: Session = Depends(get_session)):
    # print(getApiCarDriver)
    # print(type(getApiCarDriver))

    try:
        getApiCarDriver = int(getApiCarDriver)
        print(getApiCarDriver)
        print(type(getApiCarDriver))

        # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        # url = Arter_api + 'jia_yi_name_list_id'
        # body_raw = {"data_api": [{"jia_yi_id": 36557}]}
        body_raw = {"data_api": [{"jia_yi_id": getApiCarDriver}]}
        # body_raw = {"data_api": df}
        df2 = requests.get(url=url, json=body_raw)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')

        print(df2)

        getLatComment_ = getLatComment(df2[0]['jia_yi_id'], db)
        # print(df2)
    except:
        print('The provided value is not an integer')
        print(getApiCarDriver)
        print(type(getApiCarDriver))

        # url = f'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={getApiCarDriver}'
        url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={getApiCarDriver}'
        df2 = requests.get(url=url)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')

        print(df2)

        getLatComment_ = getLatComment(df2[0]['jia_yi_id'], db)
        # print(df2)
    return {"carDetails": df2, "getLatComment": getLatComment_}
# def getApiCarDriver100(getApiCarDriver: str, db: Session = Depends(get_session)):
#     # print(getApiCarDriver)
#     # print(type(getApiCarDriver))

#     try:
#         getApiCarDriver = int(getApiCarDriver)
#         print(getApiCarDriver)
#         print(type(getApiCarDriver))

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         # url = Arter_api + 'jia_yi_name_list_id'
#         # body_raw = {"data_api": [{"jia_yi_id": 36557}]}
#         body_raw = {"data_api": [{"jia_yi_id": getApiCarDriver}]}
#         # body_raw = {"data_api": df}
#         df2 = requests.get(url=url, json=body_raw)
#         df2 = df2.json()
#         df2 = pd.DataFrame(df2)
#         df2 = df2.to_dict('records')
#         # print(df2)
#     except:
#         print('The provided value is not an integer')
#         print(getApiCarDriver)
#         print(type(getApiCarDriver))

#         # url = f'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={getApiCarDriver}'
#         url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={getApiCarDriver}'
#         df2 = requests.get(url=url)
#         df2 = df2.json()
#         df2 = pd.DataFrame(df2)
#         df2 = df2.to_dict('records')
#         # print(df2)
#     return df2


def formVehicle100(hero: vehicle_insert_post, db: Session = Depends(get_session)):

    hero_to_db = vehicle_insert.from_orm(hero)
    hero_to_db.datetime = get_datetime()
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)

    return hero_to_db


def MaddActive100(vehicle_insert_post : vehicle_insert_post, db: Session = Depends(get_session)):

    statement = select(vehicle).where(vehicle.jia_yi_id == vehicle_insert_post.jia_yi_id_vehicle)
    results = db.exec(statement).first()
    # print(results)

    if (results == None):
        print("empty")

        insertName = vehicle(jia_yi_id=vehicle_insert_post.jia_yi_id_vehicle, type='noType', fen_dian_id=vehicle_insert_post.fen_dian_id)
        db.add(insertName)
        db.commit()
        db.refresh(insertName)

        res = 'empty'
    else:
        print("haveeeeeeeeeeee")

        statement = select(vehicle).where(vehicle.jia_yi_id == vehicle_insert_post.jia_yi_id_vehicle)
        results = db.exec(statement).first()

        results.fen_dian_id = vehicle_insert_post.fen_dian_id
        db.add(results)  #
        db.commit()  #
        db.refresh(results)  #

        res = 'haveThisName'

    try:
        statement = select(vehicle_insert).where(vehicle_insert.jia_yi_id_vehicle == vehicle_insert_post.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
        results = db.exec(statement).first()
        print(results.datetime, results.where)

    except:
        class DotDict(dict):
            def __getattr__(self, attr):
                return self[attr]
        results = DotDict({'where': 'aaa'})
        print(results.where)

    if (results.where == "workShop"):
        print("At workshop")

        # insertName = vehicle(jia_yi_id=vehicle_insert_post.jia_yi_id_vehicle, type='noType', fen_dian_id=vehicle_insert_post.fen_dian_id)
        # db.add(insertName)
        # db.commit()
        # db.refresh(insertName)
    else:
        print("At shop")
        hero_to_db = vehicle_insert.from_orm(vehicle_insert_post)
        hero_to_db.datetime = get_datetime()
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)

    return res


def addVehicle100(vehicle_insert_post : vehicle_insert_post, db: Session = Depends(get_session)):

    statement = select(vehicle).where(vehicle.jia_yi_id == vehicle_insert_post.jia_yi_id_vehicle)
    results = db.exec(statement).first()
    # print(results)

    if (results == None):
        print("empty")

        insertName = vehicle(jia_yi_id=vehicle_insert_post.jia_yi_id_vehicle, type='noType')
        db.add(insertName)
        db.commit()
        db.refresh(insertName)

        hero_to_db = vehicle_insert.from_orm(vehicle_insert_post)
        hero_to_db.datetime = get_datetime()
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)

        res = 'empty'
    else:
        print("haveeeeeeeeeeee")
        res = 'haveThisName'

    return res


# def MgetCarAtive100(fen_dian_id: str, db: Session = Depends(get_session)):
#     try:
#         heroesPersonal = db.exec(select(vehicle).where(vehicle.fen_dian_id == fen_dian_id)).all()
#         records = [i.dict() for i in heroesPersonal]
#         df2 = pd.DataFrame.from_records(records).fillna(0)

#         heroesPersonal2 = db.exec(select(vehicle_insert).where(vehicle_insert.fen_dian_id == fen_dian_id).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#         records = [i.dict() for i in heroesPersonal2]
#         df = pd.DataFrame.from_records(records).fillna(0)
#         df = df.query('where != "workShop"')

#         mergeTwoData = pd.merge(df2, df, left_on=['jia_yi_id','fen_dian_id'], right_on = ['jia_yi_id_vehicle','fen_dian_id'],  how='left')
#         mergeTwoData['datetime'] = pd.to_datetime(mergeTwoData['datetime'])
#         current_date = pd.datetime.now().date()
#         mergeTwoData = mergeTwoData[mergeTwoData['datetime'].dt.date != current_date]
#         print(current_date)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
#         print(getCar)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getDriver = requests.get(url=url, json=body_raw).json()
#         getDriver = pd.DataFrame(getDriver)
#         getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
#         print(getDriver)

#         mergeCar = pd.merge(mergeTwoData, getCar, on='jia_yi_id_vehicle')
#         mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
#         mergeDF2 = mergeDriver.sort_values(by='datetime', ascending=False)
#         mergeDF2 = mergeDF2.to_dict("records")
#     except:
#         mergeDF2 = []

#     return mergeDF2


# def MgetCarAtive100(fen_dian_id: str, db: Session = Depends(get_session)):
#     try:
#         heroesPersonal = db.exec(select(vehicle).where(vehicle.fen_dian_id == fen_dian_id)).all()
#         records = [i.dict() for i in heroesPersonal]
#         df2 = pd.DataFrame.from_records(records).fillna(0)

#         # heroesPersonal2 = db.exec(select(vehicle_insert).where(vehicle_insert.fen_dian_id == fen_dian_id).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#         heroesPersonal2 = db.exec(select(vehicle_insert).where(and_(
#             vehicle_insert.fen_dian_id == fen_dian_id,
#             or_(
#                 vehicle_insert.where == None,
#                 vehicle_insert.where == 'shop'
#             )
#         )).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#         records = [i.dict() for i in heroesPersonal2]
#         df = pd.DataFrame.from_records(records).fillna(0)
#         # df = df.query('where != "workShop"')

#         mergeTwoData = pd.merge(df2, df, left_on=['jia_yi_id','fen_dian_id'], right_on = ['jia_yi_id_vehicle','fen_dian_id'],  how='left')
#         mergeTwoData['datetime'] = pd.to_datetime(mergeTwoData['datetime'])
#         current_date = pd.datetime.now().date()
#         mergeTwoData = mergeTwoData[mergeTwoData['datetime'].dt.date != current_date]
#         print(current_date)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
#         print(getCar)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getDriver = requests.get(url=url, json=body_raw).json()
#         getDriver = pd.DataFrame(getDriver)
#         getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
#         print(getDriver)

#         mergeCar = pd.merge(mergeTwoData, getCar, on='jia_yi_id_vehicle')
#         mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
#         mergeDF2 = mergeDriver.sort_values(by='datetime', ascending=False)

#         jia_yi_id_values = df['jia_yi_id_vehicle'].tolist()
#         query = select(vehicle_insert).where(vehicle_insert.jia_yi_id_vehicle.in_(jia_yi_id_values), text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         # query = select(vehicle_insert).where(text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         results = db.exec(query).all()
#         records = [i.dict() for i in results]
#         A100012 = pd.DataFrame.from_records(records).fillna(0)
#         A100012['details'] = A100012['details'].fillna('{"images": [], "descript": "", "active_status": ""}')
#         A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)
#         details_A100012 = json_normalize(A100012['details'])
#         A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})
#         A100012.drop('details', axis=1, inplace=True)
#         A100012 = A100012[['jia_yi_id_vehicle', 'descript']]

#         merge_descript = pd.merge(mergeDF2, A100012, on='jia_yi_id_vehicle', how='left').fillna(0)


#         mergeDF2 = merge_descript.to_dict("records")
#     except:
#         mergeDF2 = []

#     return mergeDF2



# def MgetCarAtive100(fen_dian_id: str, db: Session = Depends(get_session)):

#     try:
#         import datetime as DT
#         today = DT.date.today()
#         week_ago = today - DT.timedelta(days=0)
#         lastDate = (
#             select(vehicle_insert.jia_yi_id_vehicle, func.max(vehicle_insert.datetime).label("max_datetime"))
#             .group_by(vehicle_insert.jia_yi_id_vehicle)
#             .subquery()
#         )
#         beforeLastDate = (
#             select(vehicle_insert.jia_yi_id_vehicle, func.max(vehicle_insert.datetime).label("max_datetime"))
#             .where(vehicle_insert.datetime < week_ago)
#             .group_by(vehicle_insert.jia_yi_id_vehicle)
#             .subquery()
#         )
#         query = (
#             select(vehicle_insert)
#             .join(lastDate, and_(
#                 vehicle_insert.jia_yi_id_vehicle == lastDate.c.jia_yi_id_vehicle,
#                 vehicle_insert.datetime == lastDate.c.max_datetime
#             ))
#             .join(beforeLastDate, and_(
#                 vehicle_insert.jia_yi_id_vehicle == beforeLastDate.c.jia_yi_id_vehicle,
#                 vehicle_insert.datetime == beforeLastDate.c.max_datetime
#             ))
#             .where(and_(
#                 vehicle_insert.fen_dian_id == fen_dian_id,
#                 or_(
#                     vehicle_insert.where == None,
#                     vehicle_insert.where == 'shop'
#                 )
#             ))
#         )
#         heroesPersonal2 = db.exec(query).all()
#         records = [i.dict() for i in heroesPersonal2]
#         df = pd.DataFrame.from_records(records).fillna(0)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
#         # print(getCar)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getDriver = requests.get(url=url, json=body_raw).json()
#         getDriver = pd.DataFrame(getDriver)
#         getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
#         # print(getDriver)

#         mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
#         mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
#         mergeDF2 = mergeDriver.sort_values(by='datetime', ascending=False)

#         jia_yi_id_values = df['jia_yi_id_vehicle'].tolist()
#         query = select(vehicle_insert).where(vehicle_insert.jia_yi_id_vehicle.in_(jia_yi_id_values), text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         results = db.exec(query).all()
#         records = [i.dict() for i in results]
#         A100012 = pd.DataFrame.from_records(records).fillna(0)
#         A100012['details'] = A100012['details'].fillna('{"images": [], "descript": "", "active_status": ""}')
#         A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)
#         details_A100012 = json_normalize(A100012['details'])
#         A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})
#         A100012.drop('details', axis=1, inplace=True)
#         A100012 = A100012[['jia_yi_id_vehicle', 'descript']]

#         merge_descript = pd.merge(mergeDF2, A100012, on='jia_yi_id_vehicle', how='left').fillna(0)

#         mergeDF2 = merge_descript.to_dict("records")
#     except:
#         mergeDF2 = []

#     return mergeDF2


# def MgetCarAtive100(fen_dian_id: str, db: Session = Depends(get_session)):

#     try:
#         import datetime as DT
#         today = DT.date.today()
#         week_ago = today - DT.timedelta(days=0)
#         lastDate = (
#             select(vehicle_insert.jia_yi_id_vehicle, func.max(vehicle_insert.datetime).label("max_datetime"))
#             .group_by(vehicle_insert.jia_yi_id_vehicle)
#             .subquery()
#         )
#         beforeLastDate = (
#             select(vehicle_insert.jia_yi_id_vehicle, func.max(vehicle_insert.datetime).label("max_datetime"))
#             .where(vehicle_insert.datetime < week_ago)
#             .group_by(vehicle_insert.jia_yi_id_vehicle)
#             .subquery()
#         )
#         query = (
#             select(vehicle_insert)
#             .join(lastDate, and_(
#                 vehicle_insert.jia_yi_id_vehicle == lastDate.c.jia_yi_id_vehicle,
#                 vehicle_insert.datetime == lastDate.c.max_datetime
#             ))
#             .join(beforeLastDate, and_(
#                 vehicle_insert.jia_yi_id_vehicle == beforeLastDate.c.jia_yi_id_vehicle,
#                 vehicle_insert.datetime == beforeLastDate.c.max_datetime
#             ))
#             .where(and_(
#                 vehicle_insert.fen_dian_id == fen_dian_id,
#                 or_(
#                     vehicle_insert.where == None,
#                     vehicle_insert.where == 'shop'
#                 )
#             ))
#         )
#         heroesPersonal2 = db.exec(query).all()
#         records = [i.dict() for i in heroesPersonal2]
#         df = pd.DataFrame.from_records(records).fillna(0)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
#         # print(getCar)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getDriver = requests.get(url=url, json=body_raw).json()
#         getDriver = pd.DataFrame(getDriver)
#         getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
#         # print(getDriver)

#         mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
#         mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
#         mergeDF2 = mergeDriver.sort_values(by='datetime', ascending=False)

#         # jia_yi_id_values = df['jia_yi_id_vehicle'].tolist()
#         query = select(vehicle_insert).where(text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         # query = select(vehicle_insert).where(vehicle_insert.jia_yi_id_vehicle.in_(jia_yi_id_values), text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         results = db.exec(query).all()
#         records = [i.dict() for i in results]
#         A100012 = pd.DataFrame.from_records(records).fillna(0)
#         A100012['details'] = A100012['details'].fillna('{"images": [], "descript": "", "active_status": ""}')
#         A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)
#         details_A100012 = json_normalize(A100012['details'])
#         A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})
#         A100012.drop('details', axis=1, inplace=True)
#         A100012 = A100012[['jia_yi_id_vehicle', 'descript']]

#         merge_descript = pd.merge(mergeDF2, A100012, on='jia_yi_id_vehicle', how='left').fillna(0)

#         mergeDF2 = merge_descript.to_dict("records")
#     except:
#         mergeDF2 = []

#     return mergeDF2


def MgetCarAtive100(fen_dian_id: str, db: Session = Depends(get_session)):

    try:
        import datetime as DT
        today = DT.date.today()
        week_ago = today - DT.timedelta(days=0)
        lastDate = (
            select(vehicle_insert.jia_yi_id_vehicle, func.max(vehicle_insert.datetime).label("max_datetime"))
            .group_by(vehicle_insert.jia_yi_id_vehicle)
            .subquery()
        )
        beforeLastDate = (
            select(vehicle_insert.jia_yi_id_vehicle, func.max(vehicle_insert.datetime).label("max_datetime"))
            .where(vehicle_insert.datetime < week_ago)
            .group_by(vehicle_insert.jia_yi_id_vehicle)
            .subquery()
        )
        query = (
            select(vehicle_insert)
            .join(lastDate, and_(
                vehicle_insert.jia_yi_id_vehicle == lastDate.c.jia_yi_id_vehicle,
                vehicle_insert.datetime == lastDate.c.max_datetime
            ))
            .join(beforeLastDate, and_(
                vehicle_insert.jia_yi_id_vehicle == beforeLastDate.c.jia_yi_id_vehicle,
                vehicle_insert.datetime == beforeLastDate.c.max_datetime
            ))
            .where(and_(
                vehicle_insert.fen_dian_id == fen_dian_id,
                or_(
                    vehicle_insert.where == None,
                    vehicle_insert.where == 'shop'
                )
            ))
        )
        heroesPersonal2 = db.exec(query).all()
        records = [i.dict() for i in heroesPersonal2]
        df = pd.DataFrame.from_records(records).fillna(0)

        # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
        to_dict = dfRename[['jia_yi_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getCar = requests.get(url=url, json=body_raw).json()
        getCar = pd.DataFrame(getCar)
        getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
        # print(getCar)

        # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
        to_dict = dfRename[['jia_yi_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getDriver = requests.get(url=url, json=body_raw).json()
        getDriver = pd.DataFrame(getDriver)
        getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
        # print(getDriver)

        mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
        mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
        mergeDF2 = mergeDriver.sort_values(by='datetime', ascending=False)

        try:
            jia_yi_id_values = df['jia_yi_id_vehicle'].tolist()
            # query = select(vehicle_insert).where(text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
            query = select(vehicle_insert).where(vehicle_insert.jia_yi_id_vehicle.in_(jia_yi_id_values), text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
            results = db.exec(query).all()
            records = [i.dict() for i in results]
            A100012 = pd.DataFrame.from_records(records).fillna(0)

            if A100012.empty:
                raise Exception("No data retrieved from the database")
        except Exception as e:
            print(f"Error: {e}")
            # Provided JSON data
            json_data = [
                {
                    "details": {
                        "images": [],
                        "descript": "",
                        "active_status": None
                    },
                    "jia_yi_id_vehicle": 99999,
                }
            ]
            # Convert JSON to DataFrame
            A100012 = pd.DataFrame(json_data)

        A100012['details'] = A100012['details'].fillna('{"images": [], "descript": "", "active_status": ""}')
        A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)
        details_A100012 = json_normalize(A100012['details'])
        A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})
        A100012.drop('details', axis=1, inplace=True)
        A100012 = A100012[['jia_yi_id_vehicle', 'descript']]

        merge_descript = pd.merge(mergeDF2, A100012, on='jia_yi_id_vehicle', how='left').fillna(0)

        mergeDF2 = merge_descript.to_dict("records")
    except:
        mergeDF2 = []

    return mergeDF2




def getCompareVehicle100(db: Session = Depends(get_session)):
    try:
        # url = "http://***********30:8100/shwethe_petrol/api/v1/product/listOfMasad"
        url = f'{petrol_api}/api/v1/product/listOfMasad'
        response = requests.get(url)
        getMasad = response.json()
        getMasad = pd.DataFrame(getMasad)
        print(getMasad)

        # url = "http://***********30:8100/shwethe_petrol/api/v1/product/listOfCar"
        url = f'{petrol_api}/api/v1/product/listOfCar'
        response = requests.get(url)
        getCar = response.json()
        getCar = pd.DataFrame(getCar)
        print(getCar)

        concatVehicle = pd.concat([getMasad, getCar]).fillna(0)

        getCarAtiveVar = getCarAtive100('all', db)
        getCarAtiveVar = pd.DataFrame(getCarAtiveVar)

        mergeApiCar = pd.merge(concatVehicle, getCarAtiveVar, left_on='che_liang_id', right_on='jia_yi_id', how="outer").fillna(0)

        mergeDF2 = mergeApiCar.to_dict("records")
    except:
        mergeDF2 = []

    return mergeDF2


def carActiveSend100(beforeDay: int, db: Session = Depends(get_session)):
    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=beforeDay)
    timeS1 = str(week_ago)
    try:
        heroesPersonal = db.exec(select(vehicle)).all()
        records = [i.dict() for i in heroesPersonal]
        df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal2 = db.exec(select(vehicle_insert).where(vehicle_insert.datetime > timeS1)).all()
        records = [i.dict() for i in heroesPersonal2]
        df2 = pd.DataFrame.from_records(records).fillna(0)

        mergeDF = pd.merge(df, df2, left_on='jia_yi_id', right_on='jia_yi_id_vehicle').fillna(0)
        mergeDF = mergeDF[['fen_dian_id_y', 'auto_id_y', 'jia_yi_id_driver', 'datetime', 'status', 'jia_yi_id_vehicle', 'image', 'type']]
        mergeDF = mergeDF.rename(columns={'fen_dian_id_y': 'fen_dian_id', 'auto_id_y': 'auto_id'})
        mergeDF = mergeDF.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def MstatusInsert100(hero: vehicle_insert_post, db: Session = Depends(get_session)):

    hero_to_db = vehicle_insert.from_orm(hero)
    hero_to_db.datetime = get_datetime()
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)

    return hero_to_db


# def MgetcarWorkShop100(db: Session = Depends(get_session)):
#     try:
#         heroesPersonal = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#         records = [i.dict() for i in heroesPersonal]
#         df = pd.DataFrame.from_records(records).fillna(0)
#         df = df.loc[df['where'] == 'workShop']

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
#         print(getCar)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getDriver = requests.get(url=url, json=body_raw).json()
#         getDriver = pd.DataFrame(getDriver)
#         getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
#         print(getDriver)

#         mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
#         mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
#         mergeDF = mergeDriver.sort_values(by='fen_dian_id', ascending=True)
#         mergeDF = mergeDF.to_dict("records")
#     except:
#         mergeDF = []

#     return mergeDF


# def MgetcarWorkShop100(db: Session = Depends(get_session)):
#     try:
#         heroesPersonal = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#         records = [i.dict() for i in heroesPersonal]
#         df = pd.DataFrame.from_records(records).fillna(0)
#         df = df.loc[df['where'] == 'workShop']

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
#         print(getCar)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getDriver = requests.get(url=url, json=body_raw).json()
#         getDriver = pd.DataFrame(getDriver)
#         getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
#         print(getDriver)

#         mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
#         mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
#         mergeDF = mergeDriver.sort_values(by='fen_dian_id', ascending=True)

#         query = select(vehicle_insert).where(text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         results = db.exec(query).all()
#         records = [i.dict() for i in results]
#         A100012 = pd.DataFrame.from_records(records).fillna(0)
#         A100012['details'] = A100012['details'].fillna('{"images": [], "descript": "", "active_status": ""}')
#         A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)
#         details_A100012 = json_normalize(A100012['details'])
#         A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})
#         A100012.drop('details', axis=1, inplace=True)
#         A100012 = A100012[['jia_yi_id_vehicle', 'descript']]
#         merge_descript = pd.merge(mergeDF, A100012, on='jia_yi_id_vehicle')

#         mergeDF = merge_descript.to_dict("records")
#     except:
#         mergeDF = []

#     return mergeDF


# def MgetcarWorkShop100(db: Session = Depends(get_session)):
#     try:
#         subquery = (
#             select(vehicle_insert.jia_yi_id_vehicle, func.max(vehicle_insert.datetime).label("max_datetime"))
#             .group_by(vehicle_insert.jia_yi_id_vehicle)
#             .subquery()
#         )
#         query = (
#             select(vehicle_insert)
#             .join(subquery, and_(
#                 vehicle_insert.jia_yi_id_vehicle == subquery.c.jia_yi_id_vehicle,
#                 vehicle_insert.datetime == subquery.c.max_datetime
#             ))
#             .where(vehicle_insert.where == 'workShop')
#         )
#         heroesPersonal = db.exec(query).all()
#         records = [i.dict() for i in heroesPersonal]
#         df = pd.DataFrame.from_records(records).fillna(0)

#         # heroesPersonal = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#         # records = [i.dict() for i in heroesPersonal]
#         # df = pd.DataFrame.from_records(records).fillna(0)
#         # df = df.loc[df['where'] == 'workShop']

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
#         print(getCar)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getDriver = requests.get(url=url, json=body_raw).json()
#         getDriver = pd.DataFrame(getDriver)
#         getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
#         print(getDriver)

#         mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
#         mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
#         mergeDF = mergeDriver.sort_values(by='fen_dian_id', ascending=True)

#         jia_yi_id_values = df['jia_yi_id_vehicle'].tolist()
#         # query = select(vehicle_insert).where(text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         query = select(vehicle_insert).where(vehicle_insert.jia_yi_id_vehicle.in_(jia_yi_id_values), text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         results = db.exec(query).all()
#         records = [i.dict() for i in results]
#         A100012 = pd.DataFrame.from_records(records).fillna(0)
#         A100012['details'] = A100012['details'].fillna('{"images": [], "descript": "", "active_status": ""}')
#         A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)
#         details_A100012 = json_normalize(A100012['details'])
#         A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})
#         A100012.drop('details', axis=1, inplace=True)
#         A100012 = A100012[['jia_yi_id_vehicle', 'descript']]
#         merge_descript = pd.merge(mergeDF, A100012, on='jia_yi_id_vehicle')

#         mergeDFLast = merge_descript.to_dict("records")
#     except:
#         mergeDFLast = []

#     return mergeDFLast


# def MgetcarWorkShop100(db: Session = Depends(get_session)):
#     try:
#         subquery = (
#             select(vehicle_insert.jia_yi_id_vehicle, func.max(vehicle_insert.datetime).label("max_datetime"))
#             .group_by(vehicle_insert.jia_yi_id_vehicle)
#             .subquery()
#         )
#         query = (
#             select(vehicle_insert)
#             .join(subquery, and_(
#                 vehicle_insert.jia_yi_id_vehicle == subquery.c.jia_yi_id_vehicle,
#                 vehicle_insert.datetime == subquery.c.max_datetime
#             ))
#             .where(vehicle_insert.where == 'workShop')
#         )
#         heroesPersonal = db.exec(query).all()
#         records = [i.dict() for i in heroesPersonal]
#         df = pd.DataFrame.from_records(records).fillna(0)

#         # heroesPersonal = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#         # records = [i.dict() for i in heroesPersonal]
#         # df = pd.DataFrame.from_records(records).fillna(0)
#         # df = df.loc[df['where'] == 'workShop']

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
#         print(getCar)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getDriver = requests.get(url=url, json=body_raw).json()
#         getDriver = pd.DataFrame(getDriver)
#         getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
#         print(getDriver)

#         mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
#         mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
#         mergeDF = mergeDriver.sort_values(by='fen_dian_id', ascending=True)

#         # jia_yi_id_values = df['jia_yi_id_vehicle'].tolist()
#         query = select(vehicle_insert).where(text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         # query = select(vehicle_insert).where(vehicle_insert.jia_yi_id_vehicle.in_(jia_yi_id_values), text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         results = db.exec(query).all()
#         records = [i.dict() for i in results]
#         A100012 = pd.DataFrame.from_records(records).fillna(0)
#         A100012['details'] = A100012['details'].fillna('{"images": [], "descript": "", "active_status": ""}')
#         A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)
#         details_A100012 = json_normalize(A100012['details'])
#         A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})
#         A100012.drop('details', axis=1, inplace=True)
#         A100012 = A100012[['jia_yi_id_vehicle', 'descript']]
#         merge_descript = pd.merge(mergeDF, A100012, on='jia_yi_id_vehicle')

#         mergeDFLast = merge_descript.to_dict("records")
#     except:
#         mergeDFLast = []

#     return mergeDFLast


def MgetcarWorkShop100(db: Session = Depends(get_session)):
    try:
        subquery = (
            select(vehicle_insert.jia_yi_id_vehicle, func.max(vehicle_insert.datetime).label("max_datetime"))
            .group_by(vehicle_insert.jia_yi_id_vehicle)
            .subquery()
        )
        query = (
            select(vehicle_insert)
            .join(subquery, and_(
                vehicle_insert.jia_yi_id_vehicle == subquery.c.jia_yi_id_vehicle,
                vehicle_insert.datetime == subquery.c.max_datetime
            ))
            .where(vehicle_insert.where == 'workShop')
        )
        heroesPersonal = db.exec(query).all()
        records = [i.dict() for i in heroesPersonal]
        df = pd.DataFrame.from_records(records).fillna(0)

        # heroesPersonal = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
        # records = [i.dict() for i in heroesPersonal]
        # df = pd.DataFrame.from_records(records).fillna(0)
        # df = df.loc[df['where'] == 'workShop']

        # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
        to_dict = dfRename[['jia_yi_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getCar = requests.get(url=url, json=body_raw).json()
        getCar = pd.DataFrame(getCar)
        getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
        print(getCar)

        # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
        to_dict = dfRename[['jia_yi_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getDriver = requests.get(url=url, json=body_raw).json()
        getDriver = pd.DataFrame(getDriver)
        getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
        print(getDriver)

        mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
        mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
        mergeDF = mergeDriver.sort_values(by='fen_dian_id', ascending=True)

        try:
            jia_yi_id_values = df['jia_yi_id_vehicle'].tolist()
            # query = select(vehicle_insert).where(text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
            query = select(vehicle_insert).where(vehicle_insert.jia_yi_id_vehicle.in_(jia_yi_id_values), text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
            results = db.exec(query).all()
            records = [i.dict() for i in results]
            A100012 = pd.DataFrame.from_records(records).fillna(0)

            if A100012.empty:
                raise Exception("No data retrieved from the database")
        except Exception as e:
            print(f"Error: {e}")
            # Provided JSON data
            json_data = [
                {
                    "details": {
                        "images": [],
                        "descript": "",
                        "active_status": None
                    },
                    "jia_yi_id_vehicle": 99999,
                }
            ]
            # Convert JSON to DataFrame
            A100012 = pd.DataFrame(json_data)

        A100012['details'] = A100012['details'].fillna('{"images": [], "descript": "", "active_status": ""}')
        A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)
        details_A100012 = json_normalize(A100012['details'])
        A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})
        A100012.drop('details', axis=1, inplace=True)
        A100012 = A100012[['jia_yi_id_vehicle', 'descript']]
        merge_descript = pd.merge(mergeDF, A100012, on='jia_yi_id_vehicle')

        mergeDFLast = merge_descript.to_dict("records")
    except:
        mergeDFLast = []

    return mergeDFLast



# def MgetcarShop100(db: Session = Depends(get_session)):
#     try:
#         heroesPersonal = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#         records = [i.dict() for i in heroesPersonal]
#         df = pd.DataFrame.from_records(records).fillna(0)
#         df = df.query('where != "workShop"')
#         df = df.loc[df['status'] == 'repair']

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
#         print(getCar)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getDriver = requests.get(url=url, json=body_raw).json()
#         getDriver = pd.DataFrame(getDriver)
#         getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
#         print(getDriver)

#         mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
#         mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
#         mergeDF = mergeDriver.sort_values(by='fen_dian_id', ascending=True)
#         mergeDF = mergeDF.to_dict("records")
#     except:
#         mergeDF = []

#     return mergeDF


# def MgetcarShop100(db: Session = Depends(get_session)):
#     try:
#         heroesPersonal = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#         records = [i.dict() for i in heroesPersonal]
#         df = pd.DataFrame.from_records(records).fillna(0)
#         df = df.query('where != "workShop"')
#         df = df.loc[df['status'] == 'repair']

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
#         print(getCar)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getDriver = requests.get(url=url, json=body_raw).json()
#         getDriver = pd.DataFrame(getDriver)
#         getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
#         print(getDriver)

#         mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
#         mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
#         mergeDF = mergeDriver.sort_values(by='fen_dian_id', ascending=True)


#         query = select(vehicle_insert).where(text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         results = db.exec(query).all()
#         records = [i.dict() for i in results]
#         A100012 = pd.DataFrame.from_records(records).fillna(0)
#         A100012['details'] = A100012['details'].fillna('{"images": [], "descript": "", "active_status": ""}')
#         A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)
#         details_A100012 = json_normalize(A100012['details'])
#         A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})
#         A100012.drop('details', axis=1, inplace=True)
#         A100012 = A100012[['jia_yi_id_vehicle', 'descript']]

#         merge_descript = pd.merge(mergeDF, A100012, on='jia_yi_id_vehicle')

#         mergeDF = merge_descript.to_dict("records")
#     except:
#         mergeDF = []

#     return mergeDF


# def MgetcarShop100(db: Session = Depends(get_session)):
#     try:
#         lastDate = (
#             select(vehicle_insert.jia_yi_id_vehicle, func.max(vehicle_insert.datetime).label("max_datetime"))
#             .group_by(vehicle_insert.jia_yi_id_vehicle)
#             .subquery()
#         )
#         query = (
#             select(vehicle_insert)
#             .join(lastDate, and_(
#                 vehicle_insert.jia_yi_id_vehicle == lastDate.c.jia_yi_id_vehicle,
#                 vehicle_insert.datetime == lastDate.c.max_datetime
#             ))
#             .where(and_(
#                 vehicle_insert.status == 'repair',
#                 or_(
#                     vehicle_insert.where == None,
#                     vehicle_insert.where == 'shop',
#                 )
#             ))
#         )
#         heroesPersonal2 = db.exec(query).all()
#         records = [i.dict() for i in heroesPersonal2]
#         df = pd.DataFrame.from_records(records).fillna(0)
#         # heroesPersonal = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#         # records = [i.dict() for i in heroesPersonal]
#         # df = pd.DataFrame.from_records(records).fillna(0)
#         # df = df.query('where != "workShop"')
#         # df = df.loc[df['status'] == 'repair']

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
#         print(getCar)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getDriver = requests.get(url=url, json=body_raw).json()
#         getDriver = pd.DataFrame(getDriver)
#         getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
#         print(getDriver)

#         mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
#         mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
#         mergeDF = mergeDriver.sort_values(by='fen_dian_id', ascending=True)

#         jia_yi_id_values = df['jia_yi_id_vehicle'].tolist()
#         # query = select(vehicle_insert).where(text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         query = select(vehicle_insert).where(vehicle_insert.jia_yi_id_vehicle.in_(jia_yi_id_values), text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         results = db.exec(query).all()
#         records = [i.dict() for i in results]
#         A100012 = pd.DataFrame.from_records(records).fillna(0)
#         A100012['details'] = A100012['details'].fillna('{"images": [], "descript": "", "active_status": ""}')
#         A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)
#         details_A100012 = json_normalize(A100012['details'])
#         A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})
#         A100012.drop('details', axis=1, inplace=True)
#         A100012 = A100012[['jia_yi_id_vehicle', 'descript']]

#         merge_descript = pd.merge(mergeDF, A100012, on='jia_yi_id_vehicle')

#         mergeDF = merge_descript.to_dict("records")
#     except:
#         mergeDF = []

#     return mergeDF


# def MgetcarShop100(db: Session = Depends(get_session)):
#     try:
#         lastDate = (
#             select(vehicle_insert.jia_yi_id_vehicle, func.max(vehicle_insert.datetime).label("max_datetime"))
#             .group_by(vehicle_insert.jia_yi_id_vehicle)
#             .subquery()
#         )
#         query = (
#             select(vehicle_insert)
#             .join(lastDate, and_(
#                 vehicle_insert.jia_yi_id_vehicle == lastDate.c.jia_yi_id_vehicle,
#                 vehicle_insert.datetime == lastDate.c.max_datetime
#             ))
#             .where(and_(
#                 vehicle_insert.status == 'repair',
#                 or_(
#                     vehicle_insert.where == None,
#                     vehicle_insert.where == 'shop',
#                 )
#             ))
#         )
#         heroesPersonal2 = db.exec(query).all()
#         records = [i.dict() for i in heroesPersonal2]
#         df = pd.DataFrame.from_records(records).fillna(0)
#         # heroesPersonal = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#         # records = [i.dict() for i in heroesPersonal]
#         # df = pd.DataFrame.from_records(records).fillna(0)
#         # df = df.query('where != "workShop"')
#         # df = df.loc[df['status'] == 'repair']

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar = requests.get(url=url, json=body_raw).json()
#         getCar = pd.DataFrame(getCar)
#         getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
#         print(getCar)

#         # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
#         to_dict = dfRename[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getDriver = requests.get(url=url, json=body_raw).json()
#         getDriver = pd.DataFrame(getDriver)
#         getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
#         print(getDriver)

#         mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
#         mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
#         mergeDF = mergeDriver.sort_values(by='fen_dian_id', ascending=True)

#         # jia_yi_id_values = df['jia_yi_id_vehicle'].tolist()
#         query = select(vehicle_insert).where(text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         # query = select(vehicle_insert).where(vehicle_insert.jia_yi_id_vehicle.in_(jia_yi_id_values), text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
#         results = db.exec(query).all()
#         records = [i.dict() for i in results]
#         A100012 = pd.DataFrame.from_records(records).fillna(0)
#         A100012['details'] = A100012['details'].fillna('{"images": [], "descript": "", "active_status": ""}')
#         A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)
#         details_A100012 = json_normalize(A100012['details'])
#         A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})
#         A100012.drop('details', axis=1, inplace=True)
#         A100012 = A100012[['jia_yi_id_vehicle', 'descript']]

#         merge_descript = pd.merge(mergeDF, A100012, on='jia_yi_id_vehicle')

#         mergeDF = merge_descript.to_dict("records")
#     except:
#         mergeDF = []

#     return mergeDF


def MgetcarShop100(db: Session = Depends(get_session)):
    try:
        lastDate = (
            select(vehicle_insert.jia_yi_id_vehicle, func.max(vehicle_insert.datetime).label("max_datetime"))
            .group_by(vehicle_insert.jia_yi_id_vehicle)
            .subquery()
        )
        query = (
            select(vehicle_insert)
            .join(lastDate, and_(
                vehicle_insert.jia_yi_id_vehicle == lastDate.c.jia_yi_id_vehicle,
                vehicle_insert.datetime == lastDate.c.max_datetime
            ))
            .where(and_(
                # vehicle_insert.status == 'active_problem',
                vehicle_insert.status.in_(['repair', 'broken', 'active_problem']),
                or_(
                    vehicle_insert.where == None,
                    vehicle_insert.where == 'shop',
                )
            ))
        )
        heroesPersonal2 = db.exec(query).all()
        records = [i.dict() for i in heroesPersonal2]
        df = pd.DataFrame.from_records(records).fillna(0)
        # heroesPersonal = db.exec(select(vehicle_insert).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
        # records = [i.dict() for i in heroesPersonal]
        # df = pd.DataFrame.from_records(records).fillna(0)
        # df = df.query('where != "workShop"')
        # df = df.loc[df['status'] == 'repair']

        # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        dfRename = df.rename(columns={'jia_yi_id_vehicle': 'jia_yi_id'})
        to_dict = dfRename[['jia_yi_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getCar = requests.get(url=url, json=body_raw).json()
        getCar = pd.DataFrame(getCar)
        getCar = getCar.rename(columns={'jia_yi_id': 'jia_yi_id_vehicle'})
        print(getCar)

        # url = 'http://***********1:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        dfRename = df.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})
        to_dict = dfRename[['jia_yi_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getDriver = requests.get(url=url, json=body_raw).json()
        getDriver = pd.DataFrame(getDriver)
        getDriver = getDriver.rename(columns={'jia_yi_id': 'jia_yi_id_driver'})
        print(getDriver)

        mergeCar = pd.merge(df, getCar, on='jia_yi_id_vehicle')
        mergeDriver = pd.merge(mergeCar, getDriver, on='jia_yi_id_driver')
        mergeDF = mergeDriver.sort_values(by='fen_dian_id', ascending=True)

        try:
            jia_yi_id_values = df['jia_yi_id_vehicle'].tolist()
            # query = select(vehicle_insert).where(text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
            query = select(vehicle_insert).where(vehicle_insert.jia_yi_id_vehicle.in_(jia_yi_id_values), text("details->>'descript' != ''")).distinct(vehicle_insert.jia_yi_id_vehicle).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())
            results = db.exec(query).all()
            records = [i.dict() for i in results]
            A100012 = pd.DataFrame.from_records(records).fillna(0)

            if A100012.empty:
                raise Exception("No data retrieved from the database")
        except Exception as e:
            print(f"Error: {e}")
            # Provided JSON data
            json_data = [
                {
                    "details": {
                        "images": [],
                        "descript": "",
                        "active_status": None
                    },
                    "jia_yi_id_vehicle": 99999,
                }
            ]
            # Convert JSON to DataFrame
            A100012 = pd.DataFrame(json_data)

        A100012['details'] = A100012['details'].fillna('{"images": [], "descript": "", "active_status": ""}')
        A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)
        details_A100012 = json_normalize(A100012['details'])
        A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})
        A100012.drop('details', axis=1, inplace=True)
        A100012 = A100012[['jia_yi_id_vehicle', 'descript']]

        merge_descript = pd.merge(mergeDF, A100012, on='jia_yi_id_vehicle')

        mergeDF = merge_descript.to_dict("records")
    except:
        mergeDF = []

    return mergeDF


# def getExpireCar100(db: Session = Depends(get_session)):

#     def get_vehicle_insert():
#         import datetime as DT
#         today = DT.date.today()
#         week_ago = today - DT.timedelta(days=30)
#         heroesPersonal = db.exec(select(vehicle_insert).where(vehicle_insert.datetime > week_ago).order_by(vehicle_insert.datetime.desc())).all()
#         df = dataframe(heroesPersonal, to_dict=False)
#         df['descriptDate'] = df['datetime'].dt.date
#         df['descriptDateYMD'] = df['datetime'].dt.date
#         df = df[['datetime', 'descriptDate', 'descriptDateYMD', 'jia_yi_id_vehicle', 'details', 'status']]

#         # Convert 'details' column into a DataFrame and concatenate with the original DataFrame
#         details_df = json_normalize(df['details'])
#         df = pd.concat([df, details_df], axis=1).replace({pd.NA: None})
#         df.drop('details', axis=1, inplace=True)
#         df = df[['datetime', 'descriptDate', 'descriptDateYMD', 'jia_yi_id_vehicle', 'descript', 'status']]

#         # Group the data by 'sector' and 'date' and calculate the cumulative sum of 'change1d'
#         df = df.groupby(['jia_yi_id_vehicle', 'descriptDate']).first().reset_index()
#         df.sort_values(by=['jia_yi_id_vehicle', 'descriptDate'], ascending=[True, False], inplace=True)
#         # df = df.head(60)

#         # Get the current date
#         df['lastDate'] = df.groupby('jia_yi_id_vehicle')['datetime'].transform('first')
#         current_date = datetime.today().date()
#         df['today_date'] = current_date
#         df['descript'] = df['descript'].fillna("")
#         unique_descripts = df.groupby('jia_yi_id_vehicle')['descript'].unique()
#         descript_dict = unique_descripts.apply(lambda x: [val for val in x if val is not None and val != '0' and val != '']).to_dict()
#         df['descript_list'] = df['jia_yi_id_vehicle'].map(descript_dict)
#         df['descriptListCount'] = df['descript_list'].apply(len)
#         df['descriptDate'] = pd.to_datetime(df['descriptDate'])
#         df['today_date'] = pd.to_datetime(df['today_date'])
#         df['dayDiff'] = (df['today_date'] - df['descriptDate']).dt.days
#         df['lastStatus'] = df.groupby('jia_yi_id_vehicle')['status'].transform('first')
#         df = df[['jia_yi_id_vehicle', 'lastDate', 'lastStatus', 'descriptDate', 'descriptDateYMD', 'status', 'descript', 'dayDiff', 'descriptListCount', 'descript_list']]

#         # Define the custom function
#         def select_last_row(group):
#             non_empty_descript_rows = group[group['descript'] != '']
#             if not non_empty_descript_rows.empty:
#                 return non_empty_descript_rows.iloc[0]
#             else:
#                 return group.iloc[0]
#         df = df.groupby('jia_yi_id_vehicle').apply(select_last_row).reset_index(drop=True)

#         return df

#     def get_carActive_api():
#         url = f'{real_backend_api}/shwethe_car_active/api/v1/form/document/car_cocument?months_until_expiration=12'
#         df = requests.get(url=url)
#         df = df.json()
#         df = pd.DataFrame(df)

#         # Convert Unix timestamps to normal date format
#         df['start_date'] = pd.to_datetime(df['start_date'], unit='ms')
#         df['expiration_date'] = pd.to_datetime(df['expiration_date'], unit='ms')
#         df['today'] = pd.to_datetime('today').normalize()
#         df['vitus_left'] = (df['expiration_date'] - df['today']).dt.days
#         df = df.loc[df['document_types_name'] == 'ကားဝိဓါတ်']
#         df = df[['che_liang_id', 'vitus_left']]
#         return df

#     def get_petrol_car():
#         url = "http://***********30:8100/shwethe_petrol/api/v1/product/sendListOfCar"
#         response = requests.get(url)
#         getCar = response.json()
#         getCar = pd.DataFrame(getCar)

#         df100 = getCar.rename(columns={"che_liang_id": 'jia_yi_id'})
#         url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#         to_dict = df100[['jia_yi_id']].to_dict('records')
#         body_raw = {"data_api": to_dict}
#         getCar2 = requests.get(url=url, json=body_raw).json()
#         getCar2 = pd.DataFrame(getCar2)

#         merge = getCar.merge(getCar2, left_on='che_liang_id', right_on='jia_yi_id', how='left')
#         merge = merge.fillna("")

#         merge = merge[['che_liang_id', 'jia_yi_idname', 'datetime', 'type']]
#         return merge

#     vget_vehicle_insert = get_vehicle_insert()
#     vget_carActive_api = get_carActive_api()
#     vget_petrol_car = get_petrol_car()
#     merge_day_left = pd.merge(vget_vehicle_insert, vget_carActive_api, left_on='jia_yi_id_vehicle', right_on='che_liang_id', how="left").fillna('')
#     del merge_day_left['che_liang_id']
#     merge_petrol = pd.merge(merge_day_left, vget_petrol_car, left_on='jia_yi_id_vehicle', right_on='che_liang_id', how="left").replace({pd.NA: None})
#     del merge_petrol['che_liang_id']
#     merge_petrol.rename(columns={'datetime': 'petrol_date'}, inplace=True)
#     merge_petrol['petrol_date'] = pd.to_datetime(merge_petrol['petrol_date'])
#     merge_petrol = merge_petrol[pd.to_datetime(merge_petrol['petrol_date']).notnull()]

#     return merge_petrol.to_dict("records")


def getExpireCar100(db: Session = Depends(get_session)):

    heroesPersonal = db.exec(select(expirevitus).where(expirevitus.type == 'expirevitus').distinct(expirevitus.type).order_by(expirevitus.type, expirevitus.datetime.desc())).first()
    heroesPersonal = heroesPersonal.detail

    return heroesPersonal


def get_map_test_data(db: Session = Depends(get_session)):
    """
    Retrieve data from the map_test table.

    Args:
        db: Database session

    Returns:
        List of map_test records sorted by created_at in descending order
    """
    try:
        # Create a query to select records from map_test table
        # and order by created_at in descending order
        statement = select(map_test).order_by(map_test.created_at.desc())

        # Execute the query and return all results
        results = db.exec(statement).all()

        # If no results, return empty list
        if not results:
            return []

        # Convert SQLModel objects to dictionary records
        records = [i.dict() for i in results]

        # Convert to DataFrame and then to JSON records
        df = pd.DataFrame.from_records(records).fillna(0)
        result_json = df.to_dict("records")

        return result_json
    except Exception as e:
        # Log the error (in a production environment)
        print(f"Error retrieving map_test data: {str(e)}")
        # Return empty list in case of error
        return []


def create_map_test_data(hero: map_test_post, db: Session = Depends(get_session)):
    """
    Insert a new record into the map_test table.

    Args:
        hero: Data to insert
        db: Database session

    Returns:
        The inserted record
    """
    try:
        # Create a new map_test object from the provided data
        db_map_test = map_test.from_orm(hero)

        # Set created_at to current datetime if not provided
        if not db_map_test.created_at:
            db_map_test.created_at = get_datetime()

        # Add to database, commit, and refresh
        db.add(db_map_test)
        db.commit()
        db.refresh(db_map_test)

        return db_map_test
    except Exception as e:
        # Log the error (in a production environment)
        print(f"Error creating map_test data: {str(e)}")
        # Rollback in case of error
        db.rollback()
        # Re-raise the exception
        raise HTTPException(status_code=500, detail=f"Error creating map_test data: {str(e)}")


def delete_map_test_data(auto_id: int, db: Session = Depends(get_session)):
    """
    Delete a record from the map_test table by auto_id.

    Args:
        auto_id: ID of the record to delete
        db: Database session

    Returns:
        Success message
    """
    try:
        # Find the record to delete
        statement = select(map_test).where(map_test.auto_id == auto_id)
        result = db.exec(statement).first()

        # If record not found, raise 404
        if not result:
            raise HTTPException(status_code=404, detail=f"Map test data with ID {auto_id} not found")

        # Delete the record
        db.delete(result)
        db.commit()

        return {"message": f"Map test data with ID {auto_id} deleted successfully"}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log the error (in a production environment)
        print(f"Error deleting map_test data: {str(e)}")
        # Rollback in case of error
        db.rollback()
        # Re-raise as HTTP exception
        raise HTTPException(status_code=500, detail=f"Error deleting map_test data: {str(e)}")

def getSummaryCarQty100(db: Session = Depends(get_session)):
    def fetch_data():
        subquery = select(
            vehicle_insert.jia_yi_id_vehicle,
            func.max(vehicle_insert.datetime).label('last_datetime')
        ).group_by(vehicle_insert.jia_yi_id_vehicle).subquery()

        query = select(
            vehicle_insert.datetime,
            vehicle_insert.jia_yi_id_vehicle,
            vehicle_insert.status,
            vehicle.type,
            vehicle.fen_dian_id,
            vehicle_insert.where
        ).join(
            subquery,
            (vehicle_insert.jia_yi_id_vehicle == subquery.c.jia_yi_id_vehicle) &
            (vehicle_insert.datetime == subquery.c.last_datetime)
        ).join(
            vehicle,
            vehicle_insert.jia_yi_id_vehicle == vehicle.jia_yi_id
        ).order_by(
            vehicle_insert.datetime.desc()
        )

        result = db.execute(query).fetchall()
        return pd.DataFrame(result, columns=['datetime', 'jia_yi_id_vehicle', 'status', 'type', 'fen_dian_id', 'where'])

    def calculate_summary(A100012):
        A100012['status2'] = A100012['status'].apply(lambda x: 'active' if x == 'active_problem' else x)

        # # Create the 'fen' column based on the conditions
        # A100012['fen'] = A100012.apply(lambda row: row['where'] if pd.notnull(row['where']) else row['fen_dian_id'], axis=1)
        # Create the 'fen' column based on the conditions
        A100012['fen'] = A100012.apply(lambda row: row['fen_dian_id'] if row['where'] == 'shop' else row['where'] if pd.notnull(row['where']) else row['fen_dian_id'], axis=1)

        # Group by 'type', 'status', and 'fen' and count the occurrences
        grouped = A100012.groupby(['type', 'status2', 'fen']).size().reset_index(name='qty')

        # Pivot the table to get the desired format
        pivot_table = grouped.pivot_table(index=['type', 'status2'], columns='fen', values='qty', aggfunc='first')

        # Fill NaN values with 0
        pivot_table = pivot_table.fillna(0)

        # Format the output
        result = pivot_table.reset_index()
        result.columns = ['type', 'status2'] + [f'{col}' for col in result.columns if col not in ['type', 'status2']]

        # Combine the columns into a single string, showing only non-zero quantities
        result['fen(qty)'] = result.apply(lambda row: ' , '.join([f'{col}({int(row[col])})' for col in result.columns if col not in ['type', 'status2'] and row[col] > 0]), axis=1)
        result['fen_qty'] = result['fen(qty)']

        # Drop the individual columns
        result = result[['type', 'status2', 'fen_qty', 'fen(qty)']]

        def convert_fen_qty(fen_qty_str):
            if pd.isnull(fen_qty_str):
                return []
            fen_qty_list = fen_qty_str.split(', ')
            fen_qty_dict_list = []
            for fen_qty in fen_qty_list:
                shop, qty = fen_qty.split('(')
                qty = qty.rstrip(')')
                fen_qty_dict_list.append({"shop": shop, "qty": qty})
            return fen_qty_dict_list
        # Apply the function to the 'fen_qty' column
        result['fen_qty'] = result['fen_qty'].apply(convert_fen_qty)

        return result

    # Fetch data
    A100012 = fetch_data()

    # Calculate summary
    A100012 = calculate_summary(A100012)

    # Convert to dictionary
    A100012 = A100012.to_dict("records")

    return A100012




# def simpleOutputCarToday100(db: Session = Depends(get_session)):

#     heroesPersonal = db.exec(select(vehicle)).all()
#     type_car = dataframe(heroesPersonal, to_dict=False)

#     import datetime as DT
#     today = DT.date.today()
#     week_ago = today - DT.timedelta(days=0)
#     heroesPersonal = db.exec(select(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.status, vehicle_insert.datetime).where(vehicle_insert.datetime > week_ago).order_by(vehicle_insert.jia_yi_id_vehicle, vehicle_insert.datetime.desc())).all()
#     car = pd.DataFrame(heroesPersonal, columns=heroesPersonal[0].keys() if heroesPersonal else [])

#     # MERGE TYPE STATUS
#     mergeCar = pd.merge(type_car, car, left_on='jia_yi_id', right_on='jia_yi_id_vehicle', how='left').fillna(0)
#     mergeCar = mergeCar.to_dict("records")
#     return mergeCar



# 输入车主 如果 是没有的话
def create_vehicle_types(hero:vehicle_types_post,db: Session = Depends(get_session)):

    query = db.query(vehicle_types).filter_by(type_name=hero.type_name).first()
    if query:
        # The jia_yi_id already exists, raise an exception
        raise HTTPException(status_code=400, detail="The jia_yi_id already exists in the database")
    else:
        # The jia_yi_id does not exist, create a new VehicleType object and add it to the database
        db_vehicle_type = vehicle_types(**hero.dict())
        db.add(db_vehicle_type)
        db.commit()
        db.refresh(db_vehicle_type)
        return db_vehicle_type





# 输入车主 如果 是没有的话
def read_vehicle_types(db: Session = Depends(get_session)):

    vehicle_types_Data = db.query(vehicle_types).all()

    from src.common.jia_yi_name2 import jia_yi_name_package

    FD10001 = sqmodel_to_df(vehicle_types_Data)
    # FD10001
    # A100001 = jia_yi_name_package()
    # A100002 = A100001.select_data_list_id_with_http_v2(FD10001[['jia_yi_id']],'jia_yi')
    # X1001 = A100002.merge(FD10001, on=['jia_yi_id'], how='inner')
    X1002 = FD10001.to_json(orient='records')
    return json.loads(X1002)

# 输入车主 如果 是没有的话
def create_vehicle_owners_types(hero:vehicle_owners_post,db: Session = Depends(get_session)):

    query = db.query(vehicle_owners).filter_by(jia_yi_id=hero.jia_yi_id).first()
    if query:
        # The jia_yi_id already exists, raise an exception
        raise HTTPException(status_code=400, detail="The jia_yi_id already exists in the database")
    else:
        # The jia_yi_id does not exist, create a new VehicleType object and add it to the database
        db_vehicle_type = vehicle_owners(**hero.dict())
        db.add(db_vehicle_type)
        db.commit()
        db.refresh(db_vehicle_type)
        return db_vehicle_type

# 输入车主 如果 是没有的话
def read_vehicle_owners_types(db: Session = Depends(get_session)):

    vehicle_types_Data = db.query(vehicle_owners).all()

    from src.common.jia_yi_name2 import jia_yi_name_package

    FD10001 = sqmodel_to_df(vehicle_types_Data)
    FD10001
    A100001 = jia_yi_name_package()
    A100002 = A100001.select_data_list_id_with_http_v2(FD10001[['jia_yi_id']],'jia_yi')
    X1001 = A100002.merge(FD10001, on=['jia_yi_id'], how='inner')
    X1002 = X1001.to_json(orient='records')
    return json.loads(X1002)


# 输入车主 如果 是没有的话
def create_vehicles(hero:vehicles_post,db: Session = Depends(get_session)):

    query = db.query(vehicles).filter_by(che_liang_id=hero.che_liang_id).first()
    if query:
        # The jia_yi_id already exists, raise an exception
        raise HTTPException(status_code=400, detail="The jia_yi_id already exists in the database")
    else:
        # The jia_yi_id does not exist, create a new VehicleType object and add it to the database
        db_vehicle_type = vehicles(**hero.dict())
        db.add(db_vehicle_type)
        db.commit()
        db.refresh(db_vehicle_type)
        return db_vehicle_type

def update_vehicle(che_liang_id: int, hero: vehicles_post, db: Session = Depends(get_session)):
    db_vehicle = db.query(vehicles).filter_by(che_liang_id=che_liang_id).first()
    if not db_vehicle:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    else:
        exclude_cols = {'che_liang_id'} # columns that are excluded from the update
        for key, value in hero.dict(exclude_unset=True, exclude=exclude_cols).items():
            setattr(db_vehicle, key, value)
        db.commit()
        db.refresh(db_vehicle)
        return db_vehicle

# 输入车主 如果 是没有的话
def read_vehicles(db: Session = Depends(get_session)):


    from src.common.jia_yi_name2 import jia_yi_name_package

    vehicles_Data = db.query(vehicles).all()

    # if not vehicle_types_Data:
    #     return {"message": "Vehicle owner ID is required"}

    # vehicle_types_data = db.query(vehicle_owners).all()

    FD10001 = sqmodel_to_df(vehicles_Data)

    # FD10001 = FD10001.rename(columns={'che_liang_id': 'vehicle_id', 'owner_id': 'owner_id'})
    A100001 = jia_yi_name_package()
    FD10001['jia_yi_id'] = FD10001['owner_id']
    A100002 = A100001.select_data_list_id_with_http_v3(FD10001[['jia_yi_id']],'owner')
    FD10001 = FD10001.drop(columns=['jia_yi_id'])
    A100002 = A100002.merge(FD10001, on=['owner_id'], how='inner')
    A100002['jia_yi_id'] = A100002['che_liang_id']
    FD10001 = A100001.select_data_list_id_with_http_v3(A100002[['jia_yi_id']],'che_liang')
    A100002 = A100002.merge(FD10001, on=['che_liang_id'], how='inner')


    # A100003 = A100001.select_data_list_id_with_http_v2(FD10001[['vehicle_id']],'che_liang')
    # X1002 = X1001.merge(FD10001, on=['owner_id', 'vehicle_id'], how='inner')
    X1003 = A100002.to_json(orient='records')

    return json.loads(X1003)



# 输入车主 如果 是没有的话
def create_document_types(hero:document_types_post,db: Session = Depends(get_session)):

    query = db.query(document_types).filter_by(document_types_name=hero.document_types_name).first()
    if query:
        # The jia_yi_id already exists, raise an exception
        raise HTTPException(status_code=400, detail="The document_types_name already exists in the database")
    else:
        # The jia_yi_id does not exist, create a new VehicleType object and add it to the database
        db_vehicle_type = document_types(**hero.dict())
        db.add(db_vehicle_type)
        db.commit()
        db.refresh(db_vehicle_type)
        return db_vehicle_type


def read_document_types(db: Session = Depends(get_session)):

    vehicle_types_Data = db.query(document_types).all()

    return vehicle_types_Data



# 输入车主 如果 是没有的话
def create_document_type_vehicle_type_relations(hero:document_type_vehicle_type_relations_post,db: Session = Depends(get_session)):

    query = db.query(document_type_vehicle_type_relations).filter_by(document_type_id=hero.document_type_id, applicable_vehicle_type_id=hero.applicable_vehicle_type_id).first()

    if query:
        # The jia_yi_id already exists, raise an exception
        raise HTTPException(status_code=400, detail="The document_types_name already exists in the database")
    else:
        # The jia_yi_id does not exist, create a new VehicleType object and add it to the database
        db_vehicle_type = document_type_vehicle_type_relations(**hero.dict())
        db.add(db_vehicle_type)
        db.commit()
        db.refresh(db_vehicle_type)
        return db_vehicle_type


def read_document_type_vehicle_type_relations(db: Session = Depends(get_session)):

    # result = db.query(
    #         document_type_vehicle_type_relations.relations_id,
    #         document_type_vehicle_type_relations.document_type_id,
    #         document_type_vehicle_type_relations.applicable_vehicle_type_id,
    #         document_types.document_types_name,
    #         document_types.document_types_description,
    #         vehicle_types.type_name

    #     )\
    #     .join(document_types, document_type_vehicle_type_relations.document_type_id == document_types.document_types_id)\
    #     .join(vehicle_types, document_type_vehicle_type_relations.applicable_vehicle_type_id == vehicle_types.type_id)\
    #     .all()

    result = db.query(
            document_type_vehicle_type_relations.relations_id,
            document_type_vehicle_type_relations.document_type_id,
            document_type_vehicle_type_relations.applicable_vehicle_type_id,
            document_types.document_types_name,
            document_types.document_types_description,
            vehicle_types.type_name
        )\
        .outerjoin(document_types, document_type_vehicle_type_relations.document_type_id == document_types.document_types_id)\
        .outerjoin(vehicle_types, document_type_vehicle_type_relations.applicable_vehicle_type_id == vehicle_types.type_id)\
        .all()

    return result


# def read_document_type_vehicle_type_relations(db: Session = Depends(get_session)):
#     query = db.query(document_type_vehicle_type_relations)\
#         .join(document_types, document_type_vehicle_type_relations.document_type_id == document_types.document_types_id)\
#         .join(vehicle_types, document_type_vehicle_type_relations.applicable_vehicle_type_id == vehicle_types.type_id)\
#         .options(# changed to relationship property
#                  joinedload(document_type_vehicle_type_relations.document_type),
#                  # changed to relationship property
#                  joinedload(document_type_vehicle_type_relations.applicable_vehicle_type))

#     # Convert the query to SQL text
#     sql_text = str(query.statement.compile(compile_kwargs={"literal_binds": True}))

#     return text(sql_text)



def getDocument(db: Session = Depends(get_session)):

    from src.common.jia_yi_name2 import jia_yi_name_package

    # query = text("""
    #     SELECT v.che_liang_id, dtvtr.document_type_id, dt.document_types_name, vd.document_id
    #     FROM vehicles AS v
    #     CROSS JOIN document_type_vehicle_type_relations AS dtvtr
    #     LEFT JOIN vehicle_documents AS vd
    #     ON v.che_liang_id = vd.che_liang_id AND dtvtr.document_type_id = vd.document_types_id
    #     INNER JOIN document_types AS dt
    #     ON dtvtr.document_type_id = dt.document_types_id
    # """)

    query = text("""
        WITH ranked_vehicle_documents AS (
            SELECT
                v.che_liang_id,
                dtvtr.document_type_id,
                dt.document_types_name,
                vd.document_id,
                vd.start_date,
                vd.expiration_date,
                vd.images,
                ROW_NUMBER() OVER (PARTITION BY v.che_liang_id, dtvtr.document_type_id ORDER BY vd.document_id DESC) AS row_number
            FROM vehicles AS v
            CROSS JOIN document_type_vehicle_type_relations AS dtvtr
            LEFT JOIN vehicle_documents AS vd
                ON v.che_liang_id = vd.che_liang_id AND dtvtr.document_type_id = vd.document_types_id
            INNER JOIN document_types AS dt
                ON dtvtr.document_type_id = dt.document_types_id
        )
        SELECT
            che_liang_id,
            document_type_id,
            document_types_name,
            document_id,
            start_date,
            expiration_date,
            images
        FROM ranked_vehicle_documents
        WHERE row_number = 1;

            """)

    result = db.execute(query)

    # Convert the result to a pandas DataFrame
    result = pd.DataFrame(result.fetchall(), columns=result.keys())
    # result = db.query(query).all()

    A100001 = jia_yi_name_package()

    result['jia_yi_id'] = result['che_liang_id']

    A100002 = A100001.select_data_list_id_with_http_v3(result[['jia_yi_id']],'che_liang')

    result = result.drop(columns=['jia_yi_id'])

    A100002 = A100002.merge(result, on=['che_liang_id'], how='inner')

    X1003 = A100002.to_json(orient='records')

    return json.loads(X1003)


def getDocumentWithParme(db: Session = Depends(get_session), months_until_expiration: int = 3):

    from src.common.jia_yi_name2 import jia_yi_name_package

    query = text(f"""
        WITH ranked_vehicle_documents AS (
            SELECT
                v.che_liang_id,
                dtvtr.document_type_id,
                dt.document_types_name,
                vd.document_id,
                vd.start_date,
                vd.expiration_date,
                vd.images,
                ROW_NUMBER() OVER (PARTITION BY v.che_liang_id, dtvtr.document_type_id ORDER BY vd.document_id DESC) AS row_number
            FROM vehicles AS v
            CROSS JOIN document_type_vehicle_type_relations AS dtvtr
            LEFT JOIN vehicle_documents AS vd
                ON v.che_liang_id = vd.che_liang_id AND dtvtr.document_type_id = vd.document_types_id
            INNER JOIN document_types AS dt
                ON dtvtr.document_type_id = dt.document_types_id
        )
        SELECT
            che_liang_id,
            document_type_id,
            document_types_name,
            document_id,
            start_date,
            expiration_date,
            images
        FROM ranked_vehicle_documents
        WHERE row_number = 1 AND expiration_date <= CURRENT_DATE + INTERVAL '{months_until_expiration} months';

            """)

    result = db.execute(query)

    result = pd.DataFrame(result.fetchall(), columns=result.keys())
    print("22222222222222222222222222222", result)

    A100001 = jia_yi_name_package()

    result['jia_yi_id'] = result['che_liang_id']

    A100002 = A100001.select_data_list_id_with_http_v3(result[['jia_yi_id']],'che_liang')

    result = result.drop(columns=['jia_yi_id'])

    A100002 = A100002.merge(result, on=['che_liang_id'], how='inner')

    X1003 = A100002.to_json(orient='records')

    return json.loads(X1003)




# 输入车主 如果 是没有的话
def postDocument(hero:vehicle_documents_post,db: Session = Depends(get_session)):

    db_vehicle_type = vehicle_documents(**hero.dict())
    db.add(db_vehicle_type)
    db.commit()
    db.refresh(db_vehicle_type)

    return db_vehicle_type

