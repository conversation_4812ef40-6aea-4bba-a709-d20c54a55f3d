import os
import configparser
from sqlalchemy import create_engine
from sqlalchemy.pool import NullPool

# 获取当前脚本的绝对路径
current_path = os.path.abspath(os.path.dirname(__file__))

# 基于当前脚本的位置构建config.ini的路径
config_path = os.path.join(current_path, '..', 'config', 'config.ini')

config = configparser.ConfigParser()
config.read(config_path)

# print(current_path)

def get_connection(db_name):
    db_config = config[db_name]
    DATABASE_URL = db_config['URL']
    connect_args = {"options": f"-c timezone={db_config['TIMEZONE']}"}
    
    # return create_engine(
    #     DATABASE_URL,
    #     echo=True,
    #     poolclass=NullPool,
    #     connect_args=connect_args
    # )

    return create_engine(
        DATABASE_URL,
        echo=True,
        pool_size=1,      # 池中维护的连接数量
        max_overflow=2,   # 超出池大小的连接数量
        pool_timeout=30,   # 获取连接的最大等待时间（秒）
        pool_recycle=3600, # 自动回收连接的时间间隔（秒）
        connect_args=connect_args
    )
