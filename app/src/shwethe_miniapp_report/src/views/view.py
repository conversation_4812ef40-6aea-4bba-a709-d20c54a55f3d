from fastapi import APIRouter, Depends, Query, WebSocket
from pydantic.types import List
from sqlmodel import Session
from fastapi.responses import HTMLResponse

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_miniapp_report.database import get_session
from src.shwethe_miniapp_report.src.crud.crud import (
    getCarAtive100,
    getTable100,
    getTableBydate100,
    getTableWithDate100,
    getSenderID100,
    getDetailsID100,
    selectStory100,
    selectReceiver100,
    getFormStory100,
    formInsert100,
    MgetTable100,
    MgetDetails100,
    MgetDetailsVer2100,
    MchangeStatus100,
    MchangeStatusVer2100,
    MchangeStatusVer3100,
    getTimelineUser100,
    formUpdate100,
    getAAAA100,
    getAAAA20100,
    Mcontain_person100,
    MallPersonStatus100,
    delByID100,
    personCommitTable100,
    day_schedule_table100,
    getPersonName100,
    getStoreName100,
    testPPP100,
    MgetStory100,
    getAppSpeechCar100,
    websocket_endpoint100,
    getChatWebSite100
)
from src.shwethe_miniapp_report.src.models.models import title_data

from fastapi import APIRouter, Depends, Query, Body

router = APIRouter()


@router.get("/getCarAtive")
def getCarAtive(db: Session = Depends(get_session)):
    return getCarAtive100(db=db)

@router.get("/getTable")
def getTable(db: Session = Depends(get_session)):
    return getTable100(db=db)

@router.get("/getTableBydate")
def getTableBydate(db: Session = Depends(get_session)):
    return getTableBydate100(db=db)

@router.get("/getTableWithDate")
def getTableWithDate(dateSelect: str, db: Session = Depends(get_session)):
    return getTableWithDate100(dateSelect=dateSelect, db=db)

@router.get("/getSenderID")
def getSenderID(title_id: int, db: Session = Depends(get_session)):
    return getSenderID100(title_id=title_id, db=db)

@router.get("/getDetailsID")
def getDetailsID(title_id: int, db: Session = Depends(get_session)):
    return getDetailsID100(title_id=title_id, db=db)

@router.get("/selectStory")
def selectStory(db: Session = Depends(get_session)):
    return selectStory100(db=db)

@router.get("/getFormStory")
def getFormStory(story_id: int, db: Session = Depends(get_session)):
    return getFormStory100(story_id=story_id, db=db)

@router.get("/selectReceiver")
def selectReceiver(getApiCarDriver: str, db: Session = Depends(get_session)):
    return selectReceiver100(getApiCarDriver=getApiCarDriver, db=db)

@router.post("/formInsert")
def formInsert(person: dict = Body(...), db: Session = Depends(get_session)):
    return formInsert100(person, db=db)

@router.get("/MgetTable")
def MgetTable(db: Session = Depends(get_session)):
    return MgetTable100(db=db)

@router.get("/MgetDetails")
def MgetDetails(title_data_id: int, db: Session = Depends(get_session)):
    return MgetDetails100(title_data_id=title_data_id, db=db)

@router.get("/MgetDetailsVer2")
def MgetDetailsVer2(title_data_id: int, user_id: int, db: Session = Depends(get_session)):
    return MgetDetailsVer2100(title_data_id=title_data_id, user_id=user_id, db=db)

@router.post("/MchangeStatus")
def MchangeStatus(person: dict = Body(...), db: Session = Depends(get_session)):
    return MchangeStatus100(person, db=db)

@router.post("/MchangeStatusVer2")
def MchangeStatusVer2(person: dict = Body(...), db: Session = Depends(get_session)):
    return MchangeStatusVer2100(person, db=db)

@router.post("/MchangeStatusVer3")
def MchangeStatusVer3(person: dict = Body(...), db: Session = Depends(get_session)):
    return MchangeStatusVer3100(person, db=db)

@router.get("/getTimelineUser")
def getTimelineUser(title_data_id: int, db: Session = Depends(get_session)):
    return getTimelineUser100(title_data_id=title_data_id, db=db)

@router.put("/formUpdate")
def formUpdate(person: dict = Body(...), db: Session = Depends(get_session)):
    return formUpdate100(person, db=db)
    
@router.get("/Mcontain_person")
def Mcontain_person(jia_yi_id: int, db: Session = Depends(get_session)):
    return Mcontain_person100(jia_yi_id=jia_yi_id, db=db)

@router.get("/MallPersonStatus")
def MallPersonStatus(title_id: int, db: Session = Depends(get_session)):
    return MallPersonStatus100(title_id=title_id, db=db)

@router.delete("/delByID")
def delByID(title_id: int, db: Session = Depends(get_session)):
    return delByID100(title_id=title_id, db=db)

@router.get("/personCommitTable")
def personCommitTable(title_id: int, db: Session = Depends(get_session)):
    return personCommitTable100(title_id=title_id, db=db)
    
@router.get("/day_schedule_table")
def day_schedule_table(year: str, month: str, db: Session = Depends(get_session)):
    return day_schedule_table100(year=year, month=month, db=db)

@router.get("/MgetStory")
def MgetStory(db: Session = Depends(get_session)):
    return MgetStory100(db=db)


@router.websocket("/chat/{group_id}/{user_id}")
def websocket_endpoint(websocket: WebSocket, group_id: str, user_id: str, db: Session = Depends(get_session)):
    return websocket_endpoint100(websocket=websocket, group_id=group_id, user_id=user_id, db=db)

@router.get("/getChatWebSite/{group_id}")
def getChatWebSite(group_id: str, db: Session = Depends(get_session)):
    return getChatWebSite100(group_id=group_id, db=db)

html = """
<!DOCTYPE html>
<html>
    <head>
        <title>Chat</title>
    </head>
    <body>
        <h1>WebSocket Chat</h1>
        <form action="" onsubmit="sendMessage(event)">
            <input type="text" id="messageText" autocomplete="off"/>
            <button>Send</button>
        </form>
        <ul id='messages'>
        </ul>
        <script>
            var groupId = "group1"; // Set this dynamically based on the group
            var ws;

            function connect() {
                ws = new WebSocket(`ws://************:8021/shwethe_miniapp_report/api/v1/form/chat/${groupId}/u1`);
                ws.onmessage = function(event) {
                    var messages = document.getElementById('messages')
                    var message = document.createElement('li')
                    var content = document.createTextNode(event.data)
                    message.appendChild(content)
                    messages.appendChild(message)
                };

                ws.onclose = function(event) {
                    console.log('WebSocket is closed. Reconnecting...');
                    setTimeout(connect, 1000);
                };

                ws.onerror = function(event) {
                    console.error('WebSocket error observed:', event);
                    ws.close();
                };
            }

            function sendMessage(event) {
                var input = document.getElementById("messageText")
                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(input.value)
                }
                input.value = ''
                event.preventDefault()
            }

            connect();
        </script>
    </body>
</html>
"""
@router.get("/chat")
async def get():
    return HTMLResponse(html)



# -------------------------------------- API select option
@router.get("/getAAAA")
def getAAAA(db: Session = Depends(get_session)):
    return getAAAA100(db=db)

@router.get("/getAAAA20")
def getAAAA20(db: Session = Depends(get_session)):
    return getAAAA20100(db=db)

@router.get("/getPersonName")
def getPersonName(db: Session = Depends(get_session)):
    return getPersonName100(db=db)

@router.get("/getStoreName")
def getStoreName(db: Session = Depends(get_session)):
    return getStoreName100(db=db)

@router.get("/testPPP")
def testPPP(db: Session = Depends(get_session)):
    return testPPP100(db=db)

@router.get("/getAppSpeechCar")
def getAppSpeechCar(db: Session = Depends(get_session)):
    return getAppSpeechCar100(db=db)
