from sqlite3 import dbapi2
import requests
from sqlmodel import Session, select,SQLModel,and_
from typing import List, Optional

from datetime import datetime, timedelta
from src.shwethe_miniapp_report.database import get_session
from helper import generate_datetime_id
from src.shwethe_miniapp_report.src.models.models import title_data, details_data, related_person, option, story, form_story, logs, schedule, store_name, chat_message
import json
import pandas as pd
import calendar
from src.time_zone.time_zone_function import get_datetime
from sqlalchemy import and_, or_, not_, func, literal_column, desc
from fastapi import APIRouter, Depends, Query, Body, WebSocket
from fastapi.responses import HTMLResponse
from src.Connect.https_connect import mongodb_data_api
# from sqlmodel.sql.expression import literal_column

pd.set_option('display.max_columns', None)

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def getCarAtive100(db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(title_data)).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records).fillna(0)

        mergeDF = df.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def getTable100(db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(title_data)).all()
        records = [i.dict() for i in heroesPersonal]   
        title_data_df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal = db.exec(select(details_data)).all()
        records = [i.dict() for i in heroesPersonal]   
        details_data_df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal = db.exec(select(form_story)).all()
        records = [i.dict() for i in heroesPersonal]   
        form_story_df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal = db.exec(select(story)).all()
        records = [i.dict() for i in heroesPersonal]   
        story_df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal = db.exec(select(option)).all()
        records = [i.dict() for i in heroesPersonal]   
        option_df = pd.DataFrame.from_records(records).fillna(0)

        find_title_data_id = pd.merge(title_data_df, details_data_df, left_on='auto_id', right_on='title_data_id')
        find_key = pd.merge(find_title_data_id, form_story_df, left_on='key', right_on='auto_id')
        mergeStory = pd.merge(find_key, story_df, left_on='type_id', right_on='auto_id')
        mergeStatus = pd.merge(mergeStory, option_df, left_on='status_id', right_on='auto_id')
        # print(mergeStatus)

        df = mergeStatus

        # # Define a custom function to aggregate the columns for each group
        def aggregate_group(group):
            result = group.iloc[0].copy()  # Get the first row of the group
            result["store"] = ""  # Initialize store and schedule_date to empty strings
            result["schedule_date"] = ""
            for _, row in group.iterrows():
                if row["input_value"] == "storeSelect":
                    result["store"] = row["value"]  # Store the value in the 'store' column
                elif row["input_value"] == "scheduleDate":
                    result["schedule_date"] = row["value"]  # Store the value in the 'schedule_date' column
            return result

        # # Group the DataFrame by 'title_data_id' and apply the custom function
        grouped_df = df.groupby("title_data_id").apply(aggregate_group).reset_index(drop=True)

        # # Select the desired columns and convert the result to a list of dicts
        result = grouped_df[["auto_id_x", "start_date", "title", "fen_dian_id", "end_date",
                            "title_data_id", "input_value", "type_name", "mm_name",
                            "store", "schedule_date"]]
        
        result['store'] = result['store'].apply(lambda x: x.split("|")[-2].strip() if "|" in x else x)

        output_df = result.sort_values(by='start_date', ascending=False)

        mergeDF = output_df.to_dict("records")
    except: 
        mergeDF = []

    return mergeDF

def getTableBydate100(db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(title_data)).all()
        records = [i.dict() for i in heroesPersonal]   
        title_data_df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal = db.exec(select(story).where(story.type_form == 'schedule')).all()
        records = [i.dict() for i in heroesPersonal]   
        story_df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal = db.exec(select(option)).all()
        records = [i.dict() for i in heroesPersonal]   
        option_df = pd.DataFrame.from_records(records).fillna(0)

        mergeStory = pd.merge(title_data_df, story_df, left_on='type_id', right_on='auto_id')
        mergeStatus = pd.merge(mergeStory, option_df, left_on='status_id', right_on='auto_id')
        timeSort = mergeStatus.sort_values(by='start_date', ascending=False)
        mergeDF = timeSort.to_dict("records")
        # mergeDF = mergeStatus.to_dict("records")
    except: 
        mergeDF = []
    return mergeDF

def getTableWithDate100(dateSelect: str, db: Session = Depends(get_session)):
    try:
        ccc = getTable100(db=db)
        filtered_list = [item for item in ccc if item["schedule_date"] == dateSelect]
        
        mergeDF = filtered_list
    except: 
        mergeDF = []
    return mergeDF

def getSenderID100(title_id: int, db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(related_person).where(related_person.title_id == title_id)).all()
        records = [i.dict() for i in heroesPersonal]   
        related_person_df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal = db.exec(select(option)).all()
        records = [i.dict() for i in heroesPersonal]   
        option_df = pd.DataFrame.from_records(records).fillna(0)

        mergeSender = pd.merge(related_person_df, option_df, left_on='key', right_on='auto_id')

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        dfRename = mergeSender.rename(columns={'value': 'jia_yi_id'})
        to_dict = dfRename[['jia_yi_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getPerson = requests.get(url=url, json=body_raw).json()
        getPerson = pd.DataFrame(getPerson)
        print(getPerson)

        mergePerson = pd.merge(mergeSender, getPerson, left_on='value', right_on='jia_yi_id')
        mergeDF = mergePerson.sort_values(by='auto_id_y', ascending=True)
        mergeDF = mergeDF.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def getDetailsID100(title_id: int, db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(details_data).where(details_data.title_data_id == title_id)).all()
        records = [i.dict() for i in heroesPersonal]   
        details_data_df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal = db.exec(select(form_story).where(form_story.story_id == details_data_df.to_dict("records")[0]['story_id'])).all()
        records = [i.dict() for i in heroesPersonal]   
        form_story_df = pd.DataFrame.from_records(records).fillna(0)

        mergeFormValue = pd.merge(details_data_df, form_story_df, left_on='key', right_on='auto_id')
        mergeFormValue = mergeFormValue.sort_values(by='index', ascending=True)

        grouped_list = {}
        for item in mergeFormValue.to_dict("records"):
            key = (item["input_string"], item["input_type"])
            if key not in grouped_list:
                grouped_list[key] = []
            grouped_list[key].append({"value": item["value"]})

        output = [{"input_string": key[0], "input_type": key[1], "list_json": values} for key, values in grouped_list.items()]

        print(output)
        
        # mergeDF = mergeFormValue.to_dict("records")
        mergeDF = output
    except:
        mergeDF = []
    return mergeDF


def selectStory100(db: Session = Depends(get_session)):
    try:
        # heroesPersonal = db.exec(select(story).where(story.type_form == 'normal')).all()
        heroesPersonal = db.exec(select(story)).all()
        records = [i.dict() for i in heroesPersonal]   
        story_df = pd.DataFrame.from_records(records).fillna(0)

        mergeDF = story_df.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def getFormStory100(story_id: int, db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(form_story).where(form_story.story_id == story_id).order_by(form_story.story_id, form_story.index)).all()
        records = [i.dict() for i in heroesPersonal]   
        form_story_df = pd.DataFrame.from_records(records).fillna(0)

        result = []
        temp = {}
        for item in form_story_df.to_dict("records"):
            if item['parent_id'] == 0:
                if temp:
                    result.append(temp)
                temp = item
                temp['optionList'] = []
            else:
                temp['optionList'].append(item)
        result.append(temp)

        print(result)

        # mergeDF = form_story_df.to_dict("records")
        mergeDF = result
    except:
        mergeDF = []
    return mergeDF


def selectReceiver100(getApiCarDriver: str, db: Session = Depends(get_session)):
    # print(getApiCarDriver)
    # print(type(getApiCarDriver))

    try:
        getApiCarDriver = int(getApiCarDriver)
        print(getApiCarDriver)
        print(type(getApiCarDriver))

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        # url = Arter_api + 'jia_yi_name_list_id'
        # body_raw = {"data_api": [{"jia_yi_id": 36557}]}
        body_raw = {"data_api": [{"jia_yi_id": getApiCarDriver}]}
        # body_raw = {"data_api": df}
        df2 = requests.get(url=url, json=body_raw)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')
        # print(df2)
    except:
        print('The provided value is not an integer')
        print(getApiCarDriver)
        print(type(getApiCarDriver))

        # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={getApiCarDriver}'
        url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={getApiCarDriver}'
        # url = Arter_api + f'jia_yi_search_text?text={getApiCarDriver}'
        df2 = requests.get(url=url)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')
        # print(df2)
    return df2


def formInsert100(person: dict = Body(...), db: Session = Depends(get_session)):

    # INSERT TO title_data
    form_json = person['insert']['form']
    hero_title_data = title_data(title=form_json['title'], type_id=form_json['story'], fen_dian_id=form_json['fen_dian_id'], start_date=get_datetime(), status_id=2)
    db.add(hero_title_data)
    db.commit()
    # --------------------------

    # INSERT TO related_person
    result = []
    result.append({
        "title_id": hero_title_data.auto_id,
        "option_id": 6,
        "key": 7,
        "value": form_json["sender"],
    })
    for receiver in form_json["receiver"]:
        result.append({
            "title_id": hero_title_data.auto_id,
            "option_id": 6,
            "key": 8,
            "value": receiver,
        })
    print(result)
    for entry in result:
            hero_related_person = related_person(title_id=entry['title_id'], option_id=entry['option_id'], key=entry['key'], value=entry['value'])
            db.add(hero_related_person)
            db.commit()
    # --------------------------

    # INSERT TO logs
    json_one = {
                "dateType": {
                    "key": "startDate",
                    "value": str(get_datetime())
                },
                "statusForm": {
                    "key": "waiting",
                    "value": "waiting"
                },
                "statusPerson": {
                    "key": "sender",
                    "value": form_json["senderShow"]
                }
            }
    json_form = {
                "insertForm": person
            }
    hero_logs = logs(json_text=json_one, title_data_id=hero_title_data.auto_id, json_form=json_form)
    db.add(hero_logs)
    db.commit()
    # --------------------------

    # INSERT TO details_data
    heroesForm_story = db.exec(select(form_story).where(form_story.story_id == form_json['story'])).all()
    records = [i.dict() for i in heroesForm_story]   

    details = form_json['details']
    result = []
    for key, value in details.items():
        if isinstance(value, list):
            for item in value:
                result.append({key: item})
        else:
            result.append({key: value})
    print(result)

    json_ccc = []
    for bbb in result:
        for aaa in records:
            if list(bbb.keys())[0] == aaa['input_value']:
                json_ccc.append({'title_data_id': hero_title_data.auto_id, 'key': aaa['auto_id'], 'value': list(bbb.values())[0], 'story_id': form_json['story']})
    print(json_ccc)

    for entry in json_ccc:
        hero_details_data = details_data(title_data_id=entry['title_data_id'], key=entry['key'], value=entry['value'], story_id=entry['story_id'])
        db.add(hero_details_data)
        db.commit()

    return person


def MgetTable100(db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(title_data)).all()
        records = [i.dict() for i in heroesPersonal]   
        title_data_df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal = db.exec(select(story)).all()
        records = [i.dict() for i in heroesPersonal]   
        story_df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal = db.exec(select(option)).all()
        records = [i.dict() for i in heroesPersonal]   
        option_df = pd.DataFrame.from_records(records).fillna(0)

        mergeStory = pd.merge(title_data_df, story_df, left_on='type_id', right_on='auto_id')
        mergeStatus = pd.merge(mergeStory, option_df, left_on='status_id', right_on='auto_id')
        mergeStatus = mergeStatus.sort_values(by='start_date', ascending=False)

        # mini app list bug create fake data first row
        fake_data = mergeStatus.iloc[[0]].copy()
        fake_data['title'] = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        fake_data['mm_name'] = ""
        concat_data = pd.concat([fake_data.iloc[[0]], mergeStatus, fake_data.iloc[1:]])

        mergeDF = concat_data.to_dict("records")
    except: 
        mergeDF = []

    return mergeDF


def MgetDetails100(title_data_id: int, db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(logs).where(logs.title_data_id == title_data_id).distinct(logs.title_data_id).order_by(logs.title_data_id, logs.json_text['dateType']['value'].desc())).all()
        records = [i.dict() for i in heroesPersonal]   
        logs_df = pd.DataFrame.from_records(records).fillna(0)

        mergeDF = logs_df.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def MgetDetailsVer2100(title_data_id: int, user_id: int, db: Session = Depends(get_session)):
    try:
        print(title_data_id, user_id)

        heroesPersonal = db.exec(select(logs) \
            .where(
                and_(
                    logs.title_data_id == title_data_id,
                    literal_column("json_text -> 'statusPerson' ->> 'value'").ilike(f"{user_id}%")
                )
            ) 
            .distinct(literal_column("json_text -> 'statusPerson' ->> 'value'"))
            .order_by(literal_column("json_text -> 'statusPerson' ->> 'value'"), desc(literal_column("json_text -> 'dateType' ->> 'value'")))
        ) \
        .all()

        if heroesPersonal:
            print("have")
            json_one = heroesPersonal
        else:
            print("no")

            heroesPersonal = db.exec(select(logs) \
                .where(
                    and_(
                        logs.title_data_id == title_data_id,
                        logs.json_text.contains({"statusForm": {"value": "waiting"}})
                    )
                ) 
                .distinct(logs.title_data_id)
                .order_by(logs.title_data_id, desc(literal_column("json_text -> 'dateType' ->> 'value'")))
            ) \
            .all()
            records = [i.dict() for i in heroesPersonal]   
            logs_waiting_last = pd.DataFrame.from_records(records).fillna(0)
            logs_waiting_last = logs_waiting_last.to_dict("records")

            # ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
            heroesPersonal = db.exec(select(related_person).where(related_person.title_id == title_data_id, related_person.value == user_id)).all()
            records = [i.dict() for i in heroesPersonal]   
            related_person_df = pd.DataFrame.from_records(records).fillna(0)
            # print(related_person_df)

            # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
            url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
            dfRename = related_person_df.rename(columns={'value': 'jia_yi_id'})
            to_dict = dfRename[['jia_yi_id']].to_dict('records')
            body_raw = {"data_api": to_dict}
            getPerson = requests.get(url=url, json=body_raw).json()
            getPerson = pd.DataFrame(getPerson)
            # print(getPerson)

            # string concat
            merged_df = pd.merge(related_person_df, getPerson, left_on='value', right_on='jia_yi_id')
            merged_df['value'] = merged_df[['value', 'jia_yi_idname', 'jia_yi_mm_name']].apply(lambda x: ' | '.join(x.astype(str)), axis=1)
            merged_df.drop(columns=['jia_yi_id', 'jia_yi_idname', 'jia_yi_mm_name', 'ri_qi'], inplace=True)
            merged_df = merged_df.to_dict("records")
            # ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

            logs_waiting_last[0]['json_text']['statusPerson']['key'] = 'receiver'
            logs_waiting_last[0]['json_text']['statusPerson']['value'] = merged_df[0]['value']

            json_one = logs_waiting_last

            
        # mergeDF = heroesPersonal.to_dict("records")
        mergeDF = json_one
    except:
        mergeDF = []
    return mergeDF


def MchangeStatus100(person: dict = Body(...), db: Session = Depends(get_session)):

    entrylistRe = person['list_report']['statusChange']
    # print(entrylistRe)

    if(entrylistRe['status'] == 'waiting'):
        print('waiting')

        changeStatus = 'processing'
        senderStatus = 'receiver'
        heroesOption = db.exec(select(option).where(option.mm_name == changeStatus)).all()
        records = [i.dict() for i in heroesOption]  

    if(entrylistRe['status'] == 'processing'):
        print('processing')

        changeStatus = 'success'
        senderStatus = 'receiver'
        heroesOption = db.exec(select(option).where(option.mm_name == changeStatus)).all()
        records = [i.dict() for i in heroesOption] 
    
    if(entrylistRe['status'] == 'success'):
        print('success')

        changeStatus = 'complete'
        senderStatus = 'sender'
        heroesOption = db.exec(select(option).where(option.mm_name == changeStatus)).all()
        records = [i.dict() for i in heroesOption]  
    

    statement = select(title_data).where(title_data.auto_id == entrylistRe['title_data_id'])
    results = db.exec(statement).first()
    results.end_date = get_datetime()
    results.status_id = records[0]['auto_id']
    db.add(results)  
    db.commit()  
    db.refresh(results)


    json_one = {
                "dateType": {
                    "key": "endDate",
                    "value": str(get_datetime())
                },
                "statusForm": {
                    "key": changeStatus,
                    "value": changeStatus
                },
                "statusPerson": {
                    "key": senderStatus,
                    "value": entrylistRe["user_show"]
                }
            }
    json_form = {
                "insertForm": person
        }

    hero_logs = logs(json_text=json_one, title_data_id=entrylistRe['title_data_id'], json_form=json_form)
    db.add(hero_logs)
    db.commit()
    
    return person


def MchangeStatusVer2100(person: dict = Body(...), db: Session = Depends(get_session)):
    
    entrylistRe = person['list_report']['statusChange']
    who = person['list_report']['view_jsonText']['statusPerson']['key']
    # print(entrylistRe)
    # print(who)

    if(who == 'sender'):
        print('sender')
        if(entrylistRe['status'] == 'waiting'):
            print('waiting')

            changeStatus = 'complete'
            senderStatus = 'sender'
            heroesOption = db.exec(select(option).where(option.mm_name == changeStatus)).all()
            records = [i.dict() for i in heroesOption]  
    else:
        print('receiver')
        if(entrylistRe['status'] == 'waiting'):
            print('waiting')

            changeStatus = 'processing'
            senderStatus = 'receiver'
            heroesOption = db.exec(select(option).where(option.mm_name == changeStatus)).all()
            records = [i.dict() for i in heroesOption]  

        if(entrylistRe['status'] == 'processing'):
            print('processing')

            changeStatus = 'success'
            senderStatus = 'receiver'
            heroesOption = db.exec(select(option).where(option.mm_name == changeStatus)).all()
            records = [i.dict() for i in heroesOption] 
        
    statement = select(title_data).where(title_data.auto_id == entrylistRe['title_data_id'])
    results = db.exec(statement).first()
    results.end_date = get_datetime()
    results.status_id = records[0]['auto_id']
    db.add(results)  
    db.commit()  
    db.refresh(results)


    json_one = {
                "dateType": {
                    "key": "endDate",
                    "value": str(get_datetime())
                },
                "statusForm": {
                    "key": changeStatus,
                    "value": changeStatus
                },
                "statusPerson": {
                    "key": senderStatus,
                    "value": entrylistRe["user_show"]
                }
            }
    json_form = {
                "insertForm": person
        }

    hero_logs = logs(json_text=json_one, title_data_id=entrylistRe['title_data_id'], json_form=json_form)
    db.add(hero_logs)
    db.commit()
    
    return person


def MchangeStatusVer2100(person: dict = Body(...), db: Session = Depends(get_session)):
    
    entrylistRe = person['list_report']['statusChange']
    who = person['list_report']['view_jsonText']['statusPerson']['key']
    # print(entrylistRe)
    # print(who)

    if(who == 'sender'):
        print('sender')
        if(entrylistRe['status'] == 'waiting'):
            print('waiting')

            changeStatus = 'complete'
            senderStatus = 'sender'
            heroesOption = db.exec(select(option).where(option.mm_name == changeStatus)).all()
            records = [i.dict() for i in heroesOption]  
    else:
        print('receiver')
        if(entrylistRe['status'] == 'waiting'):
            print('waiting')

            changeStatus = 'processing'
            senderStatus = 'receiver'
            heroesOption = db.exec(select(option).where(option.mm_name == changeStatus)).all()
            records = [i.dict() for i in heroesOption]  

        if(entrylistRe['status'] == 'processing'):
            print('processing')

            changeStatus = 'success'
            senderStatus = 'receiver'
            heroesOption = db.exec(select(option).where(option.mm_name == changeStatus)).all()
            records = [i.dict() for i in heroesOption] 
        
    statement = select(title_data).where(title_data.auto_id == entrylistRe['title_data_id'])
    results = db.exec(statement).first()
    results.end_date = get_datetime()
    results.status_id = records[0]['auto_id']
    db.add(results)  
    db.commit()  
    db.refresh(results)


    json_one = {
                "dateType": {
                    "key": "endDate",
                    "value": str(get_datetime())
                },
                "statusForm": {
                    "key": changeStatus,
                    "value": changeStatus
                },
                "statusPerson": {
                    "key": senderStatus,
                    "value": entrylistRe["user_show"]
                }
            }
    json_form = {
                "insertForm": person
        }

    hero_logs = logs(json_text=json_one, title_data_id=entrylistRe['title_data_id'], json_form=json_form)
    db.add(hero_logs)
    db.commit()
    
    return person


def MchangeStatusVer3100(person: dict = Body(...), db: Session = Depends(get_session)):
    
    entrylistRe = person['list_report']['statusChange']
    who = person['list_report']['view_jsonText']['statusPerson']['key']
    # print(entrylistRe)
    # print(who)

    print(person['update']['form']['senderShow'])


    if(entrylistRe['status'] == 'waiting'):
        print('waiting')

        changeStatus = 'complete'
        senderStatus = 'receiver'
        heroesOption = db.exec(select(option).where(option.mm_name == changeStatus)).all()
        records = [i.dict() for i in heroesOption]  

    statement = select(title_data).where(title_data.auto_id == entrylistRe['title_data_id'])
    results = db.exec(statement).first()
    results.end_date = get_datetime()
    results.status_id = records[0]['auto_id']
    db.add(results)  
    db.commit()  
    db.refresh(results)


    json_one = {
                "dateType": {
                    "key": "endDate",
                    "value": str(get_datetime())
                },
                "statusForm": {
                    "key": changeStatus,
                    "value": changeStatus
                },
                "statusPerson": {
                    "key": senderStatus,
                    "value": entrylistRe["user_show"]
                }
            }
    json_form = {
                "insertForm": person
        }
    hero_logs = logs(json_text=json_one, title_data_id=entrylistRe['title_data_id'], json_form=json_form)
    db.add(hero_logs)
    db.commit()
    # ----------------------------------
    json_one = {
                "dateType": {
                    "key": "endDate",
                    "value": str(get_datetime())
                },
                "statusForm": {
                    "key": changeStatus,
                    "value": changeStatus
                },
                "statusPerson": {
                    "key": 'sender',
                    "value": person['update']['form']['senderShow']
                }
            }
    json_form = {
                "insertForm": person
        }
    hero_logs = logs(json_text=json_one, title_data_id=entrylistRe['title_data_id'], json_form=json_form)
    db.add(hero_logs)
    db.commit()

    return person


def getTimelineUser100(title_data_id: int, db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(logs).where(logs.title_data_id == title_data_id).order_by(logs.title_data_id, logs.json_text['dateType']['value'])).all()
        records = [i.dict() for i in heroesPersonal]   
        logs_df = pd.DataFrame.from_records(records).fillna(0)

        mergeDF = logs_df.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def formUpdate100(person: dict = Body(...), db: Session = Depends(get_session)):

    varForm = person['update']['form']
    varReport = person['list_report']

    # UPDATE TO title_data
    statement_titleData = select(title_data).where(title_data.auto_id == varReport['viewID'])
    results_titleData = db.exec(statement_titleData).first()
    results_titleData.title = varForm['title']
    results_titleData.end_date = None
    results_titleData.status_id = 2
    db.add(results_titleData)  # 
    db.commit()  # 
    db.refresh(results_titleData)  #
    # --------------------------
    
    # UPDATE TO related_person
    result = []
    result.append({
        "title_id": varReport['viewID'],
        "option_id": 6,
        "key": 7,
        "value": varForm["sender"],
    })
    for receiver in varForm["receiver"]:
        result.append({
            "title_id": varReport['viewID'],
            "option_id": 6,
            "key": 8,
            "value": receiver,
        })
    print(result)
    # DELETE ALL EQUA TO TITLE ID 
    related_person_query = select(related_person).where(related_person.title_id == varReport['viewID'])
    related_person_records = db.exec(related_person_query).all()
    for record in related_person_records:
        db.delete(record)
    db.commit()
    # INSERT NEW related_person
    for entry in result:
            hero_related_person = related_person(title_id=entry['title_id'], option_id=entry['option_id'], key=entry['key'], value=entry['value'])
            db.add(hero_related_person)
            db.commit()
    # --------------------------

    # DELETE ALL EQUA TO TITLE ID 
    related_logs_query = select(logs).where(logs.title_data_id == varReport['viewID'], logs.json_text.contains({"statusPerson": {"key": "receiver"}}))
    related_logs_records = db.exec(related_logs_query).all()
    # print(related_logs_records)
    for record in related_logs_records:
        db.delete(record)
    db.commit()

    # INSERT TO logs
    json_one = {
                "dateType": {
                    "key": "startDate",
                    "value": str(get_datetime())
                },
                "statusForm": {
                    "key": "waiting",
                    "value": "waiting"
                },
                "statusPerson": {
                    "key": "sender",
                    "value": varForm["senderShow"]
                },
                "changeForm": {
                    "key": "update",
                    "value": str(get_datetime())
                }
            }
    json_form = {
                "insertForm": person
            }
    hero_logs = logs(json_text=json_one, title_data_id=varReport['viewID'], json_form=json_form)
    db.add(hero_logs)
    db.commit()
    # --------------------------

    # DELETE ALL EQUA TO TITLE ID 
    details_data_query = select(details_data).where(details_data.title_data_id == varReport['viewID'])
    details_data_records = db.exec(details_data_query).all()
    for record in details_data_records:
        db.delete(record)
    db.commit()

    # INSERT TO details_data
    heroesForm_story = db.exec(select(form_story).where(form_story.story_id == varForm['story'])).all()
    records = [i.dict() for i in heroesForm_story]   

    details = varForm['details']
    result = []
    for key, value in details.items():
        if isinstance(value, list):
            for item in value:
                result.append({key: item})
        else:
            result.append({key: value})
    print(result)

    json_ccc = []
    for bbb in result:
        for aaa in records:
            if list(bbb.keys())[0] == aaa['input_value']:
                json_ccc.append({'title_data_id': varReport['viewID'], 'key': aaa['auto_id'], 'value': list(bbb.values())[0], 'story_id': varForm['story']})
    print(json_ccc)

    for entry in json_ccc:
        hero_details_data = details_data(title_data_id=entry['title_data_id'], key=entry['key'], value=entry['value'], story_id=entry['story_id'])
        db.add(hero_details_data)
        db.commit()

    return 'hero_related_person_see'



def Mcontain_person100(jia_yi_id: int, db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(related_person).where(related_person.value == jia_yi_id)).all()
        records = [i.dict() for i in heroesPersonal]   
        related_person_df = pd.DataFrame.from_records(records).fillna(0)

        # GET ALL FORM
        json_one = db.exec(select(logs.title_data_id, logs.json_text) \
            .where(
                and_(
                    logs.title_data_id.in_(related_person_df['title_id'])
                )
            ) 
            .distinct(logs.title_data_id)
            .order_by(logs.title_data_id, literal_column("json_text -> 'dateType' ->> 'value'"))
        ) \
        .all()
        
        # GET FORM WITH LAST DATETIME BY MY NAME
        json_two = db.exec(select(logs.title_data_id, logs.json_text) \
            .where(
                and_(
                    literal_column("json_text -> 'statusPerson' ->> 'value'").ilike(f"{jia_yi_id}%")
                )
            ) 
            .distinct(logs.title_data_id)
            .order_by(logs.title_data_id, desc(literal_column("json_text -> 'dateType' ->> 'value'")))
        ) \
        .all()

        # # INSTEAD JSON
        # try:
        #     for i in range(len(json_one)):
        #         if json_one[i]['title_data_id'] == json_two[0]['title_data_id']:
        #             json_one[i] = json_two[0]
        # except:
        #     print("no json_two")

        # check if the first text in the statusPerson.value is not in any of the items in data_2
        data_new = []
        for item in json_one:
            if not any(item["title_data_id"] == x["title_data_id"] for x in json_two):
               data_new.append(item)
        data_new += json_two
        # print(data_new)
        
        # MAKE IT EASY SEND TO HTML
        data_new_output = []
        for item in data_new:
            json_text = item['json_text']
            end_date = json_text['dateType']['value']
            mm_name = json_text['statusForm']['value']
            data_new_output.append({
                'title_data_id': item['title_data_id'],
                'mm_name': mm_name,
                'end_date': end_date,
            })
        data_new_output = pd.DataFrame(data_new_output)

        # -----------------------------------------------

        heroesPersonal = db.exec(select(title_data).where(title_data.auto_id.in_(related_person_df['title_id']))).all()
        records = [i.dict() for i in heroesPersonal]   
        title_data_df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal = db.exec(select(story)).all()
        records = [i.dict() for i in heroesPersonal]   
        story_df = pd.DataFrame.from_records(records).fillna(0)

        mergeStory = pd.merge(title_data_df, story_df, left_on='type_id', right_on='auto_id')
        mergeTwo = pd.merge(data_new_output, mergeStory, left_on='title_data_id', right_on='auto_id_x')
        timeSort = mergeTwo.sort_values(by='start_date', ascending=False)

        # --------------------------------------------------

        mergeDF = timeSort.to_dict("records")
        # mergeDF = data_new
    except:
        mergeDF = []

    return mergeDF


def MallPersonStatus100(title_id: int, db: Session = Depends(get_session)):
    try:
        title_data_df = db.exec(select(title_data.start_date).where(title_data.auto_id == title_id)).all()

        heroesPersonal = db.exec(select(related_person).where(related_person.title_id == title_id, related_person.key == 8)).all()
        records = [i.dict() for i in heroesPersonal]   
        related_person_df = pd.DataFrame.from_records(records).fillna(0)

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        dfRename = related_person_df.rename(columns={'value': 'jia_yi_id'})
        to_dict = dfRename[['jia_yi_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getPerson = requests.get(url=url, json=body_raw).json()
        getPerson = pd.DataFrame(getPerson)
        print(getPerson)

        # string concat
        merged_df = pd.merge(related_person_df, getPerson, left_on='value', right_on='jia_yi_id')
        merged_df['value'] = merged_df[['value', 'jia_yi_idname', 'jia_yi_mm_name']].apply(lambda x: ' | '.join(x.astype(str)), axis=1)
        merged_df.drop(columns=['jia_yi_id', 'jia_yi_idname', 'jia_yi_mm_name', 'ri_qi'], inplace=True)
        merged_df = merged_df.to_dict("records")

        # create sample array if json_two is not have
        json_one = []
        for item in merged_df:
            if "value" in item:
                output_item = {
                    "dateType": {
                        "key": "startDate",
                        "value": str(title_data_df[0])
                    },
                    "statusForm": {
                        "key": "waiting",
                        "value": "waiting"
                    },
                    "statusPerson": {
                        "key": "receiver",
                        "value": item["value"]
                    }
                }
                json_one.append(output_item)

        # get array logs user from database
        json_two = db.exec(select(logs.json_text) \
            .where(
                and_(
                    logs.title_data_id == title_id,
                    logs.json_text.contains({"statusPerson": {"key": "receiver"}})
                )
            ) 
            .distinct(literal_column("json_text -> 'statusPerson' ->> 'value'"))
            .order_by(literal_column("json_text -> 'statusPerson' ->> 'value'"), desc(literal_column("json_text -> 'dateType' ->> 'value'")))
        ) \
        .all()

        # check if the first text in the statusPerson.value is not in any of the items in data_2
        data_new = []
        for item in json_one:
            if not any(item["statusPerson"]["value"].split(" ")[0] in x["statusPerson"]["value"] for x in json_two):
                data_new.append(item)
        data_new += json_two

        # mergeDF = merged_df.to_dict("records")
        mergeDF = data_new
    except:
        mergeDF = []
    return mergeDF


def delByID100(title_id: int, db: Session = Depends(get_session)):

    # DELETE title_data
    title_data_query = select(title_data).where(title_data.auto_id == title_id)
    title_data_query_records = db.exec(title_data_query).all()
    for record in title_data_query_records:
        db.delete(record)
    db.commit()

    # DELETE details_data
    details_data_query = select(details_data).where(details_data.title_data_id == title_id)
    details_data_records = db.exec(details_data_query).all()
    for record in details_data_records:
        db.delete(record)
    db.commit()

    # DELETE related_person
    related_person_query = select(related_person).where(related_person.title_id == title_id)
    related_person_records = db.exec(related_person_query).all()
    for record in related_person_records:
        db.delete(record)
    db.commit()

    # DELETE logs
    logs_query = select(logs).where(logs.title_data_id == title_id)
    logs_records = db.exec(logs_query).all()
    for record in logs_records:
        db.delete(record)
    db.commit()
    
    return title_id


def personCommitTable100(title_id: int, db: Session = Depends(get_session)):
    
    # title_data_df = db.exec(select(title_data.start_date).where(title_data.auto_id == title_id)).all()

    # heroesPersonal = db.exec(select(related_person).where(related_person.title_id == title_id)).all()
    # records = [i.dict() for i in heroesPersonal]   
    # related_person_df = pd.DataFrame.from_records(records).fillna(0)

    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    # dfRename = related_person_df.rename(columns={'value': 'jia_yi_id'})
    # to_dict = dfRename[['jia_yi_id']].to_dict('records')
    # body_raw = {"data_api": to_dict}
    # getPerson = requests.get(url=url, json=body_raw).json()
    # getPerson = pd.DataFrame(getPerson)
    # print(getPerson)

    # # string concat
    # merged_df = pd.merge(related_person_df, getPerson, left_on='value', right_on='jia_yi_id')
    # merged_df['value'] = merged_df[['value', 'jia_yi_idname', 'jia_yi_mm_name']].apply(lambda x: ' | '.join(x.astype(str)), axis=1)
    # merged_df.drop(columns=['jia_yi_id', 'jia_yi_idname', 'jia_yi_mm_name', 'ri_qi'], inplace=True)
    # merged_df = merged_df.to_dict("records")

    data = [
        {
          "title": [{'date': '2022', 'name': 'nern', 'status': 'success'}, {'date': '2022', 'name': 'nern', 'status': 'complete'}]
        },
        {
          "title": [{'date': '2022', 'name': 'nern', 'status': 'success'}, {'date': '2022', 'name': 'nern', 'status': 'complete'}]
        }
    ]
    
    return merged_df


def day_schedule_table100(year: str, month: str, db: Session = Depends(get_session)):
    try:
        def fromDatabase():
            schedule_df = db.exec(select(schedule)).all()
            records = [i.dict() for i in schedule_df]   
            schedule_df = pd.DataFrame.from_records(records).fillna(0)
            # schedule_df = schedule_df.to_dict("records")

            store_df = db.exec(select(store_name)).all()
            records = [i.dict() for i in store_df]   
            store_df = pd.DataFrame.from_records(records).fillna(0)
            # store_df = store_df.to_dict("records")

            story_df = db.exec(select(story)).all()
            records = [i.dict() for i in story_df]   
            story_df = pd.DataFrame.from_records(records).fillna(0)
            # story_df = story_df.to_dict("records")
            
            mergeStore = pd.merge(schedule_df, store_df, left_on='store', right_on='auto_id')
            mergeStory = pd.merge(mergeStore, story_df, left_on='job', right_on='auto_id')
            mergeStory = mergeStory.to_dict("records")
            return mergeStory
        
        bbb = fromDatabase()
        
        # Define the schedule
        schedule_df2 = [
            {"job": "eat", "day_value": "Mon", "store": "a1", "type_day": "day_week"},
            {"job": "run", "day_value": "22", "store": "a2", "type_day": "day_month"},
            {"job": "eat", "day_value": "Mon", "store": "a3", "type_day": "day_week"},
            {"job": "sleep", "day_value": "04-22", "store": "a4", "type_day": "day_year"},
            {'job': 'play', 'day_value': "5", 'store': 'a5', 'continue_day': True, 'type_day': 'day_repeat', 'start_day': '2023-04-25'},
        ]

        # Set the month and year
        month = int(month)  # April
        year = int(year)

        # Get the number of days in the month
        num_days = calendar.monthrange(year, month)[1]

        # Set the start and end dates
        start_date = datetime(year, month, 1)
        end_date = datetime(year, month, num_days)

        # Create an empty list to store the schedule for each day
        daily_schedule = []

        # Loop through each day in the range and create a dictionary for that day
        for i in range((end_date - start_date).days + 1):
            day = start_date + timedelta(days=i)
            day_dict = {"date": day.strftime("%Y-%m-%d"), "day_week": day.strftime("%a"), 
                        "day_month": day.day, "day_year": day.strftime("%m-%d"), "list_story": []}

            # Check if there are any jobs scheduled for this day
            # for s in schedule_df2:
            #     if s["type_day"] == "day_week" and s["day_value"] == day.strftime("%a"):
            #         day_dict["list_story"].append((s["store"], s["job"]))
            #     elif s["type_day"] == "day_month" and int(s["day_value"]) == day.day:
            #         day_dict["list_story"].append((s["store"], s["job"]))
            #     elif s["type_day"] == "day_year" and s["day_value"] == day.strftime("%m-%d"):
            #         day_dict["list_story"].append((s["store"], s["job"]))
            #     elif s['type_day'] == 'day_repeat': 
            #         start_day = datetime.strptime(s['start_day'], '%Y-%m-%d')
            #         if (day - start_day).days % int(s['day_value']) == 0:
            #             day_dict["list_story"].append((s["store"], s["job"]))
            
            for s in bbb:
                if s["type_day"] == "day_week" and s["day_value"] == day.strftime("%a") and s["continue_day"] == True:
                    day_dict["list_story"].append((s["symbol"], s["type_name"]))
                elif s["type_day"] == "day_month" and int(s["day_value"]) == day.day and s["continue_day"] == True:
                    day_dict["list_story"].append((s["symbol"], s["type_name"]))
                elif s["type_day"] == "day_year" and s["day_value"] == day.strftime("%m-%d") and s["continue_day"] == True:
                    day_dict["list_story"].append((s["symbol"], s["type_name"]))
                elif s['type_day'] == 'day_repeat' and s["continue_day"] == True: 
                    start_day = datetime.strptime(s['start_day'], '%Y-%m-%d')
                    if (day - start_day).days % int(s['day_value']) == 0:
                        day_dict["list_story"].append((s["symbol"], s["type_name"]))
                    

            # Group the stores with the same job together
            grouped_stores = {}
            for store, job in day_dict["list_story"]:
                grouped_stores.setdefault(job, []).append(store)

            # Replace individual stores with grouped stores
            new_list_story = [(sorted(stores), job) if len(stores) > 1 else (stores[0], job)
                            for job, stores in grouped_stores.items()]
            day_dict["list_story"] = new_list_story

            # Append the day's dictionary to the daily_schedule list
            daily_schedule.append(day_dict)

        # Convert the list of dictionaries to a pandas DataFrame
        df = pd.DataFrame(daily_schedule)
        df['list_story'] = df['list_story'].apply(lambda x: ', '.join([f'({s[0]}-{e[0]}, {e[1]})' if s[1] == e[1] else f'({s[0]}, {s[1]}), ({e[0]}, {e[1]})' for s, e in zip(x[:-1], x[1:])]) if len(x) > 1 else f'({x[0][0]}, {x[0][1]})' if len(x) == 1 else '')
        df = df.to_dict("records")

        mergeDF = df
    except:
        mergeDF = []
    return mergeDF

def MgetStory100(db: Session = Depends(get_session)):
    try:
        # mergeDF = [{"jia_yi_id": 10000, "jia_yi_idname": "10000", "jia_yi_mm_name": "AG165"}, {"jia_yi_id": 20000, "jia_yi_idname": "20000", "jia_yi_mm_name": "AG165 20"}]

        heroesPersonal = db.exec(select(story).where(story.type_form == 'schedule')).all()
        records = [i.dict() for i in heroesPersonal]   
        story_df = pd.DataFrame.from_records(records).fillna(0)
        mergeDF = story_df.to_dict("records")
    except:
        mergeDF = []
    return mergeDF

def getChatWebSite100(group_id: str, db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(chat_message).where(chat_message.group_id == group_id).order_by(chat_message.timestamp)).all()
        records = [i.dict() for i in heroesPersonal]   
        story_df = pd.DataFrame.from_records(records).fillna(0)
        mergeDF = story_df.to_dict("records")
    except:
        mergeDF = []
    return mergeDF



# def day_schedule_table100(db: Session = Depends(get_session)):
#     try:
#         heroesPersonal = db.exec(select(option).where(option.parent_id != 0, option.head_id == 13).order_by(option.auto_id, option.auto_id.desc())).all()
#         records = [i.dict() for i in heroesPersonal]   
#         option_df = pd.DataFrame.from_records(records).fillna(0)
#         option_df = option_df.to_dict("records")

#         heroesPersonal = db.exec(select(story)).all()
#         records = [i.dict() for i in heroesPersonal]   
#         story_df = pd.DataFrame.from_records(records).fillna(0)
#         story_df = story_df.to_dict("records")

#         # Create a dictionary to store type_names for each auto_id
#         type_dict = {}
#         for item in story_df:
#             type_dict[item['auto_id']] = item['type_name']

#         # Iterate over json_one to create the expected output
#         output = []
#         for item in option_df:
#             auto_id = item['auto_id']
#             mm_name = item['mm_name']
#             parent_id = item['parent_id']
#             key_list = item['json_id']['story']['key']
#             type_names = [type_dict.get(key) for key in key_list if type_dict.get(key)]
#             type_name_str = ' | '.join(type_names)
#             list_count = len(type_names)
#             output.append({
#                 'auto_id': auto_id,
#                 'mm_name': mm_name,
#                 'type_name': type_name_str,
#                 'list_count': list_count
#             })

#         # mergeDF = option_df.to_dict("records")
#         mergeDF = output
#     except:
#         mergeDF = []
#     return mergeDF


# def Mcontain_person100(jia_yi_id: int, db: Session = Depends(get_session)):
      
#     heroesPersonal = db.exec(select(related_person).where(related_person.value == jia_yi_id)).all()
#     records = [i.dict() for i in heroesPersonal]   
#     related_person_df = pd.DataFrame.from_records(records).fillna(0)

#     heroesPersonal = db.exec(select(logs.title_data_id, logs.json_text) \
#         .where(
#             and_(
#                 logs.title_data_id.in_(related_person_df['title_id']),
#                 # literal_column("json_text -> 'statusPerson' ->> 'value'").ilike(f"{jia_yi_id}%")
#             )
#         ) 
#         .distinct(logs.title_data_id)
#         .order_by(logs.title_data_id, desc(literal_column("json_text -> 'dateType' ->> 'value'")))
#     ) \
#     .all()
    

#     # heroesPersonal = db.exec(select(logs.title_data_id, logs.json_text) \
#     # # .where(logs.json_text.contains({"statusPerson": {"value": "25730 | AG125 | အားသက်"}}))) \
#     # .where(literal_column("json_text -> 'statusPerson' ->> 'value'").ilike(f"{jia_yi_id}%"))) \
#     # .all()
    

#     # mergeDF = related_person_df.to_dict("records")
#     mergeDF = heroesPersonal

#     return mergeDF

# def formInsert100(person: dict = Body(...), db: Session = Depends(get_session)):
#     def insertToTitleData(person):
#         hero_to_db = title_data.from_orm(person)
#         hero_to_db.title = person['title']
#         hero_to_db.status_id = 1
#         hero_to_db.start_date = get_datetime()
#         hero_to_db.type_id = person['type_id']
#         hero_to_db.fen_dian_id = person['fen_dian_id']
#         db.add(hero_to_db)
#         db.commit()
#         db.refresh(hero_to_db)
#         return 'insertToTitleData'
 
#     def insertToDetailsData(person):
#         json_two = []
#         json_two.append({'type_input': 'multiText', 'key': 'key', 'value': person["sender"], "type": person["type"]})
#         json_two.append({'type_input': 'multiText', 'key': 'key', 'value': person["title"], "type": person["type"]})
#         json_two.append({'type_input': 'multiText', 'key': 'key', 'value': person["multiText"], "type": person["type"]})
#         for receiver in person["receiver"]:
#             json_two.append({'type_input': 'multiText', 'key': 'key', 'value': receiver, "type": person["type"]})
#         for image in person["image"]:
#             json_two.append({'type_input': 'image', 'key': 'key', 'value': image, "type": person["type"]})
#         return json_two

#     # one = insertToTitleData(person)
#     two = insertToDetailsData(person)

#     return two


# def getCarAtive100(db: Session = Depends(get_session)):
#     try:
#         heroesPersonal = db.exec(select(title_data)).all()
#         records = [i.dict() for i in heroesPersonal]   
#         df = pd.DataFrame.from_records(records).fillna(0)

#         mergeDF = df.to_dict("records")
#     except:
#         mergeDF = []
#     return mergeDF


class ConnectionManager:
    """Class defining socket events for a group"""
    def __init__(self):
        """init method, keeping track of connections"""
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        """connect event"""
        await websocket.accept()
        self.active_connections.append(websocket)

    async def disconnect(self, websocket: WebSocket):
        """disconnect event"""
        self.active_connections.remove(websocket)
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Direct Message"""
        if websocket in self.active_connections:
            try:
                await websocket.send_text(message)
            except Exception as e:
                print(f"Failed to send message: {e}")
                await self.disconnect(websocket)
    
    async def broadcast(self, message: str):
        """Broadcast Message to all active connections"""
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                print(f"Failed to broadcast message: {e}")
                await self.disconnect(connection)


# Dictionary to maintain connection managers for each group
group_managers = {}

def get_group_manager(group_id: str) -> ConnectionManager:
    """Get the connection manager for a specific group, create if it doesn't exist"""
    if group_id not in group_managers:
        group_managers[group_id] = ConnectionManager()
    return group_managers[group_id]

async def websocket_endpoint100(websocket: WebSocket, group_id: str, user_id: str, db: Session):
    manager = get_group_manager(group_id)
    await manager.connect(websocket)
    
    # Retrieve and send chat history
    messages = db.query(chat_message).filter(chat_message.group_id == group_id).order_by(chat_message.timestamp).all()
    for message in messages:
        try:
            await websocket.send_text(f"{message.timestamp} , {message.user_id} , {message.message}")
        except Exception as e:
            print(f"Failed to send chat history message: {e}")
            break

    try:
        while True:
            data = await websocket.receive_text()
            # Save message to database
            chat_message_var = chat_message(group_id=group_id, user_id=user_id, message=data)
            db.add(chat_message_var)
            db.commit()
            db.refresh(chat_message_var)
            
            # Broadcast message
            await manager.broadcast(f"{chat_message_var.timestamp} , {chat_message_var.user_id} , {chat_message_var.message}")
    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        await manager.disconnect(websocket)




# class ConnectionManager:
#     """Class defining socket events for a group"""
#     def __init__(self):
#         """init method, keeping track of connections"""
#         self.active_connections: List[WebSocket] = []
#         self.user_connections = {}  # Mapping from user_id to WebSocket

#     async def connect(self, websocket: WebSocket, user_id: str):
#         """connect event"""
#         await websocket.accept()
#         if user_id in self.user_connections:
#             await self.user_connections[user_id].close()
#         self.user_connections[user_id] = websocket
#         self.active_connections.append(websocket)

#     async def disconnect(self, websocket: WebSocket, user_id: str):
#         """disconnect event"""
#         if websocket in self.active_connections:
#             self.active_connections.remove(websocket)
#         if user_id in self.user_connections and self.user_connections[user_id] == websocket:
#             del self.user_connections[user_id]

#     async def send_personal_message(self, message: str, websocket: WebSocket):
#         """Direct Message"""
#         if websocket in self.active_connections:
#             try:
#                 await websocket.send_text(message)
#             except Exception as e:
#                 print(f"Failed to send message: {e}")
#                 await self.disconnect(websocket)

#     async def broadcast(self, message: str):
#         """Broadcast Message to all active connections"""
#         for connection in self.active_connections:
#             try:
#                 await connection.send_text(message)
#             except Exception as e:
#                 print(f"Failed to broadcast message: {e}")
#                 await self.disconnect(connection)

# # Dictionary to maintain connection managers for each group
# group_managers = {}

# def get_group_manager(group_id: str) -> ConnectionManager:
#     """Get the connection manager for a specific group, create if it doesn't exist"""
#     if group_id not in group_managers:
#         group_managers[group_id] = ConnectionManager()
#     return group_managers[group_id]

# async def websocket_endpoint100(websocket: WebSocket, group_id: str, user_id: str, db: Session):
#     manager = get_group_manager(group_id)
#     await manager.connect(websocket, user_id)
    
#     # Retrieve and send chat history
#     messages = db.query(chat_message).filter(chat_message.group_id == group_id).order_by(chat_message.timestamp).all()
#     for message in messages:
#         try:
#             await websocket.send_text(f"{message.timestamp} , {message.user_id} , {message.message}")
#         except Exception as e:
#             print(f"Failed to send chat history message: {e}")
#             break

#     try:
#         while True:
#             data = await websocket.receive_text()
#             # Save message to database
#             chat_message_var = chat_message(group_id=group_id, user_id=user_id, message=data)
#             db.add(chat_message_var)
#             db.commit()
#             db.refresh(chat_message_var)
            
#             # Broadcast message
#             await manager.broadcast(f"{chat_message_var.timestamp} , {chat_message_var.user_id} , {chat_message_var.message}")
#     except Exception as e:
#         print(f"WebSocket error: {e}")
#     finally:
#         await manager.disconnect(websocket, user_id)





# -------------------------------------- API select option
def getAAAA100(db: Session = Depends(get_session)):
    try:
        mergeDF = [{"jia_yi_id": 25498, "jia_yi_idname": "ag165", "jia_yi_mm_name": "nern"}, {"jia_yi_id": 36415, "jia_yi_idname": "ag145", "jia_yi_mm_name": "earn"}]
    except:
        mergeDF = []
    return mergeDF

def getAAAA20100(db: Session = Depends(get_session)):
    try:
        mergeDF = [{"jia_yi_id": 10000, "jia_yi_idname": "1.2.1", "jia_yi_mm_name": "nern10000"}, {"jia_yi_id": 20000, "jia_yi_idname": "1.2.2", "jia_yi_mm_name": "earn20000"}]
    except:
        mergeDF = []
    return mergeDF

def getPersonName100(db: Session = Depends(get_session)):
    try:
        mergeDF = [{"jia_yi_id": 10000, "jia_yi_idname": "10000", "jia_yi_mm_name": "AG165"}, {"jia_yi_id": 20000, "jia_yi_idname": "20000", "jia_yi_mm_name": "AG165 20"}]

        store_name_df = db.exec(select(store_name)).all()
        records = [i.dict() for i in store_name_df]   
        store_name_df = pd.DataFrame.from_records(records).fillna(0)
        print(store_name_df)
        mergeDF = store_name_df.to_dict("records")
    except:
        mergeDF = []
    return mergeDF

def getStoreName100(db: Session = Depends(get_session)):
    try:
        mergeDF = [{"jia_yi_id": 10000, "jia_yi_idname": "10000", "jia_yi_mm_name": "AG165"}, {"jia_yi_id": 20000, "jia_yi_idname": "20000", "jia_yi_mm_name": "AG165 20"}]

        store_name_df = db.exec(select(store_name)).all()
        records = [i.dict() for i in store_name_df]   
        store_name_df = pd.DataFrame.from_records(records).fillna(0)
        store_name_df = store_name_df.rename(columns={'auto_id': 'jia_yi_id', 'symbol': 'jia_yi_idname', 'name': 'jia_yi_mm_name'})
        print(store_name_df)
        mergeDF = store_name_df.to_dict("records")
    except:
        mergeDF = []
    return mergeDF

def testPPP100(db: Session = Depends(get_session)):
 
    mergeDF = [{"jia_yi_id": 10000, "jia_yi_idname": "10000", "jia_yi_mm_name": "AG165"}, {"jia_yi_id": 20000, "jia_yi_idname": "20000", "jia_yi_mm_name": "AG165 20"}]

    schedule_df = db.exec(select(schedule)).all()
    records = [i.dict() for i in schedule_df]   
    schedule_df = pd.DataFrame.from_records(records).fillna(0)
    print(schedule_df)
    mergeDF = schedule_df.to_dict("records")

    title_data_df = db.exec(select(title_data).where(title_data.schedule_status == True)).all()
    records = [i.dict() for i in title_data_df]   
    title_data_df = pd.DataFrame.from_records(records).fillna(0)
    print(title_data_df)
    mergeDF = title_data_df.to_dict("records")

    return mergeDF


def getAppSpeechCar100(db: Session):
    # Query to filter for head_id = 16 and second layer (1.2.x)
    result = db.execute(
        select(option)
        .where(option.head_id == 16)
        .where(option.level.like('1.1.%'))
    ).all()
    
    # Process the result to match the expected output
    output = [
        {
            "jia_yi_id": row.option.auto_id,
            "jia_yi_idname": str(row.option.head_id) + ' ' + row.option.level,
            "jia_yi_mm_name": row.option.mm_name
        } for row in result
    ]
    return output

