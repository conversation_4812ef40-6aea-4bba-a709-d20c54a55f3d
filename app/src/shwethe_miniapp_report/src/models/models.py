from typing import Dict
from xmlrpc.client import DateTime

from click import option
from pydantic.types import Optional, List
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel, Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id


class title_data_base(SQLModel):
    title: Optional[str]
    type_id: Optional[int]
    fen_dian_id: Optional[int]
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    status_id: Optional[int]

class title_data(title_data_base, table=True):
    __tablename__ = "title_data"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    title: Optional[str]
    type_id: Optional[int]
    fen_dian_id: Optional[int]
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    status_id: Optional[int]
    
class title_data_read(BaseModel):
    auto_id: Optional[int]
    title: Optional[str]
    type_id: Optional[int]
    fen_dian_id: Optional[int]
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    status_id: Optional[int]

class title_data_post(BaseModel):
    title: Optional[str]
    type_id: Optional[int]
    fen_dian_id: Optional[int]
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    status_id: Optional[int]

# # ------------------------------------
class details_data_base(SQLModel):
    title_data_id: Optional[int]
    key: Optional[int]
    value: Optional[str]
    story_id: Optional[int]

class details_data(details_data_base, table=True):
    __tablename__ = "details_data"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    title_data_id: Optional[int]
    key: Optional[int]
    value: Optional[str]
    story_id: Optional[int]
    
class details_data_read(BaseModel):
    auto_id: Optional[int]
    title_data_id: Optional[int]
    key: Optional[int]
    value: Optional[str]
    story_id: Optional[int]

class details_data_post(BaseModel):
    title_data_id: Optional[int]
    key: Optional[int]
    value: Optional[str]
    story_id: Optional[int]

# # ------------------------------------
class related_person_base(SQLModel):
    title_id: Optional[int]
    option_id: Optional[int]
    key: Optional[int]
    value: Optional[int]

class related_person(related_person_base, table=True):
    __tablename__ = "related_person"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    title_id: Optional[int]
    option_id: Optional[int]
    key: Optional[int]
    value: Optional[int]
    
class related_person_read(BaseModel):
    auto_id: Optional[int]
    title_id: Optional[int]
    option_id: Optional[int]
    key: Optional[int]
    value: Optional[int]

class related_person_post(BaseModel):
    title_id: Optional[int]
    option_id: Optional[int]
    key: Optional[int]
    value: Optional[int]

# # ------------------------------------
class option_base(SQLModel):
    mm_name: Optional[str]
    parent_id: Optional[int]
    head_id: Optional[int]
    json_id: dict = Field(sa_column=Column(JSONB), default={})
    level: Optional[str] 

class option(option_base, table=True):
    __tablename__ = "option"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    mm_name: Optional[str]
    parent_id: Optional[int]
    head_id: Optional[int]
    json_id: dict = Field(sa_column=Column(JSONB), default={})
    level: Optional[str] 
    
class option_read(BaseModel):
    auto_id: Optional[int]
    mm_name: Optional[str]
    parent_id: Optional[int]
    head_id: Optional[int]
    json_id: dict = Field(sa_column=Column(JSONB), default={})
    level: Optional[str] 

class option_post(BaseModel):
    mm_name: Optional[str]
    parent_id: Optional[int]
    head_id: Optional[int]
    json_id: dict = Field(sa_column=Column(JSONB), default={})
    level: Optional[str] 

# # ------------------------------------
class story_base(SQLModel):
    type_name: Optional[str]
    type_form: Optional[str]

class story(story_base, table=True):
    __tablename__ = "story"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    type_name: Optional[str]
    type_form: Optional[str]
    
class story_read(BaseModel):
    auto_id: Optional[int]
    type_name: Optional[str]
    type_form: Optional[str]

class story_post(BaseModel):
    type_name: Optional[str]
    type_form: Optional[str]


# # ------------------------------------
class form_story_base(SQLModel):
    story_id: Optional[int]
    input_type: Optional[str]
    input_value: Optional[str]
    input_string: Optional[str]
    parent_id: Optional[int]
    head_id: Optional[int]
    api: Optional[str]
    index: Optional[int]

class form_story(form_story_base, table=True):
    __tablename__ = "form_story"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    story_id: Optional[int]
    input_type: Optional[str]
    input_value: Optional[str]
    input_string: Optional[str]
    parent_id: Optional[int]
    head_id: Optional[int]
    api: Optional[str]
    index: Optional[int]
    
class form_story_read(BaseModel):
    auto_id: Optional[int]
    story_id: Optional[int]
    input_type: Optional[str]
    input_value: Optional[str]
    input_string: Optional[str]
    parent_id: Optional[int]
    head_id: Optional[int]
    api: Optional[str]
    index: Optional[int]

class form_story_post(BaseModel):
    story_id: Optional[int]
    input_type: Optional[str]
    input_value: Optional[str]
    input_string: Optional[str]
    parent_id: Optional[int]
    head_id: Optional[int]
    api: Optional[str]
    index: Optional[int]


# ------------------------------------
class logs_base(SQLModel):
    json_text: dict = Field(sa_column=Column(JSONB), default={})
    title_data_id: Optional[int]
    json_form: dict = Field(sa_column=Column(JSONB), default={})

class logs(logs_base, table=True):
    __tablename__ = "logs"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    json_text: dict = Field(sa_column=Column(JSONB), default={})
    title_data_id: Optional[int]
    json_form: dict = Field(sa_column=Column(JSONB), default={})
    
class logs_read(BaseModel):
    auto_id: Optional[int]
    json_text: dict = Field(sa_column=Column(JSONB), default={})
    title_data_id: Optional[int]
    json_form: dict = Field(sa_column=Column(JSONB), default={})

class logs_post(BaseModel):
    json_text: dict = Field(sa_column=Column(JSONB), default={})
    title_data_id: Optional[int]
    json_form: dict = Field(sa_column=Column(JSONB), default={})

# ------------------------------------
class schedule_base(SQLModel):
    store: Optional[int]
    job: Optional[int]
    continue_day: Optional[bool]
    details: Optional[str]
    type_day: Optional[str]
    day_value: Optional[int]
    start_day: Optional[str]

class schedule(schedule_base, table=True):
    __tablename__ = "schedule"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    store: Optional[int]
    job: Optional[int]
    continue_day: Optional[bool]
    details: Optional[str]
    type_day: Optional[str]
    day_value: Optional[int]
    start_day: Optional[str]

class schedule_read(BaseModel):
    store: Optional[int]
    job: Optional[int]
    continue_day: Optional[bool]
    details: Optional[str]
    type_day: Optional[str]
    day_value: Optional[int]
    start_day: Optional[str]

class schedule_post(BaseModel):
    store: Optional[int]
    job: Optional[int]
    continue_day: Optional[bool]
    details: Optional[str]
    type_day: Optional[str]
    day_value: Optional[int]
    start_day: Optional[str]

# # ------------------------------------
class store_name_base(SQLModel):
    symbol: Optional[str]
    name: Optional[str]

class store_name(store_name_base, table=True):
    __tablename__ = "store_name"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    symbol: Optional[str]
    name: Optional[str]
    
class store_name_read(BaseModel):
    auto_id: Optional[int]
    symbol: Optional[str]
    name: Optional[str]

class store_name_post(BaseModel):
    symbol: Optional[str]
    name: Optional[str]

# # ------------------------------------
class chat_message_base(SQLModel):
    group_id: Optional[str]
    user_id : Optional[str]
    message: Optional[str]
    timestamp: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="timestamp_datetime"
    )
    
class chat_message(chat_message_base, table=True):
    __tablename__ = "chat_messages"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    group_id: Optional[str]
    user_id : Optional[str]
    message: Optional[str]
    timestamp: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="timestamp_datetime"
    )

class chat_message_read(BaseModel):
    auto_id: Optional[int]
    group_id: Optional[str]
    user_id : Optional[str]
    message: Optional[str]
    timestamp: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="timestamp_datetime"
    )

class chat_message_post(BaseModel):
    group_id: Optional[str]
    user_id : Optional[str]
    message: Optional[str]
    timestamp: Optional[str] = Field(
        default_factory=lambda: datetime.now().isoformat(), title="timestamp_datetime"
    )
