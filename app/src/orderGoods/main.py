from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time
from src.time_zone.time_zone_function import get_date_delete_day,get_date_delete_is_datetime

router = APIRouter()

@router.get("/orderGoodsIthem", status_code = 200)
def orderGoodsIthemG(type:str=None):
    try:
        import src.Connect.mongo_connect as client
        from importlib import reload
        reload(client)
        from src.Connect.mongo_connect import client

        insert_data = client['insert_data']
        not_order = insert_data['not_order']

        today = get_date_delete_is_datetime(60)



        FD10001 =  pd.DataFrame(list(not_order.find({'datetime': {"$gt": today}})))

        # print(FD10001['datetime'])
        # print("today")
        
        data = client['view']
        list_barcode_location = data['order']
        
        if type != None:
            P100001 =  pd.DataFrame(list(list_barcode_location.find({'status': False ,'type': type  },{'_id':0})))

        if type == None:
            P100001 =  pd.DataFrame(list(list_barcode_location.find({},{'_id':0})))
        try:
            P100001['day_ans'] = (P100001['day'] - P100001['order_day'])
        except:
            P100001['day_ans'] = 0 
            
        P100002 = P100001.loc[P100001['day_ans'] < 5]
        
        if type == 'G':
            P100002 = P100001.loc[P100001['day_ans'] < 10]
            
        P100002 = P100002.loc[P100001['count'] > 0]

        if not FD10001.empty:
            POPO10001 = P100002[~P100002['id'].isin(FD10001['id'].tolist())]
        else:
            POPO10001 = P100002

        import numpy as np
        P100003 = POPO10001.replace(np.nan, 0)
        P100004 =P100003.round(2)
        P100005 = P100004.to_json(orient='records')
        return json.loads(P100005)

    finally:

        client.close()
