{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install requests"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import psycopg2\n", "import requests\n", "\n", "postgresql_shwethe_personal = psycopg2.connect(\"dbname='shwethe_personal' user='postgres' host='************' port='5436' password='0818822095'\")\n", "postgresql_shwethe_miniapp_carActive = psycopg2.connect(\"dbname='shwethe_miniapp_carActive' user='postgres' host='************' port='5436' password='0818822095'\")\n", "\n", "\n", "df = pd.read_sql(\"\"\"\n", "                        SELECT *\n", "                        FROM personal_info\n", "                        WHERE key = 49 AND DATE(datetime) >= '2023-05-24'\n", "                    \"\"\", postgresql_shwethe_personal)\n", "display(df)\n", "\n", "\n", "df2 = pd.read_sql(\n", "                \"\"\"\n", "                    SELECT DISTINCT ON (vi.jia_yi_id_driver) vi.jia_yi_id_vehicle, vi.jia_yi_id_driver, vi.datetime\n", "                    FROM vehicle v\n", "                    JOIN vehicle_insert vi ON v.jia_yi_id = vi.jia_yi_id_vehicle\n", "                    WHERE v.type = 'car'\n", "                    ORDER BY vi.jia_yi_id_driver, vi.datetime DESC\n", "                \"\"\", postgresql_shwethe_miniapp_carActive)\n", "df2 = df2.dropna(subset=['jia_yi_id_driver'])\n", "df2 = df2.sort_values(by='datetime', ascending=False)\n", "df2['jia_yi_id_driver'] = df2['jia_yi_id_driver'].astype('int64')\n", "# display(df2)\n", "\n", "\n", "df3 = df2[~df2['jia_yi_id_driver'].isin(df['jia_yi_id'])]\n", "df3 = df3[df3['jia_yi_id_vehicle'] != df3['jia_yi_id_driver']]\n", "df3 = df3.drop_duplicates(subset='jia_yi_id_vehicle', keep='first')\n", "\n", "jia_yi_id_vehicle_unique = df3['jia_yi_id_vehicle'].unique()\n", "jia_yi_id_driver_unique = df3['jia_yi_id_driver'].unique()\n", "join_two_column = pd.DataFrame({'jia_yi_id': [*jia_yi_id_vehicle_unique, *jia_yi_id_driver_unique]})\n", "join_two_column = join_two_column.drop_duplicates(subset=['jia_yi_id'])\n", "\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = join_two_column[['jia_yi_id']].to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "getApiValue = requests.get(url=url, json=body_raw).json()\n", "getApiValue = pd.DataFrame(getApiValue)\n", "# display(getApiValue)\n", "\n", "merge_jia_yi_id_vehicle = pd.merge(df3, getApiValue, left_on='jia_yi_id_vehicle', right_on='jia_yi_id')\n", "merge_jia_yi_id_driver = pd.merge(merge_jia_yi_id_vehicle, getApiValue, left_on='jia_yi_id_driver', right_on='jia_yi_id')\n", "merge_jia_yi_id_driver = merge_jia_yi_id_driver.sort_values(by='datetime', ascending=False)\n", "# merge_jia_yi_id_driver = merge_jia_yi_id_driver.to_dict(\"records\")\n", "display(merge_jia_yi_id_driver)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import psycopg2\n", "import requests\n", "\n", "postgresql_shwethe_personal = psycopg2.connect(\"dbname='shwethe_personal' user='postgres' host='************' port='5436' password='0818822095'\")\n", "postgresql_shwethe_miniapp_carActive = psycopg2.connect(\"dbname='shwethe_miniapp_carActive' user='postgres' host='************' port='5436' password='0818822095'\")\n", "\n", "\n", "df = pd.read_sql(\"\"\"\n", "                        SELECT *\n", "                        FROM personal_info\n", "                        WHERE key = 49 AND DATE(datetime) >= '2023-05-24'\n", "                    \"\"\", postgresql_shwethe_personal)\n", "# display(df)\n", "\n", "\n", "df2 = pd.read_sql(\n", "                \"\"\"\n", "                    SELECT DISTINCT ON (vi.jia_yi_id_vehicle) vi.jia_yi_id_vehicle, vi.jia_yi_id_driver, vi.datetime\n", "                    FROM vehicle v\n", "                    JOIN vehicle_insert vi ON v.jia_yi_id = vi.jia_yi_id_vehicle\n", "                    WHERE v.type = 'car'\n", "                    ORDER BY vi.jia_yi_id_vehicle, vi.datetime DESC\n", "                \"\"\", postgresql_shwethe_miniapp_carActive)\n", "df2 = df2.dropna(subset=['jia_yi_id_driver'])\n", "df2 = df2.sort_values(by='datetime', ascending=False)\n", "df2['jia_yi_id_driver'] = df2['jia_yi_id_driver'].astype('int64')\n", "# display(df2)\n", "\n", "\n", "df3 = df2[~df2['jia_yi_id_driver'].isin(df['jia_yi_id'])]\n", "df3 = df3[df3['jia_yi_id_vehicle'] != df3['jia_yi_id_driver']]\n", "df3 = df3.drop_duplicates(subset='jia_yi_id_vehicle', keep='first')\n", "\n", "jia_yi_id_vehicle_unique = df3['jia_yi_id_vehicle'].unique()\n", "jia_yi_id_driver_unique = df3['jia_yi_id_driver'].unique()\n", "join_two_column = pd.DataFrame({'jia_yi_id': [*jia_yi_id_vehicle_unique, *jia_yi_id_driver_unique]})\n", "join_two_column = join_two_column.drop_duplicates(subset=['jia_yi_id'])\n", "\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "edit = df2.rename(columns={'jia_yi_id_driver': 'jia_yi_id'})\n", "to_dict = edit[['jia_yi_id']].to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "getApiValue = requests.get(url=url, json=body_raw).json()\n", "getApiValue = pd.DataFrame(getApiValue)\n", "# display(getApiValue)\n", "\n", "# merge_jia_yi_id_vehicle = pd.merge(df3, getApiValue, left_on='jia_yi_id_vehicle', right_on='jia_yi_id')\n", "merge_jia_yi_id_driver = pd.merge(df2, getApiValue, left_on='jia_yi_id_driver', right_on='jia_yi_id')\n", "# merge_jia_yi_id_driver = merge_jia_yi_id_driver.sort_values(by='datetime', ascending=False)\n", "display(merge_jia_yi_id_driver)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import psycopg2\n", "import requests\n", "\n", "postgresql_shwethe_personal = psycopg2.connect(\"dbname='shwethe_personal' user='postgres' host='************' port='5436' password='0818822095'\")\n", "postgresql_shwethe_miniapp_carActive = psycopg2.connect(\"dbname='shwethe_miniapp_carActive' user='postgres' host='************' port='5436' password='0818822095'\")\n", "\n", "\n", "df = pd.read_sql(\"\"\"\n", "                        SELECT *\n", "                        FROM personal_info\n", "                        WHERE key = 49 AND DATE(datetime) >= '2023-05-24'\n", "                    \"\"\", postgresql_shwethe_personal)\n", "display(df)\n", "\n", "df2 = pd.read_sql(\n", "                \"\"\"\n", "                    SELECT DISTINCT ON (vi.jia_yi_id_vehicle) vi.jia_yi_id_vehicle, vi.jia_yi_id_driver, vi.datetime\n", "                    FROM vehicle v\n", "                    JOIN vehicle_insert vi ON v.jia_yi_id = vi.jia_yi_id_vehicle\n", "                    WHERE v.type = 'car'\n", "                    ORDER BY vi.jia_yi_id_vehicle, vi.datetime DESC\n", "                \"\"\", postgresql_shwethe_miniapp_carActive)\n", "# df2 = df2.sort_values(by='datetime', ascending=False)\n", "display(df2)\n", "\n", "# df3 = df2[~df2['jia_yi_id_driver'].isin(df['jia_yi_id'])]\n", "# display(df3)\n", "\n", "# jia_yi_id_vehicle_unique = df3['jia_yi_id_vehicle'].unique()\n", "# jia_yi_id_driver_unique = df3['jia_yi_id_driver'].unique()\n", "# join_two_column = pd.DataFrame({'jia_yi_id': [*jia_yi_id_vehicle_unique, *jia_yi_id_driver_unique]})\n", "# join_two_column = join_two_column.drop_duplicates(subset=['jia_yi_id'])\n", "# # display(join_two_column)\n", "\n", "# url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "# to_dict = join_two_column[['jia_yi_id']].to_dict('records')\n", "# body_raw = {\"data_api\": to_dict}\n", "# getApiValue = requests.get(url=url, json=body_raw).json()\n", "# getApiValue = pd.DataFrame(getApiValue)\n", "# # display(getApiValue)\n", "\n", "# merge_jia_yi_id_vehicle = pd.merge(df3, getApiValue, left_on='jia_yi_id_vehicle', right_on='jia_yi_id')\n", "# merge_jia_yi_id_driver = pd.merge(merge_jia_yi_id_vehicle, getApiValue, left_on='jia_yi_id_driver', right_on='jia_yi_id')\n", "# merge_jia_yi_id_driver = merge_jia_yi_id_driver.sort_values(by='datetime', ascending=False)\n", "# # # merge_jia_yi_id_driver = merge_jia_yi_id_driver.to_dict(\"records\")\n", "# display(merge_jia_yi_id_driver)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}