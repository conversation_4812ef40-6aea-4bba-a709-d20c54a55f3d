{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-10-11 04:50:49,319 INFO sqlalchemy.engine.Engine select pg_catalog.version()\n", "2024-10-11 04:50:49,321 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-10-11 04:50:49,325 INFO sqlalchemy.engine.Engine select current_schema()\n", "2024-10-11 04:50:49,327 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-10-11 04:50:49,331 INFO sqlalchemy.engine.Engine show standard_conforming_strings\n", "2024-10-11 04:50:49,334 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2024-10-11 04:50:49,340 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2024-10-11 04:50:49,348 INFO sqlalchemy.engine.Engine SELECT personal.auto_id, personal.jia_yi_id, personal.datetime, personal.fen_dian_id \n", "FROM personal\n", "2024-10-11 04:50:49,354 INFO sqlalchemy.engine.Engine [generated in 0.00632s] {}\n", "2024-10-11 04:50:49,669 INFO sqlalchemy.engine.Engine SELECT tree_key.mm_name, tree_key.parent_id, tree_key.head_id, tree_key.fen_ji, tree_key.type, tree_key.input_or_text, tree_key.hide, tree_key.auto_id \n", "FROM tree_key \n", "WHERE tree_key.parent_id = %(parent_id_1)s\n", "2024-10-11 04:50:49,671 INFO sqlalchemy.engine.Engine [generated in 0.00192s] {'parent_id_1': 2}\n", "2024-10-11 04:50:49,684 INFO sqlalchemy.engine.Engine SELECT personal_info.jia_yi, personal_info.jia_yi_id, personal_info.type, personal_info.key, personal_info.value, personal_info.iid, personal_info.datetime, personal_info.auto_id \n", "FROM personal_info JOIN (SELECT personal_info.jia_yi AS jia_yi, personal_info.key AS key, max(personal_info.datetime) AS max_datetime \n", "FROM personal_info GROUP BY personal_info.jia_yi, personal_info.key) AS anon_1 ON personal_info.jia_yi = anon_1.jia_yi AND personal_info.key = anon_1.key AND personal_info.datetime = anon_1.max_datetime \n", "WHERE personal_info.key IN (%(key_1_1)s, %(key_1_2)s, %(key_1_3)s) ORDER BY personal_info.jia_yi, personal_info.key, personal_info.datetime\n", "2024-10-11 04:50:49,687 INFO sqlalchemy.engine.Engine [generated in 0.00332s] {'key_1_1': 3, 'key_1_2': 46, 'key_1_3': 5}\n", "2024-10-11 04:50:51,446 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_id</th>\n", "      <th>jia_yi_idname</th>\n", "      <th>jia_yi_mm_name</th>\n", "      <th>ri_qi</th>\n", "      <th>auto_id</th>\n", "      <th>datetime</th>\n", "      <th>fen_dian_id</th>\n", "      <th>personImage</th>\n", "      <th>personName</th>\n", "      <th>personPhone</th>\n", "      <th>日期</th>\n", "      <th>card_scan</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1236</td>\n", "      <td>AE01</td>\n", "      <td>သိန်းဌေးအောင်</td>\n", "      <td>2017-05-15 00:00:00.000</td>\n", "      <td>368.0</td>\n", "      <td>2023-03-28 10:03:36.506615</td>\n", "      <td>1.0</td>\n", "      <td>226.0</td>\n", "      <td>562.0</td>\n", "      <td>260.0</td>\n", "      <td>2024-10-10 00:00:00</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1259</td>\n", "      <td>AL02</td>\n", "      <td>ဉီးအိုက်လှ</td>\n", "      <td>2003-01-25 00:00:00.000</td>\n", "      <td>424.0</td>\n", "      <td>2023-04-18 13:07:52.908874</td>\n", "      <td>1.0</td>\n", "      <td>541.0</td>\n", "      <td>541.0</td>\n", "      <td>0.0</td>\n", "      <td>2024-10-10 00:00:00</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1338</td>\n", "      <td>AL01</td>\n", "      <td>ဉီးအိုက်တီ</td>\n", "      <td>2005-08-24 00:00:00.000</td>\n", "      <td>421.0</td>\n", "      <td>2023-04-18 12:46:14.009843</td>\n", "      <td>1.0</td>\n", "      <td>541.0</td>\n", "      <td>541.0</td>\n", "      <td>0.0</td>\n", "      <td>2024-10-10 00:00:00</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1342</td>\n", "      <td>AL08</td>\n", "      <td>စိုးမြင့်ဉီး</td>\n", "      <td>2015-11-02 00:00:00.000</td>\n", "      <td>129.0</td>\n", "      <td>2022-12-11 15:31:00.737955</td>\n", "      <td>1.0</td>\n", "      <td>669.0</td>\n", "      <td>669.0</td>\n", "      <td>58.0</td>\n", "      <td>2024-10-10 00:00:00</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1788</td>\n", "      <td>AA21</td>\n", "      <td>ကိုခင်မောင်ရွှေ</td>\n", "      <td>2009-06-28 00:00:00.000</td>\n", "      <td>140.0</td>\n", "      <td>2022-12-11 15:55:29.666734</td>\n", "      <td>1.0</td>\n", "      <td>256.0</td>\n", "      <td>669.0</td>\n", "      <td>58.0</td>\n", "      <td>2024-10-10 00:00:00</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>634</th>\n", "      <td>34335</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2023-11-02 00:00:00</td>\n", "      <td>344.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>635</th>\n", "      <td>42714</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2023-12-02 00:00:00</td>\n", "      <td>314.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>636</th>\n", "      <td>19026</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2023-12-26 00:00:00</td>\n", "      <td>290.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>637</th>\n", "      <td>18165</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2024-01-15 00:00:00</td>\n", "      <td>270.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>638</th>\n", "      <td>194</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2024-10-10 00:00:00</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>639 rows × 12 columns</p>\n", "</div>"], "text/plain": ["     jia_yi_id jia_yi_idname   jia_yi_mm_name                    ri_qi   \n", "0         1236          AE01    သိန်းဌေးအောင်  2017-05-15 00:00:00.000  \\\n", "1         1259          AL02       ဉီးအိုက်လှ  2003-01-25 00:00:00.000   \n", "2         1338          AL01       ဉီးအိုက်တီ  2005-08-24 00:00:00.000   \n", "3         1342          AL08     စိုးမြင့်ဉီး  2015-11-02 00:00:00.000   \n", "4         1788          AA21  ကိုခင်မောင်ရွှေ  2009-06-28 00:00:00.000   \n", "..         ...           ...              ...                      ...   \n", "634      34335             0                0                        0   \n", "635      42714             0                0                        0   \n", "636      19026             0                0                        0   \n", "637      18165             0                0                        0   \n", "638        194             0                0                        0   \n", "\n", "     auto_id                    datetime  fen_dian_id  personImage   \n", "0      368.0  2023-03-28 10:03:36.506615          1.0        226.0  \\\n", "1      424.0  2023-04-18 13:07:52.908874          1.0        541.0   \n", "2      421.0  2023-04-18 12:46:14.009843          1.0        541.0   \n", "3      129.0  2022-12-11 15:31:00.737955          1.0        669.0   \n", "4      140.0  2022-12-11 15:55:29.666734          1.0        256.0   \n", "..       ...                         ...          ...          ...   \n", "634      0.0                           0          0.0          0.0   \n", "635      0.0                           0          0.0          0.0   \n", "636      0.0                           0          0.0          0.0   \n", "637      0.0                           0          0.0          0.0   \n", "638      0.0                           0          0.0          0.0   \n", "\n", "     personName  personPhone                   日期  card_scan  \n", "0         562.0        260.0  2024-10-10 00:00:00        1.0  \n", "1         541.0          0.0  2024-10-10 00:00:00        1.0  \n", "2         541.0          0.0  2024-10-10 00:00:00        1.0  \n", "3         669.0         58.0  2024-10-10 00:00:00        1.0  \n", "4         669.0         58.0  2024-10-10 00:00:00        1.0  \n", "..          ...          ...                  ...        ...  \n", "634         0.0          0.0  2023-11-02 00:00:00      344.0  \n", "635         0.0          0.0  2023-12-02 00:00:00      314.0  \n", "636         0.0          0.0  2023-12-26 00:00:00      290.0  \n", "637         0.0          0.0  2024-01-15 00:00:00      270.0  \n", "638         0.0          0.0  2024-10-10 00:00:00        1.0  \n", "\n", "[639 rows x 12 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "from src.shwethe_personal.database import get_session\n", "from src.shwethe_personal.form_personal.models.models import personal, personal_info, tree_key\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_, func\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def dataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "def getCarAtive100():\n", "    with get_session_dependency() as db:\n", "\n", "        parent_id = 2\n", "\n", "\n", "        # mergeDF = dataframe(db.exec(select(check_stock)).all(), to_dict=False)\n", "        heroesPersonal = db.exec(select(personal.auto_id, personal.jia_yi_id, personal.datetime, personal.fen_dian_id)).all()\n", "        df1 = pd.DataFrame.from_records(heroesPersonal, columns=['auto_id', 'jia_yi_id', 'datetime', 'fen_dian_id'])\n", "        df11 = df1[['jia_yi_id']]\n", "        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'\n", "        to_dict = df11.to_dict('records')\n", "        body_raw = {\"data_api\": to_dict}\n", "        mergeDF = requests.get(url=url, json=body_raw).json()\n", "        mergeOne = pd.DataFrame(mergeDF)\n", "        mergeAll = pd.merge(mergeOne, df1, on='jia_yi_id').fillna(0)\n", "\n", "\n", "        heroeStree_key = db.exec(select(tree_key).where(tree_key.parent_id == parent_id)).all()\n", "        records = [i.dict() for i in heroeStree_key]   \n", "        df3 = pd.DataFrame.from_records(records)\n", "        \n", "\n", "        lastDate = (\n", "            select(\n", "                personal_info.jia_yi, personal_info.key, func.max(personal_info.datetime).label(\"max_datetime\")\n", "            )\n", "            .group_by(personal_info.jia_yi, personal_info.key)\n", "            .subquery()\n", "        )\n", "        query = (\n", "            select(personal_info)\n", "            .join(lastDate, and_(\n", "                personal_info.jia_yi == lastDate.c.jia_yi, personal_info.key == lastDate.c.key, personal_info.datetime == lastDate.c.max_datetime\n", "            ))\n", "            .where(personal_info.key.in_(df3['auto_id'].tolist()))\n", "            .order_by(personal_info.jia_yi, personal_info.key, personal_info.datetime)\n", "        )\n", "        heroesPersonal_info = db.exec(query).all()\n", "        records = [i.dict() for i in heroesPersonal_info]   \n", "        df2 = pd.DataFrame.from_records(records)\n", "\n", "\n", "        mergeDF = df2.merge(df3, left_on='key', right_on='auto_id')\n", "\n", "\n", "        mergeDF['datetime'] = pd.to_datetime(mergeDF['datetime'])\n", "        current_date = datetime.now()\n", "        mergeDF['days_passed'] = (current_date - mergeDF['datetime']).dt.days\n", "        result_df = mergeDF.pivot(index='jia_yi_id', columns='mm_name', values='days_passed')\n", "        result_df.reset_index(inplace=True)\n", "        # result_df = result_df[50:70]\n", "\n", "\n", "        merge = mergeAll.merge(result_df, left_on='jia_yi_id', right_on='jia_yi_id')\n", "\n", "\n", "        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v1/work_list/work_list_personal_latestDate'\n", "        url = mongodb_data_api + '/api/v1/work_list/work_list_personal_latestDate'\n", "        to_dict = df11.to_dict('records')\n", "        body_raw = {\"data_api\": to_dict}\n", "        last_work = requests.get(url=url, json=body_raw).json()\n", "        last_work = pd.DataFrame(last_work)\n", "        last_work = last_work.rename(columns={'乙方': 'jia_yi_id'})\n", "        last_work[\"日期\"] = pd.to_datetime(last_work[\"日期\"]).dt.tz_localize(None)\n", "        today = datetime.now()\n", "        today = today.replace(tzinfo=None)\n", "        last_work[\"card_scan\"] = (today - last_work[\"日期\"]).dt.days\n", "        mergeLastWork = pd.merge(merge, last_work, how=\"outer\", on='jia_yi_id').fillna(0)\n", "\n", "    return mergeLastWork\n", "\n", "getCarAtive100()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}