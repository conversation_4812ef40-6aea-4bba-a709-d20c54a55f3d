from builtins import int
from locale import D_FMT
from lib2to3.pgen2.pgen import DFAState
from multiprocessing.reduction import DupFd
from unittest import result
from src.shwethe_personal.form_personal.models.models import personal, personal_info, personal_info_post, personal_post, tree_key, tag, tree_key_post
from src.Connect.https_connect import mongodb_data_api
from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel
from typing import Dict, List, Optional
from pydantic.types import Optional, List, Dict

from datetime import datetime
from src.shwethe_personal.database import get_session
from src.config.shwethe_car_active.database import get_session as get_session2

from helper import generate_datetime_id
import json
import pandas as pd
import requests
from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT, text, func, and_
import numpy as np
import datetime as DT
from src.time_zone.time_zone_function import get_datetime




# def addPersonal100(hero: personal_info_post, db: Session = Depends(get_session)):

#     hero.data_sub = {'jia_yi_name':'ag164'}

#     statement = select(personal_info).where(personal_info.data_sub['jia_yi_name'].astext.cast(String) == hero.data_sub['jia_yi_name'])
#     results = db.exec(statement).first()

#     if (results == None):
#         print("emptyyyyyyyyyyy")
#         url = mongodb_data_api + '/api/v2/search/jia_yi_search_text?text={}'.format(hero.data_sub['jia_yi_name'])
#         df50 = requests.get(url=url).json()
#         df50 = pd.DataFrame(df50)
#         if len(df50) != 0:
#             print("value")
#             hero_to_db = personal_info.from_orm(hero)
#             hero_to_db.jia_yi_id = df50['jia_yi_id'].astype(int).to_list()[0]
#             db.add(hero_to_db)
#             db.commit()
#             db.refresh(hero_to_db)
#             res = 'FoundJia_yi_name'
#         else:
#             print("empty")
#             res = 'notFoundJia_yi_name'
#     else:
#         print("haveeeeeeeeeeee")
#         res = 'haveThisName'

#     return res


# def editPersonal100(jia_yi_id: int, hero: personal_info_post, db: Session = Depends(get_session)):
    
#     statement = select(personal_info).where(personal_info.jia_yi_id == jia_yi_id)
#     results = db.exec(statement).first()
#     results.name = hero.name
#     results.phone = hero.phone
#     results.image_path = hero.image_path
#     db.add(results)  
#     db.commit()  
#     db.refresh(results)  
#     print(results)
    
#     return results


# def addNrc100(hero: nrc_post, db: Session = Depends(get_session)):
    
#     hero.personal_id = 25730

#     statement = select(nrc).where(nrc.personal_id == hero.personal_id)
#     results = db.exec(statement).first()
#     print(results)

#     if (results == None):
#         print("empty")
#         hero_to_db = nrc.from_orm(hero)
#         db.add(hero_to_db)
#         db.commit()
#         db.refresh(hero_to_db)
#         res = 'empty'
#     else:
#         print("haveeeeeeeeeeee")
#         results.nrc_no = hero.nrc_no
#         results.image_path = hero.image_path
#         db.add(results)  
#         db.commit()  
#         db.refresh(results)  
#         res = 'haveThisName'
    
#     return res


# def addDepartmentList100(hero: department_list_post, db: Session = Depends(get_session)):

#     hero.parent_id = 18
#     hero.parent_head_id = 12

#     if (hero.parent_id == 0):
#         print("parent")
#         hero_to_db = department_list.from_orm(hero)
#         db.add(hero_to_db)
#         db.commit()
#         db.refresh(hero_to_db)

#         statement = select(department_list).where(department_list.auto_id == hero_to_db.auto_id)
#         results = db.exec(statement).first()

#         results.parent_head_id = results.auto_id
#         db.add(results)  
#         db.commit()  
#         db.refresh(results)  

#     if (hero.parent_id > 0):
#         print("child_parent")
#         statement = select(department_list).where(department_list.auto_id == hero.parent_id)
#         results = db.exec(statement).first()

#         results.if_parent = True
#         db.add(results)  
#         db.commit()  
#         db.refresh(results) 

#         hero_to_db = department_list.from_orm(hero)
#         db.add(hero_to_db)
#         db.commit()
#         db.refresh(hero_to_db)

#     return 'res'


# def getDepartmentParent100(db: Session = Depends(get_session)):
#     try:

#         df = db.exec(
#             select(department_list)
#             .where(department_list.parent_id == 0)
#             .order_by(department_list.auto_id)
#         ).all()
#     except: 
#         df = []                
#     return df


# def getDepartmentTree100(parent_head_id: int, db: Session = Depends(get_session)):
#     from dataclasses import dataclass, field, asdict
#     from pprint import pprint
#     from typing import List, Dict, Optional
#     import pandas as pd
#     from pandas import DataFrame


#     @dataclass
#     class Tree:
#         name: str
#         id: int
#         child_id: int
#         children: List['Tree'] = field(default_factory=list)


#     def traversal(df: DataFrame) -> Optional[Tree]:
#         tmp: List[Tree] = [Tree(name=i["name"], id=int(i["id"]), child_id=int(i["child_id"])) for i in df.to_dict("records")]
#         memo: Dict[int, Tree] = {i.id: i for i in tmp}
#         # print(memo)
#         root = []
#         for i in tmp:
#             # print(i)
#             if i.child_id == 0:
#                 root = i
#                 print(root)
#                 # root.append(i)
#             else:
#                 memo[i.child_id].children.append(i)
#                 # print(memo[i.child_id].children.append(i))
#         return print(root)
        

    
#     sql = db.exec(select(department_list).where(department_list.parent_head_id == parent_head_id)).all()
#     records = [i.dict() for i in sql]
#     df = pd.DataFrame.from_records(records)
#     df = df.rename(columns={"auto_id": "id", "mm_name": "name", "parent_id": "child_id"})    
#     res = traversal(df)
#     # print(asdict(res)

#     return '[asdict(res)]'


# def vvvvvv100(db: Session = Depends(get_session)):

#     def makehiearchy(data):
#         result = []
#         d = { "0": { "children": result } }
#         for id, name, child_id in zip(data["id"], data["name"], data["child_id"]):
#             d[id] = { "name": name, "id": id, "child_id": child_id }
        
#         for id, child_id in zip(data["id"], data["child_id"]):
#             parent = d[child_id]
#             if "children" not in parent:
#                 parent["children"] = []
#             parent["children"].append(d[id])

#         return result

#     # Example run
#     sql = db.exec(select(department_list)).all()
#     records = [i.dict() for i in sql]
#     df = pd.DataFrame.from_records(records)
#     data = {
#         'id': df['auto_id'].astype(str).to_list(),
#         'name': df['mm_name'].to_list(),
#         'child_id': df['parent_id'].astype(str).to_list()
#     }    
#     hierarchy = makehiearchy(data)
    
#     return hierarchy


# def aaa100(db: Session = Depends(get_session)):
#     try:
#         df = db.exec(select(personal_info)).all()
#     except: 
#         df = []                
#     return df

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

def dataframe(sqlModel, to_dict=False):
    records = [i.dict() for i in sqlModel]
    mergeDF = pd.DataFrame.from_records(records).fillna(0)
    if to_dict:
        mergeDF = mergeDF.to_dict("records")
    return mergeDF


def vvvvvv100(db: Session = Depends(get_session)):

    def makehiearchy(data):
        result = []
        d = { "0": { "children": result } }
        for id, name, child_id in zip(data["id"], data["name"], data["child_id"]):
            d[id] = { "name": name, "id": id, "child_id": child_id }
        
        for id, child_id in zip(data["id"], data["child_id"]):
            parent = d[child_id]
            if "children" not in parent:
                parent["children"] = []
            parent["children"].append(d[id])

        return result

    # Example run
    sql = db.exec(select(tree_key)).all()
    records = [i.dict() for i in sql]
    df = pd.DataFrame.from_records(records)
    data = {
        'id': df['auto_id'].astype(str).to_list(),
        'name': df['mm_name'].to_list(),
        'child_id': df['parent_id'].astype(str).to_list()
    }    
    hierarchy = makehiearchy(data)
    
    return hierarchy


def checkDriver100(checkDriver: str, db: Session = Depends(get_session)):
    # print(checkDriver)
    # print(type(checkDriver))

    try:
        checkDriver = int(checkDriver)
        print(checkDriver)
        print(type(checkDriver))

        # url = 'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        # body_raw = {"data_api": [{"jia_yi_id": 36557}]}
        body_raw = {"data_api": [{"jia_yi_id": checkDriver}]}
        # body_raw = {"data_api": df}
        df2 = requests.get(url=url, json=body_raw)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')
        # print(df2)
    except:
        print('The provided value is not an integer')
        print(checkDriver)
        print(type(checkDriver))

        # url = f'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={checkDriver}'
        url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={checkDriver}'
        df2 = requests.get(url=url)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')
        # print(df2)
    return df2


def addPersonalName100(hero: personal_post, db: Session = Depends(get_session)):
    
    # hero.jia_yi = 'ag125'

    # url = f'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={hero.jia_yi}'
    url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={hero.jia_yi}'
    df = requests.get(url=url).json()
    # print(df)

    statement = select(personal).where(personal.jia_yi_id == df[0]['jia_yi_id'])
    results = db.exec(statement).first()
    # print(results)

    if (results == None):
        print("empty")
        print(df[0]['jia_yi_mm_name'])
        hero_to_db = personal.from_orm(hero)
        hero_to_db.jia_yi_id = df[0]['jia_yi_id']
        hero_to_db.datetime = str(datetime.now())
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        # print(hero_to_db.auto_id)

        insertName = personal_info(jia_yi=hero_to_db.auto_id, type=19, key=3, value=df[0]['jia_yi_mm_name'], iid=2, jia_yi_id=df[0]['jia_yi_id'], datetime=str(datetime.now()))
        db.add(insertName)
        db.commit()
        db.refresh(insertName)

        res = 'empty'
    else:
        print("haveeeeeeeeeeee")
        res = 'haveThisName'
    
    return res


def getTreeParent100(db: Session = Depends(get_session)):
    
    try:
        heroeStree_key = db.exec(select(tree_key).where(tree_key.parent_id == 1)).all()
        records = [i.dict() for i in heroeStree_key]   
        df3 = pd.DataFrame.from_records(records)

        mergeDF = df3.to_dict("records")
    except:
        mergeDF = []
    return mergeDF



def getPersonalName100(parent_id: int, db: Session = Depends(get_session)):
    
    # try:
    heroesPersonal = db.exec(select(personal.auto_id, personal.jia_yi_id, personal.datetime, personal.fen_dian_id)).all()
    df1 = pd.DataFrame.from_records(heroesPersonal, columns=['auto_id', 'jia_yi_id', 'datetime', 'fen_dian_id'])
    df11 = df1[['jia_yi_id']]
    # url = 'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
    to_dict = df11.to_dict('records')
    body_raw = {"data_api": to_dict}
    mergeDF = requests.get(url=url, json=body_raw).json()
    mergeOne = pd.DataFrame(mergeDF)
    mergeAll = pd.merge(mergeOne, df1, on='jia_yi_id').fillna(0)

    # heroesPersonal_info = db.exec(select(personal_info)).all()
    heroesPersonal_info = db.exec(select(personal_info).where(personal_info.iid == parent_id)).all()
    records = [i.dict() for i in heroesPersonal_info]   
    df2 = pd.DataFrame.from_records(records)
    df2 = df2.sort_values(['jia_yi', 'key' ,'datetime']).drop_duplicates(['jia_yi', 'key'], keep='last')

    heroeStree_key = db.exec(select(tree_key).where(tree_key.parent_id == parent_id)).all()
    records = [i.dict() for i in heroeStree_key]   
    df3 = pd.DataFrame.from_records(records)

    mergeDF = df2.merge(df3, left_on='key', right_on='auto_id')
    mergeDF = mergeDF.set_index(['jia_yi_id', 'mm_name']).value.unstack().reset_index()
    
    # mergeDF.iloc[:,1:50] = mergeDF.iloc[:,1:50].notnull().mul(1000)
    exclude_value = 'licenseValidDate'
    list_box = df3[df3['mm_name'] != exclude_value]['mm_name'].tolist()
    mergeDF.loc[:, mergeDF.columns.isin(list_box)] = mergeDF.loc[:, mergeDF.columns.isin(list_box)].notnull().mul(1000)

    if(parent_id == 10):
        mergeDF['licenseValidDateCopy'] = mergeDF['licenseValidDate']
        mergeDF['licenseValidDateCopy'] = pd.to_datetime(mergeDF['licenseValidDateCopy'])
        today = datetime.now().date()
        mergeDF['licenseUpcomingDate'] = (mergeDF['licenseValidDateCopy'].dt.date - today).dt.days
        mergeDF['licenseUpcomingDate'] = mergeDF['licenseUpcomingDate'].fillna('')

    mergeDF = mergeAll.merge(mergeDF, how='outer', left_on='jia_yi_id', right_on='jia_yi_id').fillna(0)
    mergeDF = mergeDF.replace(0, '')
    mergeDF = mergeDF.replace(1000, 'have')
    mergeDF = mergeDF.rename(columns={"jia_yi_idname": "jia_yi", "jia_yi_mm_name": "name"})  
    mergeDF['datetime'] = pd.to_datetime(mergeDF['datetime'])  # Convert 'datetime' column to datetime objects  
    mergeDF = mergeDF.sort_values(by='datetime', ascending=False)

    # url = 'http://************:8200/mongodb_data_api/api/v1/work_list/work_list_personal_latestDate'
    url = mongodb_data_api + '/api/v1/work_list/work_list_personal_latestDate'
    to_dict = df11.to_dict('records')
    body_raw = {"data_api": to_dict}
    last_work = requests.get(url=url, json=body_raw).json()
    last_work = pd.DataFrame(last_work)
    last_work = last_work.rename(columns={'乙方': 'jia_yi_id'})
    last_work["日期"] = pd.to_datetime(last_work["日期"]).dt.tz_localize(None)
    today = datetime.now()
    today = today.replace(tzinfo=None)
    last_work["card_scan"] = (today - last_work["日期"]).dt.days

    mergeLastWork = pd.merge(mergeDF, last_work, how="left", on='jia_yi_id').fillna(0)

    mergeDF = mergeLastWork.to_dict("records")
    # except:
    #     mergeDF = []
    return mergeDF




# def getPersonalNameColumn100(parent_id: int, db: Session = Depends(get_session)):
    
#     try:
#         heroesPersonal = db.exec(select(personal).limit(1)).all()
#         records = [i.dict() for i in heroesPersonal]   
#         df = pd.DataFrame.from_records(records)
#         df['idc'] = 'idc'
#         df = df[['fen_dian_id', 'datetime', 'jia_yi_id', 'jia_yi', 'name', 'idc']]

#         heroeStree_key = db.exec(select(tree_key).where(tree_key.parent_id == parent_id)).all()
#         records = [i.dict() for i in heroeStree_key]   
#         df3 = pd.DataFrame.from_records(records)
#         df3['idc'] = 'idc'

#         mergeDF = df.merge(df3, left_on='idc', right_on='idc').fillna(0)
#         mergeDF = mergeDF.set_index(['jia_yi_id', 'mm_name']).idc.unstack().reset_index().fillna(0)
#         mergeDF = df.merge(mergeDF, left_on='jia_yi_id', right_on='jia_yi_id').fillna(0)
#         del mergeDF['idc']

#         mergeDF = mergeDF.columns       
#         # mergeDF = mergeDF.to_dict("records")
#     except:
#         mergeDF = []
#     return list(mergeDF)
def getPersonalNameColumn100(parent_id: int, db: Session = Depends(get_session)):
    
    try:
        heroesPersonal = db.exec(select(personal).limit(1)).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records)
        df['idc'] = 'idc'
        df['card_scan'] = 0
        df = df[['fen_dian_id', 'datetime', 'jia_yi_id', 'jia_yi', 'name', 'idc', 'card_scan']]

        heroeStree_key = db.exec(select(tree_key).where(tree_key.parent_id == parent_id)).all()
        records = [i.dict() for i in heroeStree_key]   
        df3 = pd.DataFrame.from_records(records)
        df3['idc'] = 'idc'

        mergeDF = df.merge(df3, left_on='idc', right_on='idc').fillna(0)
        mergeDF = mergeDF.set_index(['jia_yi_id', 'mm_name']).idc.unstack().reset_index().fillna(0)
        mergeDF = df.merge(mergeDF, left_on='jia_yi_id', right_on='jia_yi_id').fillna(0)
        del mergeDF['idc']

        mergeDF = mergeDF.columns       
        # mergeDF = mergeDF.to_dict("records")
    except:
        mergeDF = []
    return list(mergeDF)


# def getPersonalNameShow100(db: Session = Depends(get_session)):
    
#     try:
#         heroesPersonal = db.exec(select(personal).order_by(personal.datetime.desc())).all()
#         records = [i.dict() for i in heroesPersonal]   
#         df = pd.DataFrame.from_records(records).fillna(0)

#         heroesPersonal_info = db.exec(select(personal_info)).all()
#         records = [i.dict() for i in heroesPersonal_info]   
#         df2 = pd.DataFrame.from_records(records)
#         df2 = df2.sort_values(['jia_yi', 'key' ,'datetime']).drop_duplicates(['jia_yi', 'key'], keep='last')

#         heroeStree_key = db.exec(select(tree_key).where(tree_key.input_or_text == 'input')).all()
#         records = [i.dict() for i in heroeStree_key]   
#         df3 = pd.DataFrame.from_records(records)

#         mergeDF = df2.merge(df3, left_on='key', right_on='auto_id')
#         mergeDF = mergeDF.set_index(['jia_yi_id', 'mm_name']).value.unstack().reset_index().fillna('')
#         mergeDF = df.merge(mergeDF, left_on='jia_yi_id', right_on='jia_yi_id')
#         mergeDF = mergeDF.to_dict("records")
#     except:
#         mergeDF = []
#     return mergeDF

def getPersonalNameShow100(db: Session = Depends(get_session)):
    
    try:
        heroesPersonal = db.exec(select(personal).order_by(personal.datetime.desc())).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal_info = db.exec(select(personal_info)).all()
        records = [i.dict() for i in heroesPersonal_info]   
        df2 = pd.DataFrame.from_records(records)
        df2 = df2.sort_values(['jia_yi', 'key' ,'datetime']).drop_duplicates(['jia_yi', 'key'], keep='last')

        heroeStree_key = db.exec(select(tree_key).where(tree_key.input_or_text == 'input')).all()
        records = [i.dict() for i in heroeStree_key]   
        df3 = pd.DataFrame.from_records(records)

        mergeDF = df2.merge(df3, left_on='key', right_on='auto_id')
        mergeDF = mergeDF.set_index(['jia_yi_id', 'mm_name']).value.unstack().reset_index().fillna('')
        mergeDF = df.merge(mergeDF, left_on='jia_yi_id', right_on='jia_yi_id')

        mergeDF['licenseValidDateCopy'] = mergeDF['licenseValidDate']
        mergeDF['licenseValidDateCopy'] = pd.to_datetime(mergeDF['licenseValidDateCopy'])
        today = datetime.now().date()
        mergeDF['upcomingDate'] = (mergeDF['licenseValidDateCopy'].dt.date - today).dt.days
        mergeDF['upcomingDate'] = mergeDF['upcomingDate'].fillna('')  

        mergeDF = mergeDF.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


# def addPersonalInfo100(data: List[dict], db: Session = Depends(get_session) ):
#     data = { 
#     "type": "student",
#     "age": "17",
#     "sex": "male",
#     }
    
#     def transfrom(data, non_key_value: list, key_value: list):
#         base = {key: val for key, val in data.items() if key in non_key_value}
#         ouput = [{**base, **{"key": val, "value": data[val]}} for val in key_value]
#         return ouput

#     ggg = transfrom(data, non_key_value=["type"], key_value=["age", "sex", "name"])


#     return 'ggg'


def addPersonalInfo100(data: List[dict], db: Session = Depends(get_session)):

    for i in data:
        print(i)
        hero_to_db = personal_info(jia_yi=i['jia_yi'] ,type=i['type'], key=i['key'], value=i['value'], iid=i['iid'], jia_yi_id=i['jia_yi_id'], datetime=str(datetime.now()))
        # hero_to_db = personal_info(key=16, value='song.1')
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)

    return data


def getPersonalInfo100(auto_id: int, db: Session = Depends(get_session)):
    try:
        df = db.exec(select(
            personal.auto_id, personal.jia_yi_id, personal.jia_yi, personal.name, personal.datetime,
            personal_info.auto_id, personal_info.jia_yi, personal_info.type, personal_info.key, personal_info.value, personal_info.iid, personal_info.datetime
            )
        .where(personal.auto_id == auto_id)
        .where(personal_info.jia_yi == auto_id)
        # .where(personal_info.key != 47)
        ).all()
        df1 = pd.DataFrame(df, columns =['auto_id', 'jia_yi_id', 'jia_yi', 'name', 'datetime', 'auto_id_1', 'jia_yi_1', 'type', 'key', 'value', 'iid', 'datetime_1'])
        df1['datetime_1'] = pd.to_datetime(df1['datetime_1']).dt.date
        today = DT.date.today() - DT.timedelta(days=2)
        df1['todayMinus'] = today
        df1['dateCheckSee'] = df1.apply(lambda x: True if x['datetime_1'] > x['todayMinus'] else False, axis=1)
        df1['today'] = pd.to_datetime("today").strftime("%Y-%m-%d")
        df1['dateCheckToday'] = df1.apply(lambda x: True if str(x['datetime_1']) == str(x['today']) else False, axis=1)
        # print(df1)  

        df2 = db.exec(select(tree_key)
        .where(tree_key.input_or_text == 'input')
        ).all()
        df2 = sqmodel_to_df(df2)
        # print(df2)
 
        df21 = db.exec(select(tree_key)
        .where(tree_key.auto_id.in_(df2['parent_id']))
        ).all()
        df21 = sqmodel_to_df(df21)
        # print(df21)

        mergeDF = df2.merge(df21, how='outer', left_on='parent_id', right_on='auto_id').merge(df1, how='outer', left_on='auto_id_x', right_on='key').fillna(0)
        mergeDF = [{"title_info": k, "content": [{"title_2":k1, "list": []} if v1['auto_id_1'].all() == 0 else {"title_2":k1, "list": v1[["auto_id_1", "value", "dateCheckSee", "dateCheckToday", "hide_x"]].to_dict('records')} for k1, v1 in v.groupby("mm_name_x")]} for k,v in mergeDF.groupby(["mm_name_y"])]
        
    except: 
        mergeDF = []
    return mergeDF


def getPersonalNameById100(auto_id: int, db: Session = Depends(get_session)):
    try:
        df11 = db.exec(select(
            personal.auto_id, personal.jia_yi_id, personal.jia_yi, personal.name, personal.fen_dian_id, personal.datetime
            )
        .where(personal.auto_id == auto_id)
        ).all()
        df11 = pd.DataFrame(df11, columns =['auto_id', 'jia_yi_id', 'jia_yi', 'name', 'fen_dian_id', 'datetime'])
        # print(df11)
        
        df11 = df11.to_dict("records")
    except: 
        df11 = []
    return df11


# def changeFenDianById100(auto_id: int, db: Session = Depends(get_session)):
#     try:
#         df11 = db.exec(select(personal)
#         .where(personal.auto_id == auto_id)
#         ).all()
#         df11 = sqmodel_to_df(df11)
#         # print(df11)
        
#         df11 = df11.to_dict("records")
#     except: 
#         df11 = []
#     return df11
def changeFenDianById100(auto_id: int, new_fen_dian_id: int, db: Session = Depends(get_session)):
    try:
        # Fetch the record by auto_id
        record = db.query(personal).filter(personal.auto_id == auto_id).first()

        # Update the fen_dian_id
        if record:
            record.fen_dian_id = new_fen_dian_id
            db.commit()

            # Optionally, you can return the updated record
            updated_record = sqmodel_to_df([record])[0].to_dict()
            return updated_record
        else:
            return {"message": "Record not found"}
    except Exception as e:
        # Handle exceptions appropriately
        print(f"Error: {e}")
        # db.rollback()  # Remove or comment out this line
        return {"message": "Error updating record"}


def getPersonalTreeTag100(db: Session = Depends(get_session)):
    
    def makehiearchy(data):
        result = []
        d = { "0": { "children": result } }
        for id, name, input_or_text, au_pa_ty, child_id in zip(data["id"], data["name"], data["input_or_text"], data["au_pa_ty"], data["child_id"]):
            d[id] = { "name": name, "id": id, "input_or_text": input_or_text, "au_pa_ty": au_pa_ty, "child_id": child_id }
            if d[id]['input_or_text'] == 'text':
                d[id] = { "name": name, "id": id, "input_or_text": input_or_text, "disabled": True, "au_pa_ty": au_pa_ty, "child_id": child_id }
            else:
                d[id] = { "name": name, "id": id, "input_or_text": input_or_text, "au_pa_ty": au_pa_ty, "child_id": child_id }

        for id, child_id in zip(data["id"], data["child_id"]):
            parent = d[child_id]
            if "children" not in parent:
                parent["children"] = []
            parent["children"].append(d[id])

        return result

    # Example run
    sql = db.exec(select(tree_key).where(tree_key.head_id == 1)).all()
    records = [i.dict() for i in sql]   
    df = pd.DataFrame.from_records(records)
    df['au_pa_ty'] = df['auto_id'].astype(str) + ',' + df['parent_id'].astype(str) + ',' + df['type'].astype(str) + ',' + df['mm_name'].astype(str)
    print(df)
    data = {
        'id': df['auto_id'].astype(str).to_list(),
        'name': df['mm_name'].to_list(),
        'input_or_text': df['input_or_text'].to_list(),
        'au_pa_ty': df['au_pa_ty'].to_list(),
        'child_id': df['parent_id'].astype(str).to_list()
    }    
    hierarchy = makehiearchy(data)
    
    return hierarchy


def getPersonalStatus100(db: Session = Depends(get_session)):
    
    def makehiearchy(data):
        result = []
        d = { "0": { "children": result } }
        for id, name, input_or_text, child_id in zip(data["id"], data["name"], data["input_or_text"], data["child_id"]):
            d[id] = { "name": name, "id": id, "input_or_text": input_or_text, "child_id": child_id }
            if d[id]['input_or_text'] == 'text':
                d[id] = { "name": name, "id": id, "input_or_text": input_or_text, "disabled": True, "child_id": child_id }
            else:
                d[id] = { "name": name, "id": id, "input_or_text": input_or_text, "child_id": child_id }

        for id, child_id in zip(data["id"], data["child_id"]):
            parent = d[child_id]
            if "children" not in parent:
                parent["children"] = []
            parent["children"].append(d[id])

        return result

    # Example run
    sql = db.exec(select(tree_key).where(tree_key.head_id == 18)).all()
    records = [i.dict() for i in sql]   
    df = pd.DataFrame.from_records(records)
    data = {
        'id': df['auto_id'].astype(str).to_list(),
        'name': df['mm_name'].to_list(),
        'input_or_text': df['input_or_text'].to_list(),
        'child_id': df['parent_id'].astype(str).to_list()
    }    
    hierarchy = makehiearchy(data)
    
    return hierarchy


def getTreekeyParent100(db: Session = Depends(get_session)):
    try:

        df = db.exec(
            select(tree_key)
            .where(tree_key.parent_id == 0)
            .order_by(tree_key.auto_id)
        ).all()
    except: 
        df = []                
    return df


def selectTree100(head_id: int, db: Session = Depends(get_session)):
    
    def makehiearchy(data):
        result = []
        d = { "0": { "children": result } }
        for id, name, input_or_text, auto_fen, child_id in zip(data["id"], data["name"], data["input_or_text"], data["auto_fen"], data["child_id"]):
            d[id] = { "name": name, "id": id, "input_or_text": input_or_text, "auto_fen": auto_fen, "child_id": child_id }

        for id, child_id in zip(data["id"], data["child_id"]):
            parent = d[child_id]
            if "children" not in parent:
                parent["children"] = []
            parent["children"].append(d[id])

        return result

    # Example run
    sql = db.exec(select(tree_key).where(tree_key.head_id == head_id)).all()
    records = [i.dict() for i in sql]   
    df = pd.DataFrame.from_records(records)
    df['auto_fen'] = df['auto_id'].astype(str) + ',' + df['fen_ji'].astype(str) + ',' + df['head_id'].astype(str)
    print(df)
    data = {
        'id': df['auto_id'].astype(str).to_list(),
        'name': df['mm_name'].to_list(),
        'input_or_text': df['input_or_text'].to_list(),
        'auto_fen': df['auto_fen'].to_list(),
        'child_id': df['parent_id'].astype(str).to_list()
    }    
    hierarchy = makehiearchy(data)
    
    return hierarchy


def getTag100(db: Session = Depends(get_session)):
    try:
        df = db.exec(select(tag)).all()
    except: 
        df = []                
    return df


def addTreekey100(hero: tree_key_post, db: Session = Depends(get_session)):

    print(hero)
   
    hero_to_db = tree_key.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    
    return hero_to_db


def delPersonInfo100(auto_id: int, db: Session = Depends(get_session)):
       
    statement = select(personal_info).where(personal_info.auto_id == auto_id)
    results = db.exec(statement)
    hero = results.one()
    print("Hero: ", hero)
    db.delete(hero)
    db.commit()
    print("Deleted hero:", hero)

    return  hero


def getPersonAccess100(checkDriver: str, db: Session = Depends(get_session)):
 
    try:
        df = db.exec(
            select(personal)
            .where(personal.jia_yi_id == checkDriver)
        ).all()
    except:
        df = []
    return df


# def MgetPersonalName100(fen_dian_id: int, db: Session = Depends(get_session)):
    
#     try:
#         df = db.exec(
#             select(personal)
#             .where(personal.fen_dian_id == fen_dian_id)
#             .order_by(personal.datetime.desc())
#         ).all()
#     except:
#         df = []
#     return df

def MgetPersonalName100(fen_dian_id: int, page: int = 1, per_page: int = 5, db: Session = Depends(get_session)):
    try:
        offset = (page - 1) * per_page
        df = db.exec(
            select(personal)
            .where(personal.fen_dian_id == fen_dian_id)
            .order_by(personal.datetime.desc())
            .offset(offset)
            .limit(per_page)
        ).all()
        df = dataframe(df, to_dict=False)
        df = df[['auto_id', 'jia_yi_id', 'datetime', 'fen_dian_id', 'access']]

        # url = 'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        to_dict = df[['jia_yi_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getPerson = requests.get(url=url, json=body_raw).json()
        getPerson = pd.DataFrame(getPerson)
        getPerson = getPerson.rename(columns={'jia_yi_idname': 'jia_yi', 'jia_yi_mm_name': 'name'})

        merge = pd.merge(df, getPerson, on='jia_yi_id')
        merge = merge.to_dict("records")
    except:
        merge = []
    return merge


def MsearchPerson100(fen_dian_id: int, getApiCarDriver: str, db: Session = Depends(get_session)):
    # print(getApiCarDriver)
    # print(type(getApiCarDriver))

    try:
        getApiCarDriver = int(getApiCarDriver)
        print(getApiCarDriver)
        print(type(getApiCarDriver))

        # url = 'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        body_raw = {"data_api": [{"jia_yi_id": getApiCarDriver}]}
        df2 = requests.get(url=url, json=body_raw)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)

        df = db.exec(
            select(personal)
            .where(personal.fen_dian_id == fen_dian_id)
            .where(personal.jia_yi_id == df2['jia_yi_id'].tolist()[0])
        ).all()
        df = dataframe(df, to_dict=False)
        df = df[['auto_id', 'jia_yi_id', 'datetime', 'fen_dian_id', 'access']]

        merge = pd.merge(df2, df, on='jia_yi_id')
        merge = merge.rename(columns={'jia_yi_idname': 'jia_yi', 'jia_yi_mm_name': 'name'})
        merge = merge.to_dict('records')
    except:
        print('The provided value is not an integer')
        print(getApiCarDriver)
        print(type(getApiCarDriver))

        # url = f'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={getApiCarDriver}'
        url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={getApiCarDriver}'
        df2 = requests.get(url=url)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)

        df = db.exec(
            select(personal)
            .where(personal.fen_dian_id == fen_dian_id)
            .where(personal.jia_yi_id == df2['jia_yi_id'].tolist()[0])
        ).all()
        df = dataframe(df, to_dict=False)
        df = df[['auto_id', 'jia_yi_id', 'datetime', 'fen_dian_id', 'access']]

        merge = pd.merge(df2, df, on='jia_yi_id')
        merge = merge.rename(columns={'jia_yi_idname': 'jia_yi', 'jia_yi_mm_name': 'name'})
        merge = merge.to_dict('records')

    return merge


def MgetActive100(fen_dian_id: int, db: Session = Depends(get_session)):
    
    try:
        
        import datetime as DT
        today = DT.date.today()
        week_ago = today - DT.timedelta(days=1)
        heroesPersonal_info = db.exec(select(personal_info).where(personal_info.datetime > week_ago).where(personal_info.key == 47).order_by(personal_info.datetime.desc())).all()
        records = [i.dict() for i in heroesPersonal_info]   
        df2 = pd.DataFrame.from_records(records)
        df2['count'] = df2.groupby('jia_yi_id')['jia_yi_id'].transform('count')
        df2 = df2.groupby('jia_yi_id', as_index=False).nth(0)
        df21 = df2['jia_yi_id'].values.tolist()
        print(df21)

        heroesPersonal = db.exec(select(personal).where(personal.jia_yi_id.in_(df21)).where(personal.fen_dian_id == fen_dian_id).order_by(personal.datetime.desc())).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records)

        mergeDF = df.merge(df2, left_on='jia_yi_id', right_on='jia_yi_id')
        mergeDF = mergeDF.sort_values(by='datetime_y', ascending=False)
        mergeDF = mergeDF.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def MaddPersonalName100(hero: personal_post, db: Session = Depends(get_session)):
    
    # hero.jia_yi = 'ag125'

    # url = f'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={hero.jia_yi}'
    url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={hero.jia_yi}'
    df = requests.get(url=url).json()
    # print(df)

    statement = select(personal).where(personal.jia_yi_id == df[0]['jia_yi_id'])
    results = db.exec(statement).first()
    print(results)

    if (results == None):
        print("empty")
        print(df[0]['jia_yi_mm_name'])
        hero_to_db = personal.from_orm(hero)
        hero_to_db.jia_yi_id = df[0]['jia_yi_id']
        hero_to_db.name = df[0]['jia_yi_mm_name']
        hero_to_db.datetime = str(datetime.now())
        hero_to_db.fen_dian_id = hero.fen_dian_id
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        # print(hero_to_db.auto_id)

        insertName = personal_info(jia_yi=hero_to_db.auto_id, type=19, key=3, value=df[0]['jia_yi_mm_name'], iid=2, jia_yi_id=df[0]['jia_yi_id'], datetime=str(datetime.now()))
        db.add(insertName)
        db.commit()
        db.refresh(insertName)

        res = 'empty'
        # res = {'status': 'empty', 'value': results}
    else:
        print("haveeeeeeeeeeee")
        res = 'haveThisName'
        # res = {'status': 'haveThisName', 'value': results}
    
    return res


def MaddPersonData100(hero: personal_info_post, db: Session = Depends(get_session)):
    
    hero_to_db = personal_info.from_orm(hero)
    hero_to_db.type = 19
    hero_to_db.key = 47
    hero_to_db.iid = 0
    hero_to_db.datetime = str(datetime.now())
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)

    return hero_to_db


def getMPersonData100(auto_id: int, db: Session = Depends(get_session)):
    
    try:
        
        import datetime as DT
        today = DT.date.today()
        week_ago = today - DT.timedelta(days=1)
        heroesPersonal_info = db.exec(select(personal_info).where(personal_info.datetime > week_ago).where(personal_info.jia_yi == auto_id).where(personal_info.key == 47).order_by(personal_info.datetime.desc())).all()
        records = [i.dict() for i in heroesPersonal_info]   
        df2 = pd.DataFrame.from_records(records)
        mergeDF = df2.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def getAllEmployee100(db: Session = Depends(get_session)):
    
    try:
        heroesPersonal_info = db.exec(select(personal)).all()
        records = [i.dict() for i in heroesPersonal_info]   
        df2 = pd.DataFrame.from_records(records)
        df21 = df2[['jia_yi_id']]
        
        # url = f'http://************:8200/mongodb_data_api/api/v1/work_list/work_list_personal'
        url = f'{mongodb_data_api}/api/v1/work_list/work_list_personal'
        df = requests.get(url=url).json()
        # df = df[0:5]
        df = pd.DataFrame(df)

        mergeDF = df.merge(df21, how="left", left_on='乙方', right_on='jia_yi_id').fillna(0)
        mergeDF = mergeDF.loc[mergeDF['jia_yi_id'] == 0]
        mergeDF['jia_yi_id'] = mergeDF['乙方']
        
        # url = 'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = f'{mongodb_data_api}/api/v2/search/jia_yi_name_list_id'
        to_dict = mergeDF.to_dict('records')
        body_raw = {"data_api": to_dict}
        mergeDF = requests.get(url=url, json=body_raw).json()
        mergeDF = pd.DataFrame(mergeDF)
        mergeDF = mergeDF[~mergeDF['jia_yi_idname'].isin(['AL01', 'AL02', 'AM144', 'AG125', 'AG165'])]

        mergeDF = mergeDF.to_dict("records")
    except:
        mergeDF = []
    return mergeDF

def getDayInsert100(db: Session = Depends(get_session)):
    
    try:
        import datetime as DT
        today = DT.date.today()
        week_ago = today - DT.timedelta(days=7)
        print(week_ago)

        # heroesPersonal_info = db.exec(select(personal_info).where(personal_info.datetime > week_ago).distinct(personal_info.jia_yi_id).order_by(personal_info.jia_yi_id, personal_info.datetime.desc())).all()
        heroesPersonal_info = db.exec(select(personal_info).where(personal_info.datetime > week_ago).order_by(personal_info.datetime.desc())).all()
        records = [i.dict() for i in heroesPersonal_info]   
        df = pd.DataFrame.from_records(records)

        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values(by='datetime', ascending=False).drop_duplicates(subset='jia_yi_id')

        mergeDF = df.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def getDriverList100(db: Session = Depends(get_session), db2: Session = Depends(get_session2)):

    try:
        engine = db.get_bind()
        engine2 = db2.get_bind()

        with engine.connect() as con:
            df = pd.read_sql(text("""
                            SELECT *
                            FROM personal_info
                            WHERE key = 49 AND DATE(datetime) >= '2023-05-24'
                        """), con)

        with engine2.connect() as con:
            df2 = pd.read_sql(text("""
                            SELECT DISTINCT ON (vi.jia_yi_id_vehicle) vi.jia_yi_id_vehicle, vi.jia_yi_id_driver, vi.datetime
                            FROM vehicle v
                            JOIN vehicle_insert vi ON v.jia_yi_id = vi.jia_yi_id_vehicle
                            WHERE v.type = 'car'
                            ORDER BY vi.jia_yi_id_vehicle, vi.datetime DESC
                        """), con)
            df2 = df2.fillna(0)
            df2 = df2.sort_values(by='datetime', ascending=False)


        df3 = df2[~df2['jia_yi_id_driver'].isin(df['jia_yi_id'])]

        jia_yi_id_vehicle_unique = df3['jia_yi_id_vehicle'].unique()
        jia_yi_id_driver_unique = df3['jia_yi_id_driver'].unique()
        join_two_column = pd.DataFrame({'jia_yi_id': [*jia_yi_id_vehicle_unique, *jia_yi_id_driver_unique]})
        join_two_column = join_two_column.drop_duplicates(subset=['jia_yi_id'])

        # url = 'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        to_dict = join_two_column[['jia_yi_id']].to_dict('records')
        body_raw = {"data_api": to_dict}
        getApiValue = requests.get(url=url, json=body_raw).json() 
        getApiValue = pd.DataFrame(getApiValue)
        # display(getApiValue)

        merge_jia_yi_id_vehicle = pd.merge(df3, getApiValue, left_on='jia_yi_id_vehicle', right_on='jia_yi_id')
        merge_jia_yi_id_driver = pd.merge(merge_jia_yi_id_vehicle, getApiValue, left_on='jia_yi_id_driver', right_on='jia_yi_id')  
        merge_jia_yi_id_driver = merge_jia_yi_id_driver.sort_values(by='datetime', ascending=False)
        mergeDF = merge_jia_yi_id_driver.to_dict("records")
    except:
        mergeDF = []
  
#     return mergeDF
# def getDriverList100(db: Session = Depends(get_session), db2: Session = Depends(get_session2)):
    
#     engine = db.get_bind()
#     engine2 = db2.get_bind()

#     with engine.connect() as con:
#         df = pd.read_sql(text("""
#                         SELECT *
#                         FROM personal_info
#                         WHERE key = 49 AND DATE(datetime) >= '2023-05-24'
#                     """), con)

#     with engine2.connect() as con:
#         df2 = pd.read_sql(text("""
#                         SELECT DISTINCT ON (vi.jia_yi_id_vehicle) vi.jia_yi_id_vehicle, vi.jia_yi_id_driver, vi.datetime
#                         FROM vehicle v
#                         JOIN vehicle_insert vi ON v.jia_yi_id = vi.jia_yi_id_vehicle
#                         WHERE v.type IN ('car', 'motorcycle')
#                         ORDER BY vi.jia_yi_id_vehicle, vi.datetime DESC
#                     """), con)
#         df2 = df2.fillna(0)
#         df2 = df2.sort_values(by='datetime', ascending=False)


#     df3 = df2[~df2['jia_yi_id_driver'].isin(df['jia_yi_id'])]

#     # jia_yi_id_vehicle_unique = df3['jia_yi_id_vehicle'].unique()
#     # jia_yi_id_driver_unique = df3['jia_yi_id_driver'].unique()
#     # join_two_column = pd.DataFrame({'jia_yi_id': [*jia_yi_id_vehicle_unique, *jia_yi_id_driver_unique]})
#     # join_two_column = join_two_column.drop_duplicates(subset=['jia_yi_id'])

#     # # url = 'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#     # url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
#     # to_dict = join_two_column[['jia_yi_id']].to_dict('records')
#     # body_raw = {"data_api": to_dict}
#     # getApiValue = requests.get(url=url, json=body_raw).json() 
#     # getApiValue = pd.DataFrame(getApiValue)
#     # # display(getApiValue)

#     # merge_jia_yi_id_vehicle = pd.merge(df3, getApiValue, left_on='jia_yi_id_vehicle', right_on='jia_yi_id')
#     # merge_jia_yi_id_driver = pd.merge(merge_jia_yi_id_vehicle, getApiValue, left_on='jia_yi_id_driver', right_on='jia_yi_id')  
#     # merge_jia_yi_id_driver = merge_jia_yi_id_driver.sort_values(by='datetime', ascending=False)

#     mergeDF = df3.to_dict("records")

  
#     return mergeDF



def getUpdateEachOne100(parent_id: int, db: Session = Depends(get_session)):

    # parent_id = 2

    heroesPersonal = db.exec(select(personal.auto_id, personal.jia_yi_id, personal.datetime, personal.fen_dian_id)).all()
    df1 = pd.DataFrame.from_records(heroesPersonal, columns=['auto_id', 'jia_yi_id', 'datetime', 'fen_dian_id'])
    df11 = df1[['jia_yi_id']]
    # url = 'http://************:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
    to_dict = df11.to_dict('records')
    body_raw = {"data_api": to_dict}
    mergeDF = requests.get(url=url, json=body_raw).json()
    mergeOne = pd.DataFrame(mergeDF)
    mergeAll = pd.merge(mergeOne, df1, on='jia_yi_id').fillna(0)


    heroeStree_key = db.exec(select(tree_key).where(tree_key.parent_id == parent_id)).all()
    records = [i.dict() for i in heroeStree_key]   
    df3 = pd.DataFrame.from_records(records)
    

    lastDate = (
        select(
            personal_info.jia_yi, personal_info.key, func.max(personal_info.datetime).label("max_datetime")
        )
        .group_by(personal_info.jia_yi, personal_info.key)
        .subquery()
    )
    query = (
        select(personal_info)
        .join(lastDate, and_(
            personal_info.jia_yi == lastDate.c.jia_yi, personal_info.key == lastDate.c.key, personal_info.datetime == lastDate.c.max_datetime
        ))
        .where(personal_info.key.in_(df3['auto_id'].tolist()))
        .order_by(personal_info.jia_yi, personal_info.key, personal_info.datetime)
    )
    heroesPersonal_info = db.exec(query).all()
    records = [i.dict() for i in heroesPersonal_info]   
    df2 = pd.DataFrame.from_records(records)


    mergeDF = df2.merge(df3, left_on='key', right_on='auto_id')


    mergeDF['datetime'] = pd.to_datetime(mergeDF['datetime'])
    current_date = datetime.now()
    mergeDF['days_passed'] = (current_date - mergeDF['datetime']).dt.days
    result_df = mergeDF.pivot(index='jia_yi_id', columns='mm_name', values='days_passed')
    result_df.reset_index(inplace=True)
    # result_df = result_df[50:70]


    merge = mergeAll.merge(result_df, left_on='jia_yi_id', right_on='jia_yi_id')


    # url = 'http://************:8200/mongodb_data_api/api/v1/work_list/work_list_personal_latestDate'
    url = mongodb_data_api + '/api/v1/work_list/work_list_personal_latestDate'
    to_dict = df11.to_dict('records')
    body_raw = {"data_api": to_dict}
    last_work = requests.get(url=url, json=body_raw).json()
    last_work = pd.DataFrame(last_work)
    last_work = last_work.rename(columns={'乙方': 'jia_yi_id'})
    last_work["日期"] = pd.to_datetime(last_work["日期"]).dt.tz_localize(None)
    today = datetime.now()
    today = today.replace(tzinfo=None)
    last_work["card_scan"] = (today - last_work["日期"]).dt.days
    mergeLastWork = pd.merge(merge, last_work, how="left", on='jia_yi_id').fillna(0)
    mergeLastWork = mergeLastWork.rename(columns={"jia_yi_idname": "jia_yi", "jia_yi_mm_name": "name"})
    
    mergeDF = mergeLastWork.to_dict("records")

    return mergeDF