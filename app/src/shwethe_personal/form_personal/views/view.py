# from typing import Dict, Optional
from pydantic.types import Optional, List, Dict
from builtins import int
from src.shwethe_personal.form_personal.models.models import personal_info_post, personal_post, tree_key_post
from src.shwethe_personal.form_personal.crud.crud import addPersonalName100, changeFenDianById100, getUpdateEachOne100, addPersonalInfo100, MsearchPerson100, getPersonalInfo100, getDriverList100, getPersonalName100, getPersonalTreeTag100, vvvvvv100, getPersonalNameById100, getPersonalStatus100, checkDriver100, getTreekeyParent100, selectTree100, getTag100, addTreekey100, delPersonInfo100, getPersonalNameShow100, getPersonalNameColumn100, getTreeParent100, getPersonAccess100, MgetPersonalName100, MgetActive100, MaddPersonalName100, MaddPersonData100, getMPersonData100, getAllEmployee100, getDayInsert100
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_personal.database import get_session, create_db_and_tables
from src.config.shwethe_car_active.database import get_session as get_session2



router = APIRouter()


# @router.post("/addPersonal")
# def addPersonal(hero : personal_info_post , db: Session = Depends(get_session)):
#     return addPersonal100(hero=hero,db=db)


# @router.patch("/editPersonal/{jia_yi_id}")
# def editPersonal(jia_yi_id: int, hero : personal_info_post , db: Session = Depends(get_session)):
#     return editPersonal100(jia_yi_id=jia_yi_id, hero=hero,db=db)


# @router.post("/addNrc")
# def addNrc(hero : nrc_post , db: Session = Depends(get_session)):
#     return addNrc100(hero=hero,db=db)


# @router.post("/addDepartmentList")
# def addDepartmentList(hero : department_list_post , db: Session = Depends(get_session)):
#     return addDepartmentList100(hero=hero,db=db)


# @router.get("/getDepartmentParent")
# def getDepartmentParent(db: Session = Depends(get_session)):
#     return getDepartmentParent100(db=db) 


# @router.get("/getDepartmentTree/{parent_head_id}")
# def getDepartmentTree(parent_head_id: int, db: Session = Depends(get_session)):
#     return getDepartmentTree100(parent_head_id=parent_head_id, db=db)


# @router.get("/vvvvvv")
# def vvvvvv(db: Session = Depends(get_session)):
#     return vvvvvv100(db=db)


# @router.get("/aaa") 
# def aaa(db: Session = Depends(get_session)):
#     return aaa100(db=db) 


@router.get("/vvvvvv")
def vvvvvv(db: Session = Depends(get_session)):
    return vvvvvv100(db=db)

@router.get("/checkDriver/{checkDriver}")
def checkDriver(checkDriver : str , db: Session = Depends(get_session)):
    return checkDriver100(checkDriver=checkDriver,db=db)

@router.get("/getTreeParent")
def getTreeParent(db: Session = Depends(get_session)):
    return getTreeParent100(db=db)

@router.post("/addPersonalName")
def addPersonalName(hero : personal_post , db: Session = Depends(get_session)):
    return addPersonalName100(hero=hero,db=db)

@router.get("/getPersonalNameColumn/{parent_id}")
def getPersonalNameColumn(parent_id : int, db: Session = Depends(get_session)):
    return getPersonalNameColumn100(parent_id=parent_id, db=db)

@router.get("/getPersonalName/{parent_id}")
def getPersonalName(parent_id : int, db: Session = Depends(get_session)):
    return getPersonalName100(parent_id=parent_id, db=db)

@router.get("/getPersonalNameShow")
def getPersonalNameShow(db: Session = Depends(get_session)):
    return getPersonalNameShow100(db=db)

@router.post("/addPersonalInfo")
def addPersonalInfo(data: List[dict], db: Session = Depends(get_session)):
    return addPersonalInfo100(data=data,db=db)

# @router.post("/addPersonalInfo")
# def addPersonalInfo(hero : personal_info_post , db: Session = Depends(get_session)):
#     return addPersonalInfo100(hero=hero,db=db)

@router.get("/getPersonalNameById/{auto_id}")
def getPersonalNameById(auto_id: int, db: Session = Depends(get_session)):
    return getPersonalNameById100(auto_id=auto_id, db=db) 

# @router.put("/changeFenDianById/{auto_id}")
# def changeFenDianById(auto_id: int, db: Session = Depends(get_session)):
#     return changeFenDianById100(auto_id=auto_id, db=db) 
@router.put("/changeFenDianById")
def changeFenDianById(auto_id: int, new_fen_dian_id: int, db: Session = Depends(get_session)):
    return changeFenDianById100(auto_id=auto_id, new_fen_dian_id=new_fen_dian_id, db=db)

@router.get("/getPersonalInfo/{auto_id}")
def getPersonalInfo(auto_id: int, db: Session = Depends(get_session)):
    return getPersonalInfo100(auto_id=auto_id, db=db) 

@router.get("/getPersonalTreeTag")
def getPersonalTreeTag(db: Session = Depends(get_session)):
    return getPersonalTreeTag100(db=db)

@router.get("/getPersonalStatus")
def getPersonalStatus(db: Session = Depends(get_session)):
    return getPersonalStatus100(db=db)

@router.get("/getTreekeyParent")
def getTreekeyParent(db: Session = Depends(get_session)):
    return getTreekeyParent100(db=db) 

@router.get("/selectTree/{head_id}")
def selectTree(head_id: int, db: Session = Depends(get_session)):
    return selectTree100(head_id=head_id, db=db)

@router.get("/getTag")
def getTag(db: Session = Depends(get_session)):
    return getTag100(db=db)

@router.post("/addTreekey")
def addTreekey(hero : tree_key_post , db: Session = Depends(get_session)):
    return addTreekey100(hero=hero,db=db)

@router.delete("/delPersonInfo/{auto_id}")
def delPersonInfo(auto_id: int, db: Session = Depends(get_session)):
    return delPersonInfo100(auto_id=auto_id, db=db)

@router.get("/getPersonAccess/{checkDriver}")
def getPersonAccess(checkDriver: int, db: Session = Depends(get_session)):
    return getPersonAccess100(checkDriver=checkDriver, db=db)

# @router.get("/MgetPersonalName/{fen_dian_id}")
# def MgetPersonalName(fen_dian_id : int, db: Session = Depends(get_session)):
#     return MgetPersonalName100(fen_dian_id=fen_dian_id, db=db)

@router.get("/MgetPersonalName/{fen_dian_id}")
def MgetPersonalName(fen_dian_id: int, page: int = 1, per_page: int = 2, db: Session = Depends(get_session)):
    return MgetPersonalName100(fen_dian_id=fen_dian_id, page=page, per_page=per_page, db=db)

@router.get("/MsearchPerson")
def MsearchPerson(fen_dian_id: int, getApiCarDriver: str, db: Session = Depends(get_session)):
    return MsearchPerson100(fen_dian_id=fen_dian_id, getApiCarDriver=getApiCarDriver,db=db)
    
@router.get("/MgetActive/{fen_dian_id}")
def MgetActive(fen_dian_id : int, db: Session = Depends(get_session)):
    return MgetActive100(fen_dian_id=fen_dian_id, db=db)

@router.post("/MaddPersonalName")
def MaddPersonalName(hero : personal_post , db: Session = Depends(get_session)):
    return MaddPersonalName100(hero=hero,db=db)

@router.post("/MaddPersonData")
def MaddPersonData(hero : personal_info_post , db: Session = Depends(get_session)):
    return MaddPersonData100(hero=hero,db=db)

@router.get("/getMPersonData/{auto_id}")
def getMPersonData(auto_id: int, db: Session = Depends(get_session)):
    return getMPersonData100(auto_id=auto_id, db=db) 
    
@router.get("/getAllEmployee")
def getAllEmployee(db: Session = Depends(get_session)):
    return getAllEmployee100(db=db) 

@router.get("/getDayInsert")
def getDayInsert(db: Session = Depends(get_session)):
    return getDayInsert100(db=db) 

@router.get("/getDriverList")
def getDriverList(db: Session = Depends(get_session), db2: Session = Depends(get_session2)):
    return getDriverList100(db=db, db2=db2)

@router.get("/getUpdateEachOne/{parent_id}")
def getUpdateEachOne(parent_id : int, db: Session = Depends(get_session)):
    return getUpdateEachOne100(parent_id=parent_id, db=db)