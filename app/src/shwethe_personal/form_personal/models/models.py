from typing import Dict
from xmlrpc.client import DateTime

from click import option
from pydantic.types import Optional, List
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel, Column, JSON
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id





class tree_key_base(SQLModel):
    mm_name: Optional[str]
    parent_id: Optional[int] 
    head_id: Optional[int]
    fen_ji: Optional[int]
    type: Optional[int]
    input_or_text: Optional[str]
    hide: Optional[bool]
    
class tree_key(tree_key_base, table=True):
    __tablename__ = "tree_key"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    mm_name: Optional[str]
    parent_id: Optional[int]
    head_id: Optional[int]
    fen_ji: Optional[int]
    type: Optional[int]
    input_or_text: Optional[str]
    hide: Optional[bool]

class tree_key_read(BaseModel):
    auto_id: Optional[int]
    mm_name: Optional[str]
    parent_id: Optional[int] 
    head_id: Optional[int]
    fen_ji: Optional[int]
    type: Optional[int]
    input_or_text: Optional[str]
    hide: Optional[bool]

class tree_key_post(BaseModel):
    mm_name: Optional[str]
    parent_id: Optional[int] = 0
    head_id: Optional[int]
    fen_ji: Optional[int]
    type: Optional[int]
    input_or_text: Optional[str]
    hide: Optional[bool]

# --------------------------------

class personal_base(SQLModel):
    jia_yi_id: Optional[int]
    jia_yi: Optional[str]
    name: Optional[str]
    datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    access: Optional[bool]
    
class personal(personal_base, table=True):
    __tablename__ = "personal"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    jia_yi_id: Optional[int]
    jia_yi: Optional[str]
    name: Optional[str]
    datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    access: Optional[bool]

class personal_read(BaseModel):
    auto_id: Optional[int]
    jia_yi_id: Optional[int]
    jia_yi: Optional[str]
    name: Optional[str]
    datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    access: Optional[bool]

class personal_post(BaseModel):
    jia_yi_id: Optional[int]
    jia_yi: Optional[str]
    name: Optional[str]
    datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    access: Optional[bool] = False

# --------------------------------

class personal_info_base(SQLModel):
    jia_yi: Optional[int]
    jia_yi_id: Optional[int]
    type: Optional[int]
    key: Optional[int]
    value: Optional[str]
    iid: Optional[int]
    datetime: Optional[datetime]
    
class personal_info(personal_info_base, table=True):
    __tablename__ = "personal_info"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    jia_yi: Optional[int]
    jia_yi_id: Optional[int]
    type: Optional[int]
    key: Optional[int]
    value: Optional[str]
    iid: Optional[int]
    datetime: Optional[datetime]

class personal_info_read(BaseModel):
    auto_id: Optional[int]
    jia_yi: Optional[int]
    jia_yi_id: Optional[int]
    type: Optional[int]
    key: Optional[int]
    value: Optional[str]
    iid: Optional[int]
    datetime: Optional[datetime]

class personal_info_post(BaseModel):
    jia_yi: Optional[int]
    jia_yi_id: Optional[int]
    type: Optional[int]
    key: Optional[int]
    value: Optional[str]
    iid: Optional[int]
    datetime: Optional[datetime]

# --------------------------------

class tag_base(SQLModel):
    jia_yi: Optional[str]
    
class tag(tag_base, table=True):
    __tablename__ = "tag"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    jia_yi: Optional[str]

class tag_read(BaseModel):
    auto_id: Optional[int]
    jia_yi: Optional[str]

class tag_post(BaseModel):
    jia_yi: Optional[str]