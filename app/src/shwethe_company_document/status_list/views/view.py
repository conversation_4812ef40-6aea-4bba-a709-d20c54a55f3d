from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_in.database import get_session
from src.shwethe_in.status_list.crud.crud import (
    put_status_list,
    read_status_list,
    post_list_car_in,
    get_list_car_in,
)

from src.shwethe_in.status_list.models.models import order_head_in,order_body_in_post

router = APIRouter()


@router.get("", response_model=List[order_head_in])
def create_a_hero(db: Session = Depends(get_session)):

    return read_status_list(db=db)


# @router.get("/list_car_in_parner2", response_model=List[order_head_in])
# def create_a_hero2(db : Session = Depends(get_session)):

#     # return put_status_list(hero=hero,db=db)
#     return read_status_list(db=db)


@router.post("/list_car_in_parner", response_model=order_head_in)
def create_a_hero(hero: order_body_in_post ,  db: Session = Depends(get_session)):

    return post_list_car_in(hero=hero,db=db)


@router.get("/list_car_in_parner", response_model=List[order_head_in])
def list_car_in_parner(jin_huo_bian:int ,db: Session = Depends(get_session)):

    return get_list_car_in(jin_huo_bian = jin_huo_bian , db=db)

# @router.post("", response_model=pre_order_product_model_read)
# def create_a_hero(hero: create_pre_order_product_model, db: Session = Depends(get_session)):

#     return create_pre_order_product(hero=hero, db=db)


# @router.put("", response_model=pre_order_product_model_read)
# def create_a_hero(hero: create_put_order_product_model, db: Session = Depends(get_session)):
#     return create_put_order_sub_product(hero=hero, db=db)


# @router.post("/sub_pre_order", response_model=pre_order_product_sub_model_read)
# def create_a_hero(hero: create_pre_order_product_sub_model, db: Session = Depends(get_session)):
    
#     return create_pre_order_sub_product(hero=hero, db=db)


# @router.get("/sub_pre_order/{orderId}", response_model=List[product_sub_model])
# def create_a_hero(orderId: str , db: Session = Depends(get_session)):
    
#     return get_pre_order_sub_product(orderId=orderId, db=db)



# @router.get("/pre_order_list", response_model=List[pre_order_product_get_base])
# def pre_order_list(
#             db: Session = Depends(get_session),
#         ):
    
#     return get_pre_order_list(db=db)








# @router.post("", response_model=HeroRead)
# def create_a_hero(hero: HeroCreate, db: Session = Depends(get_session)):
#     return create_hero(hero=hero, db=db)




# @router.get("", response_model=List[HeroRead])
# def get_heroes(
#     offset: int = 0,
#     limit: int = Query(default=100, lte=100),
#     db: Session = Depends(get_session),
# ):
#     return read_heroes(offset=offset, limit=limit, db=db)


# @router.get("/{hero_id}", response_model=HeroRead)
# def get_a_hero(hero_id: int, db: Session = Depends(get_session)):
#     return read_hero(hero_id=hero_id, db=db)


# @router.patch("/{hero_id}", response_model=HeroRead)
# def update_a_hero(hero_id: int, hero: HeroUpdate, db: Session = Depends(get_session)):
#     return update_hero(hero_id=hero_id, hero=hero, db=db)


# @router.delete("/{hero_id}")
# def delete_a_hero(hero_id: int, db: Session = Depends(get_session)):
#     return delete_hero(hero_id=hero_id, db=db)


