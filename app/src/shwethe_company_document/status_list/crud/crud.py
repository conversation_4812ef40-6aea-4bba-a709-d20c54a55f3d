from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel
from typing import List, Optional

from datetime import datetime
from src.shwethe_in.database import get_session
from helper import generate_datetime_id
from src.shwethe_in.status_list.models.models import order_head_in,order_body_in_post,order_body_in
import json
import pandas as pd

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df



def read_status_list(db: Session = Depends(get_session)):
    heroes = db.exec(select(order_head_in)).all()
    return heroes





def post_list_car_in(hero:order_body_in_post,db: Session = Depends(get_session)):

    # print(hero)

    statement = select(order_body_in).where(order_body_in.jin_huo_bian == hero.jin_huo_bian,
    order_body_in.jia_yi_fang == hero.jia_yi_fang,order_body_in.fen_dian_id == hero.fen_dian_id)
    results = db.exec(statement).all()

    if not (results):
        hero_to_db = order_body_in.from_orm(hero)
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        print("aaa")
        return hero_to_db

    return []






def get_list_car_in(jin_huo_bian:int,db: Session = Depends(get_session)):
    
    # print(hero)

    statement = select(order_body_in).where(order_body_in.jin_huo_bian == jin_huo_bian)
    results = db.exec(statement).all()

    if (results):
        return  results
    return []   

    # hero.datetime = str(datetime.now())
    # hero.orderProductSubId = generate_datetime_id()

    


# def create_hero(hero: HeroCreate, db: Session = Depends(get_session)):
#     hero_to_db = Hero.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
#     return hero_to_db





# def read_hero(hero_id: int, db: Session = Depends(get_session)):
#     hero = db.get(Hero, hero_id)
#     if not hero:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {hero_id}",
#         )
#     return hero


# def update_hero(hero_id: int, hero: HeroUpdate, db: Session = Depends(get_session)):
#     hero_to_update = db.get(Hero, hero_id)
#     if not hero_to_update:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {hero_id}",
#         )

#     team_data = hero.dict(exclude_unset=True)
#     for key, value in team_data.items():
#         setattr(hero_to_update, key, value)

#     db.add(hero_to_update)
#     db.commit()
#     db.refresh(hero_to_update)
#     return hero_to_update


# def delete_hero(hero_id: int, db: Session = Depends(get_session)):
#     hero = db.get(Hero, hero_id)
#     if not hero:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {hero_id}",
#         )

#     db.delete(hero)
#     db.commit()
#     return {"ok": True}





# def create_pre_order_product(hero: pre_order_product_model_read, db: Session = Depends(get_session)):

#     hero.datetime = str(datetime.now())
#     hero.orderId = generate_datetime_id()
#     hero_to_db = pre_order_product.from_orm(hero)
    
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)

#     return hero_to_db


# def create_put_order_sub_product(hero: create_put_order_product_model, db: Session = Depends(get_session)):
#     # print(hero)
#     # hero.datetime = str(datetime.now())
#     # hero.orderProductSubId = generate_datetime_id()

#     statement = select(pre_order_product).where(pre_order_product.orderId == hero.orderId)
#     results = db.exec(statement).one()

#     print("results:", results)
#     print("hero:", hero)


#     results.lei = hero.lei
#     results.keBian = hero.keBian
#     results.statusCodeId = hero.statusCodeId
#     results.jiaYiId = hero.jiaYiId
#     results.dataSubName = hero.dataSubName
#     results.dataSub = hero.dataSub

#     db.add(results)
#     db.commit()
#     db.refresh(results)
    
#     return results


# def create_pre_order_sub_product(hero: pre_order_product_sub_model_read, db: Session = Depends(get_session)):
#     # print(hero)
#     hero.datetime = str(datetime.now())
#     hero.orderProductSubId = generate_datetime_id()
#     hero_to_db = pre_order_product_sub.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
    
#     return hero_to_db




# def get_pre_order_sub_product(orderId:str, db: Session = Depends(get_session)):
#     print(orderId)
#     # hero_to_db = pre_order_product_sub.from_orm(hero)

#     # db.add(hero_to_db)
#     # db.commit()
#     # db.refresh(hero_to_db)

#     # def read_hero(hero_id: int, db: Session = Depends(get_session)):
#     #     hero = db.get(Hero, hero_id)
#     # if not hero:
#     #     raise HTTPException(
#     #         status_code=status.HTTP_404_NOT_FOUND,
#     #         detail=f"Hero not found with id: {hero_id}",
#     #     )
#     # return hero
#     # heroes = db.exec(select(Hero).offset(offset).limit(limit)).all()
#     statement = select(pre_order_product_sub).where(pre_order_product_sub.orderId == orderId)
#     results = db.exec(statement).all()

#     for hero in results:
#         print(hero)
#     print(results)
#     print("results")
#     # hero = db.get(pre_order_product_sub_base, orderId)
#     if not results:
#         # raise HTTPException(
#         #     status_code=status.HTTP_404_NOT_FOUND,
#         #     detail=f"Hero not found with id: {orderId}",
#         # )
#         return []
        
#     return results
    


# def create_get_order_sub_product(orderId:str, hero: pre_order_product_sub_model_read, db: Session = Depends(get_session)):
#     # print(hero)
#     hero_to_db = pre_order_product_sub.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
    
#     return hero_to_db



# def getDataSubProduct(db: Session = Depends(get_session)):

#     heroes = db.exec(select(pre_order_product).offset(offset).limit(limit)).all()
    
#     return heroes


# def get_pre_order_list(db: Session = Depends(get_session)):
   
#     heroes = db.exec(select(pre_order_product)).all()

#     df = sqmodel_to_df(heroes)

#     df = df.sort_values(['datetime'], ascending=[False])

#     # df10001 = df.to_dict('records')

#     # df10002 = pd.json_normalize(df10001)

#     # df10003 = df10002.rename(columns={'dataSubName.jiaYiIdname': 'jiaYiIdname','dataSubName.jiaYiMmName': 'jiaYiMmName'
#     # ,'dataSubName.keBianIdname': 'keBianIdname','dataSubName.keBianMmName': 'keBianMmName'})
    

#     df10004 = df.to_json(orient='records')

    
#     return json.loads(df10004)