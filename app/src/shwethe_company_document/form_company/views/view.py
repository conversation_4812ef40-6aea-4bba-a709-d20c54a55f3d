from builtins import int
from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_company_document.database import get_session,create_db_and_tables
from src.shwethe_company_document.form_company.crud.crud import (
    # addFileInfo100,
    # addFileInfoChild100,
    # addFilePath100,
    addFileInfoChild100,
    addFilePath100,
    addParent100,
    getAllImage100,
    getChildImage100,
    getImageWithAllTag100,
    selectFileInfo100,
    getFileInfoParent100,
    # getFileJoin100,
    addPerson100,
    delPerson100,
    getData100,
    getIdTable100,
    getPhoneCompany100,
    getTableData100,
    post_list_car_in,
    post_list_car_in_2,
    post_list_car_in_3,
    post_list_car_in_4,
    read_status_list,
    updatePerson100,
    # sxsxsxsx
)

from src.shwethe_company_document.form_company.models.models import tb_company_address_post, tb_company_address_read, tb_company_department_base, tb_company_department_post, tb_company_file_base, tb_company_file_info_base, tb_company_file_info_post, tb_company_file_post, tb_company_name_post,tb_company_name_read, tb_company_people_info_base, tb_company_people_info_post

router = APIRouter()


@router.post("/tb_company_name_post", response_model=tb_company_name_read)
def create_a_hero(hero : tb_company_name_post , db: Session = Depends(get_session)):
    return post_list_car_in(hero=hero,db=db)


@router.post("/tb_company_address_post", response_model=tb_company_address_read)
def create_a_hero_2(hero : tb_company_address_post , db: Session = Depends(get_session)):
    return post_list_car_in_2(hero=hero,db=db)


@router.post("/tb_company_department_post", response_model=tb_company_department_base)
def create_a_hero_3(hero : tb_company_department_post , db: Session = Depends(get_session)):
    return post_list_car_in_3(hero=hero,db=db)

@router.get("/getDepartmentList")
def getData(db: Session = Depends(get_session)):
    return getData100(db=db) 


@router.post("/tb_company_people_info_post", response_model=tb_company_people_info_base)
def create_a_hero_4(hero : tb_company_people_info_post , db: Session = Depends(get_session)):
    return post_list_car_in_4(hero=hero,db=db)


@router.get("/getTableData")
def getTableData(db: Session = Depends(get_session)):
    return getTableData100(db=db)


@router.get("/getIdTable/{auto_id}")
def getIdTable(auto_id: int, db: Session = Depends(get_session)):
    return getIdTable100(auto_id=auto_id, db=db)


@router.post("/addPerson", response_model=tb_company_people_info_base)
def addPerson(hero : tb_company_people_info_post , db: Session = Depends(get_session)):
    return addPerson100(hero=hero,db=db)


@router.get("/getPhoneCompany/{auto_id}")
def getPhoneCompany(auto_id: int, db: Session = Depends(get_session)):
    return getPhoneCompany100(auto_id=auto_id, db=db)


@router.delete("/delPerson/{auto_id}")
def delPerson(auto_id: int, db: Session = Depends(get_session)):
    return delPerson100(auto_id=auto_id, db=db)


@router.put("/updatePerson/{auto_id}", response_model=tb_company_people_info_base)
def updatePerson(hero : tb_company_people_info_post, auto_id: int, db: Session = Depends(get_session)):
    return updatePerson100(auto_id=auto_id, hero=hero, db=db)


@router.get("/getFileInfoParent")
def getFileInfoParent(db: Session = Depends(get_session)):
    return getFileInfoParent100(db=db) 


@router.get("/selectFileInfo/{parent_head_id}")
def selectFileInfo(parent_head_id: int, db: Session = Depends(get_session)):
    return selectFileInfo100(parent_head_id=parent_head_id, db=db)


@router.get("/getAllImage/{company_id}")
def getAllImage(company_id: int, db: Session = Depends(get_session)):
    return getAllImage100(company_id=company_id, db=db) 


@router.get("/getChildImage/{company_id}/{auto_id}")
def getChildImage(company_id: int, auto_id: int, db: Session = Depends(get_session)):
    return getChildImage100(company_id=company_id, auto_id=auto_id, db=db) 


@router.post("/addParent", response_model=tb_company_file_info_base)
def addParent(hero : tb_company_file_info_post , db: Session = Depends(get_session)):
    return addParent100(hero=hero,db=db)


@router.post("/addFileInfoChild/{auto_id}")
def addFileInfoChild(auto_id: int, hero : tb_company_file_info_post , db: Session = Depends(get_session)):
    return addFileInfoChild100(auto_id=auto_id, hero=hero, db=db)


@router.post("/addFilePath", response_model=tb_company_file_base)
def addFilePath(hero : tb_company_file_post , db: Session = Depends(get_session)):
    return addFilePath100(hero=hero,db=db)

    
@router.get("/getImageWithAllTag/{company_id}")
def getImageWithAllTag(company_id: int, db: Session = Depends(get_session)):
    return getImageWithAllTag100(company_id=company_id, db=db) 


# @router.post("/list_car_in_parner", response_model=order_head_in)
# def create_a_hero(hero: order_body_in_post ,  db: Session = Depends(get_session)):

#     return post_list_car_in(hero=hero,db=db)



# @router.post("", response_model=pre_order_product_model_read)
# def create_a_hero(hero: create_pre_order_product_model, db: Session = Depends(get_session)):

#     return create_pre_order_product(hero=hero, db=db)


# @router.put("", response_model=pre_order_product_model_read)
# def create_a_hero(hero: create_put_order_product_model, db: Session = Depends(get_session)):
#     return create_put_order_sub_product(hero=hero, db=db)


# @router.post("/sub_pre_order", response_model=pre_order_product_sub_model_read)
# def create_a_hero(hero: create_pre_order_product_sub_model, db: Session = Depends(get_session)):
    
#     return create_pre_order_sub_product(hero=hero, db=db)


# @router.get("/sub_pre_order/{orderId}", response_model=List[product_sub_model])
# def create_a_hero(orderId: str , db: Session = Depends(get_session)):
    
#     return get_pre_order_sub_product(orderId=orderId, db=db)



# @router.get("/pre_order_list", response_model=List[pre_order_product_get_base])
# def pre_order_list(
#             db: Session = Depends(get_session),
#         ):
    
#     return get_pre_order_list(db=db)








# @router.post("", response_model=HeroRead)
# def create_a_hero(hero: HeroCreate, db: Session = Depends(get_session)):
#     return create_hero(hero=hero, db=db)




# @router.get("", response_model=List[HeroRead])
# def get_heroes(
#     offset: int = 0,
#     limit: int = Query(default=100, lte=100),
#     db: Session = Depends(get_session),
# ):
#     return read_heroes(offset=offset, limit=limit, db=db)


# @router.get("/{hero_id}", response_model=HeroRead)
# def get_a_hero(hero_id: int, db: Session = Depends(get_session)):
#     return read_hero(hero_id=hero_id, db=db)


# @router.patch("/{hero_id}", response_model=HeroRead)
# def update_a_hero(hero_id: int, hero: HeroUpdate, db: Session = Depends(get_session)):
#     return update_hero(hero_id=hero_id, hero=hero, db=db)


# @router.delete("/{hero_id}")
# def delete_a_hero(hero_id: int, db: Session = Depends(get_session)):
#     return delete_hero(hero_id=hero_id, db=db)


