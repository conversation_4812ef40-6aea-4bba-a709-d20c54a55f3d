from xmlrpc.client import DateTime

from click import option
from pydantic.types import Optional, List
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel, Column 
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id



# 资料表名称 pre_order_product 和框架
class tb_company_name_base(SQLModel):
    company_name: Optional[str]
    registered_date: Optional[datetime] 
    

class tb_company_name(tb_company_name_base, table=True):
    
    __tablename__ = "tb_company_name"

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    company_name: Optional[str]
    registered_date: Optional[datetime] 

class tb_company_name_read(BaseModel):
    auto_id: Optional[int]
    company_name: Optional[str]
    registered_date: Optional[datetime] 

class tb_company_name_post(BaseModel):
    company_name: Optional[str]
    registered_date: Optional[datetime] 

# ---------------------------------------
class tb_company_address_base(SQLModel):
    company_id: Optional[int]
    address: Optional[str]
    tel: dict = Field(sa_column=Column(JSONB), default={})

class tb_addresses_info(tb_company_address_base, table=True):
    __tablename__ = "tb_addresses_info"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    company_id: Optional[int]
    address: Optional[str]
    tel: dict = Field(sa_column=Column(JSONB), default={})

class tb_company_address_read(BaseModel):
    auto_id: Optional[int]
    company_id: Optional[int]
    address: Optional[str]
    tel: dict = Field(sa_column=Column(JSONB), default={})

class tb_company_address_post(BaseModel):
    company_id: Optional[int]
    address: Optional[str]
    tel: dict = Field(sa_column=Column(JSONB), default={})

# ---------------------------------------
class tb_company_department_base(SQLModel):
    department_name: Optional[str]

class tb_company_department(tb_company_department_base, table=True):

    __tablename__ = "tb_company_department"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    department_name: Optional[str]

class tb_company_department_read(BaseModel):
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    department_name: Optional[str]

class tb_company_department_post(BaseModel):
    department_name: Optional[str]

# ---------------------------------------
class tb_company_people_info_base(SQLModel):
    company_id: Optional[int]
    name: Optional[str]
    nrc: Optional[str]
    tel: Optional[str]
    department_id: Optional[int]
    start_date: Optional[datetime] 
    end_date: Optional[datetime]

class tb_people_info(tb_company_people_info_base, table=True):
    __tablename__ = "tb_people_info"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    company_id: Optional[int]
    name: Optional[str]
    nrc: Optional[str]
    tel: Optional[str]
    department_id: Optional[int]
    start_date: Optional[datetime] 
    end_date: Optional[datetime] 

class tb_company_people_info_read(BaseModel):
    auto_id: Optional[int]
    company_id: Optional[int]
    name: Optional[str]
    nrc: Optional[str]
    tel: Optional[str]
    department_id: Optional[int]
    start_date: Optional[datetime] 
    end_date: Optional[datetime]

class tb_company_people_info_post(BaseModel):
    company_id: Optional[int]
    name: Optional[str]
    nrc: Optional[str]
    tel: Optional[str]
    department_id: Optional[int]
    start_date: Optional[datetime] 
    end_date: Optional[datetime]

# ---------------------------------------
class tb_company_file_base(SQLModel):
    company_id: Optional[int]
    file_name_path: Optional[str]
    title: Optional[str]
    file_info_id: Optional[int]

class tb_company_file(tb_company_file_base, table=True):
    __tablename__ = "tb_company_file"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    company_id: Optional[int]
    file_name_path: Optional[str]
    title: Optional[str]
    file_info_id: Optional[int]


class tb_company_file_read(BaseModel):
    auto_id: Optional[int]
    company_id: Optional[int]
    file_name_path: Optional[str]
    title: Optional[str]
    file_info_id: Optional[int]

class tb_company_file_post(BaseModel):
    company_id: Optional[int]
    file_name_path: Optional[str]
    title: Optional[str]
    file_info_id: Optional[int]

# ---------------------------------------
class tb_company_file_info_base(SQLModel):
    auto_id: Optional[int]
    mm_name: Optional[str]
    parent_id: Optional[int]
    if_parent: Optional[int]
    parent_head_id: Optional[int]

class tb_company_file_info(tb_company_file_info_base, table=True):
    __tablename__ = "tb_company_file_info"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    mm_name: Optional[str]
    parent_id: Optional[int]
    if_parent: Optional[int]
    parent_head_id: Optional[int]

class tb_company_file_info_read(BaseModel):
    auto_id: Optional[int]
    mm_name: Optional[str]
    parent_id: Optional[int]
    if_parent: Optional[int] 
    parent_head_id: Optional[int]

class tb_company_file_info_post(BaseModel):
    mm_name: Optional[str]
    parent_id: Optional[int] = 0
    if_parent: Optional[int] = False
    parent_head_id: Optional[int]


