from builtins import int
from locale import D_FMT
from lib2to3.pgen2.pgen import DFAState
from multiprocessing.reduction import DupFd
from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel
from typing import List, Optional

from datetime import datetime
from src.shwethe_company_document.database import get_session
from helper import generate_datetime_id
from src.shwethe_company_document.form_company.models.models import tb_addresses_info, tb_company_address_post, tb_company_department_post, tb_company_file, tb_company_file_info, tb_company_file_info_post, tb_company_file_post, tb_company_name,tb_company_name_post, tb_company_department, tb_company_name_read, tb_company_people_info_post, tb_people_info
import json
import pandas as pd
from src.Connect.https_connect import mongodb_data_api


def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def read_status_list(db: Session = Depends(get_session)):
    print("db")
    heroes = db.exec(select(tb_company_name)).all()
    return heroes


def post_list_car_in(hero:tb_company_name_post,db: Session = Depends(get_session)):
    print(db)

    # print(hero)

    statement = select(tb_company_name).where(tb_company_name.company_name == hero.company_name)
    results = db.exec(statement).all()

    if not (results):
        hero_to_db = tb_company_name.from_orm(hero)
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        print("aaa")
        return hero_to_db

    return []


def post_list_car_in_2(hero:tb_company_address_post,db: Session = Depends(get_session)):

    statement = select(tb_addresses_info).where(tb_addresses_info.company_id == hero.company_id)
    results = db.exec(statement).first()

    if not (results):
        hero_to_db = tb_addresses_info.from_orm(hero)
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        print("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa")
        return hero_to_db

    results.address = hero.address
    results.tel = hero.tel
    db.add(results)  # 
    db.commit()  # 
    db.refresh(results)  # 
    print("Updated heroooooooooooooooooo:")

    return results


def post_list_car_in_3(hero:tb_company_department_post,db: Session = Depends(get_session)):
    hero_to_db = tb_company_department.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    print("aaa")
    return hero_to_db


def getData100(db: Session = Depends(get_session)):
    sql = db.query(tb_company_department).order_by(tb_company_department.auto_id).all()      
    records = [i.dict() for i in sql]
    df = pd.DataFrame.from_records(records)
    df = df.rename(columns={"auto_id": "value", "department_name": "label"})    
    df = df.to_dict('records')                
    return df


def post_list_car_in_4(hero:tb_company_people_info_post,db: Session = Depends(get_session)):
    
    statement = select(tb_people_info).where(tb_people_info.company_id == hero.company_id)
    results = db.exec(statement).first()

    if not (results):
        hero_to_db = tb_people_info.from_orm(hero)
        db.add(hero_to_db)
        db.commit()
        db.refresh(hero_to_db)
        print("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa")
        return hero_to_db

    results.name = hero.name
    results.nrc = hero.nrc
    results.tel = hero.tel
    results.department_id = hero.department_id
    results.start_date = hero.start_date
    results.end_date = hero.end_date
    db.add(results)  # 
    db.commit()  # 
    db.refresh(results)  # 
    print("Updated heroooooooooooooooooo:")

    return []


def getTableData100(db: Session = Depends(get_session)):
    df = db.exec(
        select(
            tb_company_name.auto_id, tb_company_name.company_name, tb_company_name.registered_date,
            tb_addresses_info.company_id, tb_addresses_info.address,
            tb_people_info.department_id,
            tb_company_department.auto_id, tb_company_department.department_name,
        )
        .where(
            tb_company_name.auto_id == tb_addresses_info.company_id,
            tb_company_name.auto_id == tb_people_info.company_id,
            tb_people_info.department_id == tb_company_department.auto_id
        )
        .distinct(tb_company_name.auto_id)
    ).all() 

    # df = pd.DataFrame(df, columns=['auto_id', 'company_name', 'registered_date', 'company_id', 'address', 'department_id', 'auto_id_1', 'department_name'])
    # df = df.drop_duplicates()
    # df = df.to_dict('records ') 
    return df


def getIdTable100(auto_id: int, db: Session = Depends(get_session)):
   
    print(auto_id)

    try:

        df = db.exec(
            select(
                tb_company_name.auto_id, tb_company_name.company_name, tb_company_name.registered_date,
                tb_addresses_info.company_id, tb_addresses_info.address,
                tb_people_info.auto_id, tb_people_info.company_id, tb_people_info.name, tb_people_info.nrc, tb_people_info.tel, tb_people_info.department_id, tb_people_info.start_date, tb_people_info.end_date,
                tb_company_department.auto_id, tb_company_department.department_name,
            )
            .where(
                tb_company_name.auto_id == tb_addresses_info.company_id,
                tb_company_name.auto_id == tb_people_info.company_id,
                tb_people_info.department_id == tb_company_department.auto_id
            )
            .where(tb_company_name.auto_id == auto_id)
        ).all()
    except: 
        df = []

    return  df


def addPerson100(hero: tb_company_people_info_post, db: Session = Depends(get_session)):
    
    hero_to_db = tb_people_info.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    
    return hero_to_db


def getPhoneCompany100(auto_id: int, db: Session = Depends(get_session)):
       
    print(auto_id)

    df = db.exec(
        select(tb_addresses_info.company_id, tb_addresses_info.address, tb_addresses_info.tel)
        .where(tb_addresses_info.company_id == auto_id)
    ).all()

    return  df


def delPerson100(auto_id: int, db: Session = Depends(get_session)):
       
    statement = select(tb_people_info).where(tb_people_info.auto_id == auto_id)
    results = db.exec(statement)
    hero = results.one()
    print("Hero: ", hero)
    db.delete(hero)
    db.commit()
    print("Deleted hero:", hero)

    return  hero


def updatePerson100(hero : tb_company_people_info_post, auto_id: int, db: Session = Depends(get_session)):

    statement = select(tb_people_info).where(tb_people_info.auto_id == auto_id)
    results = db.exec(statement).first()

    results.name = hero.name
    results.nrc = hero.nrc
    results.tel = hero.tel

    db.add(results)  # 
    db.commit()  # 
    db.refresh(results)  # 
    print("Updated heroooooooooooooooooo:")

    return  hero


# def addFileInfoChild100(auto_id: int, hero: tb_company_file_info_post, db: Session = Depends(get_session)):
    
#     statement = select(tb_company_file_info).where(tb_company_file_info.auto_id == auto_id)
#     results = db.exec(statement).first()
#     results.if_parent = True
#     db.add(results)  # 
#     db.commit()  # 
#     db.refresh(results)  #
#     print(results)

#     hero_to_db = tb_company_file_info.from_orm(hero)
#     hero_to_db.parent_id = auto_id
#     hero_to_db.parent_head_id = results.parent_head_id
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
#     print(hero_to_db.parent_head_id)

#     return hero_to_db


def getFileInfoParent100(db: Session = Depends(get_session)):
    try:

        df = db.exec(
            select(tb_company_file_info)
            .where(tb_company_file_info.parent_id == 0)
            .order_by(tb_company_file_info.auto_id)
        ).all()
    except: 
        df = []                
    return df


def selectFileInfo100(parent_head_id: int, db: Session = Depends(get_session)):
    from dataclasses import dataclass, field, asdict
    from pprint import pprint
    from typing import List, Dict, Optional
    import pandas as pd
    from pandas import DataFrame


    @dataclass
    class Tree:
        name: str
        id: int
        child_id: int
        children: List['Tree'] = field(default_factory=list)


    def traversal(df: DataFrame) -> Optional[Tree]:
        tmp: List[Tree] = [Tree(name=i["name"], id=int(i["id"]), child_id=int(i["child_id"])) for i in df.to_dict("records")]
        memo: Dict[int, Tree] = {i.id: i for i in tmp}
        root = None
        for i in tmp:
            if i.child_id == 0:
                root = i
            else:
                memo[i.child_id].children.append(i)
        return root

    
    sql = db.exec(select(tb_company_file_info).where(tb_company_file_info.parent_head_id == parent_head_id)).all()
    records = [i.dict() for i in sql]
    df = pd.DataFrame.from_records(records)
    df = df.rename(columns={"auto_id": "id", "mm_name": "name", "parent_id": "child_id"})    
    res = traversal(df)

    return [asdict(res)]


def getAllImage100(company_id: int, db: Session = Depends(get_session)):
    try:
        df = db.exec(select(tb_company_file).where(tb_company_file.company_id == company_id)).all()
    except: 
        df = []                
    return df


def getChildImage100(company_id: int, auto_id: int, db: Session = Depends(get_session)):
    try:
        df = db.exec(select(tb_company_file)
        .where(tb_company_file.company_id == company_id)
        .where(tb_company_file.file_info_id == auto_id)
        ).all()
    except: 
        df = []                
    return df


def addParent100(hero: tb_company_file_info_post, db: Session = Depends(get_session)):
    
    hero_to_db = tb_company_file_info.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    print(hero_to_db.auto_id)

    statement = select(tb_company_file_info).where(tb_company_file_info.auto_id == hero_to_db.auto_id)
    results = db.exec(statement).first()
    results.parent_head_id = hero_to_db.auto_id
    db.add(results)  # 
    db.commit()  # 
    db.refresh(results)  #
    print(results)

    return hero_to_db


def addFileInfoChild100(auto_id: int, hero: tb_company_file_info_post, db: Session = Depends(get_session)):
    
    statement = select(tb_company_file_info).where(tb_company_file_info.auto_id == auto_id)
    results = db.exec(statement).first()
    results.if_parent = True
    db.add(results)  # 
    db.commit()  # 
    db.refresh(results)  #
    print(results)

    hero_to_db = tb_company_file_info.from_orm(hero)
    hero_to_db.parent_id = auto_id
    hero_to_db.parent_head_id = results.parent_head_id
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    print(hero_to_db.parent_head_id)

    return hero_to_db


def addFilePath100(hero: tb_company_file_post, db: Session = Depends(get_session)):
    
    hero_to_db = tb_company_file.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)

    return hero_to_db


def getImageWithAllTag100(company_id: int, db: Session = Depends(get_session)):
    try:
        df = db.exec(select(
            tb_company_file.auto_id, tb_company_file.company_id, tb_company_file.file_name_path, tb_company_file.title, tb_company_file.file_info_id,
            tb_company_file_info.auto_id, tb_company_file_info.mm_name, tb_company_file_info.parent_id, tb_company_file_info.if_parent, tb_company_file_info.parent_head_id
            )
        .where(tb_company_file.file_info_id == tb_company_file_info.auto_id)
        .where(tb_company_file.company_id == company_id)
        .distinct(tb_company_file.file_info_id)
        ).all()
        
    except: 
        df = []                
    return df






# def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
#         """Convert a SQLModel objects into a pandas DataFrame."""
#         try:
#             records = [i.dict() for i in objs]
#             df = pd.DataFrame.from_records(records)
#             return df
#         finally:
#             df2 = pd.DataFrame()
#             c=0
            
#             for i2 in objs:
                
#                 d=0

#                 for ii in i2:

                    

#                     i33 = None

#                     i33 = i2[d].dict()
#                     try:
#                         df = pd.DataFrame.from_records(i33)
#                     except:
#                         df = pd.DataFrame.from_records([i33])

#                     print(c)
#                     print(d)

#                     VV = "abc1" + str(d)

#                     try:

#                         df2.loc[c,str(VV)] =  df.to_dict(orient='records')

#                     except:

#                         try:

#                             df2.loc[c,str(VV)] =  pd.DataFrame([df]).to_dict(orient='records')

#                         except:

#                             try:
#                                 df2[str(VV)][c] = pd.DataFrame([i33]).to_dict(orient='records')[0] 

#                             except:
#                                 # df = pd.DataFrame([i33]).to_dict(orient='records')
#                                 print("error")
#                                 print(i33)
#                                 print(i33)
#                                 # try:
#                                 df2[str(VV)][c] = pd.DataFrame(i33).to_dict(orient='records')[0]    
#                                 # except:
#                                 #     df2[str(VV)][c] = None




#                     d += 1
#                 c += 1

#             return df2


# def getTableData100(db: Session = Depends(get_session)):
#     df = db.exec(select(tb_company_name, tb_addresses_info, tb_people_info, tb_company_department)
#     .where(
#         tb_company_name.auto_id == tb_addresses_info.company_id,
#         tb_company_name.auto_id == tb_people_info.company_id,
#         tb_people_info.department_id == tb_company_department.auto_id
#     )).all() 


#     df123 = df


#     df = sqmodel_to_df(df)

#     # df = [i.dict() for i in df]
#     # df = pd.DataFrame.from_records(df) 
#     df = df.to_dict(orient='records')

#     df = pd.json_normalize(df)

#     df = df.to_json(orient='records')     
         
#     return  json.loads(df)
#     # return  df123


# def get_list_car_in(jin_huo_bian:int,db: Session = Depends(get_session)):
    
#     # print(hero)

#     statement = select(order_body_in).where(order_body_in.jin_huo_bian == jin_huo_bian)
#     results = db.exec(statement).all()

#     if (results):
#         return  results
#     return []   

    # hero.datetime = str(datetime.now())
    # hero.orderProductSubId = generate_datetime_id()

    


# def create_hero(hero: HeroCreate, db: Session = Depends(get_session)):
#     hero_to_db = Hero.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
#     return hero_to_db





# def read_hero(hero_id: int, db: Session = Depends(get_session)):
#     hero = db.get(Hero, hero_id)
#     if not hero:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {hero_id}",
#         )
#     return hero


# def update_hero(hero_id: int, hero: HeroUpdate, db: Session = Depends(get_session)):
#     hero_to_update = db.get(Hero, hero_id)
#     if not hero_to_update:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {hero_id}",
#         )

#     team_data = hero.dict(exclude_unset=True)
#     for key, value in team_data.items():
#         setattr(hero_to_update, key, value)

#     db.add(hero_to_update)
#     db.commit()
#     db.refresh(hero_to_update)
#     return hero_to_update


# def delete_hero(hero_id: int, db: Session = Depends(get_session)):
#     hero = db.get(Hero, hero_id)
#     if not hero:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {hero_id}",
#         )

#     db.delete(hero)
#     db.commit()
#     return {"ok": True}





# def create_pre_order_product(hero: pre_order_product_model_read, db: Session = Depends(get_session)):

#     hero.datetime = str(datetime.now())
#     hero.orderId = generate_datetime_id()
#     hero_to_db = pre_order_product.from_orm(hero)
    
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)

#     return hero_to_db


# def create_put_order_sub_product(hero: create_put_order_product_model, db: Session = Depends(get_session)):
#     # print(hero)
#     # hero.datetime = str(datetime.now())
#     # hero.orderProductSubId = generate_datetime_id()

#     statement = select(pre_order_product).where(pre_order_product.orderId == hero.orderId)
#     results = db.exec(statement).one()

#     print("results:", results)
#     print("hero:", hero)


#     results.lei = hero.lei
#     results.keBian = hero.keBian
#     results.statusCodeId = hero.statusCodeId
#     results.jiaYiId = hero.jiaYiId
#     results.dataSubName = hero.dataSubName
#     results.dataSub = hero.dataSub

#     db.add(results)
#     db.commit()
#     db.refresh(results)
    
#     return results


# def create_pre_order_sub_product(hero: pre_order_product_sub_model_read, db: Session = Depends(get_session)):
#     # print(hero)
#     hero.datetime = str(datetime.now())
#     hero.orderProductSubId = generate_datetime_id()
#     hero_to_db = pre_order_product_sub.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
    
#     return hero_to_db




# def get_pre_order_sub_product(orderId:str, db: Session = Depends(get_session)):
#     print(orderId)
#     # hero_to_db = pre_order_product_sub.from_orm(hero)

#     # db.add(hero_to_db)
#     # db.commit()
#     # db.refresh(hero_to_db)

#     # def read_hero(hero_id: int, db: Session = Depends(get_session)):
#     #     hero = db.get(Hero, hero_id)
#     # if not hero:
#     #     raise HTTPException(
#     #         status_code=status.HTTP_404_NOT_FOUND,
#     #         detail=f"Hero not found with id: {hero_id}",
#     #     )
#     # return hero
#     # heroes = db.exec(select(Hero).offset(offset).limit(limit)).all()
#     statement = select(pre_order_product_sub).where(pre_order_product_sub.orderId == orderId)
#     results = db.exec(statement).all()

#     for hero in results:
#         print(hero)
#     print(results)
#     print("results")
#     # hero = db.get(pre_order_product_sub_base, orderId)
#     if not results:
#         # raise HTTPException(
#         #     status_code=status.HTTP_404_NOT_FOUND,
#         #     detail=f"Hero not found with id: {orderId}",
#         # )
#         return []
        
#     return results
    


# def create_get_order_sub_product(orderId:str, hero: pre_order_product_sub_model_read, db: Session = Depends(get_session)):
#     # print(hero)
#     hero_to_db = pre_order_product_sub.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
    
#     return hero_to_db



# def getDataSubProduct(db: Session = Depends(get_session)):

#     heroes = db.exec(select(pre_order_product).offset(offset).limit(limit)).all()
    
#     return heroes


# def get_pre_order_list(db: Session = Depends(get_session)):
   
#     heroes = db.exec(select(pre_order_product)).all()

#     df = sqmodel_to_df(heroes)

#     df = df.sort_values(['datetime'], ascending=[False])

#     # df10001 = df.to_dict('records')

#     # df10002 = pd.json_normalize(df10001)

#     # df10003 = df10002.rename(columns={'dataSubName.jiaYiIdname': 'jiaYiIdname','dataSubName.jiaYiMmName': 'jiaYiMmName'
#     # ,'dataSubName.keBianIdname': 'keBianIdname','dataSubName.keBianMmName': 'keBianMmName'})
    

#     df10004 = df.to_json(orient='records')

    
#     return json.loads(df10004)