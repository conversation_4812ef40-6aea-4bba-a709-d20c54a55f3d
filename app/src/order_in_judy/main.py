from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np

router = APIRouter()



@router.get("/order_in_judy", status_code=200)
def order_in_judy():
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api


    try:
    
        # def get_data(jin_huo_bian):
        #     sql_insert = """ select che_liang,data_sub from order_s_insert where data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'status'= 'sucess' """ % jin_huo_bian
        #     OI10001 = pd.read_sql(sql_insert,psycopg2_conn_insert_data_s)
        #     OI10002 = OI10001.to_dict('records')
        #     OI10003 = pd.json_normalize(OI10002)
        #     OI10004 = OI10003.reindex(columns=['data_sub.jin_huo_bian','che_liang','data_sub.car_round'])
        #     OI10005 = OI10004.rename(columns={'data_sub.car_round': 'car_round' ,'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
        #     return  OI10005

        def get_data():
            sql_insert = """ select che_liang,data_sub from order_s_insert where  data_sub ->> 'status'= 'sucess' and datetime > current_date -7 """ 
            OI10001 = pd.read_sql(sql_insert,psycopg2_conn_insert_data_s)
            OI10002 = OI10001.to_dict('records')
            OI10003 = pd.json_normalize(OI10002)
            OI10004 = OI10003.reindex(columns=['data_sub.jin_huo_bian','che_liang'])
            OI10005 = OI10004.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
            OI10005 = OI10005.drop_duplicates()
            return  OI10005

        def get_data_3():
            sql_insert = """ select jin_huo_bian,che_liang,status_code_id from order_head_in where  status_code_id = 200 and shu_ri_qi > current_date -7 """ 
            OI10001 = pd.read_sql(sql_insert,psycopg2_conn_insert_data_s)

            # mask = (OI10001['status_code_id']==200)
            # OI10001.loc[mask, 'status'] = "sucess"


            # OI10002 = OI10001.to_dict('records')
            # OI10003 = pd.json_normalize(OI10002)
            # OI10004 = OI10003.reindex(columns=['data_sub.jin_huo_bian','che_liang'])
            # OI10005 = OI10001.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
            OI10005 = OI10001.drop_duplicates()
            return  OI10005

        def get_data_2():
            sql_insert = """ select jin_huo_bian,data_sub from duty_car_in  """ 
            OI10001 = pd.read_sql(sql_insert,psycopg2_conn_insert_data_s)
            OI10002 = OI10001.to_dict('records')
            OI10003 = pd.json_normalize(OI10002)
            OI10004 = OI10003.reindex(columns=['data_sub.status','jin_huo_bian'])
            OI10005 = OI10004.rename(columns={'data_sub.status': 'status' })
            OI10005 = OI10005.drop_duplicates()
            return  OI10005


        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            print(res_1)
            r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                        json={"data_api": res_1})

            GF10001 = pd.DataFrame(json.loads(r.json()))

            GF10001 = GF10001.rename(columns={'jia_yi_id': str,'jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mmname'})
            print(GF10001)

            return GF10001


        GF10001 = get_data()
        GF20001 = get_data_3()


        GF10001 = pd.concat([GF10001,GF20001])
        GF10001 = GF10001[['jin_huo_bian','che_liang']].drop_duplicates()

        GF10002 = GF10001[['che_liang']]
        GF10003 = GF10002.rename(columns={'che_liang': 'jia_yi_id'})
        GF10004 = get_data_api(GF10003,"che_liang")
        GF10005 = GF10004.merge(GF10001, left_on=['che_liang'],right_on=['che_liang'], how='outer')
        # GF10006 = GF10005.drop(columns=['product_id_x'])    
        GC10001 = get_data_2()
        print("GC10001")
        print(GC10001)
        GF10005 = GC10001.merge(GF10005, on=['jin_huo_bian'], how='right')



        
        GF10002 = GF10005.to_json(orient='records')
        return json.loads(GF10002)
    finally:
        psycopg2_conn_insert_data_s.close()

class order_in_judy_summit(BaseModel):
    jin_huo_bian : int
    row_type : str

@router.post("/order_in_judy", status_code=200)
def order_in_judy(req_body : order_in_judy_summit = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api

    def check_data(jin_huo_bian,row_type):
        select_data = """ select jin_huo_bian from duty_car_in where jin_huo_bian = '%s' and data_sub ->> 'row_type' = '%s' """ % (jin_huo_bian,row_type)
        TY10001 = pd.read_sql(select_data,psycopg2_conn_insert_data_s)
        return TY10001

    def summit_sucess(jin_huo_bian,jsonb):
        sql_insert = """ insert into  duty_car_in (jin_huo_bian,data_sub) values (%s,%s) """
        cur.execute(sql_insert, (jin_huo_bian,jsonb))
        return

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    check_data_10001 = check_data(req_body.jin_huo_bian,req_body.row_type)
    if check_data_10001.empty:
        summit_sucess(str(req_body.jin_huo_bian),json.dumps({'status':'wait_sucess','row_type': req_body.row_type}))
    
    psycopg2_conn_insert_data_s.close()

    return "sucess"



@router.get("/order_in_judy_in_list", status_code=200)
def order_in_judy_in_list(registration_id:int,selie:int):
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(mongodb_data_api)
    from src.Connect.https_connect import mongodb_data_api


    def get_data_api(registration_id,selie):
        r = requests.get(mongodb_data_api + '/api/v1/data/judy/judy?registration_id='  +str(registration_id)  )
        print(r)
        
        GF10001 = pd.DataFrame(r.json())
        GF10002 = GF10001.loc[GF10001['selie'] == selie]
        print(GF10002)
        return GF10002



    def get_data_api_product(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                     json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'product_id': str,'product_idname': str+'_idname','product_mm_name': str+'_mmname','product_d_name': str+'_d_name'})
        # print(GF10001)

        return GF10001


    CV10001 = get_data_api(registration_id,selie)
    print(CV10001[['judy_in']])

    merge_data10001 = CV10001[['judy_in']]
    merge_data10002 = merge_data10001.rename(columns={'judy_in': 'product_id'})
    print(merge_data10002)

    merge_data10003 = get_data_api_product(merge_data10002,"judy_in")
    merge_data10004 = merge_data10003.merge(CV10001, left_on=['judy_in'],right_on=['judy_in'], how='outer')
    merge_data10005 = merge_data10004.rename(columns={'judy_in': 'judy_in_id'})
    # merge_data10005 = merge_data10004.drop(columns=['product_id_x'])
    print(merge_data10005)
    print(merge_data10005.info())
    merge_data10006 = merge_data10005.to_json(orient='records')
    return json.loads(merge_data10006)


class order_in_judy_form_detail____(BaseModel):
    registration_id : int
    registration_selie : int
    product_id: int
    product_qty: float
    product_price: float
    lei_type_id: int
    jia_yi_fang_a:int
    jia_yi_fang_b:int

class order_in_judy_form___________(BaseModel):
    jin_huo_bian : str
    row_type : str
    data_jsonb : order_in_judy_form_detail____

@router.post("/order_in_judy_form", status_code=200)
def order_in_judy(req_body : order_in_judy_form___________ = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    jin_huo_bian = FFF['jin_huo_bian']
    jin_huo_bian
    row_type = FFF['row_type']

    product_id = FFF['data_jsonb']['product_id']
    product_id

    product_qty = FFF['data_jsonb']['product_qty']
    lei_type_id = FFF['data_jsonb']['lei_type_id']
    product_price = FFF['data_jsonb']['product_price']
    registration_id = FFF['data_jsonb']['registration_id']
    registration_selie = FFF['data_jsonb']['registration_selie']



    def check_data(registration_selie,registration_id,product_id,product_price, jin_huo_bian,lei_type_id,row_type):
        sql_insert = """
                    SELECT *
                    FROM duty_car_in 
                    ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                    where contact ->> 'registration_selie' = '%s' and  contact ->> 'registration_id' = '%s' 
                    and contact ->> 'product_id' = '%s' and contact ->> 'product_price' = '%s' and contact ->> 'lei_type_id' = '%s'
                    and jin_huo_bian = '%s' and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = '%s'
                    """ % (registration_selie,registration_id,product_id,product_price, lei_type_id,jin_huo_bian,row_type)
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        return FF100001
    def check_type_list(auto_id):
        HG100001 = pd.read_sql('select lei_a,lei_b from lei_type_list where auto_id = '+ str(auto_id) +' ',psycopg2_conn_insert_data_s)
        
        return HG100001


    def update_data(registration_selie,registration_id,product_id,product_price, lei_type_id, jin_huo_bian,FFF,row_type):
        sql_insert = """   WITH f AS
                        (
                        SELECT ('{'||index-1||'}')::text[] as path
                        FROM duty_car_in 
                        ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                        where contact ->> 'registration_selie' = '%s'  and contact ->> 'registration_id' = '%s'  
                        and  contact ->> 'product_id' = '%s' and  contact ->> 'product_price' = '%s' and contact ->> 'lei_type_id' = '%s'
                        and jin_huo_bian = %s and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = %s ) 

                        UPDATE duty_car_in
                        SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                        FROM f where jin_huo_bian = %s and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = %s  ;

                        """
        cur.execute(sql_insert, (registration_selie,registration_id,product_id,product_price, lei_type_id, jin_huo_bian,row_type,FFF, jin_huo_bian,row_type))

    def put_data(jin_huo_bian,FFF,row_type):

        sql_insert = """
                        UPDATE duty_car_in
                        SET data_detail=data_detail  || %s ::jsonb
                        where jin_huo_bian = %s and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = %s ;
                        """
        cur.execute(sql_insert, ( FFF, jin_huo_bian,row_type))

    # def summit_sucess(jin_huo_bian,jsonb):
    #     sql_insert = """ insert into  duty_car_in (jin_huo_bian,data_sub) values (%s,%s) """
    #     cur.execute(sql_insert, (jin_huo_bian,jsonb))
    #     return

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    T100001 = check_data(registration_selie,registration_id,product_id,product_price, jin_huo_bian,lei_type_id,row_type)

    try:
        T100001.contact[0]['product_qty'] = float(T100001.contact[0]['product_qty']) + float(product_qty)
        print(T100001.contact[0])
    except:
        pass

    check_type_list10001 = check_type_list(lei_type_id)

    FFF['data_jsonb']['lei_a'] = check_type_list10001['lei_a'][0]
    FFF['data_jsonb']['lei_b'] = check_type_list10001['lei_b'][0]
    FFF['data_jsonb']['datetime'] = str(generate_datetime())

    PPO10001 = pd.DataFrame([FFF['data_jsonb']])

    if not T100001.empty:
        print("1")
        print(T100001.contact[0])
        FFF2 = json.dumps(T100001.contact[0])
        update_data(registration_selie,registration_id,product_id,product_price, lei_type_id, jin_huo_bian,FFF2,row_type)

    else:
        print("2")
        PPO10001['r_id'] = generate_datetime_id()
        T100003 = PPO10001.to_dict(orient='records')
        FFF = json.dumps(T100003)
        put_data(jin_huo_bian,  FFF,row_type)
    
    psycopg2_conn_insert_data_s.close()

    return "sucess"





class order_in_judy_form_detail(BaseModel):
    product_id: int
    product_qty: float
    product_price: float
    lei_type_id: int
    bi_zhi: int
    jia_yi_fang_a:int
    jia_yi_fang_b:int

class order_in_judy_form(BaseModel):
    jin_huo_bian : str
    row_type : str
    data_jsonb : order_in_judy_form_detail

@router.post("/order_in_judy_for_car_cost_form", status_code=200)
def order_in_judy(req_body : order_in_judy_form = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    jin_huo_bian = FFF['jin_huo_bian']
    jin_huo_bian
    row_type = FFF['row_type']

    product_id = FFF['data_jsonb']['product_id']
    product_id

    product_qty = FFF['data_jsonb']['product_qty']
    lei_type_id = FFF['data_jsonb']['lei_type_id']
    product_price = FFF['data_jsonb']['product_price']
    



    def check_data(product_id,product_price, jin_huo_bian,lei_type_id,row_type):
        sql_insert = """
                    SELECT *
                    FROM duty_car_in 
                    ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                    where  contact ->> 'product_id' = '%s' and contact ->> 'product_price' = '%s' and contact ->> 'lei_type_id' = '%s'
                    and jin_huo_bian = '%s' and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = '%s'
                    """ % (product_id,product_price, lei_type_id,jin_huo_bian,row_type)
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        return FF100001
    def check_type_list(auto_id):
        HG100001 = pd.read_sql('select lei_a,lei_b from lei_type_list where auto_id = '+ str(auto_id) +' ',psycopg2_conn_insert_data_s)
        
        return HG100001


    def update_data(product_id,product_price, lei_type_id, jin_huo_bian,FFF,row_type):
        sql_insert = """   WITH f AS
                        (
                        SELECT ('{'||index-1||'}')::text[] as path
                        FROM duty_car_in 
                        ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                        where contact ->> 'product_id' = '%s' and  contact ->> 'product_price' = '%s' and contact ->> 'lei_type_id' = '%s'
                        and jin_huo_bian = %s and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = %s ) 

                        UPDATE duty_car_in
                        SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                        FROM f where jin_huo_bian = %s and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = %s  ;

                        """
        cur.execute(sql_insert, (product_id,product_price, lei_type_id, jin_huo_bian,row_type,FFF, jin_huo_bian,row_type))

    def put_data(jin_huo_bian,FFF,row_type):

        sql_insert = """
                        UPDATE duty_car_in
                        SET data_detail=data_detail  || %s ::jsonb
                        where jin_huo_bian = %s and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = %s ;
                        """
        cur.execute(sql_insert, ( FFF, jin_huo_bian,row_type))

    # def summit_sucess(jin_huo_bian,jsonb):
    #     sql_insert = """ insert into  duty_car_in (jin_huo_bian,data_sub) values (%s,%s) """
    #     cur.execute(sql_insert, (jin_huo_bian,jsonb))
    #     return

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    T100001 = check_data(product_id,product_price, jin_huo_bian,lei_type_id,row_type)

    try:
        T100001.contact[0]['product_qty'] = float(T100001.contact[0]['product_qty']) + float(product_qty)
        print(T100001.contact[0])
    except:
        pass
    
    check_type_list10001 = check_type_list(lei_type_id)
    FFF['data_jsonb']['lei_a'] = check_type_list10001['lei_a'][0]
    FFF['data_jsonb']['lei_b'] = check_type_list10001['lei_b'][0]
    FFF['data_jsonb']['datetime'] = str(generate_datetime())

    PPO10001 = pd.DataFrame([FFF['data_jsonb']])

    if not T100001.empty:
        print("1")
        print(T100001.contact[0])
        FFF2 = json.dumps(T100001.contact[0])
        update_data(product_id,product_price, lei_type_id, jin_huo_bian,FFF2,row_type)

    else:
        print("2")
        PPO10001['r_id'] = generate_datetime_id()
        T100003 = PPO10001.to_dict(orient='records')
        FFF = json.dumps(T100003)
        put_data(jin_huo_bian,  FFF,row_type)
    
    psycopg2_conn_insert_data_s.close()

    return "sucess"






@router.get("/order_in_judy_form", status_code = 200)
def order_in_judy_form(jin_huo_bian : int, row_type : str):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    print(row_type)
    # sql_insert = """
    #                     SELECT * 
    #                     FROM duty_car_in  where jin_huo_bian = '%s' and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = '%s'
    #                     """ % (jin_huo_bian,row_type)
    sql_insert = """
                        SELECT * 
                        FROM duty_car_in  where jin_huo_bian = '%s' and data_sub ->> 'row_type' = '%s'
                        """ % (jin_huo_bian,row_type)
    
    FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
    FF100001
    try:
        GG100001 = pd.DataFrame(FF100001['data_detail'][0])
    except :
        GG100001 = pd.DataFrame()

    if not GG100001.empty:
        GG100001['product_id'] = GG100001['product_id'].astype(int)
        H1000001 = GG100001[['product_id']]

        res = json.loads(H1000001.to_json(orient='records'))
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res})
        FF100002 = FF100001.to_json(orient='records')
        GF10001 = pd.DataFrame(json.loads(r.json()))
    else:
        GF10001 = pd.DataFrame()
    if not GF10001.empty:
        GG100009 = GF10001.merge(GG100001, left_on=['product_id'], right_on=['product_id'], how='inner')
        GG100009
    else:
        GG100009 = pd.DataFrame()

    GG100010 = GG100009.to_json(orient='records')

    psycopg2_conn_insert_data_s.close()
    return json.loads(GG100010)


class pre_order_n_get_Foo_(BaseModel):
    r_id : str
    product_id: int
    before_cheange_qty : float
    product_qty: float
    product_price: float



class pre_PPPP(BaseModel):
    jin_huo_bian : str
    row_type : str
    data_jsonb : pre_order_n_get_Foo_

@router.put("/order_in_judy_form", status_code = 200)  
def order_in_judy_form(req_body : pre_PPPP = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    datetime = str(generate_datetime())
    import json

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    jin_huo_bian = FFF['jin_huo_bian']
    row_type = FFF['row_type']
    jin_huo_bian
    product_id = FFF['data_jsonb']['product_id']
    product_qty = FFF['data_jsonb']['product_qty']
    before_cheange_qty = FFF['data_jsonb']['before_cheange_qty']
    product_price = FFF['data_jsonb']['product_price']
    r_id = FFF['data_jsonb']['r_id']




    def update_data(r_id,jin_huo_bian,FFF,before_cheange_qty,row_type):
        sql_insert = """   WITH f AS
                        (
                        SELECT ('{'||index-1||'}')::text[] as path
                        FROM duty_car_in 
                        ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                        where contact ->> 'r_id' = %s and contact ->> 'product_qty' = '%s'
                        and jin_huo_bian = %s and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = %s) 

                        UPDATE duty_car_in
                        SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                        FROM f where jin_huo_bian = %s and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = %s  ;

                        """
        cur.execute(sql_insert, (r_id, before_cheange_qty,jin_huo_bian,row_type, FFF, jin_huo_bian,row_type))


    def check_data(r_id,jin_huo_bian,row_type):
        sql_insert = """
                    SELECT *
                    FROM duty_car_in 
                    ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                    where contact ->> 'r_id' = '%s'
                    and jin_huo_bian = '%s' and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = '%s'
                    """ % (r_id, jin_huo_bian,row_type)

        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001

        return  FF100001

    

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    FF100001 = check_data(r_id,jin_huo_bian,row_type)

    FF100001.contact[0]['product_qty'] = product_qty


    import json
    PPO10001 = pd.DataFrame([FFF['data_jsonb']])
    T100009 = PPO10001.to_dict(orient='records')[0]
    FFF

    # print(FF100001)
    if not FF100001.empty:
        print("1")
        print(FF100001.contact[0])
        FFF2 = json.dumps(FF100001.contact[0])
        update_data(r_id,jin_huo_bian,FFF2,before_cheange_qty,row_type)

    else:
        print("2")
        PPO10001['r_id'] = generate_datetime_id()
        T100003 = PPO10001.to_dict(orient='records')
        FFF = json.dumps(T100003)
        put_data(product_id, jin_huo_bian, FFF,row_type)

    psycopg2_conn_insert_data_s.close()

    return "sucess"


class pre_order_n_get_Foo_delete(BaseModel):
    r_id : str
    product_id: int
    before_delete_qty : float



class pre_PPPP_delete(BaseModel):
    jin_huo_bian : int
    row_type : str
    data_jsonb : pre_order_n_get_Foo_delete

@router.delete("/order_in_judy_form", status_code = 200)  
def order_s_insert_delete(req_body : pre_PPPP_delete = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    datetime = str(generate_datetime())
    import json

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    jin_huo_bian = FFF['jin_huo_bian']
    row_type = FFF['row_type']
    jin_huo_bian
    product_id = FFF['data_jsonb']['product_id']
    before_delete_qty = FFF['data_jsonb']['before_delete_qty']
    r_id = FFF['data_jsonb']['r_id']

    def update_data(r_id,jin_huo_bian,FFF,before_delete_qty,row_type):
        sql_insert = """   WITH f AS
                        (
                        SELECT ('{'||index-1||'}')::text[] as path
                        FROM duty_car_in 
                        ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                        where contact ->> 'r_id' = %s and contact ->> 'product_qty' = '%s'
                        and jin_huo_bian = %s and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = %s) 

                        UPDATE duty_car_in
                        SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                        FROM f where jin_huo_bian = %s and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = %s  ;

                        """
        cur.execute(sql_insert, (r_id,before_delete_qty,jin_huo_bian,row_type, FFF, jin_huo_bian,row_type))

    def delete_data(product_id,jin_huo_bian,row_type):
        sql_insert = """   UPDATE duty_car_in set data_detail = t.js_new from (
            select jsonb_agg ((data_detail ->> (idx-1)::int)::jsonb) as js_new
            from duty_car_in cross join jsonb_array_elements(data_detail) with 
            ordinality arr(j,idx) where  j ->> 'product_id' not in ('0')  and jin_huo_bian = %s  and data_sub ->> 'row_type' = %s
        )t where jin_huo_bian = %s and data_sub ->> 'row_type' = %s;
                        """
        cur.execute(sql_insert, (jin_huo_bian,row_type,jin_huo_bian,row_type))


    def check_data(r_id,jin_huo_bian,row_type):
        sql_insert = """
                    SELECT *
                    FROM duty_car_in 
                    ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                    where contact ->> 'r_id' = '%s'
                    and jin_huo_bian = '%s' and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = '%s'
                    """ % (r_id, jin_huo_bian,row_type)

        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001  

        return  FF100001
    
    def check_data_empty(jin_huo_bian,row_type):
        sql_insert = """
                    SELECT data_detail
                    FROM duty_car_in 
                    where jin_huo_bian = '%s' and data_sub ->> 'status' = 'wait_sucess' and data_sub ->> 'row_type' = '%s'
                    """ % (jin_huo_bian,row_type)

        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001  

        return  FF100001

    def update_json(jin_huo_bian,row_type):
            sql_insert = """ UPDATE duty_car_in SET data_detail= '[]' ::jsonb WHERE jin_huo_bian = %s and data_sub ->> 'row_type' = %s ; """
            cur.execute(sql_insert, (jin_huo_bian,row_type))


    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    # print(r_id)
    FF100001 = check_data(r_id,jin_huo_bian,row_type)
    print(FF100001.contact)
    # print(FF100001.contact[0]['product_id'])
    # print(FF100001)

    # import json
    # PPO10001 = pd.DataFrame([FFF['data_jsonb']])
    # # PPO10001 = PPO10001.drop(columns=['parner_id'])
    # T100009 = PPO10001.to_dict(orient='records')[0]
    # # print(FFF)
    # FFF
    # print("1")
    # print(FF100001.contact[0])
    try:
        FF100001.contact[0]['product_id'] = 0 
        FFF2 = json.dumps(FF100001.contact[0])
        update_data(r_id,jin_huo_bian,FFF2,before_delete_qty,row_type)
    except:
        pass
    delete_data(0,jin_huo_bian,row_type)

    F10001 = check_data_empty(jin_huo_bian,row_type)
    print(F10001)
    if F10001['data_detail'][0] == None:
        print('yes')
        update_json(jin_huo_bian,row_type)
    psycopg2_conn_insert_data_s.close()
    return "sucess"





@router.get("/order_in_judy_group", status_code=200)
def order_in_judy():
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    
   

    def get_data():
        sql_insert = """ select jin_huo_bian,data_sub,data_detail from duty_car_in where data_sub ->> 'row_type' = 'for_duty' """ 
        OI10001 = pd.read_sql(sql_insert,psycopg2_conn_insert_data_s)
        OI10002 = OI10001.to_dict('records')
        OI10003 = pd.json_normalize(OI10002,'data_detail','jin_huo_bian')
        # OI10004 = OI10003.reindex(columns=['data_sub.jin_huo_bian','che_liang'])
        # OI10005 = OI10004.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
        OI10005 = OI10003.drop_duplicates()


        return  OI10005


    def get_data_api_product(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                     json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'product_id': str + '_id','product_idname': str+'_idname','product_mm_name': str+'_mmname','product_d_name': str+'_d_name'})
        # print(GF10001)

        return GF10001


    GF10001 = get_data()


    def max_date(GF10001):
        GF10001 = GF10001.replace(np.nan, 0)
        GF10001['count'] = 1
        # SD100001=GF10001.groupby(['count'])['datetime'].max().reset_index()
        GF10001 = GF10001['datetime'].drop_duplicates().reset_index()
        GF10001 = GF10001.sort_values(['datetime'], ascending=[True]).head(1).reset_index()
        print(GF10001)


        return GF10001['datetime'][0]
    print(GF10001)
    GF10001['round'] = 1
    GF10001['datetime'] = pd.to_datetime(GF10001['datetime']).dt.date
    GF10001['datetime'] = GF10001['datetime'].astype(str)
    SD100001=GF10001.groupby(['product_id','product_qty','datetime'])['round'].sum().reset_index()


    merge_data10003 = get_data_api_product(SD100001,"product")
    merge_data10004 = merge_data10003.merge(SD100001, left_on=['product_id'],right_on=['product_id'], how='outer')
    # merge_data10005 = merge_data10004.rename(columns={'judy_in': 'judy_in_id'})
    merge_data10004 = merge_data10004.sort_values(['datetime'], ascending=[True])

    loc_datetime = max_date(GF10001)
    print(loc_datetime)


    merge_data10005 = merge_data10004.loc[merge_data10004['datetime'] == loc_datetime]


    psycopg2_conn_insert_data_s.close()
    GF10002 = merge_data10005.to_json(orient='records')
    return json.loads(GF10002)



class order_in_judy_summit_(BaseModel):
    jin_huo_bian : int

@router.post("/order_in_judy_summit", status_code=200)
def order_in_judy_summit(req_body : order_in_judy_summit_ = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    
    def summit_sucess(jin_huo_bian,jsonb,status):
        sql_insert = """ UPDATE duty_car_in  
        SET data_sub=data_sub  || %s ::jsonb  
        where   jin_huo_bian = %s and data_sub ->> 'status' = %s"""
        cur.execute(sql_insert, (jsonb,jin_huo_bian,status))
        return

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    summit_sucess(str(req_body.jin_huo_bian),json.dumps({'status':'sucessing'}),'wait_sucess')


    summit_sucess(str(req_body.jin_huo_bian),json.dumps({'status':'sucess'}),'sucessing')

    psycopg2_conn_insert_data_s.close()
    
    return "sucess"



@router.post("/order_in_judy_summit/wait_sucess", status_code=200)
def order_in_judy_summit(req_body : order_in_judy_summit_ = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    
    def summit_sucess(jin_huo_bian,jsonb,status):
        sql_insert = """ UPDATE duty_car_in  
        SET data_sub=data_sub  || %s ::jsonb  
        where   jin_huo_bian = %s """
        cur.execute(sql_insert, (jsonb,jin_huo_bian))
        return

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    summit_sucess(str(req_body.jin_huo_bian),json.dumps({'status':'wait_sucess'}),'wait_sucess')


    # summit_sucess(str(req_body.jin_huo_bian),json.dumps({'status':'sucess'}),'sucessing')

    psycopg2_conn_insert_data_s.close()
    
    return "sucess"