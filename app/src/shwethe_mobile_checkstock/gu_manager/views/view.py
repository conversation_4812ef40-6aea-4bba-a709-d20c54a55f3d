from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_mobile_checkstock.database import get_session
from src.shwethe_mobile_checkstock.gu_manager.crud.crud import (
    create_gu_product,
    formScanCheckB,
    getBarcode200,
    getItemBarcode200,
    getData200,
)
from src.shwethe_mobile_checkstock.gu_manager.models.models import big_table_post, big_table_post200,big_table_read, big_table_read200

router = APIRouter()

@router.post("", response_model=big_table_read)
def create_a_hero(hero: big_table_post, db: Session = Depends(get_session)):

    return create_gu_product(hero=hero, db=db)



@router.post("/formScanCheck", response_model=big_table_read200)
def formScanCheck(hero: big_table_post200, db: Session = Depends(get_session)):
    return formScanCheckB(hero=hero, db=db)    


@router.get("/getBarcode")
def getBarcode(barcode_id: int, fen: int, db: Session = Depends(get_session)):
    return getBarcode200(barcode_id=barcode_id, fen=fen, db=db)


@router.get("/getItemBarcode")
def getItemBarcode(productID: str, db: Session = Depends(get_session)):
    return getItemBarcode200(productID=productID, db=db)  


@router.get("/get")
def getData(db: Session = Depends(get_session)):
    return getData200(db=db)   



