from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_miniapp_departScan.database import get_session
from src.shwethe_miniapp_departScan.src.crud.crud import (
    getBarcode200,
    checkDriver100,
    insertPersonForm100,
    departSend100
)
from src.shwethe_miniapp_departScan.src.models.models import insertDepart_read, insertDepart_post

router = APIRouter()


@router.get("/getBarcode")
def getBarcode(db: Session = Depends(get_session)):
    return getBarcode200(db=db)

@router.get("/checkDriver/{checkDriver}")
def checkDriver(checkDriver: str, db: Session = Depends(get_session)):
    return checkDriver100(checkDriver=checkDriver,db=db)

@router.post("/insertPersonForm")
def insertPersonForm(hero: insertDepart_post, db: Session = Depends(get_session)):
    return insertPersonForm100(hero=hero,db=db)

@router.get("/departSend")
def departSend(beforeDay: int, db: Session = Depends(get_session)):
    return departSend100(beforeDay=beforeDay, db=db)
