from typing import Dict
from xmlrpc.client import DateTime

from click import option
from pydantic.types import Optional, List
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel, Column, JSON
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id


class insertDepart_base(SQLModel):
    shu_riqi_datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    jia_yi_id: Optional[int]
    department_id: Optional[int]

class insertDepart(insertDepart_base, table=True):
    __tablename__ = "insertDepart"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    shu_riqi_datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    jia_yi_id: Optional[int]
    department_id: Optional[int]

class insertDepart_read(BaseModel):
    auto_id: Optional[int]
    shu_riqi_datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    jia_yi_id: Optional[int]
    department_id: Optional[int]

class insertDepart_post(BaseModel):
    shu_riqi_datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    jia_yi_id: Optional[int]
    department_id: Optional[int]



# class GetDay(BaseModel):
#     day = Optional[int]