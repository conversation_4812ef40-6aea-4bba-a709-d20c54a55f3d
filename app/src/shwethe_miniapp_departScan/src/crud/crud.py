from sqlite3 import dbapi2
import requests
from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,and_
from typing import List, Optional

from datetime import datetime
from src.shwethe_miniapp_departScan.database import get_session
from helper import generate_datetime_id
from src.shwethe_miniapp_departScan.src.models.models import insertDepart, insertDepart_post
import json
import pandas as pd
from src.time_zone.time_zone_function import get_datetime
from src.Connect.https_connect import mongodb_data_api
from src.Connect.postgresql_nern import real_backend_api, petrol_api

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def getBarcode200(db: Session = Depends(get_session)):
    
    try:
        heroesPersonal = db.exec(select(insertDepart)).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records)
        df = df.to_dict("records")
    except:
        df = []
    return df


def checkDriver100(checkDriver: str, db: Session = Depends(get_session)):
    # print(checkDriver)
    # print(type(checkDriver))

    try:
        checkDriver = int(checkDriver)
        print(checkDriver)
        print(type(checkDriver))

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = f'{real_backend_api}/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        # url = Arter_api + 'jia_yi_name_list_id'
        # body_raw = {"data_api": [{"jia_yi_id": 36557}]}
        body_raw = {"data_api": [{"jia_yi_id": checkDriver}]}
        # body_raw = {"data_api": df}
        df2 = requests.get(url=url, json=body_raw)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')
        # print(df2)
    except:
        print('The provided value is not an integer')
        print(checkDriver)
        print(type(checkDriver))

        # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={checkDriver}'
        url = f'{real_backend_api}/mongodb_data_api/api/v2/search/jia_yi_search_text?text={checkDriver}'
        # url = Arter_api + f'jia_yi_search_text?text={checkDriver}'
        df2 = requests.get(url=url)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')
        # print(df2)
    return df2


def insertPersonForm100(hero: insertDepart_post, db: Session = Depends(get_session)):
    
    hero_to_db = insertDepart.from_orm(hero)
    hero_to_db.shu_riqi_datetime = get_datetime()
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    
    return hero_to_db


def departSend100(beforeDay: int, db: Session = Depends(get_session)):
    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=beforeDay)
    timeS1 = str(week_ago)
    try:
        heroesPersonal = db.exec(select(insertDepart).where(insertDepart.shu_riqi_datetime > timeS1)).all()
    except:
        heroesPersonal = []
    return heroesPersonal