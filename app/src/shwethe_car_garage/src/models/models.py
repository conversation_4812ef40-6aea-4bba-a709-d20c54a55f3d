from xmlrpc.client import DateTime
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id

# 资料表名称 pre_order_product 和框架
class big_table_base(SQLModel):
    product_id: Optional[int]
    product_qty: Optional[int] 
    product_price: Optional[int] 
    jia_yi_fang_a: Optional[str]
    jia_yi_fang_b : Optional[int]
    bu_bian: Optional[int]
    shu_riqi_datetime: datetime
    riqi_datetime : datetime
    product_qtyp  : Optional[float]
    product_qtyn : Optional[float]
    bi_zhi : Optional[int]


class big_table_base200(SQLModel):
    # auto_id: Optional[int]
    product_id: Optional[int]
    product_qty: Optional[int] 
    product_price: Optional[int] 
    jia_yi_fang_a: Optional[str]
    jia_yi_fang_b : Optional[int]
    bu_bian: Optional[int]
    shu_riqi_datetime: datetime
    riqi_datetime : datetime
    product_qtyp  : Optional[float]
    product_qtyn : Optional[float]
    bi_zhi : Optional[int]    


class big_table(big_table_base, big_table_base200, table=True):
    __table_args__ = {'extend_existing': True}
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    product_id: Optional[int]
    product_qty: Optional[int] 
    product_price: Optional[int] 
    jia_yi_fang_a: Optional[str]
    jia_yi_fang_b : Optional[int]
    bu_bian: Optional[int]
    shu_riqi_datetime: datetime
    riqi_datetime : datetime
    product_qtyp  : Optional[float]
    product_qtyn : Optional[float]
    bi_zhi : Optional[int]


# 输入资料
class big_table_post(BaseModel):

    ke_bian: Optional[int]
    shu_riqi_datetime : Optional[datetime]
    riqi_datetime : Optional[datetime]
    product_id: Optional[int]
    class Config:
        schema_extra = {
            "example": {
                "ke_bian": 0,
                "shu_riqi_datetime": "2022-04-22 08:10:33.137",
                "riqi_datetime": "2022-04-22 08:10:33.137",
                "product_id": 1150,
            }
        }


# 输入资料
class big_table_read(big_table_base):

    auto_id: Optional[int]
   
# --------------------------------------------------------


# class big_table_post200(BaseModel): 
#     ke_bian: Optional[int]
#     shu_riqi_datetime : Optional[datetime]
#     riqi_datetime : Optional[datetime]
#     product_id: Optional[int]
#     product_qty: Optional[int] 
    
#     class Config:
#         schema_extra = {
#             "example": {

#                 "shu_riqi_datetime": "2022-04-22 08:10:33.137",
#                 "riqi_datetime": "2022-04-22 08:10:33.137",
#                 "product_id": 700,
#             }
#         }


class big_table_post200(BaseModel): 
    product_id: Optional[int]
    product_qty: Optional[int] 
    product_price: Optional[int] 
    jia_yi_fang_a: Optional[str]
    jia_yi_fang_b : Optional[int]
    bu_bian: Optional[int]
    shu_riqi_datetime: datetime
    riqi_datetime : datetime
    product_qtyp  : Optional[float]
    product_qtyn : Optional[float]
    bi_zhi : Optional[int]   
    
    class Config:
        schema_extra = {
            "example": {
                "product_id": 0,
                "product_qty": 0,
                "product_price": 0,
                "jia_yi_fang_a": 0,
                "jia_yi_fang_b": 0,
                "bu_bian": 0,
                "shu_riqi_datetime": "2022-04-22 08:10:33.137",
                "riqi_datetime": "2022-04-22 08:10:33.137",
                "product_qtyp": 0,
                "product_qtyn": 0,
                "bi_zhi": 138,
            }
        }


class big_table_read200(big_table_base200):
    
    auto_id: Optional[int]