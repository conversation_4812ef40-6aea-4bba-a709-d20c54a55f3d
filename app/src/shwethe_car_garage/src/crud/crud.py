from sqlite3 import dbapi2
import requests
from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,and_
from typing import List, Optional

from datetime import datetime
from src.shwethe_mobile_checkstock.database import get_session
from helper import generate_datetime_id
from src.shwethe_mobile_checkstock.gu_manager.models.models import big_table, big_table_base200,big_table_post,big_table_read
import json
import pandas as pd

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def create_gu_product(hero: big_table_read, db: Session = Depends(get_session)):

    hero.shu_riqi_datetime = str(datetime.now())
    hero.riqi_datetime = str(datetime.now())
    hero_to_db = big_table.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)

    return hero_to_db


def formScanCheckB(hero: big_table_base200, db: Session = Depends(get_session)):
    
    hero.shu_riqi_datetime = str(datetime.now())
    hero.riqi_datetime = str(datetime.now())
    hero_to_db = big_table.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)

    return hero_to_db


def getBarcode200(barcode_id: int, fen: int, db: Session = Depends(get_session)):
    barcode_id = barcode_id
    fen = fen
    url = 'http://192.168.1.11:8200/mongodb_data_api/api/v1/barcode_location?barcode_id={0}&fen={1}'.format(barcode_id, fen)
    df10 = requests.get(url=url).json()
    df10 = pd.DataFrame(df10)           
    return df10.to_dict('records')


def getItemBarcode200(productID: str, db: Session = Depends(get_session)):
    productID = productID
    url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_idname?ID={0}'.format(productID)
    df10 = requests.get(url=url).json()
    df10 = pd.DataFrame(df10)
    df10 = df10.to_dict('records') 
    return df10


def getData200(db: Session = Depends(get_session)):
    # sql = db.query(big_table.riqi_datetime, big_table.product_id).distinct(big_table.product_id).all()   
    sql = db.exec(select(big_table.riqi_datetime, big_table.product_id).order_by(big_table.product_id, big_table.riqi_datetime.desc()).distinct(big_table.product_id)).all()                    
    return sql


# async def indayDetails(color_components_id: int):

#     session = Session()
#     try:

#         df20 = df10
#         df20 = df20.rename(columns={"color_id": 'product_id'})
        
#         url = Arter_api + 'product_list_id'
#         to_dict = df20.to_dict('records')
#         body_raw = {"data_api": to_dict}
#         df30 = requests.get(url=url, json=body_raw).json()
#         df30 = pd.DataFrame(df30)
        
#         merge = df10.merge(df30, left_on='color_id', right_on='product_id', how='left')
#         merge = merge.to_dict('records')
#     except:
#         merge = []    
#     session.close()
#     return merge

# # 获取商品资料
# def get_tb_pos_body(auto_id : int ,db: Session = Depends(get_session)):
       
#     heroes = db.exec(select(tb_pos_body).where(tb_pos_body.tb_pos_head_id == auto_id)).all()
#     df = sqmodel_to_df(heroes)
#     df = df.sort_values(['shu_ri_qi'], ascending=[False])
#     df10004 = df.to_json(orient='records')
    
#     return json.loads(df10004)

# # 检查资料有没有商品资料 和 价钱是同一样吗
# def check_get_tb_pos_body(auto_id : int , product_id:int ,product_price:float ,db: Session = Depends(get_session)):
       
#     heroes = db.exec(select(tb_pos_body).where(and_(tb_pos_body.tb_pos_head_id == auto_id,
#     tb_pos_body.product_id == product_id,
#     tb_pos_body.product_price == product_price))).all()
    
#     return heroes


# def create_tb_pos_body(hero: tb_pos_body_read, db: Session = Depends(get_session)):
    
#     hero.shu_ri_qi = str(datetime.now())

#     if not check_get_tb_pos_body(hero.tb_pos_head_id,hero.product_id,hero.product_price,db):
#         hero_to_db = tb_pos_body.from_orm(hero)
#         db.add(hero_to_db)
#         db.commit()
#         db.refresh(hero_to_db)
#     else:
#         statement = select(tb_pos_body).where(and_(tb_pos_body.tb_pos_head_id == hero.tb_pos_head_id,
#         tb_pos_body.product_id == hero.product_id,
#         tb_pos_body.product_price == hero.product_price))
#         results = db.exec(statement).one()
#         results.product_qty = results.product_qty + 1
#         results.shu_ri_qi = str(datetime.now())
#         db.add(results)
#         db.commit()
#         db.refresh(results)

#     FD10001 = get_tb_pos_body(hero.tb_pos_head_id,db)

#     return FD10001

# def put_tb_pos_body(hero: tb_pos_body_read, db: Session = Depends(get_session)):
    
#     # hero.shu_ri_qi = str(datetime.now())

#     statement = select(tb_pos_body).where(and_(

#     tb_pos_body.auto_id == hero.auto_id

#     ))
#     results = db.exec(statement).one()
#     results.product_qty = hero.product_qty 
#     results.product_price = hero.product_price 
#     results.shu_ri_qi = str(datetime.now())
#     db.add(results)
#     db.commit()
#     db.refresh(results)

#     FD10001 = get_tb_pos_body(hero.tb_pos_head_id,db)

#     return FD10001


# def create_put_order_sub_product(hero: create_put_order_product_model, db: Session = Depends(get_session)):
#     # print(hero)
#     # hero.datetime = str(datetime.now())
#     # hero.orderProductSubId = generate_datetime_id()

#     statement = select(pre_order_product).where(pre_order_product.orderId == hero.orderId)
#     results = db.exec(statement).one()

#     print("results:", results)
#     print("hero:", hero)


#     results.lei = hero.lei
#     results.keBian = hero.keBian
#     results.statusCodeId = hero.statusCodeId
#     results.jiaYiId = hero.jiaYiId
#     results.dataSubName = hero.dataSubName


#     # hero_to_db = put_order_product.from_orm(hero)
#     db.add(results)
#     db.commit()
#     db.refresh(results)
    
#     return results


# def create_pre_order_sub_product(hero: pre_order_product_sub_model_read, db: Session = Depends(get_session)):
#     # print(hero)
#     hero.datetime = str(datetime.now())
#     hero.orderProductSubId = generate_datetime_id()
#     hero_to_db = pre_order_product_sub.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
    
#     return hero_to_db




# def get_pre_order_sub_product(orderId:str, db: Session = Depends(get_session)):
#     print(orderId)
#     # hero_to_db = pre_order_product_sub.from_orm(hero)

#     # db.add(hero_to_db)
#     # db.commit()
#     # db.refresh(hero_to_db)

#     # def read_hero(hero_id: int, db: Session = Depends(get_session)):
#     #     hero = db.get(Hero, hero_id)
#     # if not hero:
#     #     raise HTTPException(
#     #         status_code=status.HTTP_404_NOT_FOUND,
#     #         detail=f"Hero not found with id: {hero_id}",
#     #     )
#     # return hero
#     # heroes = db.exec(select(Hero).offset(offset).limit(limit)).all()
#     statement = select(pre_order_product_sub).where(pre_order_product_sub.orderId == orderId)
#     results = db.exec(statement).all()

#     for hero in results:
#         print(hero)
#     print(results)
#     print("results")
#     # hero = db.get(pre_order_product_sub_base, orderId)
#     if not results:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {orderId}",
#         )
        
#     return results
    


# def create_get_order_sub_product(orderId:str, hero: pre_order_product_sub_model_read, db: Session = Depends(get_session)):
#     # print(hero)
#     hero_to_db = pre_order_product_sub.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
    
#     return hero_to_db



# def getDataSubProduct(db: Session = Depends(get_session)):

#     heroes = db.exec(select(pre_order_product).offset(offset).limit(limit)).all()
    
#     return heroes


# def get_pre_order_list(db: Session = Depends(get_session)):
   
#     heroes = db.exec(select(pre_order_product)).all()

#     df = sqmodel_to_df(heroes)

#     df = df.sort_values(['datetime'], ascending=[False])

#     # df10001 = df.to_dict('records')

#     # df10002 = pd.json_normalize(df10001)

#     # df10003 = df10002.rename(columns={'dataSubName.jiaYiIdname': 'jiaYiIdname','dataSubName.jiaYiMmName': 'jiaYiMmName'
#     # ,'dataSubName.keBianIdname': 'keBianIdname','dataSubName.keBianMmName': 'keBianMmName'})
    

#     df10004 = df.to_json(orient='records')

    
#     return json.loads(df10004)