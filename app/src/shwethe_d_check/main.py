from fastapi import FastAP<PERSON>, APIRouter, Body, Response, BackgroundTasks, Header,HTTPException,Query
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np
from sqlmodel import Session,select,or_
router = APIRouter()



@router.get("/d_check/{warehourse_id}", status_code = 200)
def order_s_insert_list(warehourse_id: str):

    import src.Connect.postgresql_d_check as psycopg2_conn_d_check
    import importlib
    importlib.reload(psycopg2_conn_d_check)
    from src.Connect.postgresql_d_check import psycopg2_conn_d_check
    try:
        def get_data(warehourse_id):

            sql_insert = """
            select * from d_check_qty where datetime > current_date  and  warehouse_id = %s ORDER BY product_idname ASC;
            """ % (warehourse_id)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_d_check)

            return FF100001
        T00001 = get_data(warehourse_id)
        T00002 = T00001.to_json(orient='records')
        return json.loads(T00002)
    finally:
        psycopg2_conn_d_check.close()


class che_liang_A(BaseModel):
    auto_id : int
    product_qty : float

    
@router.put("/d_check/{warehourse_id}", status_code = 200)
def order_s_insert_list(warehourse_id: str,req_body : che_liang_A = Body(...)):

    import src.Connect.postgresql_d_check as psycopg2_conn_d_check
    import importlib
    importlib.reload(psycopg2_conn_d_check)
    from src.Connect.postgresql_d_check import psycopg2_conn_d_check


    req_body_ = req_body.json()
    FFF = json.loads(req_body_)


    try:
        psycopg2_conn_d_check.autocommit = True
        cur = psycopg2_conn_d_check.cursor()
        def update_data(auto_id,product_qty):
            sql_insert = """ update d_check_qty set product_check_qty = %s where auto_id = '%s'   """ 
            cur.execute(sql_insert, (product_qty,auto_id))
            return "aaaa"
        update_data(FFF['auto_id'],FFF['product_qty'])
        return "json.loads(GF10001)"


    finally:
        psycopg2_conn_d_check.close()
    


@router.get("/manager/d_check", status_code = 200)
def order_s_insert_list():

    import src.Connect.postgresql_d_check as psycopg2_conn_d_check
    import importlib
    importlib.reload(psycopg2_conn_d_check)
    from src.Connect.postgresql_d_check import psycopg2_conn_d_check


    # req_body_ = req_body.json()
    # FFF = json.loads(req_body_)


    try:
        psycopg2_conn_d_check.autocommit = True
        cur = psycopg2_conn_d_check.cursor()

        def get_data():

            DFG1001 = pd.read_sql(""" select auto_id,product_id,product_idname,p_qty as product_qty,datetime AT TIME ZONE 'MMT',product_check_qty,warehouse_id as jia_fang,product_mm_name,product_d_name from d_check_qty where datetime > current_date  """,psycopg2_conn_d_check)
            DFG1001 = DFG1001.replace(np.nan, 0)
            DFG1001.head()
            return DFG1001
        A100001 = get_data()    

        A100001['ANS'] = (A100001['product_qty'] - A100001['product_check_qty'])

        A100001 = A100001.loc[A100001['ANS'] != 0.0]
        A100001 = A100001.loc[A100001['ANS'] != 0]


        A100002 = A100001.to_json(orient='records')
        # update_data(FFF['auto_id'],FFF['product_qty'])
        return json.loads(A100002)


    finally:
        psycopg2_conn_d_check.close()