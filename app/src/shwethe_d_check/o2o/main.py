from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header,HTTPException,Query
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np
from sqlmodel import Session,select,or_
router = APIRouter()


class exchange_post(BaseModel):
    product_id : int
    jia_yi_fang_a : int
    jia_yi_fang_b : int
    product_qty : float


@router.post("/exchange", status_code = 200)
def order_s_insert_list(req_body : exchange_post = Body(...)):

    import src.Connect.postgresql_d_check as psycopg2_conn_d_check
    import importlib
    importlib.reload(psycopg2_conn_d_check)
    from src.Connect.postgresql_d_check import psycopg2_conn_d_check
    
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    datetime = str(generate_datetime())

    try:
        def summit_sucess(product_id,jia_yi_fang_a,jia_yi_fang_b,shu_riqi_datetime,riqi_datetime,json,product_qty):
            sql_insert = """ insert into  o2o_insert (product_id,jia_yi_fang_a,jia_yi_fang_b,lei_a,lei_b,shu_riqi_datetime,riqi_datetime,data_sub,product_qty) values (%s,%s,%s,22,22,%s,%s,%s,%s) """
            cur.execute(sql_insert, (product_id,jia_yi_fang_a,jia_yi_fang_b,shu_riqi_datetime,riqi_datetime,json,product_qty))

        psycopg2_conn_d_check.autocommit = True
        cur = psycopg2_conn_d_check.cursor()
        summit_sucess(req_body.product_id,req_body.jia_yi_fang_a,req_body.jia_yi_fang_b,datetime,datetime,json.dumps({'status':'wait_success'}),req_body.product_qty)

        return "json.loads(T00002)"
    finally:
        psycopg2_conn_d_check.close()


@router.get("/exchange_list/{jia_yi_fang}", status_code = 200)
def exchange_list(jia_yi_fang:int):

    import src.Connect.postgresql_d_check as psycopg2_conn_d_check
    import importlib
    importlib.reload(psycopg2_conn_d_check)
    from src.Connect.postgresql_d_check import psycopg2_conn_d_check
    from src.Connect.https_connect import mongodb_data_api
    
    try:

        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            print(res_1)
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})

            GF10001 = pd.DataFrame(json.loads(r.json()))

            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            # print(GF10001)

            return GF10001

        def get_data_jia_yi_name(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))

            r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                        json={"data_api": res_1})

            GF10001 = pd.DataFrame(json.loads(r.json()))

            GF10001 = GF10001.rename(columns={'jia_yi_id': str+'_id','jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mm_name'})
            print(GF10001)

            return GF10001


        def get_data(jia_yi_fang):
            A100001 = (''' select product_id,product_qty,jia_yi_fang_b,data_sub from o2o_insert where shu_riqi_datetime > current_date -2  and jia_yi_fang_a = %s ''' % (jia_yi_fang) )
            A100001 = pd.read_sql(A100001, psycopg2_conn_d_check)
            return A100001
        
        try:
            HG10001 = get_data(jia_yi_fang)

            HG20001 = get_data_api(HG10001,"product")




            CC100001 = HG10001.merge(HG20001,
                                    left_on=['product_id'],right_on = ['product_id'], how='inner')

            HG30001 = CC100001.rename(columns={'jia_yi_fang_b': 'jia_yi_id'})
            print(HG30001)
            HG30001 = get_data_jia_yi_name(HG30001,"jia_yi_fang_b")

            
            print("HG30001.info()")
            print(HG30001.info())
            
            print("CC100001.info()")
            print(CC100001.info())

            CC100001 = CC100001.merge(HG30001,
                                    left_on=['jia_yi_fang_b'],right_on = ['jia_yi_fang_b_id'], how='outer')

            CC100001 = CC100001.replace(np.nan, 0)


            HG10001 = CC100001.to_json(orient='records')

            return json.loads(HG10001)
        except:
            return []
    finally:
        psycopg2_conn_d_check.close()


@router.get("/exchange_recipient_list/{jia_yi_fang}", status_code = 200)
def exchange_list(jia_yi_fang:int):

    import src.Connect.postgresql_d_check as psycopg2_conn_d_check
    import importlib
    importlib.reload(psycopg2_conn_d_check)
    from src.Connect.postgresql_d_check import psycopg2_conn_d_check
    from src.Connect.https_connect import mongodb_data_api
    
    try:

        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            print(res_1)
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})

            GF10001 = pd.DataFrame(json.loads(r.json()))

            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            # print(GF10001)

            return GF10001

        def get_data_jia_yi_name(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))

            r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                        json={"data_api": res_1})

            GF10001 = pd.DataFrame(json.loads(r.json()))

            GF10001 = GF10001.rename(columns={'jia_yi_id': str+'_id','jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mm_name'})
            print(GF10001)

            return GF10001


        def get_data(jia_yi_fang):
            A100001 = (''' select auto_id,product_id,product_qty,jia_yi_fang_b,data_sub from o2o_insert where shu_riqi_datetime > current_date -2 and jia_yi_fang_b = %s ''' % (jia_yi_fang) )
            A100001 = pd.read_sql(A100001, psycopg2_conn_d_check)
            return A100001
        
        try:
            HG10001 = get_data(jia_yi_fang)

            HG20001 = get_data_api(HG10001,"product")




            CC100001 = HG10001.merge(HG20001,
                                    left_on=['product_id'],right_on = ['product_id'], how='inner')

            HG30001 = CC100001.rename(columns={'jia_yi_fang_b': 'jia_yi_id'})
            print(HG30001)
            HG30001 = get_data_jia_yi_name(HG30001,"jia_yi_fang_b")

            
            print("HG30001.info()")
            print(HG30001.info())
            
            print("CC100001.info()")
            print(CC100001.info())

            CC100001 = CC100001.merge(HG30001,
                                    left_on=['jia_yi_fang_b'],right_on = ['jia_yi_fang_b_id'], how='outer')

            CC100001 = CC100001.replace(np.nan, 0)


            HG10001 = CC100001.to_json(orient='records')

            return json.loads(HG10001)
        except:
            return []
    finally:
        psycopg2_conn_d_check.close()

class exchange_recipient_list_put(BaseModel):
    auto_id : int

@router.put("/exchange_recipient_list/{jia_yi_fang}", status_code = 200)
def exchange_recipient_list(req_body : exchange_recipient_list_put = Body(...)):

    import src.Connect.postgresql_d_check as psycopg2_conn_d_check
    import importlib
    importlib.reload(psycopg2_conn_d_check)
    from src.Connect.postgresql_d_check import psycopg2_conn_d_check
    from src.Connect.https_connect import mongodb_data_api

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)


    try:

        def put_data(auto_id,json):
            sql_insert = """
                            UPDATE o2o_insert
                            SET data_sub=data_sub  || %s ::jsonb
                            where auto_id = %s ;

                            """
            cur.execute(sql_insert, ( json, auto_id))
        psycopg2_conn_d_check.autocommit = True
        cur = psycopg2_conn_d_check.cursor()
        
        try:
            

            put_data(FFF['auto_id'],json.dumps({'status':'success'}))
            # HG10001 = CC100001.to_json(orient='records')
            return "json.loads(HG10001)"
        except:
            return False
    finally:
        psycopg2_conn_d_check.close()