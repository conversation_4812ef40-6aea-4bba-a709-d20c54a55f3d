from fastapi import FastAPI, APIRouter, Body, Response,BackgroundTasks,Header
from pydantic import BaseModel
from helper import generate_id,generate_datetime
from typing import List,Optional
import pandas as pd
import requests

import json
import time
router = APIRouter()


@router.get("/jia_yi_search_text", status_code = 200)
def product_search_text(text : str):

    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(https_connect)
    from src.Connect.https_connect import mongodb_data_api
    params = {'text':text}
    try:
        r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_search_text',params=params
                            )

        GF10001 = pd.DataFrame(r.json())
        GF10001 = GF10001.to_json(orient='records')
    except:
        GF10001 = False
    
    return json.loads(GF10001)

@router.get("/product_search_text", status_code = 200)
def product_search_text(text : str):

    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(https_connect)
    from src.Connect.https_connect import mongodb_data_api
    params = {'text':text}
    try:
        r = requests.get(mongodb_data_api + '/api/v1/search/product_search_text',params=params
                            )

        GF10001 = pd.DataFrame(r.json())
        GF10001 = GF10001.to_json(orient='records')
        print(GF10001)
    except:
        GF10001 = False
    
    return json.loads(GF10001)

@router.get("/product_search/{product_id}", status_code = 200)
def product_search(product_id : int):

    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(https_connect)
    from src.Connect.https_connect import mongodb_data_api
    params = {'ID':product_id}
    try:
        r = requests.get(mongodb_data_api + '/api/v1/search/product_id',params=params
                            )
        print(r.json())
        GF10001 = pd.DataFrame(r.json())
        GF10001 = GF10001.to_json(orient='records',lines=True)
        return json.loads(GF10001)
    except:
        GF10001 = False
        return GF10001
    