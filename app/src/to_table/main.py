from fastapi import Fast<PERSON><PERSON>, APIRouter, Body, Response, BackgroundTasks, Header,HTTPException
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np
router = APIRouter()



@router.post("/to_table_order_s_insert", status_code = 200)
def order_s_insert_list():
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import shwethe_mysql_api
    def check_data_x():
    
        sql_insert = """
                            SELECT che_liang,order_s_insert_id,jia_fang,data_detail,data_sub,datetime
                            FROM order_s_insert  where  data_sub ->> 'status' = 'sucess' and data_sub ->> 'row_type' = 'for_product' and datetime > NOW() - INTERVAL '15 DAY' ;
                            """
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001
        FF100001 = FF100001.to_dict('records')
        FF100001 = pd.json_normalize(FF100001)
        FF100002 = FF100001.reindex(columns=['data_sub.bi_zhi','data_sub.jin_huo_bian','data_sub.fen_dian_id','data_detail','datetime','jia_fang','order_s_insert_id','che_liang'])
        FF100003 = FF100002.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian','data_sub.fen_dian_id': 'fen_dian_id' })
        FF100004 = FF100003.to_dict('records')
        FF100005 = pd.json_normalize(FF100004,'data_detail',['jin_huo_bian','datetime','fen_dian_id','jia_fang','order_s_insert_id','che_liang'])
        FF100006 = FF100005.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' })

        return  FF100005

    def check_data_y():
        
        sql_insert = """
                            SELECT data_detail,data_sub,datetime
                            FROM order_s_d_insert  where  datetime > NOW() - INTERVAL '15 DAY' ;
                            """
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001
        FF100001 = FF100001.to_dict('records')
        FF100001 = pd.json_normalize(FF100001)
        FF100002 = FF100001.reindex(columns=['data_sub.jin_huo_bian','data_sub.fen_dian_id','data_detail','datetime'])
        FF100003 = FF100002.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian','data_sub.fen_dian_id': 'fen_dian_id' })
        FF100004 = FF100003.to_dict('records')
        FF100005 = pd.json_normalize(FF100004,'data_detail',['jin_huo_bian','datetime','fen_dian_id'])
        FF100006 = FF100005.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' })

        return  FF100005


    def mer_data(data_x,data_y):
        OI10001 = data_x.merge(data_y, on=['jin_huo_bian','fen_dian_id','product_id'], how='inner', indicator=True)

        T100004 = OI10001

        T100005 = T100004.drop(columns=['_merge'])
        return T100005


    def mer_data_b(data_x,data_y):
        T100005 = pd.concat([data_x,data_y])
        return T100005


    FF100005 = check_data_x()
    FF100005

    FF200005 = check_data_y()
    FF200005

    FF300001 = mer_data(FF100005,FF200005)

    FF300001['b_id'] = 0
    FF300001['line'] = 378
    FF300001['kind'] = 1595152
    FF300001['bi_zhi'] = FF300001['bi_zhi'].replace(np.nan, 138)
    # FF300001['bi_zhi'] = 138
    # FF300001['che_ci']  = FF300001['order_s_insert_id']

    FF300002 = FF300001[['order_s_insert_id','b_id','product_id','product_qty_x','product_price','datetime_x','jia_fang','type_user_jia_yi','lei_a','lei_b','che_liang','line','kind','bi_zhi','s_bill_id','jin_huo_bian','order_s_insert_id']]
    FF300002.columns = ['order_s_insert_id','b_id','product_id','product_qty_x','product_price','datetime_x','jia_fang','type_user_jia_yi','lei_a','lei_b','che_liang','line','kind','bi_zhi','s_bill_id','jin_huo_bian','che_ci']

    FF300003 = FF300002.rename(
        columns={'order_s_insert_id': 'a_id', 'product_qty_x': 'product_qty','type_user_jia_yi':'jia_yi_fang_b'
                 ,'datetime_x':'datetime','jia_fang':'jia_yi_fang_a','s_bill_id':'jin_huo_dang','product_price':'product_price_x'})

    FF300003['product_price_y'] = 0
    FF300003['che_ci'] = 1
    FF300003 = FF300003[['a_id','b_id','product_id','product_qty','product_price_x','product_price_y','datetime','jia_yi_fang_a','jia_yi_fang_b','lei_a','lei_b','line','che_liang','kind','bi_zhi','jin_huo_dang','jin_huo_bian','che_ci']]
    FF300003['product_qty'] = (FF300003['product_qty'] * -1)
    
    FF300003['datetime'] = FF300003['datetime'].astype(str)
    FF300003['jin_huo_bian'] = FF300003['jin_huo_bian'].astype(int)
    FF300003['jia_yi_fang_a'] = FF300003['jia_yi_fang_a'].astype(int)
    FF300003['che_liang'] = FF300003['che_liang'].astype(int)
    FF300003['a_id'] = FF300003['a_id'].astype(int)
    FF300003['product_price_yy'] = FF300003['product_price_y'].astype(float)
    FF300003

    mata = {
    'data' : FF300003.to_dict(orient='records')
    }
    psycopg2_conn_insert_data_s.close()

    r = requests.post(shwethe_mysql_api + '/api/v1/table/mysql', data= json.dumps(mata))

    if r.status_code == 200:
        return "sucess"
    else:
        raise HTTPException(status_code=404, detail="Item not found")

@router.post("/to_table_order_s_insert_for_car_cost", status_code = 200)
def order_s_insert_list():
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import shwethe_mysql_api
    def check_data():
    
        sql_insert = """
                            SELECT che_liang,order_s_insert_id,jia_fang,data_detail,data_sub,datetime
                            FROM order_s_insert  where  data_sub ->> 'status' = 'sucess' and data_sub ->> 'row_type' = 'for_car_cost' and datetime > NOW() - INTERVAL '30 DAY' ;
                            """
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001
        FF100001 = FF100001.to_dict('records')
        FF100001 = pd.json_normalize(FF100001)
        FF100002 = FF100001.reindex(columns=['data_sub.bi_zhi','data_sub.jin_huo_bian','data_sub.fen_dian_id','data_detail','datetime','jia_fang','order_s_insert_id','che_liang'])
        FF100003 = FF100002.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian','data_sub.fen_dian_id': 'fen_dian_id' })
        FF100004 = FF100003.to_dict('records')
        FF100005 = pd.json_normalize(FF100004,'data_detail',['jin_huo_bian','datetime','fen_dian_id','jia_fang','order_s_insert_id','che_liang'])
        FF100006 = FF100005.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' })

        return  FF100005



    # FF100005 = check_data_x()
    # FF100005

    # FF200005 = check_data_y()
    # FF200005

    # FF300001 = mer_data(FF100005,FF200005)


    FF300001 = check_data()

    FF300001['b_id'] = 0
    FF300001['line'] = 378
    FF300001['kind'] = 1595152
    FF300001['bi_zhi'] = FF300001['bi_zhi'].replace(np.nan, 138)
    # FF300001['bi_zhi'] = 138
    # FF300001['che_ci']  = FF300001['order_s_insert_id']

    FF300002 = FF300001[['order_s_insert_id','b_id','product_id','product_qty','product_price','datetime','jia_fang','jia_fang','jia_fang','lei_a','lei_b','che_liang','line','kind','bi_zhi','s_bill_id','jin_huo_bian','order_s_insert_id']]
    FF300002.columns = ['order_s_insert_id','b_id','product_id','product_qty_x','product_price','datetime_x','jia_fang','type_user_jia_yi','ke_bian','lei_a','lei_b','che_liang','line','kind','bi_zhi','s_bill_id','jin_huo_bian','che_ci']

    FF300003 = FF300002.rename(
        columns={'order_s_insert_id': 'a_id', 'product_qty_x': 'product_qty','type_user_jia_yi':'jia_yi_fang_b'
                 ,'datetime_x':'datetime','jia_fang':'jia_yi_fang_a','s_bill_id':'jin_huo_dang','product_price':'product_price_x'})
    FF300003['che_ci'] = 1
    FF300003['product_price_y'] = 0
    FF300003 = FF300003[['a_id','b_id','product_id','product_qty','product_price_x','product_price_y','datetime','jia_yi_fang_a','jia_yi_fang_b','lei_a','lei_b','line','che_liang','kind','bi_zhi','jin_huo_dang','jin_huo_bian','che_ci','ke_bian']]
    FF300003['product_qty'] = (FF300003['product_qty'] * -1)
    FF300003['datetime'] = FF300003['datetime'].astype(str)
    FF300003['jin_huo_bian'] = FF300003['jin_huo_bian'].astype(int)
    FF300003['jia_yi_fang_a'] = FF300003['jia_yi_fang_a'].astype(int)
    FF300003['che_liang'] = FF300003['che_liang'].astype(int)
    FF300003['a_id'] = FF300003['a_id'].astype(int)
    FF300003['product_price_yy'] = FF300003['product_price_y'].astype(float)
    FF300003

    mata = {
    'data' : FF300003.to_dict(orient='records')
    }
    psycopg2_conn_insert_data_s.close()

    r = requests.post(shwethe_mysql_api + '/api/v1/table/mysql', data= json.dumps(mata))

    if r.status_code == 200:
        return "sucess"
    else:
        raise HTTPException(status_code=404, detail="Item not found")

@router.post("/to_table_order_s_insert_for_car_cost", status_code = 200)
def order_s_insert_list():
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import shwethe_mysql_api
    def check_data():
    
        sql_insert = """
                            SELECT che_liang,order_s_insert_id,jia_fang,data_detail,data_sub,datetime
                            FROM order_s_insert  where  data_sub ->> 'status' = 'sucess' and data_sub ->> 'row_type' = 'for_car_cost' and datetime > NOW() - INTERVAL '15 DAY' ;
                            """
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001
        FF100001 = FF100001.to_dict('records')
        FF100001 = pd.json_normalize(FF100001)
        FF100002 = FF100001.reindex(columns=['data_sub.bi_zhi','data_sub.jin_huo_bian','data_sub.fen_dian_id','data_detail','datetime','jia_fang','order_s_insert_id','che_liang'])
        FF100003 = FF100002.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian','data_sub.fen_dian_id': 'fen_dian_id' })
        FF100004 = FF100003.to_dict('records')
        FF100005 = pd.json_normalize(FF100004,'data_detail',['jin_huo_bian','datetime','fen_dian_id','jia_fang','order_s_insert_id','che_liang'])
        FF100006 = FF100005.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' })

        return  FF100005



    # FF100005 = check_data_x()
    # FF100005

    # FF200005 = check_data_y()
    # FF200005

    # FF300001 = mer_data(FF100005,FF200005)


    FF300001 = check_data()

    FF300001['b_id'] = 0
    FF300001['line'] = 378
    FF300001['kind'] = 1595152
    FF300001['bi_zhi'] = FF300001['bi_zhi'].replace(np.nan, 138)
    # FF300001['bi_zhi'] = 138
    # FF300001['che_ci']  = FF300001['order_s_insert_id']

    FF300002 = FF300001[['order_s_insert_id','b_id','product_id','product_qty','product_price','datetime','jia_fang','jia_fang','jia_fang','lei_a','lei_b','che_liang','line','kind','bi_zhi','s_bill_id','jin_huo_bian','order_s_insert_id']]
    FF300002.columns = ['order_s_insert_id','b_id','product_id','product_qty_x','product_price','datetime_x','jia_fang','type_user_jia_yi','ke_bian','lei_a','lei_b','che_liang','line','kind','bi_zhi','s_bill_id','jin_huo_bian','che_ci']

    FF300003 = FF300002.rename(
        columns={'order_s_insert_id': 'a_id', 'product_qty_x': 'product_qty','type_user_jia_yi':'jia_yi_fang_b'
                 ,'datetime_x':'datetime','jia_fang':'jia_yi_fang_a','s_bill_id':'jin_huo_dang','product_price':'product_price_x'})
    FF300003['che_ci'] = 1
    FF300003['product_price_y'] = 0
    FF300003 = FF300003[['a_id','b_id','product_id','product_qty','product_price_x','product_price_y','datetime','jia_yi_fang_a','jia_yi_fang_b','lei_a','lei_b','line','che_liang','kind','bi_zhi','jin_huo_dang','jin_huo_bian','che_ci','ke_bian']]
    FF300003['datetime'] = FF300003['datetime'].astype(str)
    FF300003['jin_huo_bian'] = FF300003['jin_huo_bian'].astype(int)
    FF300003['jia_yi_fang_a'] = FF300003['jia_yi_fang_a'].astype(int)
    FF300003['che_liang'] = FF300003['che_liang'].astype(int)
    FF300003['a_id'] = FF300003['a_id'].astype(int)
    FF300003['product_price_yy'] = FF300003['product_price_y'].astype(float)
    FF300003

    mata = {
    'data' : FF300003.to_dict(orient='records')
    }
    psycopg2_conn_insert_data_s.close()

    r = requests.post(shwethe_mysql_api + '/api/v1/table/mysql', data= json.dumps(mata))

    if r.status_code == 200:
        return "sucess"
    else:
        raise HTTPException(status_code=404, detail="Item not found")



@router.post("/to_table_for_juty_in", status_code = 200)
def order_s_insert_list():
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import sql2000

    try:
        def check_data_x():
            sql_insert = """
                            SELECT data_detail,data_sub,jin_huo_bian
                            FROM duty_car_in  where  data_sub ->> 'status' = 'sucess'
                            and shu_ri_qi > NOW() - INTERVAL '14 DAY';
                            """
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
            FF100001
            FF100001 = FF100001.to_dict('records')
            FF100001 = pd.json_normalize(FF100001)
            FF100002 = FF100001.reindex(columns=['data_sub.row_type','jin_huo_bian','data_sub.fen_dian_id','data_detail','datetime','jia_fang','order_s_insert_id','che_liang'])
            FF100003 = FF100002.rename(columns={'data_sub.row_type': 'row_type','data_sub.fen_dian_id': 'fen_dian_id' })
            FF100004 = FF100003.to_dict('records')
            FF100005 = pd.json_normalize(FF100004,'data_detail',['row_type','jin_huo_bian','fen_dian_id','jia_fang','order_s_insert_id'])
        #     FF100006 = FF100005\.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' })
            return  FF100005

        def check_data_y():
            
            sql_insert = """
                                SELECT order_s_insert_id,che_liang,data_detail,data_sub,datetime
                                FROM order_s_insert  where  datetime < NOW() - INTERVAL '300 DAY'and data_sub ->> 'row_type' = 'for_product' ;
                                """
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
            FF100001
            FF100001 = FF100001.to_dict('records')
            FF100001 = pd.json_normalize(FF100001)
            FF100002 = FF100001.reindex(columns=['order_s_insert_id','che_liang','data_sub.jin_huo_bian','data_sub.fen_dian_id','data_detail','datetime'])
            FF100003 = FF100002.rename(columns={'order_s_insert_id': 'che_ci','data_sub.jin_huo_bian': 'jin_huo_bian','data_sub.fen_dian_id': 'fen_dian_id' })
            FF100003['che_ci'] = 1
            FF100004 = FF100003.to_dict('records')
            FF100005 = pd.json_normalize(FF100004,'data_detail',['jin_huo_bian','datetime','fen_dian_id','che_liang','che_ci'])
            FF100006 = FF100005.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian','datetime': 'risi'})
            FF100006 = FF100006[['che_liang','jin_huo_bian','risi','che_ci']]
            FF100006['risi'] =FF100006['risi'].astype(str)
            return  FF100006


        def check_data_y():
                
            sql_insert = """
                                SELECT order_s_insert_id,che_liang,data_detail,data_sub,datetime
                                FROM order_s_insert  where  datetime < NOW() - INTERVAL '300 DAY'and data_sub ->> 'row_type' = 'for_product' ;
                                """
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
            FF100001
            FF100001 = FF100001.to_dict('records')
            FF100001 = pd.json_normalize(FF100001)
            FF100002 = FF100001.reindex(columns=['order_s_insert_id','che_liang','data_sub.jin_huo_bian','data_sub.fen_dian_id','data_detail','datetime'])
            FF100003 = FF100002.rename(columns={'order_s_insert_id': 'che_ci','data_sub.jin_huo_bian': 'jin_huo_bian','data_sub.fen_dian_id': 'fen_dian_id' })
            FF100003['che_ci'] = 1
            FF100004 = FF100003.to_dict('records')
            FF100005 = pd.json_normalize(FF100004,'data_detail',['jin_huo_bian','datetime','fen_dian_id','che_liang','che_ci'])
            FF100006 = FF100005.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian','datetime': 'risi'})
            FF100006 = FF100006[['che_liang','jin_huo_bian','risi','che_ci']]
            FF100006['risi'] =FF100006['risi'].astype(str)

            return  FF100006

        def check_data_y_y():
            sql_insert = """ select jin_huo_bian,che_liang,status_code_id,shu_ri_qi from order_head_in where  status_code_id = 200 and shu_ri_qi > current_date -7 """ 
            OI10001 = pd.read_sql(sql_insert,psycopg2_conn_insert_data_s)

            OI10001['che_ci'] = 1

            FF100006 = OI10001.rename(columns={'shu_ri_qi': 'risi'})
            FF100006 = FF100006[['che_liang','jin_huo_bian','risi','che_ci']]

            # mask = (OI10001['status_code_id']==200)
            # OI10001.loc[mask, 'status'] = "sucess"


            # OI10002 = OI10001.to_dict('records')
            # OI10003 = pd.json_normalize(OI10002)
            # OI10004 = OI10003.reindex(columns=['data_sub.jin_huo_bian','che_liang'])
            # OI10005 = OI10001.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
            OI10005 = FF100006.drop_duplicates()
            return  OI10005


        def mer_data(data_x,data_y):
            OI10001 = data_x.merge(data_y, on=['jin_huo_bian'], how='inner')

            T100004 = OI10001

        #     T100005 = T100004.drop(columns=['_merge'])
            return T100004

        AS100001 = check_data_y().drop_duplicates()
        AS100009 = check_data_y_y().drop_duplicates()

        # AS100001 = pd.concat([AS100009])
        AS100001 = pd.concat([AS100001,AS100009])
        
        AS100001.head()


        VC100001 = check_data_x()
        HG100001 = mer_data(VC100001,AS100001)
        VC100001 = HG100001
        VC100001


        VC100001['shu_riqi'] = pd.to_datetime(VC100001['datetime']).dt.date
        VC100001['date_now'] = pd.to_datetime('today').normalize() - pd.DateOffset(15)
        VC100001['date_now'] = pd.to_datetime(VC100001['date_now']).dt.date
        VC100001.info()


        VC100001['riqi'] = pd.to_datetime(VC100001['risi']).dt.date
        VC100001


        for_duty_10001 = VC100001.loc[VC100001['row_type'] == 'for_duty']
        for_duty_10001['time'] = (for_duty_10001['shu_riqi'] > for_duty_10001['date_now'])
        for_duty_10002 = for_duty_10001.loc[for_duty_10001['time'] == True]
        for_duty_10002.info()


        GF10001 = for_duty_10002[['jin_huo_bian','registration_selie','registration_id','product_id','product_qty','jia_yi_fang_a','shu_riqi','product_price','che_liang','risi','che_ci']]
        GF10001['jin_huo_bian'] = GF10001['jin_huo_bian'].astype(int)
        GF10001['registration_selie'] = GF10001['registration_selie'].astype(int)
        GF10001['registration_id'] = GF10001['registration_id'].astype(int)
        GF10001['shu_riqi'] = GF10001['shu_riqi'].astype(str)
        GF10001.info()


        GF10001
        GF10001['riqi'] = pd.to_datetime(GF10001['risi']).dt.date
        GF10001['shu_riqi'] = pd.to_datetime(GF10001['shu_riqi']).dt.date
        GF10001['riqi'] = GF10001['riqi'].astype(str)
        GF10001['risi'] = GF10001['risi'].astype(str)
        GF10001['shu_riqi'] = GF10001['shu_riqi'].astype(str)

        print(GF10001.info())
        print(GF10001)
        print("GF10001.info()")


        # print(GF10001.loc[GF10001['jin_huo_bian'] == 76162])
        # print(sql2000.info())

        mata = {
        'data' : GF10001.to_dict(orient='records')
        }
        print(mata)

        r = requests.post(sql2000 + '/api/v1/qty/judy_in/judy_in', data= json.dumps(mata))
        

        for_duty_cost_10001 = VC100001.loc[VC100001['row_type'] == 'for_duty_cost']
        for_duty_cost_10001.head()

        if r.status_code == 200:
            return "sucess"
        else:
            raise HTTPException(status_code=404, detail="Item not found")

    finally:
        psycopg2_conn_insert_data_s.close()


@router.post("/to_table_for_duty_cost", status_code = 200)
def order_s_insert_list():
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import shwethe_mysql_api
    
    def check_data_x():
        sql_insert = """
                            SELECT data_detail,data_sub,jin_huo_bian,auto_id
                            FROM duty_car_in  where  data_sub ->> 'status' = 'sucess' and data_sub ->> 'row_type' = 'for_duty_cost' 
                            and shu_ri_qi > NOW() - INTERVAL '30 DAY';

                            """
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001
        FF100001 = FF100001.to_dict('records')
        FF100001 = pd.json_normalize(FF100001)
        FF100002 = FF100001.reindex(columns=['auto_id','data_sub.row_type','jin_huo_bian','data_sub.fen_dian_id','data_detail','datetime','jia_fang','order_s_insert_id','che_liang'])
        FF100003 = FF100002.rename(columns={'data_sub.row_type': 'row_type','data_sub.fen_dian_id': 'fen_dian_id' })
        FF100004 = FF100003.to_dict('records')
        FF100005 = pd.json_normalize(FF100004,'data_detail',['auto_id','row_type','jin_huo_bian','fen_dian_id','jia_fang','order_s_insert_id'])
    #     FF100006 = FF100005\.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' })
        return  FF100005

    def check_data_y():
        
        sql_insert = """
                            SELECT order_s_insert_id,che_liang,data_detail,data_sub,datetime
                            FROM order_s_insert  where  datetime < NOW() - INTERVAL '300 DAY' and data_sub ->> 'row_type' = 'for_product' ;
                            """
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001
        FF100001 = FF100001.to_dict('records')
        FF100001 = pd.json_normalize(FF100001)
        FF100002 = FF100001.reindex(columns=['order_s_insert_id','che_liang','data_sub.jin_huo_bian','data_sub.fen_dian_id','data_detail','datetime'])
        FF100003 = FF100002.rename(columns={'order_s_insert_id': 'che_ci','data_sub.jin_huo_bian': 'jin_huo_bian','data_sub.fen_dian_id': 'fen_dian_id' })
        FF100003['che_ci'] = 1
        FF100004 = FF100003.to_dict('records')
        FF100005 = pd.json_normalize(FF100004,'data_detail',['jin_huo_bian','datetime','fen_dian_id','che_liang','che_ci'])
        FF100006 = FF100005.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian'})
        FF100006 = FF100006[['che_liang','jin_huo_bian','che_ci']]
        
        return  FF100006

    def check_data_y_y():
            sql_insert = """ select jin_huo_bian,che_liang,status_code_id,shu_ri_qi from order_head_in where  status_code_id = 200 and shu_ri_qi > current_date -7 """ 
            OI10001 = pd.read_sql(sql_insert,psycopg2_conn_insert_data_s)

            OI10001['che_ci'] = 1

            FF100006 = OI10001.rename(columns={'shu_ri_qi': 'risi'})
            FF100006 = FF100006[['che_liang','jin_huo_bian','che_ci']]
            OI10005 = FF100006.drop_duplicates()

            return  OI10005

    def mer_data(data_x,data_y):
        OI10001 = data_x.merge(data_y, on=['jin_huo_bian'], how='inner')

        T100004 = OI10001

        #     T100005 = T100004.drop(columns=['_merge'])
        return T100004


    AS100001 = check_data_y().drop_duplicates()
    AS100009 = check_data_y_y().drop_duplicates()

    AS100001 = pd.concat([AS100001,AS100009])
    # AS100001 = pd.concat([AS100009])


    VC100001 = check_data_x()


    HG100001 = mer_data(VC100001,AS100001)
    VC100001 = HG100001
    VC100001


    VC100001['shu_riqi'] = pd.to_datetime(VC100001['datetime']).dt.date
    VC100001['date_now'] = pd.to_datetime('today').normalize() - pd.DateOffset(15)
    VC100001['date_now'] = pd.to_datetime(VC100001['date_now']).dt.date
    VC100001.head()


    for_duty_10001 = VC100001.loc[VC100001['row_type'] == 'for_duty_cost']
    for_duty_10001['time'] = (for_duty_10001['shu_riqi'] > for_duty_10001['date_now'])
    for_duty_10002 = for_duty_10001.loc[for_duty_10001['time'] == True]
    for_duty_10002.info()


    for_duty_10002['b_id'] = 0
    for_duty_10002['line'] = 1007
    for_duty_10002['kind'] = 1595548
    # for_duty_10002['bi_zhi'] = 138


    for_duty_10003 = for_duty_10002[['auto_id','b_id','product_id','product_qty','product_price','product_price','datetime','jia_yi_fang_a','jia_yi_fang_b','jia_yi_fang_a','lei_a','lei_b','che_liang','line','kind','bi_zhi','jin_huo_bian','che_ci']]
    for_duty_10003.columns =['auto_id','b_id','product_id','product_qty','product_price_x','product_price_y','datetime','jia_yi_fang_a','jia_yi_fang_b','ke_bian','lei_a','lei_b','che_liang','line','kind','bi_zhi','jin_huo_bian','che_ci']
    FF300003 = for_duty_10003.rename(
        columns={'auto_id': 'a_id'})

    print(for_duty_10002.info())
    mata = {
    'data' : FF300003.to_dict(orient='records')
    }
    
    r = requests.post(shwethe_mysql_api +'/api/v1/table/mysql', data= json.dumps(mata))

    psycopg2_conn_insert_data_s.close()

    if r.status_code == 200:
        return "sucess"
    else:
        raise HTTPException(status_code=404, detail="Item not found")
