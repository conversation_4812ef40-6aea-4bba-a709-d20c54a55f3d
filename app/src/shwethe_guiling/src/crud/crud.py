from sqlite3 import dbapi2
import requests
from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,and_, join
from sqlmodel import select, func, and_, distinct, extract
from typing import List, Optional
from sqlalchemy.orm import joinedload
from datetime import datetime, date
from sqlalchemy.sql import func
import datetime
from sqlalchemy.sql import cast
from sqlalchemy import Date
from src.shwethe_guiling.database import get_session
from helper import generate_datetime_id
# from src.shwethe_guiling.src.models.models import (
# receive_deposit_worker, receive_deposit_worker_post,
# car_rent_insert, car_rent_insert_post, car_rent_update_post,
# descript_option, descript_option_post, car_rent_receive_info_post
# )
from sqlalchemy import text
import json
import pandas as pd
import urllib.request
import cv2
import numpy as np
from src.time_zone.time_zone_function import get_datetime
from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api
from src.Connect.postgresql_nern import real_backend_api, petrol_api
from sqlalchemy import and_, or_, not_
from pandas import json_normalize
from pprint import pprint

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel items into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

def dataframe(sqlModel, to_dict=False):
    records = [i.dict() for i in sqlModel]
    mergeDF = pd.DataFrame.from_records(records).fillna(0)
    if to_dict:
        mergeDF = mergeDF.to_dict("records")
    return mergeDF


def get_jia_yi100(item: str, db: Session = Depends(get_session)):
    # print(item)
    # print(type(item))

    try:
        item = int(item)
        print(item)
        print(type(item))

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'
        # url = Arter_api + 'jia_yi_name_list_id'
        # body_raw = {"data_api": [{"jia_yi_id": 36557}]}
        body_raw = {"data_api": [{"jia_yi_id": item}]}
        # body_raw = {"data_api": df}
        df2 = requests.get(url=url, json=body_raw)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')

        print(df2)

        # print(df2)
    except:
        print('The provided value is not an integer')
        print(item)
        print(type(item))

        # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={item}'
        url = f'{mongodb_data_api}/api/v2/search/jia_yi_search_text?text={item}'
        df2 = requests.get(url=url)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')

        print(df2)
        # print(df2)
    return df2



def sendToBigTable100(db: Session = Depends(get_session)):
    # try:
    from src.Connect.postgresql_nern import postgresql_shwethe_car_rent
    import pandas as pd


    # A10001 = pd.read_sql(""" select * from item_insert WHERE datetime >= NOW() - INTERVAL '2 days' """, postgresql_shwethe_car_rent)
    A10001 = pd.read_sql("""
                            SELECT *
                            FROM car_rent_insert
                            WHERE datetime >= NOW() - INTERVAL '4 days' 
                            AND status = 'complete'
                        """, postgresql_shwethe_car_rent)
    json_nor = A10001.to_dict("records")


    # INSERT TO ARTER DATABASE
    aaa = []
    for ioo in json_nor:
        d = {
            'a_id': ioo['auto_id'],
            'b_id': ioo['auto_id'],
            'product_id': 47775,
            'product_qty': -1,
            'product_price_a' : ioo['price'],
            'product_price_b' : ioo['price'],
            'ke_bian': ioo['jia_yi_fang_a'],
            'jia_yi_fang_a': ioo['jia_yi_fang_a'],
            'jia_yi_fang_b': ioo['jia_yi_fang_b'],
            'lei_a': ioo['lei_a'],
            'lei_b': ioo['lei_b'],
            'bu_bian': 1005,
            'jin_huo_bian': 0,   
            'jin_huo_dang': ioo['record_id'],   
            'ci_bian': 0,
            'u_id': 0,
            'shu_riqi_datetime': str(ioo['datetime']),
            'riqi_datetime': str(ioo['datetime']),
            'che_liang': ioo['detail_json']['jia_yi_id_vehicle'],
            'kind': 11237,
            'product_qtyp': 0,
            'product_qtyn': 0,
            'bi_zhi': ioo['bi_zhi']
            }
        aaa.append(d)
    df1 = pd.DataFrame(aaa) 
    mata = {
    'data' : df1.to_dict(orient='records')
    }
        
    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v2/table/mysql', data= json.dumps(mata))
    url = shwethe_mysql_api + '/api/v1/table/mysql_big_table'
    dd = requests.post(url, data= json.dumps(mata))
    # dd = requests.post('http://pv-api.shwethe.com/shwethe_mysql_api/api/v1/table/mysql_big_table', data= json.dumps(mata))
    out = dd.status_code
    print(out)

    return out
