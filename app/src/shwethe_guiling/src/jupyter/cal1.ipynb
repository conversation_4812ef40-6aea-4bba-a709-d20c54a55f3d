{"cells": [{"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-07-30 09:01:04,306 INFO sqlalchemy.engine.Engine select pg_catalog.version()\n", "2023-07-30 09:01:04,314 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2023-07-30 09:01:04,333 INFO sqlalchemy.engine.Engine select current_schema()\n", "2023-07-30 09:01:04,335 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2023-07-30 09:01:04,340 INFO sqlalchemy.engine.Engine show standard_conforming_strings\n", "2023-07-30 09:01:04,342 INFO sqlalchemy.engine.Engine [raw sql] {}\n", "2023-07-30 09:01:04,398 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2023-07-30 09:01:04,448 INFO sqlalchemy.engine.Engine SELECT vehicle_insert.details, vehicle_insert.jia_yi_id_vehicle, vehicle_insert.jia_yi_id_driver, vehicle_insert.image, vehicle_insert.datetime, vehicle_insert.fen_dian_id, vehicle_insert.status, vehicle_insert.\"where\", vehicle_insert.image_victus, vehicle_insert.image_victus_back, vehicle_insert.auto_id \n", "FROM vehicle_insert \n", "WHERE vehicle_insert.datetime > %(datetime_1)s ORDER BY vehicle_insert.datetime DESC\n", "2023-07-30 09:01:04,455 INFO sqlalchemy.engine.Engine [generated in 0.00842s] {'datetime_1': datetime.date(2023, 6, 30)}\n", "2023-07-30 09:01:05,716 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_id_vehicle</th>\n", "      <th>lastDate</th>\n", "      <th>lastStatus</th>\n", "      <th>descriptDate</th>\n", "      <th>status</th>\n", "      <th>descript</th>\n", "      <th>dayDiff</th>\n", "      <th>descriptListCount</th>\n", "      <th>descript_list</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>104</td>\n", "      <td>2023-07-30 07:56:37.557998+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-27</td>\n", "      <td>active</td>\n", "      <td>cdcdcdc</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>[cdcdcdc]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>105</td>\n", "      <td>2023-07-29 08:01:52.424249+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-29</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>106</td>\n", "      <td>2023-07-30 08:05:41.650878+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>194</td>\n", "      <td>2023-07-29 16:31:22.103367+06:30</td>\n", "      <td>pause</td>\n", "      <td>2023-07-08</td>\n", "      <td>pause</td>\n", "      <td>ကားဆရာနေမကောင်းပါသဖြင့်ခွင့်ယူထားပါသည်</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>[ကားဆရာနေမကောင်းပါသဖြင့်ခွင့်ယူထားပါသည်]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2290</td>\n", "      <td>2023-07-30 07:55:00.337943+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2343</td>\n", "      <td>2023-07-30 07:59:44.722826+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-29</td>\n", "      <td>active</td>\n", "      <td>xxx</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>[xxx]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7565</td>\n", "      <td>2023-07-30 08:13:37.217843+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-18</td>\n", "      <td>pause</td>\n", "      <td>ကားဆရာခွင့်ယူထားပါသည်</td>\n", "      <td>12</td>\n", "      <td>2</td>\n", "      <td>[ကားဆရာခွင့်ယူထားပါသည်, ခွင့်ယူထားပါသည်]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>11069</td>\n", "      <td>2023-07-30 11:26:17.056446+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>13417</td>\n", "      <td>2023-07-30 10:25:19.476549+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>16696</td>\n", "      <td>2023-07-30 08:54:16.679507+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-17</td>\n", "      <td>pause</td>\n", "      <td>ကားဆရာခွင့်ယူထားပါသည်</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>[ကားဆရာခွင့်ယူထားပါသည်, ကားမောင်းမရှိပါ]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>19562</td>\n", "      <td>2023-07-30 15:13:04.531956+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>19937</td>\n", "      <td>2023-07-30 08:44:08.214757+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-17</td>\n", "      <td>repair</td>\n", "      <td>ကားစီယာတိုင်ပြင်</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "      <td>[ကားစီယာတိုင်ပြင်]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>20141</td>\n", "      <td>2023-07-30 08:31:26.064122+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>20455</td>\n", "      <td>2023-07-30 08:00:19.834148+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>20929</td>\n", "      <td>2023-07-30 08:35:03.660811+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>22046</td>\n", "      <td>2023-07-30 08:09:12.975150+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>22085</td>\n", "      <td>2023-07-30 07:55:18.797520+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-25</td>\n", "      <td>pause</td>\n", "      <td>ကားဆရာခွင့်ယူထားပါသည်</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>[ကားဆရာခွင့်ယူထားပါသည်]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>23021</td>\n", "      <td>2023-07-29 16:31:13.057640+06:30</td>\n", "      <td>pause</td>\n", "      <td>2023-07-28</td>\n", "      <td>pause</td>\n", "      <td>အလုပ်မရှိ</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>[အလုပ်မရှိ, အလုပ်မရှိဂိုဒေါင်တွင်ရပ်ထားပါသည်, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>23536</td>\n", "      <td>2023-07-24 07:53:44.151881+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-24</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>24529</td>\n", "      <td>2023-07-29 09:31:11.937546+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-29</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>24968</td>\n", "      <td>2023-07-30 08:48:33.506138+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>25366</td>\n", "      <td>2023-07-24 07:53:52.677996+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-24</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>25370</td>\n", "      <td>2023-07-30 07:50:53.314459+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>25371</td>\n", "      <td>2023-07-29 16:31:54.306602+06:30</td>\n", "      <td>repair</td>\n", "      <td>2023-07-26</td>\n", "      <td>repair</td>\n", "      <td>ဝပ်ရှော့ပို့ထားပါသည်</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>[ဝပ်ရှော့ပို့ထားပါသည်]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>26594</td>\n", "      <td>2023-07-30 08:39:06.118784+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>31838</td>\n", "      <td>2023-07-30 08:21:10.214528+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-17</td>\n", "      <td>repair</td>\n", "      <td>ဝပ်ရှော့ပို့ထားပါသည်</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "      <td>[ဝပ်ရှော့ပို့ထားပါသည်]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>36230</td>\n", "      <td>2023-07-30 07:58:23.759121+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>36555</td>\n", "      <td>2023-07-30 08:25:11.931888+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>36768</td>\n", "      <td>2023-07-30 07:52:12.413733+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>37441</td>\n", "      <td>2023-07-30 07:58:27.572873+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>37620</td>\n", "      <td>2023-07-30 08:47:08.258114+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-01</td>\n", "      <td>pause</td>\n", "      <td>ကားဆရာအလုပ်ထွက်</td>\n", "      <td>29</td>\n", "      <td>1</td>\n", "      <td>[ကားဆရာအလုပ်ထွက်]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>37650</td>\n", "      <td>2023-07-15 07:59:43.935790+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-15</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>15</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>37710</td>\n", "      <td>2023-07-29 08:03:07.215904+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-08</td>\n", "      <td>repair</td>\n", "      <td>ကားပြင်နေပါသည်</td>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>[ကားပြင်နေပါသည်]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>38201</td>\n", "      <td>2023-07-30 08:00:23.604606+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-16</td>\n", "      <td>pause</td>\n", "      <td>12ဘီးကားမောင်းပါသည်</td>\n", "      <td>14</td>\n", "      <td>1</td>\n", "      <td>[12ဘီးကားမောင်းပါသည်]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>38374</td>\n", "      <td>2023-07-30 07:53:59.037760+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>38755</td>\n", "      <td>2023-07-30 09:43:39.087360+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>40658</td>\n", "      <td>2023-07-29 07:58:17.087047+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-29</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>40659</td>\n", "      <td>2023-07-29 16:31:17.837907+06:30</td>\n", "      <td>repair</td>\n", "      <td>2023-07-26</td>\n", "      <td>repair</td>\n", "      <td>ကားပျက်</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>[ကားပျက်]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>41085</td>\n", "      <td>2023-07-30 08:00:11.086461+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-16</td>\n", "      <td>pause</td>\n", "      <td>ခွင့်ယူထားပါသည်</td>\n", "      <td>14</td>\n", "      <td>1</td>\n", "      <td>[ခွင့်ယူထားပါသည်]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>42401</td>\n", "      <td>2023-07-30 08:44:39.870551+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>43162</td>\n", "      <td>2023-07-30 08:01:30.587811+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>43887</td>\n", "      <td>2023-07-30 08:56:12.381087+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>43888</td>\n", "      <td>2023-07-30 07:58:30.200095+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>43889</td>\n", "      <td>2023-07-30 07:59:29.990688+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>44098</td>\n", "      <td>2023-07-30 09:00:51.071947+06:30</td>\n", "      <td>active</td>\n", "      <td>2023-07-30</td>\n", "      <td>active</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    jia_yi_id_vehicle                         lastDate lastStatus   \n", "0                 104 2023-07-30 07:56:37.557998+06:30     active  \\\n", "1                 105 2023-07-29 08:01:52.424249+06:30     active   \n", "2                 106 2023-07-30 08:05:41.650878+06:30     active   \n", "3                 194 2023-07-29 16:31:22.103367+06:30      pause   \n", "4                2290 2023-07-30 07:55:00.337943+06:30     active   \n", "5                2343 2023-07-30 07:59:44.722826+06:30     active   \n", "6                7565 2023-07-30 08:13:37.217843+06:30     active   \n", "7               11069 2023-07-30 11:26:17.056446+06:30     active   \n", "8               13417 2023-07-30 10:25:19.476549+06:30     active   \n", "9               16696 2023-07-30 08:54:16.679507+06:30     active   \n", "10              19562 2023-07-30 15:13:04.531956+06:30     active   \n", "11              19937 2023-07-30 08:44:08.214757+06:30     active   \n", "12              20141 2023-07-30 08:31:26.064122+06:30     active   \n", "13              20455 2023-07-30 08:00:19.834148+06:30     active   \n", "14              20929 2023-07-30 08:35:03.660811+06:30     active   \n", "15              22046 2023-07-30 08:09:12.975150+06:30     active   \n", "16              22085 2023-07-30 07:55:18.797520+06:30     active   \n", "17              23021 2023-07-29 16:31:13.057640+06:30      pause   \n", "18              23536 2023-07-24 07:53:44.151881+06:30     active   \n", "19              24529 2023-07-29 09:31:11.937546+06:30     active   \n", "20              24968 2023-07-30 08:48:33.506138+06:30     active   \n", "21              25366 2023-07-24 07:53:52.677996+06:30     active   \n", "22              25370 2023-07-30 07:50:53.314459+06:30     active   \n", "23              25371 2023-07-29 16:31:54.306602+06:30     repair   \n", "24              26594 2023-07-30 08:39:06.118784+06:30     active   \n", "25              31838 2023-07-30 08:21:10.214528+06:30     active   \n", "26              36230 2023-07-30 07:58:23.759121+06:30     active   \n", "27              36555 2023-07-30 08:25:11.931888+06:30     active   \n", "28              36768 2023-07-30 07:52:12.413733+06:30     active   \n", "29              37441 2023-07-30 07:58:27.572873+06:30     active   \n", "30              37620 2023-07-30 08:47:08.258114+06:30     active   \n", "31              37650 2023-07-15 07:59:43.935790+06:30     active   \n", "32              37710 2023-07-29 08:03:07.215904+06:30     active   \n", "33              38201 2023-07-30 08:00:23.604606+06:30     active   \n", "34              38374 2023-07-30 07:53:59.037760+06:30     active   \n", "35              38755 2023-07-30 09:43:39.087360+06:30     active   \n", "36              40658 2023-07-29 07:58:17.087047+06:30     active   \n", "37              40659 2023-07-29 16:31:17.837907+06:30     repair   \n", "38              41085 2023-07-30 08:00:11.086461+06:30     active   \n", "39              42401 2023-07-30 08:44:39.870551+06:30     active   \n", "40              43162 2023-07-30 08:01:30.587811+06:30     active   \n", "41              43887 2023-07-30 08:56:12.381087+06:30     active   \n", "42              43888 2023-07-30 07:58:30.200095+06:30     active   \n", "43              43889 2023-07-30 07:59:29.990688+06:30     active   \n", "44              44098 2023-07-30 09:00:51.071947+06:30     active   \n", "\n", "   descriptDate  status                                descript  dayDiff   \n", "0    2023-07-27  active                                 cdcdcdc        3  \\\n", "1    2023-07-29  active                                    None        1   \n", "2    2023-07-30  active                                    None        0   \n", "3    2023-07-08   pause  ကားဆရာနေမကောင်းပါသဖြင့်ခွင့်ယူထားပါသည်       22   \n", "4    2023-07-30  active                                    None        0   \n", "5    2023-07-29  active                                     xxx        1   \n", "6    2023-07-18   pause                   ကားဆရာခွင့်ယူထားပါသည်       12   \n", "7    2023-07-30  active                                    None        0   \n", "8    2023-07-30  active                                    None        0   \n", "9    2023-07-17   pause                   ကားဆရာခွင့်ယူထားပါသည်       13   \n", "10   2023-07-30  active                                    None        0   \n", "11   2023-07-17  repair                        ကားစီယာတိုင်ပြင်       13   \n", "12   2023-07-30  active                                    None        0   \n", "13   2023-07-30  active                                    None        0   \n", "14   2023-07-30  active                                    None        0   \n", "15   2023-07-30  active                                    None        0   \n", "16   2023-07-25   pause                   ကားဆရာခွင့်ယူထားပါသည်        5   \n", "17   2023-07-28   pause                               အလုပ်မရှိ        2   \n", "18   2023-07-24  active                                    None        6   \n", "19   2023-07-29  active                                    None        1   \n", "20   2023-07-30  active                                    None        0   \n", "21   2023-07-24  active                                    None        6   \n", "22   2023-07-30  active                                    None        0   \n", "23   2023-07-26  repair                    ဝပ်ရှော့ပို့ထားပါသည်        4   \n", "24   2023-07-30  active                                    None        0   \n", "25   2023-07-17  repair                    ဝပ်ရှော့ပို့ထားပါသည်       13   \n", "26   2023-07-30  active                                    None        0   \n", "27   2023-07-30  active                                    None        0   \n", "28   2023-07-30  active                                    None        0   \n", "29   2023-07-30  active                                    None        0   \n", "30   2023-07-01   pause                         ကားဆရာအလုပ်ထွက်       29   \n", "31   2023-07-15  active                                    None       15   \n", "32   2023-07-08  repair                          ကားပြင်နေပါသည်       22   \n", "33   2023-07-16   pause                     12ဘီးကားမောင်းပါသည်       14   \n", "34   2023-07-30  active                                    None        0   \n", "35   2023-07-30  active                                    None        0   \n", "36   2023-07-29  active                                    None        1   \n", "37   2023-07-26  repair                                 ကားပျက်        4   \n", "38   2023-07-16   pause                         ခွင့်ယူထားပါသည်       14   \n", "39   2023-07-30  active                                    None        0   \n", "40   2023-07-30  active                                    None        0   \n", "41   2023-07-30  active                                    None        0   \n", "42   2023-07-30  active                                    None        0   \n", "43   2023-07-30  active                                    None        0   \n", "44   2023-07-30  active                                    None        0   \n", "\n", "    descriptListCount                                      descript_list  \n", "0                   1                                          [cdcdcdc]  \n", "1                   0                                                 []  \n", "2                   0                                                 []  \n", "3                   1           [ကားဆရာနေမကောင်းပါသဖြင့်ခွင့်ယူထားပါသည်]  \n", "4                   0                                                 []  \n", "5                   1                                              [xxx]  \n", "6                   2           [ကားဆရာခွင့်ယူထားပါသည်, ခွင့်ယူထားပါသည်]  \n", "7                   0                                                 []  \n", "8                   0                                                 []  \n", "9                   2           [ကားဆရာခွင့်ယူထားပါသည်, ကားမောင်းမရှိပါ]  \n", "10                  0                                                 []  \n", "11                  1                                 [ကားစီယာတိုင်ပြင်]  \n", "12                  0                                                 []  \n", "13                  0                                                 []  \n", "14                  0                                                 []  \n", "15                  0                                                 []  \n", "16                  1                            [ကားဆရာခွင့်ယူထားပါသည်]  \n", "17                  7  [အလုပ်မရှိ, အလုပ်မရှိဂိုဒေါင်တွင်ရပ်ထားပါသည်, ...  \n", "18                  0                                                 []  \n", "19                  0                                                 []  \n", "20                  0                                                 []  \n", "21                  0                                                 []  \n", "22                  0                                                 []  \n", "23                  1                             [ဝပ်ရှော့ပို့ထားပါသည်]  \n", "24                  0                                                 []  \n", "25                  1                             [ဝပ်ရှော့ပို့ထားပါသည်]  \n", "26                  0                                                 []  \n", "27                  0                                                 []  \n", "28                  0                                                 []  \n", "29                  0                                                 []  \n", "30                  1                                  [ကားဆရာအလုပ်ထွက်]  \n", "31                  0                                                 []  \n", "32                  1                                   [ကားပြင်နေပါသည်]  \n", "33                  1                              [12ဘီးကားမောင်းပါသည်]  \n", "34                  0                                                 []  \n", "35                  0                                                 []  \n", "36                  0                                                 []  \n", "37                  1                                          [ကားပျက်]  \n", "38                  1                                  [ခွင့်ယူထားပါသည်]  \n", "39                  0                                                 []  \n", "40                  0                                                 []  \n", "41                  0                                                 []  \n", "42                  0                                                 []  \n", "43                  0                                                 []  \n", "44                  0                                                 []  "]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "from src.shwethe_car_active.database import get_session\n", "from src.shwethe_car_active.src.models.models import vehicle_insert\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "from src.Connect.postgresql_nern import real_backend_api\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "from pandas import json_normalize\n", "import requests\n", "\n", "\n", "@contextmanager\n", "def get_session_dependency():\n", "    session = next(get_session())\n", "    try:\n", "        yield session\n", "    finally:\n", "        session.close()\n", "\n", "def dataframe(sqlModel, to_dict=False):\n", "    records = [i.dict() for i in sqlModel]\n", "    mergeDF = pd.DataFrame.from_records(records).fillna(0)\n", "    if to_dict:\n", "        mergeDF = mergeDF.to_dict(\"records\")\n", "    return mergeDF\n", "\n", "def get_vehicle_insert():\n", "    with get_session_dependency() as db:\n", "        import datetime as DT\n", "        today = DT.date.today()\n", "        week_ago = today - DT.<PERSON><PERSON>(days=30)\n", "        heroesPersonal = db.exec(select(vehicle_insert).where(vehicle_insert.datetime > week_ago).order_by(vehicle_insert.datetime.desc())).all()\n", "        df = dataframe(heroesPersonal, to_dict=False)\n", "        df['descriptDate'] = df['datetime'].dt.date\n", "        df = df[['datetime', 'descriptDate', 'jia_yi_id_vehicle', 'details', 'status']]\n", "        \n", "        # Convert 'details' column into a DataFrame and concatenate with the original DataFrame\n", "        details_df = json_normalize(df['details'])\n", "        df = pd.concat([df, details_df], axis=1)\n", "        df.drop('details', axis=1, inplace=True)\n", "        df = df[['datetime', 'descriptDate', 'jia_yi_id_vehicle', 'descript', 'status']]\n", "\n", "        # Group the data by 'sector' and 'date' and calculate the cumulative sum of 'change1d'\n", "        df = df.groupby(['jia_yi_id_vehicle', 'descriptDate']).first().reset_index()\n", "        df.sort_values(by=['jia_yi_id_vehicle', 'descriptDate'], ascending=[True, False], inplace=True)\n", "        # df = df.head(60)\n", "\n", "        # Get the current date\n", "        df['lastDate'] = df.groupby('jia_yi_id_vehicle')['datetime'].transform('first')\n", "        current_date = datetime.today().date()\n", "        df['today_date'] = current_date\n", "        df['descript'] = df['descript'].replace('', None)\n", "        unique_descripts = df.groupby('jia_yi_id_vehicle')['descript'].unique()\n", "        descript_dict = unique_descripts.apply(lambda x: [val for val in x if val is not None and val != '0']).to_dict()\n", "        df['descript_list'] = df['jia_yi_id_vehicle'].map(descript_dict)\n", "        df['descriptListCount'] = df['descript_list'].apply(len)\n", "        df['descriptDate'] = pd.to_datetime(df['descriptDate'])\n", "        df['today_date'] = pd.to_datetime(df['today_date'])\n", "        df['dayDiff'] = (df['today_date'] - df['descriptDate']).dt.days\n", "        df['lastStatus'] = df.groupby('jia_yi_id_vehicle')['status'].transform('first')\n", "        df = df[['jia_yi_id_vehicle', 'lastDate', 'lastStatus', 'descriptDate', 'status', 'descript', 'dayDiff', 'descriptListCount', 'descript_list']]\n", "\n", "        # Define a custom function to select the last row with a non-null 'descript' value\n", "        def select_last_row(group):\n", "            non_null_descript_rows = group[~group['descript'].isnull()]\n", "            if not non_null_descript_rows.empty:\n", "                return non_null_descript_rows.iloc[0]\n", "            else:\n", "                return group.iloc[0]\n", "        # Apply the custom function to each group after sorting\n", "        df = df.groupby('jia_yi_id_vehicle').apply(select_last_row).reset_index(drop=True)\n", "\n", "    return df\n", "\n", "\n", "def get_carActive_api():\n", "    url = f'{real_backend_api}/shwethe_car_active/api/v1/form/document/car_cocument?months_until_expiration=12'\n", "    df = requests.get(url=url)\n", "    df = df.json()\n", "    df = pd.DataFrame(df)\n", "\n", "    # Convert Unix timestamps to normal date format\n", "    df['start_date'] = pd.to_datetime(df['start_date'], unit='ms')\n", "    df['expiration_date'] = pd.to_datetime(df['expiration_date'], unit='ms')\n", "    df['today'] = pd.to_datetime('today').normalize()\n", "    df['days_left'] = (df['expiration_date'] - df['today']).dt.days\n", "    df = df.loc[df['document_types_name'] == 'ကားဝိဓါတ်']\n", "    df = df[['che_liang_id', 'days_left']]\n", "    return df\n", "\n", "def get_petrol_car():\n", "    url = \"http://192.168.1.130:8100/shwethe_petrol/api/v1/product/sendListOfCar\"\n", "    response = requests.get(url)\n", "    getCar = response.json()\n", "    getCar = pd.DataFrame(getCar)\n", "\n", "    df100 = getCar.rename(columns={\"che_liang_id\": 'jia_yi_id'})\n", "    url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'\n", "    to_dict = df100[['jia_yi_id']].to_dict('records')\n", "    body_raw = {\"data_api\": to_dict}\n", "    getCar2 = requests.get(url=url, json=body_raw).json()\n", "    getCar2 = pd.DataFrame(getCar2)\n", "\n", "    merge = getCar.merge(getCar2, left_on='che_liang_id', right_on='jia_yi_id', how='left')\n", "    merge = merge.fillna(\"\")\n", "\n", "    merge = merge[['che_liang_id', 'jia_yi_idname', 'datetime', 'type']]\n", "    return merge\n", "\n", "\n", "vget_vehicle_insert = get_vehicle_insert()\n", "# vget_carActive_api = get_carActive_api()\n", "# vget_petrol_car = get_petrol_car()\n", "# merge_day_left = pd.merge(vget_vehicle_insert, vget_carActive_api, left_on='jia_yi_id_vehicle', right_on='che_liang_id', how=\"left\").fillna('')\n", "# del merge_day_left['che_liang_id']\n", "# merge_petrol = pd.merge(merge_day_left, vget_petrol_car, left_on='jia_yi_id_vehicle', right_on='che_liang_id', how=\"left\").replace({pd.NA: None})\n", "# del merge_petrol['che_liang_id']\n", "# merge_petrol.rename(columns={'datetime': 'petrol_date'}, inplace=True)\n", "# merge_petrol = merge_petrol[pd.to_datetime(merge_petrol['petrol_date']).notnull()]\n", "vget_vehicle_insert"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-07-30 10:27:24,853 INFO sqlalchemy.engine.Engine BEGIN (implicit)\n", "2023-07-30 10:27:24,863 INFO sqlalchemy.engine.Engine SELECT vehicle_insert.details, vehicle_insert.jia_yi_id_vehicle, vehicle_insert.jia_yi_id_driver, vehicle_insert.image, vehicle_insert.datetime, vehicle_insert.fen_dian_id, vehicle_insert.status, vehicle_insert.\"where\", vehicle_insert.image_victus, vehicle_insert.image_victus_back, vehicle_insert.auto_id \n", "FROM vehicle_insert \n", "WHERE vehicle_insert.datetime > %(datetime_1)s ORDER BY vehicle_insert.datetime DESC\n", "2023-07-30 10:27:24,866 INFO sqlalchemy.engine.Engine [cached since 5180s ago] {'datetime_1': datetime.date(2023, 7, 29)}\n", "2023-07-30 10:27:25,016 INFO sqlalchemy.engine.Engine ROLLBACK\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_id_vehicle</th>\n", "      <th>descriptDate</th>\n", "      <th>datetime</th>\n", "      <th>descript</th>\n", "      <th>status</th>\n", "      <th>lastDate</th>\n", "      <th>today_date</th>\n", "      <th>descript_list</th>\n", "      <th>descriptListCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>104</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 07:56:37.557998+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 07:56:37.557998+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>105</td>\n", "      <td>2023-07-29</td>\n", "      <td>2023-07-29 08:01:52.424249+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-29 08:01:52.424249+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>106</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:05:41.650878+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:05:41.650878+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>194</td>\n", "      <td>2023-07-29</td>\n", "      <td>2023-07-29 16:31:22.103367+06:30</td>\n", "      <td></td>\n", "      <td>pause</td>\n", "      <td>2023-07-29 16:31:22.103367+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2290</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 07:55:00.337943+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 07:55:00.337943+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2343</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 07:59:44.722826+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 07:59:44.722826+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7565</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:13:37.217843+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:13:37.217843+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>11069</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 11:26:17.056446+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 11:26:17.056446+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>13417</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 10:25:19.476549+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 10:25:19.476549+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>16696</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:54:16.679507+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:54:16.679507+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>19562</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 15:13:04.531956+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 15:13:04.531956+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>19937</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:44:08.214757+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:44:08.214757+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>20141</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:31:26.064122+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:31:26.064122+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>20455</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:00:19.834148+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:00:19.834148+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>20929</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:35:03.660811+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:35:03.660811+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>22046</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:09:12.975150+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:09:12.975150+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>22085</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 07:55:18.797520+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 07:55:18.797520+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>23021</td>\n", "      <td>2023-07-29</td>\n", "      <td>2023-07-29 16:31:13.057640+06:30</td>\n", "      <td></td>\n", "      <td>pause</td>\n", "      <td>2023-07-29 16:31:13.057640+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>24529</td>\n", "      <td>2023-07-29</td>\n", "      <td>2023-07-29 09:31:11.937546+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-29 09:31:11.937546+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>24968</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:48:33.506138+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:48:33.506138+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>25370</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 07:50:53.314459+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 07:50:53.314459+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>25371</td>\n", "      <td>2023-07-29</td>\n", "      <td>2023-07-29 16:31:54.306602+06:30</td>\n", "      <td></td>\n", "      <td>repair</td>\n", "      <td>2023-07-29 16:31:54.306602+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>26594</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:39:06.118784+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:39:06.118784+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>31838</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:21:10.214528+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:21:10.214528+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>36230</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 07:58:23.759121+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 07:58:23.759121+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>36555</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:25:11.931888+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:25:11.931888+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>36768</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 07:52:12.413733+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 07:52:12.413733+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>37441</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 07:58:27.572873+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 07:58:27.572873+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>37620</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:47:08.258114+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:47:08.258114+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>37710</td>\n", "      <td>2023-07-29</td>\n", "      <td>2023-07-29 08:03:07.215904+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-29 08:03:07.215904+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>38201</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:00:23.604606+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:00:23.604606+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>38374</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 07:53:59.037760+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 07:53:59.037760+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>38755</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 09:43:39.087360+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 09:43:39.087360+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>40658</td>\n", "      <td>2023-07-29</td>\n", "      <td>2023-07-29 07:58:17.087047+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-29 07:58:17.087047+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>40659</td>\n", "      <td>2023-07-29</td>\n", "      <td>2023-07-29 16:31:17.837907+06:30</td>\n", "      <td></td>\n", "      <td>repair</td>\n", "      <td>2023-07-29 16:31:17.837907+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>41085</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:00:11.086461+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:00:11.086461+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>42401</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:44:39.870551+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:44:39.870551+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>43162</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:01:30.587811+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:01:30.587811+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>43887</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 08:56:12.381087+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 08:56:12.381087+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>43888</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 07:58:30.200095+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 07:58:30.200095+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>43889</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 07:59:29.990688+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 07:59:29.990688+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>44098</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-30 09:00:51.071947+06:30</td>\n", "      <td></td>\n", "      <td>active</td>\n", "      <td>2023-07-30 09:00:51.071947+06:30</td>\n", "      <td>2023-07-30</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    jia_yi_id_vehicle descriptDate                         datetime descript   \n", "0                 104   2023-07-30 2023-07-30 07:56:37.557998+06:30           \\\n", "1                 105   2023-07-29 2023-07-29 08:01:52.424249+06:30            \n", "2                 106   2023-07-30 2023-07-30 08:05:41.650878+06:30            \n", "3                 194   2023-07-29 2023-07-29 16:31:22.103367+06:30            \n", "4                2290   2023-07-30 2023-07-30 07:55:00.337943+06:30            \n", "5                2343   2023-07-30 2023-07-30 07:59:44.722826+06:30            \n", "6                7565   2023-07-30 2023-07-30 08:13:37.217843+06:30            \n", "7               11069   2023-07-30 2023-07-30 11:26:17.056446+06:30            \n", "8               13417   2023-07-30 2023-07-30 10:25:19.476549+06:30            \n", "9               16696   2023-07-30 2023-07-30 08:54:16.679507+06:30            \n", "10              19562   2023-07-30 2023-07-30 15:13:04.531956+06:30            \n", "11              19937   2023-07-30 2023-07-30 08:44:08.214757+06:30            \n", "12              20141   2023-07-30 2023-07-30 08:31:26.064122+06:30            \n", "13              20455   2023-07-30 2023-07-30 08:00:19.834148+06:30            \n", "14              20929   2023-07-30 2023-07-30 08:35:03.660811+06:30            \n", "15              22046   2023-07-30 2023-07-30 08:09:12.975150+06:30            \n", "16              22085   2023-07-30 2023-07-30 07:55:18.797520+06:30            \n", "17              23021   2023-07-29 2023-07-29 16:31:13.057640+06:30            \n", "18              24529   2023-07-29 2023-07-29 09:31:11.937546+06:30            \n", "19              24968   2023-07-30 2023-07-30 08:48:33.506138+06:30            \n", "20              25370   2023-07-30 2023-07-30 07:50:53.314459+06:30            \n", "21              25371   2023-07-29 2023-07-29 16:31:54.306602+06:30            \n", "22              26594   2023-07-30 2023-07-30 08:39:06.118784+06:30            \n", "23              31838   2023-07-30 2023-07-30 08:21:10.214528+06:30            \n", "24              36230   2023-07-30 2023-07-30 07:58:23.759121+06:30            \n", "25              36555   2023-07-30 2023-07-30 08:25:11.931888+06:30            \n", "26              36768   2023-07-30 2023-07-30 07:52:12.413733+06:30            \n", "27              37441   2023-07-30 2023-07-30 07:58:27.572873+06:30            \n", "28              37620   2023-07-30 2023-07-30 08:47:08.258114+06:30            \n", "29              37710   2023-07-29 2023-07-29 08:03:07.215904+06:30            \n", "30              38201   2023-07-30 2023-07-30 08:00:23.604606+06:30            \n", "31              38374   2023-07-30 2023-07-30 07:53:59.037760+06:30            \n", "32              38755   2023-07-30 2023-07-30 09:43:39.087360+06:30            \n", "33              40658   2023-07-29 2023-07-29 07:58:17.087047+06:30            \n", "34              40659   2023-07-29 2023-07-29 16:31:17.837907+06:30            \n", "35              41085   2023-07-30 2023-07-30 08:00:11.086461+06:30            \n", "36              42401   2023-07-30 2023-07-30 08:44:39.870551+06:30            \n", "37              43162   2023-07-30 2023-07-30 08:01:30.587811+06:30            \n", "38              43887   2023-07-30 2023-07-30 08:56:12.381087+06:30            \n", "39              43888   2023-07-30 2023-07-30 07:58:30.200095+06:30            \n", "40              43889   2023-07-30 2023-07-30 07:59:29.990688+06:30            \n", "41              44098   2023-07-30 2023-07-30 09:00:51.071947+06:30            \n", "\n", "    status                         lastDate  today_date descript_list   \n", "0   active 2023-07-30 07:56:37.557998+06:30  2023-07-30            []  \\\n", "1   active 2023-07-29 08:01:52.424249+06:30  2023-07-30            []   \n", "2   active 2023-07-30 08:05:41.650878+06:30  2023-07-30            []   \n", "3    pause 2023-07-29 16:31:22.103367+06:30  2023-07-30            []   \n", "4   active 2023-07-30 07:55:00.337943+06:30  2023-07-30            []   \n", "5   active 2023-07-30 07:59:44.722826+06:30  2023-07-30            []   \n", "6   active 2023-07-30 08:13:37.217843+06:30  2023-07-30            []   \n", "7   active 2023-07-30 11:26:17.056446+06:30  2023-07-30            []   \n", "8   active 2023-07-30 10:25:19.476549+06:30  2023-07-30            []   \n", "9   active 2023-07-30 08:54:16.679507+06:30  2023-07-30            []   \n", "10  active 2023-07-30 15:13:04.531956+06:30  2023-07-30            []   \n", "11  active 2023-07-30 08:44:08.214757+06:30  2023-07-30            []   \n", "12  active 2023-07-30 08:31:26.064122+06:30  2023-07-30            []   \n", "13  active 2023-07-30 08:00:19.834148+06:30  2023-07-30            []   \n", "14  active 2023-07-30 08:35:03.660811+06:30  2023-07-30            []   \n", "15  active 2023-07-30 08:09:12.975150+06:30  2023-07-30            []   \n", "16  active 2023-07-30 07:55:18.797520+06:30  2023-07-30            []   \n", "17   pause 2023-07-29 16:31:13.057640+06:30  2023-07-30            []   \n", "18  active 2023-07-29 09:31:11.937546+06:30  2023-07-30            []   \n", "19  active 2023-07-30 08:48:33.506138+06:30  2023-07-30            []   \n", "20  active 2023-07-30 07:50:53.314459+06:30  2023-07-30            []   \n", "21  repair 2023-07-29 16:31:54.306602+06:30  2023-07-30            []   \n", "22  active 2023-07-30 08:39:06.118784+06:30  2023-07-30            []   \n", "23  active 2023-07-30 08:21:10.214528+06:30  2023-07-30            []   \n", "24  active 2023-07-30 07:58:23.759121+06:30  2023-07-30            []   \n", "25  active 2023-07-30 08:25:11.931888+06:30  2023-07-30            []   \n", "26  active 2023-07-30 07:52:12.413733+06:30  2023-07-30            []   \n", "27  active 2023-07-30 07:58:27.572873+06:30  2023-07-30            []   \n", "28  active 2023-07-30 08:47:08.258114+06:30  2023-07-30            []   \n", "29  active 2023-07-29 08:03:07.215904+06:30  2023-07-30            []   \n", "30  active 2023-07-30 08:00:23.604606+06:30  2023-07-30            []   \n", "31  active 2023-07-30 07:53:59.037760+06:30  2023-07-30            []   \n", "32  active 2023-07-30 09:43:39.087360+06:30  2023-07-30            []   \n", "33  active 2023-07-29 07:58:17.087047+06:30  2023-07-30            []   \n", "34  repair 2023-07-29 16:31:17.837907+06:30  2023-07-30            []   \n", "35  active 2023-07-30 08:00:11.086461+06:30  2023-07-30            []   \n", "36  active 2023-07-30 08:44:39.870551+06:30  2023-07-30            []   \n", "37  active 2023-07-30 08:01:30.587811+06:30  2023-07-30            []   \n", "38  active 2023-07-30 08:56:12.381087+06:30  2023-07-30            []   \n", "39  active 2023-07-30 07:58:30.200095+06:30  2023-07-30            []   \n", "40  active 2023-07-30 07:59:29.990688+06:30  2023-07-30            []   \n", "41  active 2023-07-30 09:00:51.071947+06:30  2023-07-30            []   \n", "\n", "    descriptListCount  \n", "0                   0  \n", "1                   0  \n", "2                   0  \n", "3                   0  \n", "4                   0  \n", "5                   0  \n", "6                   0  \n", "7                   0  \n", "8                   0  \n", "9                   0  \n", "10                  0  \n", "11                  0  \n", "12                  0  \n", "13                  0  \n", "14                  0  \n", "15                  0  \n", "16                  0  \n", "17                  0  \n", "18                  0  \n", "19                  0  \n", "20                  0  \n", "21                  0  \n", "22                  0  \n", "23                  0  \n", "24                  0  \n", "25                  0  \n", "26                  0  \n", "27                  0  \n", "28                  0  \n", "29                  0  \n", "30                  0  \n", "31                  0  \n", "32                  0  \n", "33                  0  \n", "34                  0  \n", "35                  0  \n", "36                  0  \n", "37                  0  \n", "38                  0  \n", "39                  0  \n", "40                  0  \n", "41                  0  "]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["def getExpireCar100():\n", "    \n", "    def get_vehicle_insert():\n", "        with get_session_dependency() as db:\n", "            import datetime as DT\n", "            today = DT.date.today()\n", "            week_ago = today - DT.<PERSON><PERSON><PERSON>(days=1)\n", "            heroesPersonal = db.exec(select(vehicle_insert).where(vehicle_insert.datetime > week_ago).order_by(vehicle_insert.datetime.desc())).all()\n", "            df = dataframe(heroesPersonal, to_dict=False)\n", "            df['descriptDate'] = df['datetime'].dt.strftime('%Y-%m-%d')\n", "            df = df[['datetime', 'descriptDate', 'jia_yi_id_vehicle', 'details', 'status']]\n", "            \n", "            # Convert 'details' column into a DataFrame and concatenate with the original DataFrame\n", "            details_df = json_normalize(df['details'])\n", "            df = pd.concat([df, details_df], axis=1).replace({pd.NA: None})\n", "            df.drop('details', axis=1, inplace=True)\n", "            df = df[['datetime', 'descriptDate', 'jia_yi_id_vehicle', 'descript', 'status']]\n", "\n", "            # Group the data by 'sector' and 'date' and calculate the cumulative sum of 'change1d'\n", "            df = df.groupby(['jia_yi_id_vehicle', 'descriptDate']).first().reset_index()\n", "            df.sort_values(by=['jia_yi_id_vehicle', 'descriptDate'], ascending=[True, False], inplace=True)\n", "            # df = df.tail(60)\n", "            # df = df.iloc[60:80]\n", "\n", "            # # Get the current date\n", "            # df['lastDate'] = df.groupby('jia_yi_id_vehicle')['datetime'].transform('first')\n", "            # current_date = datetime.today().date()\n", "            # df['today_date'] = current_date\n", "            # df['descript'] = df['descript'].replace('', None)\n", "            # unique_descripts = df.groupby('jia_yi_id_vehicle')['descript'].unique()\n", "            # descript_dict = unique_descripts.apply(lambda x: [val for val in x if val is not None and val != '0']).to_dict()\n", "            # df['descript_list'] = df['jia_yi_id_vehicle'].map(descript_dict)\n", "            # df['descriptListCount'] = df['descript_list'].apply(len)\n", "            # df['descriptDate'] = pd.to_datetime(df['descriptDate'])\n", "            # df['today_date'] = pd.to_datetime(df['today_date'])\n", "            # df['dayDiff'] = (df['today_date'] - df['descriptDate']).dt.days\n", "            # df['lastStatus'] = df.groupby('jia_yi_id_vehicle')['status'].transform('first')\n", "            # df = df[['jia_yi_id_vehicle', 'lastDate', 'lastStatus', 'descriptDate', 'status', 'descript', 'dayDiff', 'descriptListCount', 'descript_list']]\n", "\n", "            # # Define a custom function to select the last row with a non-null 'descript' value\n", "            # def select_last_row(group):\n", "            #     non_null_descript_rows = group[~group['descript'].isnull()]\n", "            #     if not non_null_descript_rows.empty:\n", "            #         return non_null_descript_rows.iloc[0]\n", "            #     else:\n", "            #         return group.iloc[0]\n", "            # # Apply the custom function to each group after sorting\n", "            # df = df.groupby('jia_yi_id_vehicle').apply(select_last_row).reset_index(drop=True)\n", "\n", "            # Get the current date\n", "            df['lastDate'] = df.groupby('jia_yi_id_vehicle')['datetime'].transform('first')\n", "            current_date = datetime.today().date()\n", "            df['today_date'] = current_date\n", "            df['descript'] = df['descript'].fillna(\"\")\n", "            unique_descripts = df.groupby('jia_yi_id_vehicle')['descript'].unique()\n", "            descript_dict = unique_descripts.apply(lambda x: [val for val in x if val is not None and val != '0' and val != '' and val != 'None']).to_dict()\n", "            df['descript_list'] = df['jia_yi_id_vehicle'].map(descript_dict)\n", "            df['descriptListCount'] = df['descript_list'].apply(len)\n", "            df['descriptDate'] = pd.to_datetime(df['descriptDate'])\n", "            # df['today_date'] = pd.to_datetime(df['today_date'])\n", "            # df['dayDiff'] = (df['today_date'] - df['descriptDate']).dt.days\n", "            # df['lastStatus'] = df.groupby('jia_yi_id_vehicle')['status'].transform('first')\n", "            # df = df[['jia_yi_id_vehicle', 'lastDate', 'lastStatus', 'descriptDate', 'status', 'descript', 'dayDiff', 'descriptListCount', 'descript_list']]\n", "\n", "            # Define the custom function\n", "            def select_last_row(group):\n", "                non_empty_descript_rows = group[group['descript'] != '']\n", "                if not non_empty_descript_rows.empty:\n", "                    return non_empty_descript_rows.iloc[0]\n", "                else:\n", "                    return group.iloc[0]\n", "            df = df.groupby('jia_yi_id_vehicle').apply(select_last_row).reset_index(drop=True)\n", "\n", "        return df\n", "\n", "    vget_vehicle_insert = get_vehicle_insert()\n", "\n", "    return vget_vehicle_insert\n", "\n", "getExpireCar100()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}