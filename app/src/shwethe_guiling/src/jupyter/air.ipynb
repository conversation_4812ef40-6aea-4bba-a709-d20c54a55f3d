{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "from src.shwethe_car_active.database import get_session\n", "from src.shwethe_car_active.src.models.models import vehicle_insert\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "from src.Connect.postgresql_nern import real_backend_api, postgresql_shwethe_miniapp_carActive\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "from pandas import json_normalize\n", "import requests\n", "from psycopg2.extras import <PERSON>son\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_57889/142719557.py:17: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.\n", "  A100012 = pd.read_sql(\"\"\"\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jia_yi_id_vehicle</th>\n", "      <th>lastDate</th>\n", "      <th>lastStatus</th>\n", "      <th>descriptDate</th>\n", "      <th>descriptDateYMD</th>\n", "      <th>status</th>\n", "      <th>descript</th>\n", "      <th>dayDiff</th>\n", "      <th>descriptListCount</th>\n", "      <th>descript_list</th>\n", "      <th>vitus_left</th>\n", "      <th>jia_yi_idname</th>\n", "      <th>jia_yi_mm_name</th>\n", "      <th>petrol_date</th>\n", "      <th>type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>104</td>\n", "      <td>2023-08-23 01:51:50.274259+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>342.0</td>\n", "      <td>W01A</td>\n", "      <td>နှစ်တန်ကားတင်ဘာပါ  2A-6114</td>\n", "      <td>2023-08-17 16:28:03.811950</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>105</td>\n", "      <td>2023-08-23 01:30:34.311725+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>254.0</td>\n", "      <td>W02A</td>\n", "      <td>နှစ်တန်ကားတင်ဘာပါ 2A-3844</td>\n", "      <td>2023-08-21 16:28:19.463312</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>106</td>\n", "      <td>2023-08-23 01:34:45.590268+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>160.0</td>\n", "      <td>W03A</td>\n", "      <td>နှစ်တန်ကားတင်ဘာပါ 3A-9900</td>\n", "      <td>2023-08-20 17:01:39.436488</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>194</td>\n", "      <td>2023-08-23 09:32:02.853272+00:00</td>\n", "      <td>pause</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>pause</td>\n", "      <td>ကားဆရာခွင့်ယူပါသည်</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>[ကားဆရာခွင့်ယူပါသည်]</td>\n", "      <td>189.0</td>\n", "      <td>W06A</td>\n", "      <td>နှစ်တန်ကားတင်ဘာပါ P-6308</td>\n", "      <td>2023-08-20 17:03:28.077296</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1569</td>\n", "      <td>2023-08-23 01:32:36.766970+00:00</td>\n", "      <td>repair</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>repair</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>XM07</td>\n", "      <td>ဆိုင်ကယ်(ပြာ)6ယ/67075</td>\n", "      <td>2023-04-05 15:52:56.628905</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2290</td>\n", "      <td>2023-08-23 01:28:30.700506+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>129.0</td>\n", "      <td>W07A</td>\n", "      <td>နှစ်တန်ကားတင်ဘာပါ 1R-1730</td>\n", "      <td>2023-08-20 16:55:30.268864</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2343</td>\n", "      <td>2023-08-23 01:21:40.294183+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.3</td>\n", "      <td>ဘိလပ်တင်မစက်အသစ် 3 Ton</td>\n", "      <td>2023-08-19 12:22:01.658201</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>7565</td>\n", "      <td>2023-08-23 01:24:58.864057+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>37.0</td>\n", "      <td>W14D</td>\n", "      <td>လေးတန်ကားတင်ဘာပါ 9H-3468</td>\n", "      <td>2023-08-13 15:01:54.236651</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>11069</td>\n", "      <td>2023-08-23 05:02:38.661614+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>160.0</td>\n", "      <td>W16B</td>\n", "      <td>နှစ်တန်ကားရိုးရိုး J-7681</td>\n", "      <td>2023-08-17 16:23:58.582416</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>13417</td>\n", "      <td>2023-08-22 01:35:02.115621+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-22</td>\n", "      <td>2023-08-22</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>XM11</td>\n", "      <td>ဆိုင်ကယ် 6ယ/67324</td>\n", "      <td>2023-08-18 14:09:15.198748</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>16696</td>\n", "      <td>2023-08-23 02:23:20.337892+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>37.0</td>\n", "      <td>W08A</td>\n", "      <td>နှစ်တန်ကားတင်ဘာပါ RA-1097</td>\n", "      <td>2023-08-19 17:03:42.705688</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>19562</td>\n", "      <td>2023-08-23 01:30:29.569976+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>250.0</td>\n", "      <td>W12C</td>\n", "      <td>သုံးတန်ကားရိုးရိုး 7E-5470</td>\n", "      <td>2023-08-19 17:12:57.726016</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>19937</td>\n", "      <td>2023-08-23 01:32:27.944640+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>U4</td>\n", "      <td>လိုက်ထရပ်ကား 2F-4084</td>\n", "      <td>2023-08-20 16:46:20.247750</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>20141</td>\n", "      <td>2023-08-23 01:35:15.941347+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>342.0</td>\n", "      <td>W13C</td>\n", "      <td>သုံးတန်ကားရိုးရိုး 4F-5207</td>\n", "      <td>2023-08-20 16:49:41.562734</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>20455</td>\n", "      <td>2023-08-23 01:29:49.785993+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>68.0</td>\n", "      <td>W04A</td>\n", "      <td>နှစ်တန်ကားတင်ဘာပါ 9F-7235</td>\n", "      <td>2023-08-20 16:40:00.841525</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>20929</td>\n", "      <td>2023-08-23 02:03:24.349246+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>XM01</td>\n", "      <td>ဆိုင်ကယ် 24ယ/55726</td>\n", "      <td>2023-08-21 16:30:27.609074</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>22046</td>\n", "      <td>2023-08-23 01:38:37.684054+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>U6</td>\n", "      <td>ဟိုက်ဂျက် 1H-7013</td>\n", "      <td>2023-08-20 17:06:46.500252</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>22085</td>\n", "      <td>2023-08-23 02:26:32.417622+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>226.0</td>\n", "      <td>W11B</td>\n", "      <td>နှစ်တန်ကားရိုးရိုး 7H-4121</td>\n", "      <td>2023-08-22 16:40:05.069759</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>23021</td>\n", "      <td>2023-08-23 03:06:26.332913+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>W15E</td>\n", "      <td>12ဘီးကား 7I-9141</td>\n", "      <td>2023-08-21 16:37:21.069569</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>23536</td>\n", "      <td>2023-08-23 08:41:21.338816+00:00</td>\n", "      <td>repair</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>repair</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.14</td>\n", "      <td>ဘိလပ်တင်စက် 3 Ton</td>\n", "      <td>2022-05-16 13:55:42.693998</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>24529</td>\n", "      <td>2023-08-23 03:23:19.437360+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>XM10</td>\n", "      <td>ဆိုင်ကယ် 40ယ/60056</td>\n", "      <td>2023-05-07 08:42:47.868964</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>24968</td>\n", "      <td>2023-08-23 01:42:11.721635+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.16</td>\n", "      <td>ဘိလပ်မြေ တင်စက် (မ.စက်) 3.5 Ton</td>\n", "      <td>2023-08-16 08:40:38.293111</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>25366</td>\n", "      <td>2023-08-22 10:11:55.402742+00:00</td>\n", "      <td>repair</td>\n", "      <td>2023-08-22</td>\n", "      <td>2023-08-22</td>\n", "      <td>repair</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.17</td>\n", "      <td>ဘိလပ်မြေတင်စက် (မ.စက်) 3.5 Ton</td>\n", "      <td>2023-01-05 16:58:06.479187</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>25370</td>\n", "      <td>2023-08-23 01:23:25.885752+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.4</td>\n", "      <td>လိလပ်မြေတင်စက် (မ.စက်) 3 Ton</td>\n", "      <td>2023-08-18 09:14:38.016749</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>25371</td>\n", "      <td>2023-08-22 09:36:15.537435+00:00</td>\n", "      <td>repair</td>\n", "      <td>2023-08-22</td>\n", "      <td>2023-08-22</td>\n", "      <td>repair</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>[ ]</td>\n", "      <td></td>\n", "      <td>V.7</td>\n", "      <td>ဘိခပ်မြေတင်စက် (မ.စက်) 3 Ton</td>\n", "      <td>2023-07-23 16:41:51.924533</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>26594</td>\n", "      <td>2023-08-23 01:47:44.507953+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.18</td>\n", "      <td>ဘိလပ်မြေတင်စက် 3 Ton</td>\n", "      <td>2023-08-20 08:58:48.426343</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>31838</td>\n", "      <td>2023-08-23 01:41:08.581101+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.20</td>\n", "      <td>ဘိလပ်တင်စက် 3 Ton</td>\n", "      <td>2023-08-19 15:35:20.032623</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>31966</td>\n", "      <td>2023-08-23 08:41:16.665532+00:00</td>\n", "      <td>repair</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>repair</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaT</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>36230</td>\n", "      <td>2023-08-23 01:23:11.843362+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.22</td>\n", "      <td>ဘိလပ်တင်မစက်အသစ် 3 Ton</td>\n", "      <td>2023-08-22 11:27:04.908595</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>36555</td>\n", "      <td>2023-08-23 02:32:26.584282+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>281.0</td>\n", "      <td>W10A</td>\n", "      <td>နှစ်တန်ကားတင်ဘာပါ 7R-6894</td>\n", "      <td>2023-08-22 17:05:07.412941</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>36768</td>\n", "      <td>2023-08-23 01:22:10.686362+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.5</td>\n", "      <td>ဘိလပ်တင်မစက် 4မစ် 3 Ton</td>\n", "      <td>2023-08-23 08:46:36.854200</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>37441</td>\n", "      <td>2023-08-23 01:20:35.498823+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.6</td>\n", "      <td>ဘိလပ်တင်မစက် 4မစ် 3 Ton</td>\n", "      <td>2023-08-21 13:59:08.797970</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>37620</td>\n", "      <td>2023-08-23 02:18:17.842463+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>98.0</td>\n", "      <td>W09A</td>\n", "      <td>နှစ်တန်ကားတင်ဘာပါ 1S-1776</td>\n", "      <td>2023-08-20 16:37:13.638537</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>37650</td>\n", "      <td>2023-08-23 01:46:31.678617+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.8</td>\n", "      <td>ဘိလပ်တင်မစက် 4မစ် 3 Ton</td>\n", "      <td>2023-08-20 15:58:46.083962</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>37710</td>\n", "      <td>2023-08-23 07:44:43.782183+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>223.0</td>\n", "      <td>W05B</td>\n", "      <td>နှစ်တန်ကားရိုးရိုး 7R-2956</td>\n", "      <td>2023-08-20 16:32:23.275796</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>38201</td>\n", "      <td>2023-08-23 01:31:02.533360+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>189.0</td>\n", "      <td>W17D</td>\n", "      <td>နှစ်တန်ခွဲကားတင်ဘာပါ 2R-1964</td>\n", "      <td>2023-08-16 11:21:31.056526</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>38374</td>\n", "      <td>2023-08-23 01:25:16.139117+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.9</td>\n", "      <td>ဘိလပ်တင်မစက် 4မစ် 3 Ton</td>\n", "      <td>2023-08-22 15:41:18.501487</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>38755</td>\n", "      <td>2023-08-23 01:32:40.508502+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>XM14</td>\n", "      <td>ဆိုင်ကယ် 42ယ/5386</td>\n", "      <td>2023-08-17 16:36:45.832984</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>40658</td>\n", "      <td>2023-08-23 01:25:38.011798+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>W18C</td>\n", "      <td>သုံးတန်ကားရိုးရိုး 6R-8220</td>\n", "      <td>2023-08-20 16:58:13.149028</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>40659</td>\n", "      <td>2023-08-23 01:23:53.370576+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>249.0</td>\n", "      <td>W19B</td>\n", "      <td>နှစ်တန်ကားရိုးရိုး 7R-2942</td>\n", "      <td>2023-08-20 16:51:54.889636</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>41085</td>\n", "      <td>2023-08-23 01:26:21.709305+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>189.0</td>\n", "      <td>W20D</td>\n", "      <td>နှစ်တန်ခွဲကားတင်ဘာပါ 2R-1959</td>\n", "      <td>2023-08-20 17:00:11.100060</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>42401</td>\n", "      <td>2023-08-22 09:26:29.128748+00:00</td>\n", "      <td>repair</td>\n", "      <td>2023-08-22</td>\n", "      <td>2023-08-22</td>\n", "      <td>repair</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td>281.0</td>\n", "      <td>W21E</td>\n", "      <td>12 ဘီးကရိန်းကား 4Q-5836</td>\n", "      <td>2023-08-14 15:35:28.927258</td>\n", "      <td>car</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>43162</td>\n", "      <td>2023-08-23 01:21:15.844477+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.10</td>\n", "      <td>ဘိလပ်တင်မစက် 5မစ် 5 Ton</td>\n", "      <td>2023-08-22 09:17:24.155807</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>43887</td>\n", "      <td>2023-08-23 01:42:28.000157+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.11</td>\n", "      <td>ဘိလပ်တင်မစက် 4မစ် 3 Ton</td>\n", "      <td>2023-08-19 08:35:57.653055</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>43888</td>\n", "      <td>2023-08-23 01:22:36.312909+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.12</td>\n", "      <td>ဘိလပ်တင်မစက် 4မစ် 3 Ton</td>\n", "      <td>2023-08-22 14:28:01.360531</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>43889</td>\n", "      <td>2023-08-23 01:23:02.401421+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-23</td>\n", "      <td>2023-08-23</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>V.13</td>\n", "      <td>ဘိလပ်တင်မစက် 4မစ် 3 Ton</td>\n", "      <td>2023-08-23 09:40:10.371127</td>\n", "      <td>masad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>44098</td>\n", "      <td>2023-08-22 02:17:02.540355+00:00</td>\n", "      <td>active</td>\n", "      <td>2023-08-22</td>\n", "      <td>2023-08-22</td>\n", "      <td>active</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>[]</td>\n", "      <td></td>\n", "      <td>XM16</td>\n", "      <td>ဆိုင်ကယ် 84ယ/32581</td>\n", "      <td>2023-08-17 16:25:41.433779</td>\n", "      <td>car</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    jia_yi_id_vehicle                         lastDate lastStatus   \n", "0                 104 2023-08-23 01:51:50.274259+00:00     active  \\\n", "1                 105 2023-08-23 01:30:34.311725+00:00     active   \n", "2                 106 2023-08-23 01:34:45.590268+00:00     active   \n", "3                 194 2023-08-23 09:32:02.853272+00:00      pause   \n", "4                1569 2023-08-23 01:32:36.766970+00:00     repair   \n", "5                2290 2023-08-23 01:28:30.700506+00:00     active   \n", "6                2343 2023-08-23 01:21:40.294183+00:00     active   \n", "7                7565 2023-08-23 01:24:58.864057+00:00     active   \n", "8               11069 2023-08-23 05:02:38.661614+00:00     active   \n", "9               13417 2023-08-22 01:35:02.115621+00:00     active   \n", "10              16696 2023-08-23 02:23:20.337892+00:00     active   \n", "11              19562 2023-08-23 01:30:29.569976+00:00     active   \n", "12              19937 2023-08-23 01:32:27.944640+00:00     active   \n", "13              20141 2023-08-23 01:35:15.941347+00:00     active   \n", "14              20455 2023-08-23 01:29:49.785993+00:00     active   \n", "15              20929 2023-08-23 02:03:24.349246+00:00     active   \n", "16              22046 2023-08-23 01:38:37.684054+00:00     active   \n", "17              22085 2023-08-23 02:26:32.417622+00:00     active   \n", "18              23021 2023-08-23 03:06:26.332913+00:00     active   \n", "19              23536 2023-08-23 08:41:21.338816+00:00     repair   \n", "20              24529 2023-08-23 03:23:19.437360+00:00     active   \n", "21              24968 2023-08-23 01:42:11.721635+00:00     active   \n", "22              25366 2023-08-22 10:11:55.402742+00:00     repair   \n", "23              25370 2023-08-23 01:23:25.885752+00:00     active   \n", "24              25371 2023-08-22 09:36:15.537435+00:00     repair   \n", "25              26594 2023-08-23 01:47:44.507953+00:00     active   \n", "26              31838 2023-08-23 01:41:08.581101+00:00     active   \n", "27              31966 2023-08-23 08:41:16.665532+00:00     repair   \n", "28              36230 2023-08-23 01:23:11.843362+00:00     active   \n", "29              36555 2023-08-23 02:32:26.584282+00:00     active   \n", "30              36768 2023-08-23 01:22:10.686362+00:00     active   \n", "31              37441 2023-08-23 01:20:35.498823+00:00     active   \n", "32              37620 2023-08-23 02:18:17.842463+00:00     active   \n", "33              37650 2023-08-23 01:46:31.678617+00:00     active   \n", "34              37710 2023-08-23 07:44:43.782183+00:00     active   \n", "35              38201 2023-08-23 01:31:02.533360+00:00     active   \n", "36              38374 2023-08-23 01:25:16.139117+00:00     active   \n", "37              38755 2023-08-23 01:32:40.508502+00:00     active   \n", "38              40658 2023-08-23 01:25:38.011798+00:00     active   \n", "39              40659 2023-08-23 01:23:53.370576+00:00     active   \n", "40              41085 2023-08-23 01:26:21.709305+00:00     active   \n", "41              42401 2023-08-22 09:26:29.128748+00:00     repair   \n", "42              43162 2023-08-23 01:21:15.844477+00:00     active   \n", "43              43887 2023-08-23 01:42:28.000157+00:00     active   \n", "44              43888 2023-08-23 01:22:36.312909+00:00     active   \n", "45              43889 2023-08-23 01:23:02.401421+00:00     active   \n", "46              44098 2023-08-22 02:17:02.540355+00:00     active   \n", "\n", "   descriptDate descriptDateYMD  status            descript  dayDiff   \n", "0    2023-08-23      2023-08-23  active                            0  \\\n", "1    2023-08-23      2023-08-23  active                            0   \n", "2    2023-08-23      2023-08-23  active                            0   \n", "3    2023-08-23      2023-08-23   pause  ကားဆရာခွင့်ယူပါသည်        0   \n", "4    2023-08-23      2023-08-23  repair                            0   \n", "5    2023-08-23      2023-08-23  active                            0   \n", "6    2023-08-23      2023-08-23  active                            0   \n", "7    2023-08-23      2023-08-23  active                            0   \n", "8    2023-08-23      2023-08-23  active                            0   \n", "9    2023-08-22      2023-08-22  active                            1   \n", "10   2023-08-23      2023-08-23  active                            0   \n", "11   2023-08-23      2023-08-23  active                            0   \n", "12   2023-08-23      2023-08-23  active                            0   \n", "13   2023-08-23      2023-08-23  active                            0   \n", "14   2023-08-23      2023-08-23  active                            0   \n", "15   2023-08-23      2023-08-23  active                            0   \n", "16   2023-08-23      2023-08-23  active                            0   \n", "17   2023-08-23      2023-08-23  active                            0   \n", "18   2023-08-23      2023-08-23  active                            0   \n", "19   2023-08-23      2023-08-23  repair                            0   \n", "20   2023-08-23      2023-08-23  active                            0   \n", "21   2023-08-23      2023-08-23  active                            0   \n", "22   2023-08-22      2023-08-22  repair                            1   \n", "23   2023-08-23      2023-08-23  active                            0   \n", "24   2023-08-22      2023-08-22  repair                            1   \n", "25   2023-08-23      2023-08-23  active                            0   \n", "26   2023-08-23      2023-08-23  active                            0   \n", "27   2023-08-23      2023-08-23  repair                            0   \n", "28   2023-08-23      2023-08-23  active                            0   \n", "29   2023-08-23      2023-08-23  active                            0   \n", "30   2023-08-23      2023-08-23  active                            0   \n", "31   2023-08-23      2023-08-23  active                            0   \n", "32   2023-08-23      2023-08-23  active                            0   \n", "33   2023-08-23      2023-08-23  active                            0   \n", "34   2023-08-23      2023-08-23  active                            0   \n", "35   2023-08-23      2023-08-23  active                            0   \n", "36   2023-08-23      2023-08-23  active                            0   \n", "37   2023-08-23      2023-08-23  active                            0   \n", "38   2023-08-23      2023-08-23  active                            0   \n", "39   2023-08-23      2023-08-23  active                            0   \n", "40   2023-08-23      2023-08-23  active                            0   \n", "41   2023-08-22      2023-08-22  repair                            1   \n", "42   2023-08-23      2023-08-23  active                            0   \n", "43   2023-08-23      2023-08-23  active                            0   \n", "44   2023-08-23      2023-08-23  active                            0   \n", "45   2023-08-23      2023-08-23  active                            0   \n", "46   2023-08-22      2023-08-22  active                            1   \n", "\n", "    descriptListCount         descript_list vitus_left jia_yi_idname   \n", "0                   0                    []      342.0          W01A  \\\n", "1                   0                    []      254.0          W02A   \n", "2                   0                    []      160.0          W03A   \n", "3                   1  [ကားဆရာခွင့်ယူပါသည်]      189.0          W06A   \n", "4                   0                    []                     XM07   \n", "5                   0                    []      129.0          W07A   \n", "6                   0                    []                      V.3   \n", "7                   0                    []       37.0          W14D   \n", "8                   0                    []      160.0          W16B   \n", "9                   0                    []                     XM11   \n", "10                  0                    []       37.0          W08A   \n", "11                  0                    []      250.0          W12C   \n", "12                  0                    []                       U4   \n", "13                  0                    []      342.0          W13C   \n", "14                  0                    []       68.0          W04A   \n", "15                  0                    []                     XM01   \n", "16                  0                    []                       U6   \n", "17                  0                    []      226.0          W11B   \n", "18                  0                    []                     W15E   \n", "19                  0                    []                     V.14   \n", "20                  0                    []                     XM10   \n", "21                  0                    []                     V.16   \n", "22                  0                    []                     V.17   \n", "23                  0                    []                      V.4   \n", "24                  1                   [ ]                      V.7   \n", "25                  0                    []                     V.18   \n", "26                  0                    []                     V.20   \n", "27                  0                    []                     None   \n", "28                  0                    []                     V.22   \n", "29                  0                    []      281.0          W10A   \n", "30                  0                    []                      V.5   \n", "31                  0                    []                      V.6   \n", "32                  0                    []       98.0          W09A   \n", "33                  0                    []                      V.8   \n", "34                  0                    []      223.0          W05B   \n", "35                  0                    []      189.0          W17D   \n", "36                  0                    []                      V.9   \n", "37                  0                    []                     XM14   \n", "38                  0                    []                     W18C   \n", "39                  0                    []      249.0          W19B   \n", "40                  0                    []      189.0          W20D   \n", "41                  0                    []      281.0          W21E   \n", "42                  0                    []                     V.10   \n", "43                  0                    []                     V.11   \n", "44                  0                    []                     V.12   \n", "45                  0                    []                     V.13   \n", "46                  0                    []                     XM16   \n", "\n", "                     jia_yi_mm_name                petrol_date   type  \n", "0        နှစ်တန်ကားတင်ဘာပါ  2A-6114 2023-08-17 16:28:03.811950    car  \n", "1         နှစ်တန်ကားတင်ဘာပါ 2A-3844 2023-08-21 16:28:19.463312    car  \n", "2         နှစ်တန်ကားတင်ဘာပါ 3A-9900 2023-08-20 17:01:39.436488    car  \n", "3          နှစ်တန်ကားတင်ဘာပါ P-6308 2023-08-20 17:03:28.077296    car  \n", "4             ဆိုင်ကယ်(ပြာ)6ယ/67075 2023-04-05 15:52:56.628905    car  \n", "5         နှစ်တန်ကားတင်ဘာပါ 1R-1730 2023-08-20 16:55:30.268864    car  \n", "6            ဘိလပ်တင်မစက်အသစ် 3 Ton 2023-08-19 12:22:01.658201  masad  \n", "7          လေးတန်ကားတင်ဘာပါ 9H-3468 2023-08-13 15:01:54.236651    car  \n", "8         နှစ်တန်ကားရိုးရိုး J-7681 2023-08-17 16:23:58.582416    car  \n", "9                 ဆိုင်ကယ် 6ယ/67324 2023-08-18 14:09:15.198748    car  \n", "10        နှစ်တန်ကားတင်ဘာပါ RA-1097 2023-08-19 17:03:42.705688    car  \n", "11       သုံးတန်ကားရိုးရိုး 7E-5470 2023-08-19 17:12:57.726016    car  \n", "12             လိုက်ထရပ်ကား 2F-4084 2023-08-20 16:46:20.247750    car  \n", "13       သုံးတန်ကားရိုးရိုး 4F-5207 2023-08-20 16:49:41.562734    car  \n", "14        နှစ်တန်ကားတင်ဘာပါ 9F-7235 2023-08-20 16:40:00.841525    car  \n", "15               ဆိုင်ကယ် 24ယ/55726 2023-08-21 16:30:27.609074    car  \n", "16                ဟိုက်ဂျက် 1H-7013 2023-08-20 17:06:46.500252    car  \n", "17       နှစ်တန်ကားရိုးရိုး 7H-4121 2023-08-22 16:40:05.069759    car  \n", "18                 12ဘီးကား 7I-9141 2023-08-21 16:37:21.069569    car  \n", "19                ဘိလပ်တင်စက် 3 Ton 2022-05-16 13:55:42.693998  masad  \n", "20               ဆိုင်ကယ် 40ယ/60056 2023-05-07 08:42:47.868964    car  \n", "21  ဘိလပ်မြေ တင်စက် (မ.စက်) 3.5 Ton 2023-08-16 08:40:38.293111  masad  \n", "22   ဘိလပ်မြေတင်စက် (မ.စက်) 3.5 Ton 2023-01-05 16:58:06.479187  masad  \n", "23     လိလပ်မြေတင်စက် (မ.စက်) 3 Ton 2023-08-18 09:14:38.016749  masad  \n", "24     ဘိခပ်မြေတင်စက် (မ.စက်) 3 Ton 2023-07-23 16:41:51.924533  masad  \n", "25             ဘိလပ်မြေတင်စက် 3 Ton 2023-08-20 08:58:48.426343  masad  \n", "26                ဘိလပ်တင်စက် 3 Ton 2023-08-19 15:35:20.032623  masad  \n", "27                             None                        NaT   None  \n", "28           ဘိလပ်တင်မစက်အသစ် 3 Ton 2023-08-22 11:27:04.908595  masad  \n", "29        နှစ်တန်ကားတင်ဘာပါ 7R-6894 2023-08-22 17:05:07.412941    car  \n", "30          ဘိလပ်တင်မစက် 4မစ် 3 Ton 2023-08-23 08:46:36.854200  masad  \n", "31          ဘိလပ်တင်မစက် 4မစ် 3 Ton 2023-08-21 13:59:08.797970  masad  \n", "32        နှစ်တန်ကားတင်ဘာပါ 1S-1776 2023-08-20 16:37:13.638537    car  \n", "33          ဘိလပ်တင်မစက် 4မစ် 3 Ton 2023-08-20 15:58:46.083962  masad  \n", "34       နှစ်တန်ကားရိုးရိုး 7R-2956 2023-08-20 16:32:23.275796    car  \n", "35     နှစ်တန်ခွဲကားတင်ဘာပါ 2R-1964 2023-08-16 11:21:31.056526    car  \n", "36          ဘိလပ်တင်မစက် 4မစ် 3 Ton 2023-08-22 15:41:18.501487  masad  \n", "37                ဆိုင်ကယ် 42ယ/5386 2023-08-17 16:36:45.832984    car  \n", "38       သုံးတန်ကားရိုးရိုး 6R-8220 2023-08-20 16:58:13.149028    car  \n", "39       နှစ်တန်ကားရိုးရိုး 7R-2942 2023-08-20 16:51:54.889636    car  \n", "40     နှစ်တန်ခွဲကားတင်ဘာပါ 2R-1959 2023-08-20 17:00:11.100060    car  \n", "41          12 ဘီးကရိန်းကား 4Q-5836 2023-08-14 15:35:28.927258    car  \n", "42          ဘိလပ်တင်မစက် 5မစ် 5 Ton 2023-08-22 09:17:24.155807  masad  \n", "43          ဘိလပ်တင်မစက် 4မစ် 3 Ton 2023-08-19 08:35:57.653055  masad  \n", "44          ဘိလပ်တင်မစက် 4မစ် 3 Ton 2023-08-22 14:28:01.360531  masad  \n", "45          ဘိလပ်တင်မစက် 4မစ် 3 Ton 2023-08-23 09:40:10.371127  masad  \n", "46               ဆိုင်ကယ် 84ယ/32581 2023-08-17 16:25:41.433779    car  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["import psycopg2\n", "import pandas as pd\n", "\n", "# Assuming you have the connection details\n", "postgresql_shwethe_car_active = psycopg2.connect(\n", "    host=\"************\",\n", "    database=\"shwethe_miniapp_carActive\",\n", "    user=\"postgres\",\n", "    password=\"0818822095\",\n", "    port=\"5436\"\n", ")\n", "\n", "\n", "def get_vehicle_insert():\n", "    # Use a context manager to execute the query\n", "    with postgresql_shwethe_car_active:\n", "        A100012 = pd.read_sql(\"\"\"\n", "            SELECT *\n", "            FROM vehicle_insert \n", "            WHERE datetime > (CURRENT_DATE - INTERVAL '1 days')\n", "            ORDER BY datetime DESC;\n", "        \"\"\", postgresql_shwethe_car_active)\n", "        A100012['descriptDate'] = A100012['datetime'].dt.date\n", "        A100012['descriptDateYMD'] = A100012['datetime'].dt.date\n", "        A100012 = A100012[['datetime', 'descriptDate', 'descriptDateYMD', 'jia_yi_id_vehicle', 'details', 'status']]\n", "\n", "        # Convert 'details' column into a DataFrame and concatenate with the original DataFrame\n", "        A100012['details'] = A100012['details'].fillna('{\"images\": [], \"descript\": \"\", \"active_status\": \"\"}')\n", "        A100012['details'] = A100012['details'].apply(lambda x: json.loads(x) if isinstance(x, str) else x)\n", "        details_A100012 = json_normalize(A100012['details'])\n", "        A100012 = pd.concat([A100012, details_A100012], axis=1).replace({pd.NA: None})\n", "        A100012.drop('details', axis=1, inplace=True)\n", "        A100012 = A100012[['datetime', 'descriptDate', 'descriptDateYMD', 'jia_yi_id_vehicle', 'descript', 'status']]\n", "\n", "        # Group the data by 'sector' and 'date' and calculate the cumulative sum of 'change1d'\n", "        A100012 = A100012.groupby(['jia_yi_id_vehicle', 'descriptDate']).first().reset_index()\n", "        A100012.sort_values(by=['jia_yi_id_vehicle', 'descriptDate'], ascending=[True, False], inplace=True)\n", "\n", "        # Get the current date\n", "        A100012['lastDate'] = A100012.groupby('jia_yi_id_vehicle')['datetime'].transform('first')\n", "        current_date = datetime.today().date()\n", "        A100012['today_date'] = current_date\n", "        A100012['descript'] = A100012['descript'].fillna(\"\")\n", "        unique_descripts = A100012.groupby('jia_yi_id_vehicle')['descript'].unique()\n", "        descript_dict = unique_descripts.apply(lambda x: [val for val in x if val is not None and val != '0' and val != '']).to_dict()\n", "        A100012['descript_list'] = A100012['jia_yi_id_vehicle'].map(descript_dict)\n", "        A100012['descriptListCount'] = A100012['descript_list'].apply(len)\n", "        A100012['descriptDate'] = pd.to_datetime(A100012['descriptDate'])\n", "        A100012['today_date'] = pd.to_datetime(A100012['today_date'])\n", "        A100012['dayDiff'] = (A100012['today_date'] - A100012['descriptDate']).dt.days\n", "        A100012['lastStatus'] = A100012.groupby('jia_yi_id_vehicle')['status'].transform('first')\n", "        A100012 = A100012[['jia_yi_id_vehicle', 'lastDate', 'lastStatus', 'descriptDate', 'descriptDateYMD', 'status', 'descript', 'dayDiff', 'descriptListCount', 'descript_list']]\n", "\n", "        # Define the custom function\n", "        def select_last_row(group):\n", "            non_empty_descript_rows = group[group['descript'] != '']\n", "            if not non_empty_descript_rows.empty:\n", "                return non_empty_descript_rows.iloc[0]\n", "            else:\n", "                return group.iloc[0]\n", "        A100012 = A100012.groupby('jia_yi_id_vehicle').apply(select_last_row).reset_index(drop=True)\n", "        # print(\"*********************************************\")\n", "        # print(A100012.to_dict(\"records\"))\n", "        return A100012\n", "    \n", "def get_carActive_api():\n", "        url = f'{real_backend_api}/shwethe_car_active/api/v1/form/document/car_cocument?months_until_expiration=12'\n", "        df = requests.get(url=url)\n", "        df = df.json()\n", "        df = pd.DataFrame(df)\n", "\n", "        # Convert Unix timestamps to normal date format\n", "        df['start_date'] = pd.to_datetime(df['start_date'], unit='ms')\n", "        df['expiration_date'] = pd.to_datetime(df['expiration_date'], unit='ms')\n", "        df['today'] = pd.to_datetime('today').normalize()\n", "        df['vitus_left'] = (df['expiration_date'] - df['today']).dt.days\n", "        df = df.loc[df['document_types_name'] == 'ကားဝိဓါတ်']\n", "        df = df[['che_liang_id', 'vitus_left']]\n", "        # print(\"222222222222222222222222222222222222222222222\")\n", "        # print(df.to_dict(\"records\"))\n", "        return df\n", "\n", "def get_petrol_car():\n", "        url = \"http://192.168.1.130:8100/shwethe_petrol/api/v1/product/sendListOfCar\"\n", "        response = requests.get(url)\n", "        getCar = response.json()\n", "        getCar = pd.DataFrame(getCar)\n", "\n", "        df100 = getCar.rename(columns={\"che_liang_id\": 'jia_yi_id'})\n", "        url = mongodb_data_api + '/api/v2/search/jia_yi_name_list_id'\n", "        to_dict = df100[['jia_yi_id']].to_dict('records')\n", "        body_raw = {\"data_api\": to_dict}\n", "        getCar2 = requests.get(url=url, json=body_raw).json()\n", "        getCar2 = pd.DataFrame(getCar2)\n", "\n", "        merge = getCar.merge(getCar2, left_on='che_liang_id', right_on='jia_yi_id', how='left')\n", "        merge = merge.fillna(\"\")\n", "\n", "        merge = merge[['che_liang_id', 'jia_yi_idname', 'jia_yi_mm_name', 'datetime', 'type']]\n", "        # print(\"************************************************\")\n", "        # print(merge.to_dict(\"records\"))\n", "        return merge\n", "\n", "def fusion_data():\n", "    vget_vehicle_insert = get_vehicle_insert()\n", "    vget_carActive_api = get_carActive_api()\n", "    vget_petrol_car = get_petrol_car()\n", "    merge_day_left = pd.merge(vget_vehicle_insert, vget_carActive_api, left_on='jia_yi_id_vehicle', right_on='che_liang_id', how=\"left\").fillna('')\n", "\n", "    # merge_day_left['vitus_left'] = pd.to_numeric(merge_day_left['vitus_left'], errors='coerce').astype('Int64')\n", "    # # merge_day_left['vitus_left'] = pd.to_numeric(merge_day_left['vitus_left'], errors='coerce')\n", "\n", "    del merge_day_left['che_liang_id']\n", "    merge_petrol = pd.merge(merge_day_left, vget_petrol_car, left_on='jia_yi_id_vehicle', right_on='che_liang_id', how=\"left\").replace({pd.NA: None})\n", "    del merge_petrol['che_liang_id']\n", "    merge_petrol.rename(columns={'datetime': 'petrol_date'}, inplace=True)\n", "    merge_petrol['petrol_date'] = pd.to_datetime(merge_petrol['petrol_date'])\n", "    # merge_petrol = merge_petrol[pd.to_datetime(merge_petrol['petrol_date']).notnull()]\n", "\n", "    # merge_petrol = merge_petrol.to_dict(\"records\")\n", "    # print(\"44444444444444444444444444444444444444\")\n", "    # print(merge_petrol)\n", "    return merge_petrol\n", "\n", "\n", "def airflow():\n", "    # Sample JSON data\n", "    # detail_data = [{'jia_yi_id_vehicle': 104, 'lastDate': '2023-07-31 02:40:08.060443+0000',\n", "    #                 'lastStatus': 'active', 'descriptDate': '2023-07-31 00:00:00',\n", "    #                 'descriptDateYMD': '2023-07-31', 'status': 'active', 'descript': '',\n", "    #                 'dayDiff': 0, 'descriptListCount': 0, 'descript_list': [],\n", "    #                 'vitus_left': -1.0, 'jia_yi_idname': 'W01A',\n", "    #                 'jia_yi_mm_name': 'နှစ်တန်ကားတင်ဘာပါ  2A-6114',\n", "    #                 'petrol_date': '2023-07-30 17:14:25.408519', 'type': 'car'}]\n", "    detail_data = fusion_data()\n", "    detail_data = detail_data.to_dict(\"records\")\n", "\n", "    # Convert datetime objects to strings\n", "    for data in detail_data:\n", "        data['lastDate'] = str(pd.Timestamp(data['lastDate']))\n", "        data['descriptDate'] = str(pd.Timestamp(data['descriptDate']))\n", "        data['descriptDateYMD'] = str(data['descriptDateYMD'])\n", "        data['petrol_date'] = str(pd.Timestamp(data['petrol_date']))\n", "\n", "    # SQL query to insert data into the \"expirevitus\" table\n", "    insert_query = \"\"\"\n", "        INSERT INTO expirevitus (auto_id, type, detail, datetime, period)\n", "        VALUES (DEFAULT, %s, %s, %s, %s);\n", "    \"\"\"\n", "\n", "    # Use a cursor to execute the insert query\n", "    with postgresql_shwethe_car_active.cursor() as cursor:\n", "        # Convert the detail data to JSONB format\n", "        detail_jsonb = Json(detail_data)\n", "\n", "        # Get the current timestamp for the \"datetime\" column\n", "        current_datetime = datetime.now()\n", "\n", "        # Execute the insert query with the JSONB data and current timestamp\n", "        cursor.execute(insert_query, ('expirevitus', detail_jsonb, current_datetime, 365))\n", "\n", "        # Commit the changes to the database\n", "        postgresql_shwethe_car_active.commit()\n", "    # return detail_data\n", "    # print(\"55555555555555555555555555555555555555555555\")\n", "    # print(detail_data)\n", "\n", "# try:\n", "#     airflow()\n", "# except Exception as e:\n", "#     print(f\"Error: {e}\")\n", "# finally:\n", "#     # Always close the connection, even if an exception occurs\n", "#     postgresql_shwethe_car_active.close()\n", "    \n", "# print(vvv.info())\n", "# vvv = vvv[['vitus_left']]\n", "# vvv.sort_values(by='vitus_left', ascending=False)\n", "\n", "vvv = fusion_data()\n", "postgresql_shwethe_car_active.close()\n", "vvv"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}