{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('../../../../../app/')\n", "import requests\n", "from sqlmodel import Session, select, SQLModel\n", "from src.shwethe_car_active.database import get_session\n", "from src.shwethe_car_active.src.models.models import vehicle_insert\n", "import json\n", "import pandas as pd\n", "from sqlalchemy import and_\n", "from contextlib import contextmanager\n", "from src.Connect.https_connect import mongodb_data_api, shwethe_mysql_api\n", "from src.Connect.postgresql_nern import real_backend_api, postgresql_shwethe_miniapp_carActive\n", "import logging\n", "from datetime import datetime, timedelta, date\n", "import pytz\n", "from collections import defaultdict\n", "from sqlmodel import update\n", "from sqlalchemy import distinct\n", "from pandas import json_normalize\n", "import requests\n", "from psycopg2.extras import <PERSON>son\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_14426/2544610282.py:50: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.\n", "  A100012 = pd.read_sql(\"\"\"\n", "/tmp/ipykernel_14426/2544610282.py:50: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.\n", "  A100012 = pd.read_sql(\"\"\"\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>jia_yi_id_vehicle</th>\n", "      <th>status</th>\n", "      <th>type</th>\n", "      <th>fen_dian_id</th>\n", "      <th>where</th>\n", "      <th>status2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-10-25 07:09:08.835155+00:00</td>\n", "      <td>37710</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-10-25 04:30:56.217680+00:00</td>\n", "      <td>36768</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-10-25 03:56:34.110819+00:00</td>\n", "      <td>20455</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-10-25 03:16:03.795536+00:00</td>\n", "      <td>23021</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-10-25 03:15:42.170350+00:00</td>\n", "      <td>48548</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-10-25 03:14:55.521027+00:00</td>\n", "      <td>40658</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-10-25 03:14:41.562649+00:00</td>\n", "      <td>31966</td>\n", "      <td>broken</td>\n", "      <td>masad</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>broken</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-10-25 03:14:38.297294+00:00</td>\n", "      <td>25366</td>\n", "      <td>broken</td>\n", "      <td>masad</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>broken</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-10-25 03:14:34.904420+00:00</td>\n", "      <td>23536</td>\n", "      <td>broken</td>\n", "      <td>masad</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>broken</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-10-25 03:11:38.897141+00:00</td>\n", "      <td>19562</td>\n", "      <td>pause</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>pause</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-10-25 03:11:30.981916+00:00</td>\n", "      <td>105</td>\n", "      <td>pause</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>pause</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-10-25 03:11:24.797239+00:00</td>\n", "      <td>16696</td>\n", "      <td>pause</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>pause</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-10-25 03:10:02.627231+00:00</td>\n", "      <td>24968</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-10-25 03:07:48.693495+00:00</td>\n", "      <td>38374</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-10-25 03:07:41.654812+00:00</td>\n", "      <td>31838</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-10-25 03:06:08.421196+00:00</td>\n", "      <td>26594</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-10-25 02:47:52.875458+00:00</td>\n", "      <td>2290</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-10-25 02:42:08.286469+00:00</td>\n", "      <td>37650</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2024-10-25 02:41:16.564950+00:00</td>\n", "      <td>43888</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-10-25 02:30:26.954962+00:00</td>\n", "      <td>13417</td>\n", "      <td>active</td>\n", "      <td>motorcycle</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2024-10-25 02:28:44.677838+00:00</td>\n", "      <td>2343</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2024-10-25 02:26:45.115425+00:00</td>\n", "      <td>44098</td>\n", "      <td>active_problem</td>\n", "      <td>motorcycle</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2024-10-25 02:25:37.330164+00:00</td>\n", "      <td>43887</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2024-10-25 02:24:35.252660+00:00</td>\n", "      <td>43889</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2024-10-25 02:22:09.160407+00:00</td>\n", "      <td>194</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2024-10-25 02:18:33.305428+00:00</td>\n", "      <td>41085</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2024-10-25 02:12:57.986726+00:00</td>\n", "      <td>25370</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2024-10-25 02:12:05.266834+00:00</td>\n", "      <td>106</td>\n", "      <td>repair</td>\n", "      <td>car</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>repair</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2024-10-25 02:11:24.247210+00:00</td>\n", "      <td>104</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2024-10-25 02:11:17.586911+00:00</td>\n", "      <td>20929</td>\n", "      <td>active</td>\n", "      <td>motorcycle</td>\n", "      <td>4</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>2024-10-25 02:10:39.222901+00:00</td>\n", "      <td>20141</td>\n", "      <td>active_problem</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>2024-10-25 02:10:36.632152+00:00</td>\n", "      <td>37620</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>2024-10-25 02:10:14.601206+00:00</td>\n", "      <td>36230</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2024-10-25 02:09:42.493071+00:00</td>\n", "      <td>38755</td>\n", "      <td>active</td>\n", "      <td>motorcycle</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>2024-10-25 02:09:36.245031+00:00</td>\n", "      <td>37441</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>2024-10-25 02:08:22.422463+00:00</td>\n", "      <td>42401</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>2024-10-25 02:07:20.283224+00:00</td>\n", "      <td>40659</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>2024-10-24 10:31:11.488216+00:00</td>\n", "      <td>11069</td>\n", "      <td>repair</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>workShop</td>\n", "      <td>repair</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>2024-10-24 10:31:04.600534+00:00</td>\n", "      <td>22085</td>\n", "      <td>broken</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>workShop</td>\n", "      <td>broken</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>2024-10-24 10:30:58.573699+00:00</td>\n", "      <td>22046</td>\n", "      <td>repair</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>workShop</td>\n", "      <td>repair</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>2024-10-24 10:30:35.092334+00:00</td>\n", "      <td>19937</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>shop</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>2024-10-24 10:28:00.872723+00:00</td>\n", "      <td>25371</td>\n", "      <td>broken</td>\n", "      <td>masad</td>\n", "      <td>1</td>\n", "      <td>workShop</td>\n", "      <td>broken</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>2024-10-24 01:46:22.032139+00:00</td>\n", "      <td>43162</td>\n", "      <td>active</td>\n", "      <td>masad</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>2024-10-24 01:39:28.734684+00:00</td>\n", "      <td>36555</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>2024-10-24 01:35:12.590233+00:00</td>\n", "      <td>7565</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>2024-10-24 01:31:58.221687+00:00</td>\n", "      <td>38201</td>\n", "      <td>active</td>\n", "      <td>car</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>2024-10-23 10:32:32.423131+00:00</td>\n", "      <td>24529</td>\n", "      <td>active</td>\n", "      <td>motorcycle</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>active</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>2023-08-30 01:56:39.619236+00:00</td>\n", "      <td>1569</td>\n", "      <td>disappear</td>\n", "      <td>motorcycle</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>disappear</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           datetime  jia_yi_id_vehicle          status   \n", "0  2024-10-25 07:09:08.835155+00:00              37710          active  \\\n", "1  2024-10-25 04:30:56.217680+00:00              36768          active   \n", "2  2024-10-25 03:56:34.110819+00:00              20455          active   \n", "3  2024-10-25 03:16:03.795536+00:00              23021          active   \n", "4  2024-10-25 03:15:42.170350+00:00              48548          active   \n", "5  2024-10-25 03:14:55.521027+00:00              40658          active   \n", "6  2024-10-25 03:14:41.562649+00:00              31966          broken   \n", "7  2024-10-25 03:14:38.297294+00:00              25366          broken   \n", "8  2024-10-25 03:14:34.904420+00:00              23536          broken   \n", "9  2024-10-25 03:11:38.897141+00:00              19562           pause   \n", "10 2024-10-25 03:11:30.981916+00:00                105           pause   \n", "11 2024-10-25 03:11:24.797239+00:00              16696           pause   \n", "12 2024-10-25 03:10:02.627231+00:00              24968          active   \n", "13 2024-10-25 03:07:48.693495+00:00              38374          active   \n", "14 2024-10-25 03:07:41.654812+00:00              31838          active   \n", "15 2024-10-25 03:06:08.421196+00:00              26594          active   \n", "16 2024-10-25 02:47:52.875458+00:00               2290          active   \n", "17 2024-10-25 02:42:08.286469+00:00              37650          active   \n", "18 2024-10-25 02:41:16.564950+00:00              43888          active   \n", "19 2024-10-25 02:30:26.954962+00:00              13417          active   \n", "20 2024-10-25 02:28:44.677838+00:00               2343          active   \n", "21 2024-10-25 02:26:45.115425+00:00              44098  active_problem   \n", "22 2024-10-25 02:25:37.330164+00:00              43887          active   \n", "23 2024-10-25 02:24:35.252660+00:00              43889          active   \n", "24 2024-10-25 02:22:09.160407+00:00                194          active   \n", "25 2024-10-25 02:18:33.305428+00:00              41085          active   \n", "26 2024-10-25 02:12:57.986726+00:00              25370          active   \n", "27 2024-10-25 02:12:05.266834+00:00                106          repair   \n", "28 2024-10-25 02:11:24.247210+00:00                104          active   \n", "29 2024-10-25 02:11:17.586911+00:00              20929          active   \n", "30 2024-10-25 02:10:39.222901+00:00              20141  active_problem   \n", "31 2024-10-25 02:10:36.632152+00:00              37620          active   \n", "32 2024-10-25 02:10:14.601206+00:00              36230          active   \n", "33 2024-10-25 02:09:42.493071+00:00              38755          active   \n", "34 2024-10-25 02:09:36.245031+00:00              37441          active   \n", "35 2024-10-25 02:08:22.422463+00:00              42401          active   \n", "36 2024-10-25 02:07:20.283224+00:00              40659          active   \n", "37 2024-10-24 10:31:11.488216+00:00              11069          repair   \n", "38 2024-10-24 10:31:04.600534+00:00              22085          broken   \n", "39 2024-10-24 10:30:58.573699+00:00              22046          repair   \n", "40 2024-10-24 10:30:35.092334+00:00              19937          active   \n", "41 2024-10-24 10:28:00.872723+00:00              25371          broken   \n", "42 2024-10-24 01:46:22.032139+00:00              43162          active   \n", "43 2024-10-24 01:39:28.734684+00:00              36555          active   \n", "44 2024-10-24 01:35:12.590233+00:00               7565          active   \n", "45 2024-10-24 01:31:58.221687+00:00              38201          active   \n", "46 2024-10-23 10:32:32.423131+00:00              24529          active   \n", "47 2023-08-30 01:56:39.619236+00:00               1569       disappear   \n", "\n", "          type  fen_dian_id     where    status2  \n", "0          car            3      None     active  \n", "1        masad            1      None     active  \n", "2          car            1      None     active  \n", "3          car            1      None     active  \n", "4        masad            1      None     active  \n", "5          car            1      None     active  \n", "6        masad            2      None     broken  \n", "7        masad            2      None     broken  \n", "8        masad            2      None     broken  \n", "9          car            1      None      pause  \n", "10         car            1      None      pause  \n", "11         car            1      None      pause  \n", "12       masad            1      None     active  \n", "13       masad            3      None     active  \n", "14       masad            1      None     active  \n", "15       masad            1      None     active  \n", "16         car            1      None     active  \n", "17       masad            2      None     active  \n", "18       masad            2      None     active  \n", "19  motorcycle            1      None     active  \n", "20       masad            2      None     active  \n", "21  motorcycle            1      None     active  \n", "22       masad            2      None     active  \n", "23       masad            2      None     active  \n", "24         car            3      None     active  \n", "25         car            3      None     active  \n", "26       masad            3      None     active  \n", "27         car            2      None     repair  \n", "28         car            3      None     active  \n", "29  motorcycle            4      None     active  \n", "30         car            1      None     active  \n", "31         car            1      None     active  \n", "32       masad            3      None     active  \n", "33  motorcycle            1      None     active  \n", "34       masad            3      None     active  \n", "35         car            1      None     active  \n", "36         car            1      None     active  \n", "37         car            1  workShop     repair  \n", "38         car            1  workShop     broken  \n", "39         car            1  workShop     repair  \n", "40         car            1      shop     active  \n", "41       masad            1  workShop     broken  \n", "42       masad            2      None     active  \n", "43         car            2      None     active  \n", "44         car            1      None     active  \n", "45         car            1      None     active  \n", "46  motorcycle            2      None     active  \n", "47  motorcycle            1      None  disappear  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>status2</th>\n", "      <th>fen_qty</th>\n", "      <th>fen(qty)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>car</td>\n", "      <td>active</td>\n", "      <td>[{'shop': '1', 'qty': '11) '}, {'shop': '2', '...</td>\n", "      <td>1(11) , 2(1) , 3(4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>car</td>\n", "      <td>broken</td>\n", "      <td>[{'shop': 'workShop', 'qty': '1'}]</td>\n", "      <td>workShop(1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>car</td>\n", "      <td>pause</td>\n", "      <td>[{'shop': '1', 'qty': '3'}]</td>\n", "      <td>1(3)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>car</td>\n", "      <td>repair</td>\n", "      <td>[{'shop': '2', 'qty': '1) '}, {'shop': 'workSh...</td>\n", "      <td>2(1) , workShop(2)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>masad</td>\n", "      <td>active</td>\n", "      <td>[{'shop': '1', 'qty': '5) '}, {'shop': '2', 'q...</td>\n", "      <td>1(5) , 2(6) , 3(4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>masad</td>\n", "      <td>broken</td>\n", "      <td>[{'shop': '2', 'qty': '3) '}, {'shop': 'workSh...</td>\n", "      <td>2(3) , workShop(1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>motorcycle</td>\n", "      <td>active</td>\n", "      <td>[{'shop': '1', 'qty': '3) '}, {'shop': '2', 'q...</td>\n", "      <td>1(3) , 2(1) , 4(1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>motorcycle</td>\n", "      <td>disappear</td>\n", "      <td>[{'shop': '1', 'qty': '1'}]</td>\n", "      <td>1(1)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         type    status2                                            fen_qty   \n", "0         car     active  [{'shop': '1', 'qty': '11) '}, {'shop': '2', '...  \\\n", "1         car     broken                 [{'shop': 'workShop', 'qty': '1'}]   \n", "2         car      pause                        [{'shop': '1', 'qty': '3'}]   \n", "3         car     repair  [{'shop': '2', 'qty': '1) '}, {'shop': 'workSh...   \n", "4       masad     active  [{'shop': '1', 'qty': '5) '}, {'shop': '2', 'q...   \n", "5       masad     broken  [{'shop': '2', 'qty': '3) '}, {'shop': 'workSh...   \n", "6  motorcycle     active  [{'shop': '1', 'qty': '3) '}, {'shop': '2', 'q...   \n", "7  motorcycle  disappear                        [{'shop': '1', 'qty': '1'}]   \n", "\n", "              fen(qty)  \n", "0  1(11) , 2(1) , 3(4)  \n", "1          workShop(1)  \n", "2                 1(3)  \n", "3   2(1) , workShop(2)  \n", "4   1(5) , 2(6) , 3(4)  \n", "5   2(3) , workShop(1)  \n", "6   1(3) , 2(1) , 4(1)  \n", "7                 1(1)  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import psycopg2\n", "import pandas as pd\n", "\n", "# Assuming you have the connection details\n", "postgresql_shwethe_car_active = psycopg2.connect(\n", "    host=\"************\",\n", "    database=\"shwethe_miniapp_carActive\",\n", "    user=\"postgres\",\n", "    password=\"0818822095\",\n", "    port=\"5436\"\n", ")\n", "\n", "\n", "# def get_vehicle_insert():\n", "#     # Use a context manager to execute the query\n", "#     with postgresql_shwethe_car_active:\n", "#         A100012 = pd.read_sql(\"\"\"\n", "#             SELECT datetime, jia_yi_id_vehicle, status\n", "#             FROM vehicle_insert \n", "#             WHERE datetime > (CURRENT_DATE - INTERVAL '1 days')\n", "#             ORDER BY datetime DESC;\n", "#         \"\"\", postgresql_shwethe_car_active)\n", "#         # A100012 = A100012[['datetime', 'jia_yi_id_vehicle', 'status']]\n", "\n", "#         # print(\"*********************************************\")\n", "#         # print(A100012.to_dict(\"records\"))\n", "#         return A100012\n", "\n", "\n", "# def get_vehicle_insert():\n", "#     # Use a context manager to execute the query\n", "#     with postgresql_shwethe_car_active:\n", "#         A100012 = pd.read_sql(\"\"\"\n", "#             SELECT vi.datetime, vi.jia_yi_id_vehicle, vi.status, v.type, v.fen_dian_id, vi.where\n", "#             FROM vehicle_insert vi\n", "#             JOIN (\n", "#                 SELECT jia_yi_id_vehicle, MAX(datetime) AS last_datetime\n", "#                 FROM vehicle_insert\n", "#                 GROUP BY jia_yi_id_vehicle\n", "#             ) latest ON vi.jia_yi_id_vehicle = latest.jia_yi_id_vehicle AND vi.datetime = latest.last_datetime\n", "#             JOIN vehicle v ON vi.jia_yi_id_vehicle = v.jia_yi_id\n", "#             ORDER BY vi.datetime DESC;\n", "#         \"\"\", postgresql_shwethe_car_active)\n", "  \n", "#         return A100012\n", "\n", "def get_vehicle_insert():\n", "    # Use a context manager to execute the query\n", "    with postgresql_shwethe_car_active:\n", "        A100012 = pd.read_sql(\"\"\"\n", "            SELECT vi.datetime, vi.jia_yi_id_vehicle, vi.status, v.type, v.fen_dian_id, vi.where\n", "            FROM vehicle_insert vi\n", "            JOIN (\n", "                SELECT jia_yi_id_vehicle, MAX(datetime) AS last_datetime\n", "                FROM vehicle_insert\n", "                GROUP BY jia_yi_id_vehicle\n", "            ) latest ON vi.jia_yi_id_vehicle = latest.jia_yi_id_vehicle AND vi.datetime = latest.last_datetime\n", "            JOIN vehicle v ON vi.jia_yi_id_vehicle = v.jia_yi_id\n", "            ORDER BY vi.datetime DESC;\n", "        \"\"\", postgresql_shwethe_car_active)\n", "\n", "        # Create a new column 'status2' where 'active_problem' is changed to 'active'\n", "        A100012['status2'] = A100012['status'].apply(lambda x: 'active' if x == 'active_problem' else x)\n", "\n", "        return A100012\n", "    \n", "\n", "# def cal():\n", "#     A100012 = get_vehicle_insert()\n", "\n", "#     # Create the 'fen' column based on the conditions\n", "#     A100012['fen'] = A100012.apply(lambda row: row['where'] if pd.notnull(row['where']) else row['fen_dian_id'], axis=1)\n", "\n", "#     # Group by 'type', 'status', and 'fen' and count the occurrences\n", "#     grouped = A100012.groupby(['type', 'status2', 'fen']).size().reset_index(name='qty')\n", "\n", "#     # Pivot the table to get the desired format\n", "#     pivot_table = grouped.pivot_table(index=['type', 'status2'], columns='fen', values='qty', aggfunc='first')\n", "\n", "#     # Fill NaN values with 0\n", "#     pivot_table = pivot_table.fillna(0)\n", "\n", "#     # Format the output\n", "#     result = pivot_table.reset_index()\n", "#     result.columns = ['type', 'status2'] + [f'{col}' for col in result.columns if col not in ['type', 'status2']]\n", "\n", "#     # Combine the columns into a single string, showing only non-zero quantities\n", "#     result['fen(qty)'] = result.apply(lambda row: ' , '.join([f'{col}({int(row[col])})' for col in result.columns if col not in ['type', 'status2'] and row[col] > 0]), axis=1)\n", "#     result['fen_qty'] = result['fen(qty)']\n", "\n", "#     # Drop the individual columns\n", "#     result = result[['type', 'status2', 'fen_qty', 'fen(qty)']]\n", "\n", "\n", "#     def convert_fen_qty(fen_qty_str):\n", "#         if pd.isnull(fen_qty_str):\n", "#             return []\n", "#         fen_qty_list = fen_qty_str.split(', ')\n", "#         fen_qty_dict_list = []\n", "#         for fen_qty in fen_qty_list:\n", "#             shop, qty = fen_qty.split('(')\n", "#             qty = qty.rstrip(')')\n", "#             fen_qty_dict_list.append({\"shop\": shop, \"qty\": qty})\n", "#         return fen_qty_dict_list\n", "\n", "#     # Apply the function to the 'fen_qty' column\n", "#     result['fen_qty'] = result['fen_qty'].apply(convert_fen_qty)\n", "\n", "\n", "#     return result\n", "\n", "def cal():\n", "    A100012 = get_vehicle_insert()\n", "\n", "    # Create the 'fen' column based on the conditions\n", "    A100012['fen'] = A100012.apply(lambda row: row['fen_dian_id'] if row['where'] == 'shop' else row['where'] if pd.notnull(row['where']) else row['fen_dian_id'], axis=1)\n", "\n", "    # Group by 'type', 'status', and 'fen' and count the occurrences\n", "    grouped = A100012.groupby(['type', 'status2', 'fen']).size().reset_index(name='qty')\n", "\n", "    # Pivot the table to get the desired format\n", "    pivot_table = grouped.pivot_table(index=['type', 'status2'], columns='fen', values='qty', aggfunc='first')\n", "\n", "    # Fill NaN values with 0\n", "    pivot_table = pivot_table.fillna(0)\n", "\n", "    # Format the output\n", "    result = pivot_table.reset_index()\n", "    result.columns = ['type', 'status2'] + [f'{col}' for col in result.columns if col not in ['type', 'status2']]\n", "\n", "    # Combine the columns into a single string, showing only non-zero quantities\n", "    result['fen(qty)'] = result.apply(lambda row: ' , '.join([f'{col}({int(row[col])})' for col in result.columns if col not in ['type', 'status2'] and row[col] > 0]), axis=1)\n", "    result['fen_qty'] = result['fen(qty)']\n", "\n", "    # Drop the individual columns\n", "    result = result[['type', 'status2', 'fen_qty', 'fen(qty)']]\n", "\n", "    def convert_fen_qty(fen_qty_str):\n", "        if pd.isnull(fen_qty_str):\n", "            return []\n", "        fen_qty_list = fen_qty_str.split(', ')\n", "        fen_qty_dict_list = []\n", "        for fen_qty in fen_qty_list:\n", "            shop, qty = fen_qty.split('(')\n", "            qty = qty.rstrip(')')\n", "            fen_qty_dict_list.append({\"shop\": shop, \"qty\": qty})\n", "        return fen_qty_dict_list\n", "\n", "    # Apply the function to the 'fen_qty' column\n", "    result['fen_qty'] = result['fen_qty'].apply(convert_fen_qty)\n", "\n", "    return result\n", "\n", "\n", "aaa = get_vehicle_insert()\n", "bbb = cal()\n", "postgresql_shwethe_car_active.close()\n", "display(aaa)\n", "display(bbb)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}