from fastapi import APIRouter, Depends, Query
from typing import Optional
from pydantic.types import List
from sqlmodel import Session
from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_guiling.database import get_session
from src.shwethe_guiling.src.crud.crud import (
    sendToBigTable100
)
# from src.shwethe_guiling.src.models.models import(
#     receive_deposit_worker_post,
#     car_rent_insert_post,
#     car_rent_update_post,
#     descript_option_post,
#     car_rent_receive_info_post
# )
router = APIRouter()


@router.get("/getCarAtive")
def getCarAtive(db: Session = Depends(get_session)):
    return "getCarAtive100()"

@router.post("/sendToBigTable")
def sendToBigTable(db: Session = Depends(get_session)):
    return sendToBigTable100(db=db)
