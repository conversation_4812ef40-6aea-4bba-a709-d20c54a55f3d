from typing import Optional

from sqlmodel import Field, SQLModel, create_engine

from src.Connect.postgresql_sqlalchemy import db_db_info
from src.Connect.postgresql_sqlalchemy import db_db_info_engine as tb_goods_category_engine
from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import Field, SQLModel, create_engine,Column
from src.Connect.postgresql_sqlalchemy import db_db_info_engine as tb_brand_engine

class tb_brand_base(SQLModel):
    brand_id: int


class tb_brand(tb_brand_base, table=True):
    brand_id: Optional[int] = Field(default=None, primary_key=True)
    name: str
    images: dict = Field(sa_column=Column(JSONB), default={})
    letter:str

# SQLModel.metadata.create_all(tb_brand_engine)