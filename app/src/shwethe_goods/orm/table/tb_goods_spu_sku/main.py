from typing import Optional

from sqlmodel import Field, SQLModel, create_engine,Column
from sqlalchemy.dialects.postgresql import JSONB

from src.Connect.postgresql_sqlalchemy import db_db_info
from src.Connect.postgresql_sqlalchemy import db_db_info_engine as tb_goods_sku_engine



class tb_goods_sku_base(SQLModel):
    auto_id: int



class tb_goods_sku(tb_goods_sku_base, table=True):
    __table_args__ = {'extend_existing': True}
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    sku_id: Optional[str] = None
    spu_id: Optional[str] = None
    goods_id : int
    spg_id : int 
    brand_id : int
    goods_qty : float
    goods_price : float
    goods_param: dict = Field(sa_column=Column(JSONB), default={})
    gui_ge_param: dict = Field(sa_column=Column(JSONB), default={})
    images: dict = Field(sa_column=Column(JSONB), default={})
    

# tb_goods_sku_engine = create_engine(db_db_info, echo=True,    
#         max_overflow=2,  # 超过连接池大小外最多创建的连接
#         pool_size=1,  # 连接池大小
#         pool_timeout=30,  # 池中没有线程最多等待的时间，否则报错
#         pool_recycle=60  # 多久之后对线程池中的线程进行一次连接的回收（重置）
#         )

# SQLModel.metadata.create_all(tb_goods_sku_engine)