from typing import List, Optional

from sqlmodel import Field, SQLModel, create_engine,Relationship

from src.Connect.postgresql_sqlalchemy import db_db_info_engine as tb_goods_sh_goods_gui_ge_engine


class tb_goods_sh_goods_gui_ge_base(SQLModel):
    auto_id: int

class tb_goods_sh_goods_gui_ge(tb_goods_sh_goods_gui_ge_base, table=True):
    auto_id : Optional[int] = Field(default=None, primary_key=True)
    parent_id : str
    category_id : str
    mm_name : str

# SQLModel.metadata.create_all(tb_goods_sh_goods_gui_ge_engine)