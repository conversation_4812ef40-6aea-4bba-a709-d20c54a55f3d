from typing import List, Optional

from sqlmodel import Field, SQLModel, create_engine,Relationship

from src.Connect.postgresql_sqlalchemy import db_db_info_engine as tb_goods_sh_goods_selector_engine


class tb_goods_sh_goods_selector_base(SQLModel):
    auto_id: int

class tb_goods_sh_goods_selector(tb_goods_sh_goods_selector_base, table=True):
    auto_id : Optional[int] = Field(default=None, primary_key=True)
    parent_id : int
    category_id : int
    mm_name : str
    # heroes: List["tb_goods_sh_goods_selector_value"] = Relationship(back_populates="team")


class tb_goods_sh_goods_selector_value_base(SQLModel):
    auto_id: int

class tb_goods_sh_goods_selector_value(tb_goods_sh_goods_selector_value_base, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    goods_id: int
    selector_id: int
    selector_value : str
    # selector_value: Optional[int] = Field(default=None, foreign_key="tb_goods_sh_goods_selector.auto_id")
    # team: Optional[tb_goods_sh_goods_selector] = Relationship(back_populates="heroes")

# tb_goods_translate_entry_engine = create_engine(db_db_info, echo=True,   
#         max_overflow=2,  # 超过连接池大小外最多创建的连接
#         pool_size=1,  # 连接池大小
#         pool_timeout=30,  # 池中没有线程最多等待的时间，否则报错
#         pool_recycle=60,  # 多久之后对线程池中的线程进行一次连接的回收（重置）
#         )

# SQLModel.metadata.create_all(tb_goods_sh_goods_selector_engine)