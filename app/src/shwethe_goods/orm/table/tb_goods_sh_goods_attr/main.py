from typing import List, Optional

from sqlmodel import Field, SQLModel, create_engine,Relationship

from src.Connect.postgresql_sqlalchemy import db_db_info_engine as tb_goods_sh_goods_attr_engine

class tb_goods_sh_goods_attr_value_base(SQLModel):
    auto_id: int

class tb_goods_sh_goods_attr_value(tb_goods_sh_goods_attr_value_base, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    goods_id: int
    # attr_id: str
    attr_id: int
    attr_value: str
    
class tb_goods_sh_goods_attr_base(SQLModel):
    auto_id: int

class tb_goods_sh_goods_attr(tb_goods_sh_goods_attr_base, table=True):
    auto_id : Optional[int] = Field(default=None, primary_key=True)
    parent_id : int
    category_id : int
    mm_name : str
    fen_ji : int






    # attr_value: Optional[int] = Field(default=None, foreign_key="tb_goods_sh_goods_attr.auto_id")
    # team: Optional[tb_goods_sh_goods_attr] = Relationship(back_populates="heroes")

# tb_goods_translate_entry_engine = create_engine(db_db_info, echo=True,   
#         max_overflow=2,  # 超过连接池大小外最多创建的连接
#         pool_size=1,  # 连接池大小
#         pool_timeout=30,  # 池中没有线程最多等待的时间，否则报错
#         pool_recycle=60,  # 多久之后对线程池中的线程进行一次连接的回收（重置）
#         )

# SQLModel.metadata.create_all(tb_goods_sh_goods_attr_engine)