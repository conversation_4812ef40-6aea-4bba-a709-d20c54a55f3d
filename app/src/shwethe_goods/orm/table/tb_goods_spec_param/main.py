from typing import Optional

from sqlmodel import Field, SQLModel, create_engine

from src.Connect.postgresql_sqlalchemy import db_db_info
from src.Connect.postgresql_sqlalchemy import db_db_info_engine as tb_goods_spec_param_engine



class tb_goods_spec_param_base(SQLModel):
    goods_spec_param_id: int


class tb_goods_spec_param(tb_goods_spec_param_base, table=True):
    goods_spec_param_id: Optional[int] = Field(default=None, primary_key=True)
    segments: bool
    spec_type_id:int
    translation_id:int
    spg_id:int
    

# tb_goods_spec_param_engine = create_engine(db_db_info, echo=True,    
#         max_overflow=2,  # 超过连接池大小外最多创建的连接
#         pool_size=1,  # 连接池大小
#         pool_timeout=30,  # 池中没有线程最多等待的时间，否则报错
#         pool_recycle=60  # 多久之后对线程池中的线程进行一次连接的回收（重置）
#         )

# SQLModel.metadata.create_all(tb_goods_spec_param_engine)