from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header,HTTPException,Query
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np
from sqlmodel import Session,select,or_,and_,SQLModel
router = APIRouter()
type_list = [1,2,3]
language_code_list = 'mm'


@router.get("/goods_attr/{locale}", status_code = 200)
def goods_selector(locale: str,parent : int = 0,category_id : int = 0):
    from src.shwethe_goods.orm.table.tb_goods_sh_goods_attr.main import tb_goods_sh_goods_attr_engine,tb_goods_sh_goods_attr
    def get_data():
        with Session(tb_goods_sh_goods_attr_engine) as session:
            if parent == 0:
                heroes = session.exec(select(tb_goods_sh_goods_attr).where(tb_goods_sh_goods_attr.category_id == category_id)).all()
            else:
                heroes = session.exec(select(tb_goods_sh_goods_attr).where(and_(tb_goods_sh_goods_attr.category_id == category_id,tb_goods_sh_goods_attr.parent_id == parent))).all()
                
            return heroes
    ANS10001 = get_data()
    return ANS10001

@router.get("/goods_attr/{locale}/{goods_id}", status_code = 200)
def goods_selector(goods_id:int,locale: str,parent : int = 0):
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku
    from src.shwethe_goods.orm.table.tb_goods_sh_goods_attr.main import tb_goods_sh_goods_attr_engine,tb_goods_sh_goods_attr,tb_goods_sh_goods_attr_value
    
    def get_product(goods_id):
        with Session(tb_goods_sku_engine) as session:
            # T100001 = pd.read_sql(session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_id == goods_id).limit(1)).statement,con=session.bind)
            heroes = session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_id == goods_id).limit(1))
                
            return heroes

    def get_data(category_id):
        with Session(tb_goods_sh_goods_attr_engine) as session:
            print(category_id)
            heroes = session.exec(select(tb_goods_sh_goods_attr).where(tb_goods_sh_goods_attr.category_id == int(category_id)))

            return heroes


    def get_data_2(goods_id):
        with Session(tb_goods_sh_goods_attr_engine) as session:
            heroes = session.exec(select(tb_goods_sh_goods_attr_value).where(tb_goods_sh_goods_attr_value.goods_id == int(goods_id)))

            return heroes

    def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df



    ANS10001 = get_product(goods_id)

    df = sqmodel_to_df(ANS10001)

    # df10001 = df.spg_id[0]
    ANS20001 = get_data(df.spg_id[0])
    df2 = sqmodel_to_df(ANS20001)

    ANS30001 = get_data_2(goods_id)
    df3 = sqmodel_to_df(ANS30001)
    try:
        X1001 = df2.merge(df3, left_on=['auto_id'], right_on=['attr_id'], how='left', indicator=True)
    except:
        X1001 = df2
        X1001 = X1001.rename(columns={'auto_id': 'auto_id_x'})
    if not X1001.empty:
        try:
            X1001 = X1001.loc[X1001['fen_ji'] != 3]
        except:
            X1001 = X1001
            X1001 = X1001.rename(columns={'auto_id': 'auto_id_x'})
    else:
        X1001 = pd.DataFrame()

    PO1000007 = X1001.to_json(orient='records')
    
    return json.loads(PO1000007)

class goods_attr_post(BaseModel):
    fen_ji:int
    name : str

@router.post("/goods_attr/{locale}", status_code = 200)
def goods_selector(locale: str,parent : int = 0,category_id : int = 0,req_body : goods_attr_post = Body(...)):

    from src.shwethe_goods.orm.table.tb_goods_sh_goods_attr.main import tb_goods_sh_goods_attr_engine,tb_goods_sh_goods_attr
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    def insert_data(parent,category_id,mm_name,fen_ji):
        with Session(tb_goods_sh_goods_attr_engine) as session:
            tb_goods_sku_ = tb_goods_sh_goods_attr(parent_id=parent,category_id=category_id,mm_name=mm_name,fen_ji=fen_ji)
            session.add(tb_goods_sku_)
            session.commit()
            session.refresh(tb_goods_sku_)
            print("Created hero:", tb_goods_sku_)
            return "Created hero"

    insert_data(parent,category_id,FFF['name'],FFF['fen_ji'])

    return "json.loads(PO1000007)"


class goods_attr_value_post(BaseModel):
    goods_id:int
    data_jsonb : dict = Body(...)

@router.post("/goods_attr_value/{locale}", status_code = 200)
def goods_selector(locale: str,parent : int = 0,category_id : int = 0,req_body : goods_attr_value_post = Body(...)):

    from src.shwethe_goods.orm.table.tb_goods_sh_goods_attr.main import tb_goods_sh_goods_attr_engine,tb_goods_sh_goods_attr_value
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    print(FFF)
    # BV100001 = pd.DataFrame([FFF])
    # BV100002 = BV100001.to_dict('records')
    # BV100003 = pd.json_normalize(BV100002,'data_jsonb_1',['product_id','spg_id','brand_id'])
    def check_data(goods_id,selector_id):
        with Session(tb_goods_sh_goods_attr_engine) as session:
            statement = select(tb_goods_sh_goods_attr_value).where(and_(tb_goods_sh_goods_attr_value.goods_id == goods_id,tb_goods_sh_goods_attr_value.attr_id == selector_id))
            results = session.exec(statement).all()
            return results

    def insert_data(goods_id,attr_id,attr_value):
        with Session(tb_goods_sh_goods_attr_engine) as session:
            tb_goods_sku_ = tb_goods_sh_goods_attr_value(goods_id=goods_id,attr_id=attr_id,attr_value=attr_value)
            session.add(tb_goods_sku_)
            session.commit()
            session.refresh(tb_goods_sku_)
            print("Created hero:", tb_goods_sku_)
            return "Created hero"
    for key in FFF['data_jsonb']:
        key_data = key
        value_data = FFF['data_jsonb'][key]
        if not value_data == '' and not value_data == None:
            print(key_data) # for the keys
            print(value_data) # for the values
            results = check_data(FFF['goods_id'],key_data)
            print(results)
            print("results")
            if results == []:
                insert_data(FFF['goods_id'],key_data,value_data)

    return "json.loads(PO1000007)"


class goods_attr_value_put(BaseModel):
    goods_id:int
    data_jsonb : dict = Body(...)

@router.put("/goods_attr_value/{locale}", status_code = 200)
def goods_selector(locale: str,parent : int = 0,category_id : int = 0,req_body : goods_attr_value_put = Body(...)):

    from src.shwethe_goods.orm.table.tb_goods_sh_goods_attr.main import tb_goods_sh_goods_attr_engine,tb_goods_sh_goods_attr_value
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    print(FFF)
    # BV100001 = pd.DataFrame([FFF])
    # BV100002 = BV100001.to_dict('records')
    # BV100003 = pd.json_normalize(BV100002,'data_jsonb_1',['product_id','spg_id','brand_id'])
    def check_data(goods_id,selector_id):
        with Session(tb_goods_sh_goods_attr_engine) as session:
            statement = select(tb_goods_sh_goods_attr_value).where(and_(tb_goods_sh_goods_attr_value.goods_id == goods_id,tb_goods_sh_goods_attr_value.attr_id == selector_id))
            results = session.exec(statement).all()
            return results
    def insert_data(goods_id,attr_id,attr_value):
        with Session(tb_goods_sh_goods_attr_engine) as session:
            tb_goods_sku_ = tb_goods_sh_goods_attr_value(goods_id=goods_id,attr_id=attr_id,attr_value=attr_value)
            session.add(tb_goods_sku_)
            session.commit()
            session.refresh(tb_goods_sku_)
            print("Created hero:", tb_goods_sku_)
            return "Created hero"
    def put_data(goods_id,attr_id,attr_value):
        with Session(tb_goods_sh_goods_attr_engine) as session:
            # tb_goods_sku_ = tb_goods_sh_goods_attr_value(goods_id=goods_id,attr_id=attr_id,attr_value=attr_value)
            statement = select(tb_goods_sh_goods_attr_value).where(and_(tb_goods_sh_goods_attr_value.goods_id == int(goods_id),tb_goods_sh_goods_attr_value.attr_id == int(attr_id)))
            results = session.exec(statement)
            hero = results.one()
            hero.attr_value = attr_value
            session.add(hero)
            session.commit()
            # print("Created hero:", tb_goods_sku_)
            return "Created hero"

    for key in FFF['data_jsonb']:
        key_data = key
        value_data = FFF['data_jsonb'][key]
        if not value_data == '' and not value_data == None:
            print(key_data) # for the keys
            print(value_data) # for the values
            results = check_data(FFF['goods_id'],key_data)
            print(results)
            print("results")
            if results != []:
                put_data(FFF['goods_id'],key_data,value_data)
            else:
                insert_data(FFF['goods_id'],key_data,value_data)

    return "json.loads(PO1000007)"



@router.get("/goods_selector/{locale}", status_code = 200)
def goods_selector(locale: str,parent : int = 0,category_id : int = 0):
    from src.shwethe_goods.orm.table.tb_goods_sh_goods_selector.main import tb_goods_sh_goods_selector_engine,tb_goods_sh_goods_selector
    def get_data():
        with Session(tb_goods_sh_goods_selector_engine) as session:
            if parent == 0:
                heroes = session.exec(select(tb_goods_sh_goods_selector).where(tb_goods_sh_goods_selector.category_id == category_id)).all()
            else:
                heroes = session.exec(select(tb_goods_sh_goods_selector).where(and_(tb_goods_sh_goods_selector.category_id == category_id,tb_goods_sh_goods_selector.parent_id == parent))).all()
            return heroes
    ANS10001 = get_data()
    return ANS10001

class goods_selector_post(BaseModel):
    name : str

@router.post("/goods_selector/{locale}", status_code = 200)
def goods_selector(locale: str,parent : int = 0,category_id : int = 0,req_body : goods_selector_post = Body(...)):

    from src.shwethe_goods.orm.table.tb_goods_sh_goods_selector.main import tb_goods_sh_goods_selector_engine,tb_goods_sh_goods_selector
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)



    def insert_data(parent,category_id,mm_name):
        with Session(tb_goods_sh_goods_selector_engine) as session:
            tb_goods_sku_ = tb_goods_sh_goods_selector(parent_id=parent,category_id=category_id,mm_name=mm_name)
            session.add(tb_goods_sku_)
            session.commit()
            session.refresh(tb_goods_sku_)
            print("Created hero:", tb_goods_sku_)
            return "Created hero"

    insert_data(parent,category_id,FFF['name'])

    return "json.loads(PO1000007)"


@router.get("/goods_selector/{locale}/{goods_id}", status_code = 200)
def goods_selector(goods_id:int,locale: str,parent : int = 0):
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku
    from src.shwethe_goods.orm.table.tb_goods_sh_goods_selector.main import tb_goods_sh_goods_selector_engine,tb_goods_sh_goods_selector,tb_goods_sh_goods_selector_value
    
    def get_product(goods_id):
        with Session(tb_goods_sku_engine) as session:
            # T100001 = pd.read_sql(session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_id == goods_id).limit(1)).statement,con=session.bind)
            heroes = session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_id == goods_id).limit(1))
                
            return heroes

    def get_data(category_id):
        with Session(tb_goods_sh_goods_selector_engine) as session:
            print(category_id)
            heroes = session.exec(select(tb_goods_sh_goods_selector).where(tb_goods_sh_goods_selector.category_id == int(category_id)))

            return heroes


    def get_data_2(goods_id):
        with Session(tb_goods_sh_goods_selector_engine) as session:
            heroes = session.exec(select(tb_goods_sh_goods_selector_value).where(tb_goods_sh_goods_selector_value.goods_id == int(goods_id)))

            return heroes

    def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df



    ANS10001 = get_product(goods_id)

    df = sqmodel_to_df(ANS10001)

    # df10001 = df.spg_id[0]
    ANS20001 = get_data(df.spg_id[0])
    df2 = sqmodel_to_df(ANS20001)

    ANS30001 = get_data_2(goods_id)
    df3 = sqmodel_to_df(ANS30001)
    try:
        X1001 = df2.merge(df3, left_on=['auto_id'], right_on=['selector_id'], how='left', indicator=True)
    except:
        X1001 = df2
        X1001 = X1001.rename(columns={'auto_id': 'auto_id_x'})
    # if not X1001.empty:
    #     X1001 = X1001.loc[X1001['fen_ji'] != 3]
    # else:
    #     X1001 = pd.DataFrame()

    PO1000007 = X1001.to_json(orient='records')
    
    return json.loads(PO1000007)


class goods_selector_value_post(BaseModel):
    goods_id:int
    data_jsonb : dict = Body(...)

@router.post("/goods_selector_value/{locale}", status_code = 200)
def goods_selector(locale: str,parent : int = 0,category_id : int = 0,req_body : goods_selector_value_post = Body(...)):

    from src.shwethe_goods.orm.table.tb_goods_sh_goods_selector.main import tb_goods_sh_goods_selector_engine,tb_goods_sh_goods_selector_value
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    print(FFF)
    # BV100001 = pd.DataFrame([FFF])
    # BV100002 = BV100001.to_dict('records')
    # BV100003 = pd.json_normalize(BV100002,'data_jsonb_1',['product_id','spg_id','brand_id'])
    def check_data(goods_id,selector_id):
        with Session(tb_goods_sh_goods_selector_engine) as session:
            statement = select(tb_goods_sh_goods_selector_value).where(and_(tb_goods_sh_goods_selector_value.goods_id == goods_id,tb_goods_sh_goods_selector_value.selector_id == selector_id))
            results = session.exec(statement).all()
            return results

    def insert_data(goods_id,selector_id,selector_value):
        with Session(tb_goods_sh_goods_selector_engine) as session:
            tb_goods_sku_ = tb_goods_sh_goods_selector_value(goods_id=goods_id,selector_id=selector_id,selector_value=selector_value)
            session.add(tb_goods_sku_)
            session.commit()
            session.refresh(tb_goods_sku_)
            print("Created hero:", tb_goods_sku_)
            return "Created hero"
    for key in FFF['data_jsonb']:
        key_data = key
        value_data = FFF['data_jsonb'][key]
        if not value_data == '' and not value_data == None:
            print(key_data) # for the keys
            print(value_data) # for the values
            results = check_data(FFF['goods_id'],key_data)
            print(results)
            print("results")
            if results == []:
                insert_data(FFF['goods_id'],key_data,value_data)

    return "json.loads(PO1000007)"

class goods_selector_value_put(BaseModel):
    goods_id:int
    data_jsonb : dict = Body(...)

@router.put("/goods_selector_value/{locale}", status_code = 200)
def goods_selector(locale: str,parent : int = 0,category_id : int = 0,req_body : goods_selector_value_put = Body(...)):

    from src.shwethe_goods.orm.table.tb_goods_sh_goods_selector.main import tb_goods_sh_goods_selector_engine,tb_goods_sh_goods_selector_value
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    print(FFF)
    # BV100001 = pd.DataFrame([FFF])
    # BV100002 = BV100001.to_dict('records')
    # BV100003 = pd.json_normalize(BV100002,'data_jsonb_1',['product_id','spg_id','brand_id'])
    def check_data(goods_id,selector_id):
        with Session(tb_goods_sh_goods_selector_engine) as session:
            statement = select(tb_goods_sh_goods_selector_value).where(and_(tb_goods_sh_goods_selector_value.goods_id == goods_id,tb_goods_sh_goods_selector_value.selector_id == selector_id))
            results = session.exec(statement).all()
            return results
    def insert_data(goods_id,selector_id,selector_value):
        with Session(tb_goods_sh_goods_selector_engine) as session:
            tb_goods_sku_ = tb_goods_sh_goods_selector_value(goods_id=goods_id,selector_id=selector_id,selector_value=selector_value)
            session.add(tb_goods_sku_)
            session.commit()
            session.refresh(tb_goods_sku_)
            print("Created hero:", tb_goods_sku_)
            return "Created hero"

    def put_data(goods_id,selector_id,selector_value):
        with Session(tb_goods_sh_goods_selector_engine) as session:
            # tb_goods_sku_ = tb_goods_sh_goods_selector_value(goods_id=goods_id,selector_id=selector_id,selector_value=selector_value)
            
            statement = select(tb_goods_sh_goods_selector_value).where(and_(tb_goods_sh_goods_selector_value.goods_id == int(goods_id),tb_goods_sh_goods_selector_value.selector_id == int(selector_id)))
            results = session.exec(statement)
            hero = results.one()
            hero.selector_value = selector_value
            session.add(hero)
            session.commit()


            # print("Created hero:", tb_goods_sku_)
            return "Created hero"
    for key in FFF['data_jsonb']:
        key_data = key
        value_data = FFF['data_jsonb'][key]
        if not value_data == '' and not value_data == None:
            print(key_data) # for the keys
            print(value_data) # for the values
            results = check_data(FFF['goods_id'],key_data)
            print(results)
            print("results")
            if results != []:
                put_data(FFF['goods_id'],key_data,value_data)
            else:
                insert_data(FFF['goods_id'],key_data,value_data)

    return "json.loads(PO1000007)"



class goods_gui_ge_post(BaseModel):
    name : str

@router.post("/goods_gui_ge/{locale}", status_code = 200)
def goods_gui_ge(locale: str,parent : int = 0,category_id : int = 0,req_body : goods_gui_ge_post = Body(...)):

    from src.shwethe_goods.orm.table.tb_goods_sh_goods_gui_ge.main import tb_goods_sh_goods_gui_ge_engine,tb_goods_sh_goods_gui_ge
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    def insert_data(parent,category_id,mm_name):
        with Session(tb_goods_sh_goods_gui_ge_engine) as session:
            tb_goods_sku_ = tb_goods_sh_goods_gui_ge(parent_id=parent,category_id=category_id,mm_name=mm_name)
            session.add(tb_goods_sku_)
            session.commit()
            session.refresh(tb_goods_sku_)
            print("Created hero:", tb_goods_sku_)
            return "Created hero"

    insert_data(parent,category_id,FFF['name'])

    return "json.loads(PO1000007)"



@router.get("/goods_gui_ge/{locale}", status_code = 200)
def goods_gui_ge(locale: str,parent : int = 0,category_id : int = 0):
    from src.shwethe_goods.orm.table.tb_goods_sh_goods_gui_ge.main import tb_goods_sh_goods_gui_ge_engine,tb_goods_sh_goods_gui_ge
    def get_data():
        with Session(tb_goods_sh_goods_gui_ge_engine) as session:
            heroes = session.exec(select(tb_goods_sh_goods_gui_ge).where(tb_goods_sh_goods_gui_ge.category_id == category_id)).all()
            return heroes
    ANS10001 = get_data()
    return ANS10001


@router.get("/goods_gui_ge/{locale}/{goods_id}", status_code = 200)
def goods_gui_ge(goods_id:int,locale: str,parent : int = 0):
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku
    from src.shwethe_goods.orm.table.tb_goods_sh_goods_gui_ge.main import tb_goods_sh_goods_gui_ge_engine,tb_goods_sh_goods_gui_ge

    print(goods_id)
    def get_product(goods_id):
        with Session(tb_goods_sku_engine) as session:
            # T100001 = pd.read_sql(session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_id == goods_id).limit(1)).statement,con=session.bind)
            heroes = session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_id == goods_id).limit(1))
                
            return heroes
    ANS10001 = get_product(goods_id)

    def get_data(category_id):
        with Session(tb_goods_sh_goods_gui_ge_engine) as session:
            heroes = session.exec(select(tb_goods_sh_goods_gui_ge).where(tb_goods_sh_goods_gui_ge.category_id == int(category_id))).all()
            return heroes

    def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

    df = sqmodel_to_df(ANS10001)
    T100001  = {}
    if df.gui_ge_param[0] == {}:
        ANS20001 = get_data(df.spg_id[0])
        df = sqmodel_to_df(ANS20001)

        for index, row in df.iterrows():
            A = str(row['mm_name'])
            T100001[A] = ''
            print(T100001)
        print("T100001")
        print(json.loads('{"A":"B"}'))

        G100001 = pd.DataFrame([T100001])
        df['gui_ge_param'] = G100001.to_dict('records')

    PO1000007 = df.to_json(orient='records')
    
    return json.loads(PO1000007)