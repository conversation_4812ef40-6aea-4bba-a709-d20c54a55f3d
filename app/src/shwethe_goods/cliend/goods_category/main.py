from fastapi import FastAP<PERSON>, APIRouter, Body, Response, BackgroundTasks, Header,HTTPException,Query
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np
from sqlmodel import Session,select,or_
router = APIRouter()
type_list = [1,2,3]
language_code_list = 'mm'


@router.get("/goods_category/{locale}", status_code = 200)
def order_s_insert_list(locale: str,parent : int = 0):

    from src.shwethe_goods.orm.table.tb_goods_category.main import tb_goods_category_engine,tb_goods_category
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry

    def c100001():
        with Session(tb_goods_category_engine) as session:
            heroes = session.exec(select(tb_goods_category).where(tb_goods_category.parent_id == parent)).all()
            return heroes

    def c200001():
        with Session(tb_goods_translate_entry_engine) as session:
            heroes = session.exec(select(tb_goods_translate_entry).where(tb_goods_translate_entry.language_code == locale)).all()
            return heroes
    T100001 = c100001()
    T200001 = c200001()
    YU100001 = []
    for i in T100001:
        FD100001 = {'category_id':i.category_id,'translation_id':i.translation_id,'name':i.translation_id,'parent_id':i.parent_id,'if_parent':i.if_parent}
        YU100001.append(FD100001)
    CX100001 = pd.DataFrame(YU100001)
    print(CX100001)

    YU100002 = []
    for i in T200001:
        FD100001 = {'translation_id':i.translation_id,'field_text':i.field_text,'language_code':i.language_code}
        YU100002.append(FD100001)

    CX200001 = pd.DataFrame(YU100002)

    CX100001['name'] = CX100001[['name']].replace(to_replace =CX200001['translation_id'].to_list(), 
                            value =CX200001['field_text'].to_list())
                            
    PO1000007 = CX100001.to_json(orient='records')
    
    return json.loads(PO1000007)

@router.get("/goods_category/{locale}/{category_id}", status_code = 200)
def goods_category_(category_id : int,locale: str,parent : int = 0):

    from src.shwethe_goods.orm.table.tb_goods_category.main import tb_goods_category_engine,tb_goods_category
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry

    def c100001(category_id):
        with Session(tb_goods_category_engine) as session:
            heroes = session.exec(select(tb_goods_category).where(tb_goods_category.category_id == category_id)).all()
            return heroes
            
    T100001 = c100001(category_id)
    YU100001 = []
    for i in T100001:
        FD100001 = {'category_id':i.category_id,'translation_id':i.translation_id,'name':i.mm_name,'parent_id':i.parent_id,'if_parent':i.if_parent}
        YU100001.append(FD100001)
    CX100001 = pd.DataFrame(YU100001)
    print(CX100001)
    PO1000007 = CX100001.to_json(orient='records')

    return json.loads(PO1000007)

@router.get("/goods_info/{locale}", status_code = 200)
def goods_info(locale: str,parent : int = 0,spg_id : int = None):
    from src.shwethe_goods.orm.table.tb_goods_spec_param.main import tb_goods_spec_param_engine,tb_goods_spec_param
    from src.shwethe_goods.orm.table.tb_goods_spec_type.main import tb_goods_spec_type_engine,tb_goods_spec_type
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry

    def c100001():
        with Session(tb_goods_spec_type_engine) as session:
            heroes = session.exec(select(tb_goods_spec_type).where(tb_goods_spec_type.spec_type_id.in_(type_list))).all()
            return heroes

    def c500001():
        with Session(tb_goods_spec_param_engine) as session:
            heroes = session.exec(select(tb_goods_spec_param).where(tb_goods_spec_param.spec_type_id.in_(type_list),tb_goods_spec_param.spg_id.in_([spg_id]))).all()
            return heroes

    def c200001():
        with Session(tb_goods_translate_entry_engine) as session:
            heroes = session.exec(select(tb_goods_translate_entry).where(tb_goods_translate_entry.language_code == locale)).all()
            return heroes

    T100001 = c100001()

    T500001 = c500001()

    T200001 = c200001()
    YU100001 = []
    for i in T100001:
        FD100001 = {'spec_type_id':i.spec_type_id,'spec_type_translation_id':i.translation_id,'spec_type_name':i.translation_id}
        YU100001.append(FD100001)
    CX100001 = pd.DataFrame(YU100001)

    YU100002 = []
    for i in T200001:
        FD100001 = {'translation_id':i.translation_id,'field_text':i.field_text,'language_code':i.language_code}
        YU100002.append(FD100001)

    CX200001 = pd.DataFrame(YU100002)

    YU100003 = []
    for i in T500001:
        if i.segments == False:
            generate = generate_datetime_id()
            FD100001 = {'app_translation_r_id':generate,'spec_param_id':i.goods_spec_param_id,'segments':i.segments,'spec_type_id':i.spec_type_id,'spec_param_name':i.translation_id,'spec_param_translation_id':i.translation_id}
        else:
            FD100001 = {'spec_param_id':i.goods_spec_param_id,'segments':i.segments,'spec_type_id':i.spec_type_id,'spec_param_name':i.translation_id,'spec_param_translation_id':i.translation_id}
        YU100003.append(FD100001)
    CX300001 = pd.DataFrame(YU100003)
    # ER100001 = CX100001.merge(CX300001, on=['spec_type_id'], how='inner')
    # ER100001[['spec_type_name','spec_param_name']] = ER100001[['spec_type_name','spec_param_name']].replace(to_replace =CX200001['translation_id'].to_list(), 
    #                         value =CX200001['field_text'].to_list())
    # print(ER100001)
    # PO1000007 = ER100001.to_json(orient='records')
    
    # return json.loads(PO1000007)

    try:
        CX100001 = CX100001.replace(np.nan, 0)
        ER100001 = CX100001.merge(CX300001, on=['spec_type_id'], how='left')
        ER100001 = ER100001.loc[ER100001['spec_type_name'] != 0.0]
        ER100001 = ER100001.replace(np.nan, 0)    
        ER100001[['spec_type_name','spec_param_name']] = ER100001[['spec_type_name','spec_param_name']].replace(to_replace =CX200001['translation_id'].to_list(), 
                                value =CX200001['field_text'].to_list())
        PO1000007 = ER100001.to_json(orient='records')
    
        return json.loads(PO1000007)
    except :
        return []

class Foo_(BaseModel):
    image_p : List = Body(...)


class goods_info_post(BaseModel):
    product_id : int
    spg_id:int
    images : Foo_
    data_jsonb_1 : List[dict] = Body(...)


@router.post("/goods_info/{locale}", status_code = 200)
def goods_info_post(locale: str,req_body : goods_info_post = Body(...)):
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    print(FFF)
    BV100001 = pd.DataFrame([FFF])
    BV100002 = BV100001.to_dict('records')
    BV100003 = pd.json_normalize(BV100002,'data_jsonb_1',['product_id','spg_id'])
    BV100004 = BV100003.to_dict('records')
    BV100005 = pd.json_normalize(BV100004)

    BV100003 = BV100005

    print(BV100003)
    print(BV100003.info())

    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry,tb_goods_app_translation
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku

    def c100001(locale_,value,app_translation_r_id):
        with Session(tb_goods_translate_entry_engine) as session:
            tb_goods_app_translation_ = tb_goods_app_translation(r_id=app_translation_r_id)
            session.add(tb_goods_app_translation_)
            session.commit()

            tb_goods_translate_entry_ = tb_goods_translate_entry(
            language_code=locale_,
            field_text=value,
            translation_id=tb_goods_app_translation_.auto_id,
            )
            session.add(tb_goods_translate_entry_)
            session.commit()
            session.refresh(tb_goods_translate_entry_)
            print("Created hero:", tb_goods_translate_entry_)
            return tb_goods_app_translation_.auto_id

    def insert_data(product_id,spg_id,jsonb,images):
        with Session(tb_goods_sku_engine) as session:
            tb_goods_sku_ = tb_goods_sku(goods_id=product_id,goods_param=jsonb,images=images,spg_id=spg_id)
            session.add(tb_goods_sku_)
            session.commit()
            session.refresh(tb_goods_sku_)


            print("Created hero:", tb_goods_sku_)
            return "Created hero"

    BV100004 = BV100003.replace(np.nan, 0)
    BV100004['name'] = BV100004['name'].astype(int)
    print(BV100004['app_translation_id'])
    for index, row in BV100004.iterrows():
        B = str(row['value'])
        app_translation_id = str(row['app_translation_id'])
        app_translation_r_id = str(row['app_translation_r_id'])

        if B != '0':
            print(index)
            print("index_____")
            value = str(row['value'])
            segments = row['segments']
            print("app_translation_r_id")
            print(app_translation_r_id)
            if segments == False:
                TR10001 = c100001(locale,value,app_translation_r_id)
                BV100004.at[index,'app_translation_id'] = TR10001
            else:
                BV100004.at[index,'app_translation_id'] = app_translation_id

    print(BV100004['app_translation_id'])
    BV100005 = BV100004
    ZX100001 = {'A':{},'B':{},'C':{}}
    TY10001 = type_list
    for index, row in BV100005.iterrows():
        product_id = row['product_id']
        spg_id = row['spg_id']
        B = str(row['value'])
        if (row['spec_type_id'] in TY10001 ) :
            if B != '0':
                ZX100001['A'][row['name']] = int(row['app_translation_id'])
    print(FFF['images'])
    insert_data(product_id,spg_id,ZX100001['A'],FFF['images'])

    print(BV100005)
    print(BV100005.info())
    print(ZX100001)
    return "abc"

@router.get("/goods_info/{locale}/{goods_id}", status_code = 200)
def goods_info(locale: str,goods_id : int,spg_id : int = None):
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku
    from src.shwethe_goods.orm.table.tb_goods_spec_param.main import tb_goods_spec_param_engine,tb_goods_spec_param
    from src.shwethe_goods.orm.table.tb_goods_spec_type.main import tb_goods_spec_type_engine,tb_goods_spec_type
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry

    def c100001():
        with Session(tb_goods_spec_type_engine) as session:
            heroes = session.exec(select(tb_goods_spec_type).where(tb_goods_spec_type.spec_type_id.in_(type_list))).all()
            return heroes

    def c500001(goods_id_spg_id):
        with Session(tb_goods_spec_param_engine) as session:
            # heroes = session.exec(select(tb_goods_spec_param).where(tb_goods_spec_param.spg_id.in_(goods_id),tb_goods_spec_param.spec_type_id.in_(type_list))).all()
            heroes = session.exec(select(tb_goods_spec_param).where(tb_goods_spec_param.spg_id.in_(goods_id_spg_id),tb_goods_spec_param.spec_type_id.in_(type_list))).all()
            
            return heroes

    def c200001():
        with Session(tb_goods_translate_entry_engine) as session:
            heroes = session.exec(select(tb_goods_translate_entry).where(tb_goods_translate_entry.language_code.in_([language_code_list,locale]))).all()
            return heroes

    def get_data_tb_goods_spu_sku():
        with Session(tb_goods_sku_engine) as session:
            heroes = session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_id == goods_id)).all()
            return heroes

    T100001 = c100001()
    T200001 = c200001()
    T900001 = get_data_tb_goods_spu_sku()
    goods_id_spg_id = []
    YU100001 = []
    for i in T900001:
        print(i)
        goods_id_spg_id.append(i.spg_id)
        try:
            if len(i.goods_param) > 0:
                for key, value in i.goods_param.items():
                    print(i.spg_id)
                    print("i.spg_id")
                    goods_id_spg_id.append(i.spg_id)
                    FD100001 = {'images':i.images,'goods_id':i.goods_id,'goods_id':i.goods_id,'spec_param_translation_id':int(key),'app_translation_id':int(value),'value_id':int(value),'value':int(value)}
                    YU100001.append(FD100001)
            else:
                FD100001 = {'images':i.images,'goods_id':i.goods_id,'goods_id':i.goods_id,'spg_id':i.spg_id}
                YU100001.append(FD100001)
        except:
            FD100001 = {'images':i.images,'goods_id':i.goods_id,'goods_id':i.goods_id,'spg_id':i.spg_id}
            YU100001.append(FD100001)

        
    CX100001 = pd.json_normalize(YU100001)
    print(goods_id_spg_id)
    T500001 = c500001(goods_id_spg_id)
    YU100001 = []
    for i in T100001:
        FD100001 = {'spec_type_id':i.spec_type_id,'spec_type_translation_id':i.translation_id,'spec_type_name':i.translation_id}
        YU100001.append(FD100001)
    Cz100001 = pd.DataFrame(YU100001)
    YU100002 = []
    for i in T200001:
        FD100001 = {'translation_id':i.translation_id,'field_text':i.field_text,'language_code':i.language_code}
        YU100002.append(FD100001)

    CX200001 = pd.DataFrame(YU100002)
    CX200001['language_code_index'] = CX200001['language_code'].replace({locale:0, language_code_list:1})
    CX200001 = CX200001.sort_values(['translation_id','language_code_index'], ascending=[True,True])
    # print(CX200001[['translation_id','language_code_index']])

    CX200001 = CX200001.drop_duplicates(subset=['translation_id'], keep='first')
    # print(CX200001[['translation_id','language_code_index']])
    # print("CX200001")

    # print(CX200001)
    # mapper = {str(dct['translation_id']): dct['field_text'] for dct in YU100002}
    try:
        CX100001[['value']] = CX100001[['value']].replace(to_replace =CX200001['translation_id'].to_list(), 
                                value =CX200001['field_text'].to_list())
    except:
        pass
    # ER100001 = T500001.merge(CX100001, on=['spec_param_translation_id'], how='outer')
    # print(ER100001)
    YU100003 = []

    print(T500001)
    print("T500001")
    
    for i in T500001:
        print(i)
        if i.segments == False:
            generate = generate_datetime_id()
            FD100001 = {'spg_id':i.spg_id,'app_translation_r_id':generate,'spec_param_id':i.goods_spec_param_id,'segments':i.segments,'spec_type_id':i.spec_type_id,'spec_param_name':i.translation_id,'spec_param_translation_id':i.translation_id}
        else:
            FD100001 = {'spg_id':i.spg_id,'spec_param_id':i.goods_spec_param_id,'segments':i.segments,'spec_type_id':i.spec_type_id,'spec_param_name':i.translation_id,'spec_param_translation_id':i.translation_id}
        YU100003.append(FD100001)

    CX300001 = pd.DataFrame(YU100003)

    Cz100001 = Cz100001.replace(np.nan, 0)
    CX300001 = CX300001.replace(np.nan, 0)
    ER100001 = Cz100001.merge(CX300001, on=['spec_type_id'], how='left')
    ER100001 = ER100001.loc[ER100001['spec_type_name'] != 0.0]
    ER100001 = ER100001.replace(np.nan, 0) 
    ER100001[['spec_type_name','spec_param_name']] = ER100001[['spec_type_name','spec_param_name']].replace(to_replace =CX200001['translation_id'].to_list(), 
                            value =CX200001['field_text'].to_list())
    try:
        ER100001 = ER100001.merge(CX100001, on=['spec_param_translation_id'], how='left')

        PO100001 = pd.DataFrame()
        ER100001['value'] = ER100001['value'].replace(np.nan, '')
        for index, row in ER100001.iterrows():
            U1004 = ER100001.iloc[[index]]
            value = row['value']
            
            if value == '':
                generate = generate_datetime_id()
                U1004['app_translation_r_id'] = generate
                print("None")
                print(value)
            else:
                U1004['app_translation_r_id'] = None
            PO100001 = PO100001.append(U1004)
        print(PO100001)

        PO1000007 = PO100001.to_json(orient='records')
        return json.loads(PO1000007)
    except:
        print(ER100001.info())
        print("ER100001")
        print(CX100001.info())
        ER100001 = ER100001.merge(CX100001, on=['spg_id'], how='inner')
        ER100001 = ER100001.to_json(orient='records')
        return json.loads(ER100001)




class goods_id_Foo_(BaseModel):
    image_p : List = Body(...)


class goods_info_goods_id_post(BaseModel):
    product_id : int 
    images : goods_id_Foo_
    data_jsonb_1 : List[dict] = Body(...)

@router.put("/goods_info/{locale}/{goods_id}", status_code = 200)
def goods_info(locale: str,goods_id : int ,req_body : goods_info_goods_id_post = Body(...)):
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    print(FFF)
    BV100001 = pd.DataFrame([FFF])
    BV100002 = BV100001.to_dict('records')
    BV100003 = pd.json_normalize(BV100002,'data_jsonb_1',['product_id'])
    BV100004 = BV100003.to_dict('records')
    BV100005 = pd.json_normalize(BV100004)

    BV100003 = BV100005

    print(BV100003)
    print(BV100003.info())

    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry,tb_goods_app_translation
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku

    def c100001(locale_,value,app_translation_r_id):
        with Session(tb_goods_translate_entry_engine) as session:
            tb_goods_app_translation_ = tb_goods_app_translation(r_id=app_translation_r_id)
            session.add(tb_goods_app_translation_)
            session.commit()

            tb_goods_translate_entry_ = tb_goods_translate_entry(
            language_code=locale_,
            field_text=value,
            translation_id=tb_goods_app_translation_.auto_id,
            )
            session.add(tb_goods_translate_entry_)
            session.commit()
            session.refresh(tb_goods_translate_entry_)
            print("Created hero:", tb_goods_translate_entry_)
            return tb_goods_app_translation_.auto_id

    def update_c100001(locale_,value,app_translation_r_id):
        with Session(tb_goods_translate_entry_engine) as session:
            results = session.exec(select(tb_goods_translate_entry).where(tb_goods_translate_entry.translation_id == app_translation_r_id,tb_goods_translate_entry.language_code == locale_))
            hero = results.one()
            print("Hero:", hero)

            hero.field_text = value
            session.add(hero)
            session.commit()
            session.refresh(hero)

            return "tb_goods_app_translation_.auto_id"

    def insert_data(product_id,jsonb,images):
        with Session(tb_goods_sku_engine) as session:
            tb_goods_sku_ = tb_goods_sku(goods_id=product_id,goods_param=jsonb,images=images)
            session.add(tb_goods_sku_)
            session.commit()
            session.refresh(tb_goods_sku_)
            print("Created hero:", tb_goods_sku_)
            return "Created hero"

    def update_data(product_id,jsonb,images):
        with Session(tb_goods_sku_engine) as session:
            results = session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_id == product_id))
            hero = results.one()
            print("Hero:", hero)

            hero.goods_param = jsonb
            hero.images = images
            session.add(hero)
            session.commit()
            session.refresh(hero)
            print("Created hero:", hero)
            return "Created hero"
    print("BV100004['app_translation_r_id']")
    print(BV100003)
    BV100003['app_translation_r_id'] = BV100003['app_translation_r_id'].replace(np.nan, '')
    print(BV100003[['app_translation_r_id','app_translation_id']])
    BV100004 = BV100003.replace(np.nan, 0)
    # BV100004['name'] = BV100004['name'].astype(int)
    for index, row in BV100004.iterrows():
        B = str(row['value'])
        app_translation_id = str(row['app_translation_id'])
        app_translation_r_id = str(row['app_translation_r_id'])
        spec_param_translation_id = row['spec_param_translation_id']

        if B != '0':
            print(index)
            print("index_____")
            value = str(row['value'])
            segments = row['segments']
            app_translation_id = row['app_translation_id']
            print("app_translation_id")
            print(app_translation_id)

            
            print("app_translation_r_id")
            print(app_translation_r_id)
            if segments == True:
                TR10001 = update_c100001(locale,value,app_translation_id)
            else:
                if app_translation_r_id != '':
                    TR10001 = c100001(locale,value,app_translation_r_id)
                    BV100004.at[index,'app_translation_id'] = TR10001
                else:
                    TR10001 = update_c100001(locale,value,app_translation_id)
                    # BV100004.at[index,'app_translation_id'] = app_translation_id

    print(BV100004['app_translation_id'])
    BV100005 = BV100004
    ZX100001 = {'A':{},'B':{},'C':{}}
    TY10001 = type_list
    for index, row in BV100005.iterrows():
        product_id = row['product_id']
        B = str(row['value'])
        if (row['spec_type_id'] in TY10001 ) :
            if B != '0':
                ZX100001['A'][row['spec_param_translation_id']] = int(row['app_translation_id'])
    print(ZX100001['A'])
    update_data(product_id,ZX100001['A'],FFF['images'])

    print(BV100005)
    print(BV100005.info())
    print(ZX100001)
    
    return "abc"


@router.get("/goods_spec_param/{locale}", status_code = 200)
def goods_spec_param(locale: str):

    from src.shwethe_goods.orm.table.tb_goods_spec_param.main import tb_goods_spec_param_engine,tb_goods_spec_param
    from src.shwethe_goods.orm.table.tb_goods_spec_type.main import tb_goods_spec_type_engine,tb_goods_spec_type
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry

    def c100001():
        with Session(tb_goods_spec_type_engine) as session:
            heroes = session.exec(select(tb_goods_spec_type).where(tb_goods_spec_type.spec_type_id.in_(type_list))).all()
            return heroes


    def c500001():
        with Session(tb_goods_spec_param_engine) as session:
            heroes = session.exec(select(tb_goods_spec_param).where(tb_goods_spec_param.spec_type_id.in_(type_list))).all()
            return heroes


    def c200001():
        with Session(tb_goods_translate_entry_engine) as session:
            heroes = session.exec(select(tb_goods_translate_entry).where(tb_goods_translate_entry.language_code == locale)).all()
            return heroes

    T100001 = c100001()

    T500001 = c500001()

    T200001 = c200001()
    YU100001 = []
    for i in T100001:
        FD100001 = {'spec_type_id':i.spec_type_id,'spec_type_translation_id':i.translation_id,'spec_type_name':i.translation_id}
        YU100001.append(FD100001)
    CX100001 = pd.DataFrame(YU100001)

    YU100002 = []
    for i in T200001:
        FD100001 = {'translation_id':i.translation_id,'field_text':i.field_text,'language_code':i.language_code}
        YU100002.append(FD100001)

    CX200001 = pd.DataFrame(YU100002)

    YU100003 = []
    for i in T500001:
        FD100001 = {'spec_param_id':i.goods_spec_param_id,'segments':i.segments,'spec_type_id':i.spec_type_id,'spec_param_name':i.translation_id,'spec_param_translation_id':i.translation_id}
        YU100003.append(FD100001)
    CX300001 = pd.DataFrame(YU100003)
    ER100001 = CX100001.merge(CX300001, on=['spec_type_id'], how='inner')
    ER100001[['spec_type_name','spec_param_name']] = ER100001[['spec_type_name','spec_param_name']].replace(to_replace =CX200001['translation_id'].to_list(), 
                            value =CX200001['field_text'].to_list())
    print(ER100001)
    PO1000007 = ER100001.to_json(orient='records')
    
    return json.loads(PO1000007)

class goods_spec_param_post(BaseModel):
    spec_type_id : int
    spg_id : int
    text : str

@router.post("/goods_spec_param/{locale}", status_code = 200)
def goods_spec_param(locale: str,req_body : goods_spec_param_post = Body(...)):


    from src.shwethe_goods.orm.table.tb_goods_spec_param.main import tb_goods_spec_param_engine,tb_goods_spec_param
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry,tb_goods_app_translation




    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    def insert_text(locale_,value,app_translation_r_id):
        with Session(tb_goods_translate_entry_engine) as session:
            tb_goods_app_translation_ = tb_goods_app_translation(r_id=app_translation_r_id)
            session.add(tb_goods_app_translation_)
            session.commit()

            tb_goods_translate_entry_ = tb_goods_translate_entry(
            language_code=locale_,
            field_text=value,
            translation_id=tb_goods_app_translation_.auto_id,
            )
            session.add(tb_goods_translate_entry_)
            session.commit()
            session.refresh(tb_goods_translate_entry_)
            print("Created hero:", tb_goods_translate_entry_)
            return tb_goods_app_translation_.auto_id

    def insert_goods_spec(spg_id,segments,spec_type_id,translation_id):
        with Session(tb_goods_spec_param_engine) as session:
            print(spg_id,segments,spec_type_id,translation_id)
            tb_goods_app_translation_ = tb_goods_spec_param(
                segments=segments,
                spg_id=spg_id,
                spec_type_id=spec_type_id,
                translation_id=translation_id)
            session.add(tb_goods_app_translation_)
            session.commit()
            session.refresh(tb_goods_app_translation_)
            return 'tb_goods_app_translation_.auto_id'



    BV10001 = insert_text(locale,FFF['text'],12345)

    BV20001 = insert_goods_spec(FFF['spg_id'],False,FFF['spec_type_id'],BV10001)
    
    return "json.loads(PO1000007)"

class segments_param_post(BaseModel):
    text : str

@router.post("/segments/{locale}/{spec_param_id}", status_code = 200)
def segments(spec_param_id:int,locale: str,req_body : segments_param_post = Body(...)):


    from src.shwethe_goods.orm.table.tb_goods_segments.main import tb_goods_segments_engine,tb_goods_segments
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry,tb_goods_app_translation

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    def insert_text(locale_,value,app_translation_r_id):
        with Session(tb_goods_translate_entry_engine) as session:
            tb_goods_app_translation_ = tb_goods_app_translation(r_id=app_translation_r_id)
            session.add(tb_goods_app_translation_)
            session.commit()

            tb_goods_translate_entry_ = tb_goods_translate_entry(
            language_code=locale_,
            field_text=value,
            translation_id=tb_goods_app_translation_.auto_id,
            )
            session.add(tb_goods_translate_entry_)
            session.commit()
            session.refresh(tb_goods_translate_entry_)
            print("Created hero:", tb_goods_translate_entry_)
            return tb_goods_app_translation_.auto_id

    def insert_goods_spec(goods_spec_param_id,translation_id):
        with Session(tb_goods_segments_engine) as session:
            print(translation_id)
            tb_goods_segments_ = tb_goods_segments(
                goods_spec_param_id=goods_spec_param_id,
                translation_id=translation_id)
            session.add(tb_goods_segments_)
            session.commit()
            session.refresh(tb_goods_segments_)
            return 'tb_goods_app_translation_.auto_id'



    BV10001 = insert_text(locale,FFF['text'],12345)

    BV20001 = insert_goods_spec(spec_param_id,BV10001)
    
    return "json.loads(PO1000007)"

@router.get("/segments/{locale}/{spec_param_id}", status_code = 200)
def segments(spec_param_id:int,locale: str):


    from src.shwethe_goods.orm.table.tb_goods_segments.main import tb_goods_segments_engine,tb_goods_segments
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry,tb_goods_app_translation



    def get_text(list_data):
        with Session(tb_goods_translate_entry_engine) as session:
            print(list_data)
            print(list_data)
            heroes = session.exec(select(tb_goods_translate_entry).where(tb_goods_translate_entry.translation_id.in_(list_data),tb_goods_translate_entry.language_code.in_([locale]))).all()
            return heroes

    def get_tb_goods_segments(spec_param_id):
        with Session(tb_goods_segments_engine) as session:
            heroes = session.exec(select(tb_goods_segments).where(tb_goods_segments.goods_spec_param_id.in_([spec_param_id]))).all()
            return heroes   

    get_tb_goods_segments = get_tb_goods_segments(spec_param_id)

    YU100001 = []
    for i in get_tb_goods_segments:
        FD100001 = {'goods_segments_id':i.goods_segments_id,'goods_spec_param_id':i.goods_spec_param_id,'goods_spec_param_translation_id':i.translation_id,'goods_spec_param_name':i.translation_id}
        YU100001.append(FD100001)
    CX100001 = pd.DataFrame(YU100001)
    try:
        BV10001 = get_text(CX100001['goods_spec_param_translation_id'].values.tolist())
        YU100002 = []
        for i in BV10001:
            FD100001 = {'translation_id':i.translation_id,'field_text':i.field_text,'language_code':i.language_code}
            YU100002.append(FD100001)

        CX200001 = pd.DataFrame(YU100002)

        CX100001['goods_spec_param_name'] = CX100001[['goods_spec_param_name']].replace(to_replace =CX200001['translation_id'].to_list(), 
                            value =CX200001['field_text'].to_list())

        PO1000007 = CX100001.to_json(orient='records')
        
        return json.loads(PO1000007)
    except :
        return []
