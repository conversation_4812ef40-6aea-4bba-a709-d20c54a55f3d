from datetime import datetime
from typing import Optional
from sqlmodel import SQLModel, <PERSON>
from sqlalchemy import Column
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import TIMESTAMP,JSONB


class tb_goods_category(SQLModel, table=True):
    category_id: Optional[int] = Field(default=None, primary_key=True, title="category_id")
    mm_name: str = Field(default=..., title="mm_name")
    parent_id: int = Field(default=..., title="parent_id")
    sort: int = Field(default=..., title="sort")
    cn_name: str = Field(default=..., title="cn_name")
    translation_id: int = Field(default=..., title="translation_id")
    if_parent: str = Field(default=..., title="if_parent")
    
    
    
class tb_brand(SQLModel, table=True):
    brand_id: Optional[int] = Field(default=None, primary_key=True, title="brand_id")
    name: str = Field(default=..., title="name")
    images: dict = Field(sa_column=Column(JSONB),default=..., title="images")
    letter: int = Field(default=..., title="letter")
    
    
class tb_goods_sku(SQLModel, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True, title="auto_id")
    goods_param: dict = Field(sa_column=Column(JSONB),default=..., title="goods_param")
    images: dict = Field(sa_column=Column(JSONB),default=..., title="images")
    sku_id: int = Field(default=..., title="sku_id")
    spu_id: int = Field(default=..., title="spu_id")
    goods_id: int = Field(default=..., title="goods_id")
    goods_qty: float = Field(default=..., title="goods_qty")
    goods_price: float = Field(default=..., title="goods_price")
    brand_id: int = Field(default=..., title="brand_id")
    gui_ge_param: dict = Field(sa_column=Column(JSONB),default=..., title="gui_ge_param")
    
    

