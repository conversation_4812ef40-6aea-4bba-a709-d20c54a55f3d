from dateutil.parser import parse
from typing import Optional
from pydantic import BaseModel, validator
from datetime import datetime


class tb_goods_post(BaseModel):
    sort: int
    parent_id: int 
    translation_id: int
    mm_name: str
    cn_name: str
    if_parent: str
    
    
    
class tb_goods_put(BaseModel):
    category_id: int
    parent_id: int 
    
class tb_brand_post(BaseModel):
    name: str
    images: dict 
    letter: int
    
class tb_brand_product_post(BaseModel):
    product_id: int
    brand_id: int