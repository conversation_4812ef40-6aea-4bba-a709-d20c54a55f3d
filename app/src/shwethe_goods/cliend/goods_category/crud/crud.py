from sqlmodel import Session, select
from typing import List
from sqlalchemy import and_,desc,or_,cast, String
from sqlalchemy import join

from src.shwethe_goods.cliend.goods_category.schemas.schemas import (
    tb_goods_post,
    tb_brand_post,
    tb_brand_product_post,
    tb_goods_put
)
from src.shwethe_goods.cliend.goods_category.model.model import (
    tb_goods_category,
    tb_brand,
    tb_goods_sku
)
from fastapi import HTTPException, status


def create_tb_goods_category(db: Session, tb_goods_post_data: tb_goods_post):
    
    new_jia_yi_fang = tb_goods_category.from_orm(tb_goods_post_data)
    # new_jia_yi_fang = tb_goods_category(**tb_goods_post_data.dict(exclude_unset=True))
    db.add(new_jia_yi_fang)
    db.commit()
    db.refresh(new_jia_yi_fang)

    return new_jia_yi_fang


def put_tb_goods_category(db: Session, tb_goods_post_data: tb_goods_put):
    
    result = db.query(tb_goods_category).filter(tb_goods_category.category_id == tb_goods_post_data.category_id).first()
    # new_jia_yi_fang = tb_goods_category(**tb_goods_post_data.dict(exclude_unset=True))
    result.parent_id = tb_goods_post_data.parent_id
    db.add(result)
    db.commit()
    db.refresh(result)

    return result


def create_tb_brand(db: Session, tb_brand_post_data: tb_brand_post):
    
    result = db.query(tb_brand).filter(tb_brand.name == tb_brand_post_data.name).first()
    
    if result:
        
        result.images = tb_brand_post_data.images
        db.add(result)
        db.commit()
        db.refresh(result)
        
        return result
    
    new_jia_yi_fang = tb_brand(**tb_brand_post_data.dict(exclude_unset=True))
    db.add(new_jia_yi_fang)
    db.commit()
    db.refresh(new_jia_yi_fang)

    return new_jia_yi_fang

def get_tb_brand(db: Session):
    
    result = db.query(tb_brand).all()

    return result


def create_tb_brand_by_product(hero:tb_brand_product_post,db: Session):
    
    result = db.query(tb_goods_sku).filter(tb_goods_sku.goods_id == hero.product_id).first()
    
    if result:
    
        result.brand_id = hero.brand_id
        db.add(result)
        db.commit()
        db.refresh(result)
        
        return result
    
    raise HTTPException(status_code=400, detail="this not data")


def get_tb_brand_by_product(productId:int,db: Session):
    
    result = db.query(tb_goods_sku).filter(tb_goods_sku.goods_id == productId).first()
    
    if result:
    
        return result
    
    raise HTTPException(status_code=400, detail="this not data")
