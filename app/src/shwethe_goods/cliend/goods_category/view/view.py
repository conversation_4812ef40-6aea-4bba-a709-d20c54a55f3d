from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from src.config.db_info.database import get_db
from src.shwethe_goods.cliend.goods_category.schemas.schemas import (
    tb_goods_post,
    tb_brand_post,
    tb_brand_product_post,
    tb_goods_put
)

from src.shwethe_goods.cliend.goods_category.crud.crud import (
    create_tb_goods_category,
    create_tb_brand,
    get_tb_brand,
    create_tb_brand_by_product,
    get_tb_brand_by_product,
    put_tb_goods_category
)

from sqlalchemy.exc import IntegrityError
router = APIRouter()


@router.post("/goods_category_new")
def create_product_handler(tb_goods_post_data: tb_goods_post, db: Session = Depends(get_db)):
    return create_tb_goods_category(db=db, tb_goods_post_data=tb_goods_post_data)

@router.put("/goods_category_new")
def create_product_handler(tb_goods_post_data: tb_goods_put, db: Session = Depends(get_db)):
    return put_tb_goods_category(db=db, tb_goods_post_data=tb_goods_post_data)

@router.post("/tb_brand")
def create_product_handler(tb_brand_post_data: tb_brand_post, db: Session = Depends(get_db)):
    return create_tb_brand(db=db, tb_brand_post_data=tb_brand_post_data)

@router.get("/tb_brand")
def create_product_handler(db: Session = Depends(get_db)):
    return get_tb_brand(db=db)

@router.post("/tb_brand/goods")
def create_product_handler(hero:tb_brand_product_post,db: Session = Depends(get_db)):
    return create_tb_brand_by_product(db=db,hero=hero)

@router.get("/tb_brand/goods/{productId}")
def create_product_handler(productId:int,db: Session = Depends(get_db)):
    return get_tb_brand_by_product(productId=productId,db=db)