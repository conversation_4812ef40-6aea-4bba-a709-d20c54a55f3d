from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header,HTTPException,Query
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np
from sqlmodel import Session,select,or_
router = APIRouter()
type_list = [1,2,3]
language_code_list = 'mm'

class goods_info_post_Foo_(BaseModel):
    image_p : List = Body(...)


class goods_info_post(BaseModel):
    product_id : int
    brand_id : int
    spg_id:int
    images : goods_info_post_Foo_
    # data_jsonb_1 : List[dict] = Body(...)
    data_jsonb_1 : dict = Body(...)


@router.post("/goods_info/{locale}", status_code = 200)
def goods_info_post(locale: str,req_body : goods_info_post = Body(...)):
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    print(FFF)
    BV100001 = pd.DataFrame([FFF])
    BV100002 = BV100001.to_dict('records')
    # BV100003 = pd.json_normalize(BV100002,'data_jsonb_1',['product_id','spg_id','brand_id'])
    BV100003 = pd.json_normalize(BV100002)
    BV100004 = BV100003.to_dict('records')
    BV100005 = pd.json_normalize(BV100004)

    BV100003 = BV100005

    print(BV100003)
    print(BV100003.info())

    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry,tb_goods_app_translation
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku


    def check_data(goods_id):
        with Session(tb_goods_sku_engine) as session:
            statement = select(tb_goods_sku).where(tb_goods_sku.goods_id == goods_id)
            results = session.exec(statement).all()
            return results


    def insert_data(product_id,brand_id,spg_id,jsonb,images):
        with Session(tb_goods_sku_engine) as session:
            tb_goods_sku_ = tb_goods_sku(goods_id=product_id,gui_ge_param=jsonb,images=images,spg_id=spg_id,brand_id=brand_id)
            session.add(tb_goods_sku_)
            session.commit()
            session.refresh(tb_goods_sku_)
            print("Created hero:", tb_goods_sku_)
            return "Created hero"

    BV100004 = BV100003.replace(np.nan, 0)
    BV100005 = BV100004
    ZX100001 = {'A':{},'B':{},'C':{}}
    TY10001 = type_list
    for index, row in BV100005.iterrows():
        product_id = row['product_id']
        brand_id = row['brand_id']
        spg_id = row['spg_id']

    print(FFF['images'])
    results = check_data(product_id)
    if results == []:
        insert_data(product_id,brand_id,spg_id,FFF['data_jsonb_1'],FFF['images'])

    print(BV100005)
    print(BV100005.info())
    print(ZX100001)
    return "abc"


class Foo_(BaseModel):
    image_p : List = Body(...)


class goods_info_put(BaseModel):
    product_id : int
    brand_id : int
    spg_id:int
    images : Foo_
    # data_jsonb_1 : List[dict] = Body(...)
    data_jsonb_1 : dict = Body(...)


@router.put("/goods_info/{locale}", status_code = 200)
def goods_info_put(locale: str,req_body : goods_info_put = Body(...)):
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    print(FFF)
    BV100001 = pd.DataFrame([FFF])
    BV100002 = BV100001.to_dict('records')
    # BV100003 = pd.json_normalize(BV100002,'data_jsonb_1',['product_id','spg_id','brand_id'])
    BV100003 = pd.json_normalize(BV100002)
    BV100004 = BV100003.to_dict('records')
    BV100005 = pd.json_normalize(BV100004)

    BV100003 = BV100005

    print(BV100003)
    print(BV100003.info())

    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry,tb_goods_app_translation
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku


    def check_data(goods_id):
        with Session(tb_goods_sku_engine) as session:
            statement = select(tb_goods_sku).where(tb_goods_sku.goods_id == goods_id)
            results = session.exec(statement).all()
            return results


    def put_data(product_id,brand_id,spg_id,jsonb,images):
        with Session(tb_goods_sku_engine) as session:
            # tb_goods_sku_ = tb_goods_sku(goods_id=product_id,gui_ge_param=jsonb,images=images,spg_id=spg_id,brand_id=brand_id)

            statement = select(tb_goods_sku).where(tb_goods_sku.goods_id == int(product_id))
            results = session.exec(statement)
            hero = results.one()
            print("Hero:", hero)

            hero.gui_ge_param = jsonb
            hero.images = images
            hero.brand_id = brand_id
            session.add(hero)
            session.commit()
            session.refresh(hero)
            print("Created hero:", hero)
            return "Created hero"

    BV100004 = BV100003.replace(np.nan, 0)
    BV100005 = BV100004
    ZX100001 = {'A':{},'B':{},'C':{}}
    TY10001 = type_list
    for index, row in BV100005.iterrows():
        product_id = row['product_id']
        brand_id = row['brand_id']
        spg_id = row['spg_id']
        
    print(FFF['images'])
    results = check_data(product_id)
    if results != []:
        put_data(product_id,brand_id,spg_id,FFF['data_jsonb_1'],FFF['images'])
    print(FFF['data_jsonb_1'])
    print(BV100005)
    print(BV100005.info())
    print(ZX100001)
    return "abc"