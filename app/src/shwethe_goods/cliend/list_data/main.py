from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header,HTTPException,Query
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np
from sqlalchemy import desc
from sqlmodel import Session,select,or_,and_,SQLModel
router = APIRouter()
type_list = [1,3,4]
language_code_list = 'mm'



@router.get("/test/{locale}", status_code = 200)
def order_s_insert_list(locale: str,parent : int = 0,offset:int = 0,limit:int=9 , goods_spec_param_id : str = None):
    
    print(offset)
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(mongodb_data_api)
    from src.Connect.https_connect import mongodb_data_api
    print(limit)
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry
    
    def get_data_tb_goods_spu_sku(array_list):
        with Session(tb_goods_sku_engine) as session:
            print(array_list)
            print("array_list")
            if array_list != None:
                heroes = session.exec(select(tb_goods_sku).where(or_(tb_goods_sku.goods_param.op("@>") (v) for v in (array_list))).offset(offset).limit(limit).order_by(desc(tb_goods_sku.goods_qty))).all()
            else:
                heroes = session.exec(select(tb_goods_sku).offset(offset).limit(limit)).all()

            return heroes

    def get_data_tb_goods_spu_sku_(key,value):
        with Session(tb_goods_sku_engine) as session:
            heroes = session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_param.op('->')(key) == value).offset(offset).limit(limit)).all()
            return heroes

    if goods_spec_param_id == None or goods_spec_param_id == '':
        print("__________________")
        print(goods_spec_param_id)
        T900001 = get_data_tb_goods_spu_sku(goods_spec_param_id)
    else:
        print("++++++++++++++++++")
        print(goods_spec_param_id)
        data = json.loads(goods_spec_param_id)
        new_array = {}
        data_2={}
        for new_array_1 in data:
            if  str(data[new_array_1]) != '[]':
                print("+-+-+-+-+-+-+-+-")
                print(data[new_array_1])

        for key in data:
            # if data[key] != [] or  data[key] != '[]':
            if  str(data[key]) != '[]':
                data_2[key] = [int(val) for val in data[key]]
        print(data_2)
        data = data_2
        def product(*args, repeat=1):
            pools = [tuple(pool) for pool in args] * repeat
            result = [[]]
            for pool in pools:
                result = [x+[y] for x in result for y in pool]
            for prod in result:
                yield tuple(prod)
        V10001 = [dict(zip(data, v)) for v in product(*data.values())]

        print(V10001)

        T900001 = get_data_tb_goods_spu_sku(V10001)
    def c200001():
        with Session(tb_goods_translate_entry_engine) as session:
            heroes = session.exec(select(tb_goods_translate_entry).where(tb_goods_translate_entry.language_code == locale)).all()
            return heroes
    T100001 = c200001()
    goods_id = []
    YU100001 = []
    
    for i in T900001:
        FD100001 = {'images':i.images,'goods_id':i.goods_id,'spg_id':i.spg_id,'goods_qty':i.goods_qty,'goods_price':i.goods_price}
        goods_id.append(i.spg_id)
        YU100001.append(FD100001)
        

    Cz100001 = pd.DataFrame(YU100001)


    def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                     json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mmname','product_d_name': str+'_d_name'})
        # print(GF10001)
        return GF10001

    if not Cz100001.empty:
        merge_data10001 = Cz100001[['goods_id']]
        merge_data10002 = merge_data10001.rename(columns={"goods_id": "product_id"})
        merge_data10003 = get_data_api(merge_data10002,"goods")

        merge_data10004 = Cz100001.merge(merge_data10003, left_on=['goods_id'],right_on=['goods_id'], how='inner')

        Cz100001 = merge_data10004


        YU100002 = []
        for i in T100001:
            FD100001 = {'translation_id':i.translation_id,'field_text':i.field_text,'language_code':i.language_code}
            YU100002.append(FD100001)



        Cz100001['goods_qty_price'] = (Cz100001['goods_qty'] * Cz100001['goods_price'])
        # Cz100001['goods_qty_price_check'] = (Cz100001['goods_qty_price'] > 100000)
        Cz100001['goods_qty'].mask(Cz100001['goods_qty_price'] >=100000 ,'NaN', inplace=True)   
        print(Cz100001[['goods_price','goods_qty_price']])

        PO1000007 = Cz100001.to_json(orient='records')
        return json.loads(PO1000007)
    else:
        return []



@router.get("/category/{locale}", status_code = 200)
def order_s_insert_list(category_id:int,locale: str,parent : int = 0,offset:int = 0,limit:int=9 , goods_spec_param_id : str = None):
    
    print(offset)
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(mongodb_data_api)
    from src.Connect.https_connect import mongodb_data_api
    print(limit)
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry
    
    def get_data_tb_goods_spu_sku(array_list,category_id):
        with Session(tb_goods_sku_engine) as session:
            print(array_list)
            print("array_list")
            if array_list != None:
                heroes = session.exec(select(tb_goods_sku).where(and_(tb_goods_sku.goods_param.op("@>") (v) for v in (array_list))).where(tb_goods_sku.spg_id == category_id).offset(offset).limit(limit).order_by(desc(tb_goods_sku.goods_qty))).all()
            else:
                heroes = session.exec(select(tb_goods_sku).where(tb_goods_sku.spg_id == category_id).offset(offset).limit(limit).order_by(desc(tb_goods_sku.goods_qty))).all()

            return heroes

    def get_data_tb_goods_spu_sku_(key,value):
        with Session(tb_goods_sku_engine) as session:
            heroes = session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_param.op('->')(key) == value).offset(offset).limit(limit)).all()
            return heroes

    if goods_spec_param_id == None or goods_spec_param_id == '':
        print("__________________")
        print(goods_spec_param_id)
        T900001 = get_data_tb_goods_spu_sku(goods_spec_param_id,category_id)
    else:
        print("++++++++++++++++++")
        print(goods_spec_param_id)
        data = json.loads(goods_spec_param_id)
        new_array = {}
        data_2={}
        for new_array_1 in data:
            if  str(data[new_array_1]) != '[]':
                print("+-+-+-+-+-+-+-+-")
                print(data[new_array_1])

        for key in data:
            # if data[key] != [] or  data[key] != '[]':
            if  str(data[key]) != '[]':
                data_2[key] = [int(val) for val in data[key]]
        print(data_2)
        data = data_2
        def product(*args, repeat=1):
            pools = [tuple(pool) for pool in args] * repeat
            result = [[]]
            for pool in pools:
                result = [x+[y] for x in result for y in pool]
            for prod in result:
                yield tuple(prod)
        V10001 = [dict(zip(data, v)) for v in product(*data.values())]

        print(V10001)

        T900001 = get_data_tb_goods_spu_sku(V10001,category_id)
    def c200001():
        with Session(tb_goods_translate_entry_engine) as session:
            heroes = session.exec(select(tb_goods_translate_entry).where(tb_goods_translate_entry.language_code == locale)).all()
            return heroes
    T100001 = c200001()
    goods_id = []
    YU100001 = []
    
    for i in T900001:
        FD100001 = {'images':i.images,'goods_id':i.goods_id,'spg_id':i.spg_id,'goods_qty':i.goods_qty,'goods_price':i.goods_price}
        goods_id.append(i.spg_id)
        YU100001.append(FD100001)
        

    Cz100001 = pd.DataFrame(YU100001)


    def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                     json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mmname','product_d_name': str+'_d_name'})
        # print(GF10001)
        return GF10001

    if not Cz100001.empty:
        merge_data10001 = Cz100001[['goods_id']]
        merge_data10002 = merge_data10001.rename(columns={"goods_id": "product_id"})
        merge_data10003 = get_data_api(merge_data10002,"goods")

        merge_data10004 = Cz100001.merge(merge_data10003, left_on=['goods_id'],right_on=['goods_id'], how='inner')

        Cz100001 = merge_data10004


        YU100002 = []
        for i in T100001:
            FD100001 = {'translation_id':i.translation_id,'field_text':i.field_text,'language_code':i.language_code}
            YU100002.append(FD100001)



        Cz100001['goods_qty_price'] = (Cz100001['goods_qty'] * Cz100001['goods_price'])
        # Cz100001['goods_qty_price_check'] = (Cz100001['goods_qty_price'] > 100000)
        Cz100001['goods_qty'].mask(Cz100001['goods_qty_price'] >=100000 ,'NaN', inplace=True)

        print(Cz100001[['goods_price','goods_qty_price']])
        
        PO1000007 = Cz100001.to_json(orient='records')
        return json.loads(PO1000007)
    else:
        return []


class spec_type(BaseModel):
    goods_spec_param_id : List[dict] = Body(...)

@router.get("/test/spec_type/{locale}", status_code = 200)
def order_s_insert_list(locale: str):
    
    from src.shwethe_goods.orm.table.tb_goods_spec_param.main import tb_goods_spec_param_engine,tb_goods_spec_param
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry
    from src.shwethe_goods.orm.table.tb_goods_segments.main import tb_goods_segments_engine,tb_goods_segments


    


    def c200001():
        with Session(tb_goods_translate_entry_engine) as session:
            heroes = session.exec(select(tb_goods_translate_entry).where(tb_goods_translate_entry.language_code.in_([language_code_list,locale]))).all()
            return heroes

    def get_tb_goods_segments(spec_param_id):
        with Session(tb_goods_segments_engine) as session:
            heroes = session.exec(select(tb_goods_segments).where(tb_goods_segments.goods_spec_param_id.in_(spec_param_id))).all()
            return heroes   

    def c500001():
        with Session(tb_goods_spec_param_engine) as session:
            heroes = session.exec(select(tb_goods_spec_param).where(tb_goods_spec_param.spg_id.in_([6]),tb_goods_spec_param.segments == True)).all()
            return heroes


    T100001 = c200001()
    T900001 = c500001()
    goods_id = []
    goods_spec_param_id = []
    YU100001 = []
    
    for i in T900001:
        FD100001 = {'translation_id_id':i.translation_id,'translation_id':i.translation_id,'goods_spec_param_id':int(i.goods_spec_param_id),'spg_id':i.spg_id}
        goods_id.append(i.spg_id)
        goods_spec_param_id.append(int(i.goods_spec_param_id))
        YU100001.append(FD100001)

    Cz100001 = pd.DataFrame(YU100001)


    get_tb_goods_segments_10001 = get_tb_goods_segments(goods_spec_param_id)


    TT100002 = []
    for i in get_tb_goods_segments_10001:
        FD100001 = {'goods_segments_translation_id_id':i.translation_id,'goods_segments_translation_id':i.translation_id,'goods_spec_param_id':int(i.goods_spec_param_id),'goods_segments_id':i.goods_segments_id}
        TT100002.append(FD100001)

    TT100003 = pd.DataFrame(TT100002)


    Cz100001 = Cz100001.merge(TT100003, on=['goods_spec_param_id'], how='inner')


    YU100002 = []
    for i in T100001:
        FD100001 = {'translation_id':i.translation_id,'field_text':i.field_text,'language_code':i.language_code}
        YU100002.append(FD100001)

    CX200001 = pd.DataFrame(YU100002)
    CX200001['language_code_index'] = CX200001['language_code'].replace({locale:0, language_code_list:1})
    CX200001 = CX200001.sort_values(['translation_id','language_code_index'], ascending=[True,True])
    CX200001 = CX200001.drop_duplicates(subset=['translation_id'], keep='first')

    Cz100001[['translation_id','goods_segments_translation_id']] = Cz100001[['translation_id','goods_segments_translation_id']].replace(to_replace =CX200001['translation_id'].to_list(), 
                            value =CX200001['field_text'].to_list())

    PO1000007 = Cz100001.to_json(orient='records')

    return json.loads(PO1000007)


class spec_type(BaseModel):
    goods_spec_param_id : List[dict] = Body(...)

@router.get("/category/spec_type/{locale}", status_code = 200)
def order_s_insert_list(locale: str,category_id:int):
    
    from src.shwethe_goods.orm.table.tb_goods_spec_param.main import tb_goods_spec_param_engine,tb_goods_spec_param
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry
    from src.shwethe_goods.orm.table.tb_goods_segments.main import tb_goods_segments_engine,tb_goods_segments


    


    def c200001():
        with Session(tb_goods_translate_entry_engine) as session:
            heroes = session.exec(select(tb_goods_translate_entry).where(tb_goods_translate_entry.language_code.in_([language_code_list,locale]))).all()
            return heroes

    def get_tb_goods_segments(spec_param_id):
        with Session(tb_goods_segments_engine) as session:
            heroes = session.exec(select(tb_goods_segments).where(tb_goods_segments.goods_spec_param_id.in_(spec_param_id))).all()
            return heroes   

    def c500001(category_id):
        with Session(tb_goods_spec_param_engine) as session:
            heroes = session.exec(select(tb_goods_spec_param).where(tb_goods_spec_param.spg_id.in_([category_id]),tb_goods_spec_param.segments == True)).all()
            return heroes


    T100001 = c200001()
    T900001 = c500001(category_id)
    goods_id = []
    goods_spec_param_id = []
    YU100001 = []
    
    for i in T900001:
        FD100001 = {'translation_id_id':i.translation_id,'translation_id':i.translation_id,'goods_spec_param_id':int(i.goods_spec_param_id),'spg_id':i.spg_id}
        goods_id.append(i.spg_id)
        goods_spec_param_id.append(int(i.goods_spec_param_id))
        YU100001.append(FD100001)

    Cz100001 = pd.DataFrame(YU100001)


    get_tb_goods_segments_10001 = get_tb_goods_segments(goods_spec_param_id)


    TT100002 = []
    for i in get_tb_goods_segments_10001:
        FD100001 = {'goods_segments_translation_id_id':i.translation_id,'goods_segments_translation_id':i.translation_id,'goods_spec_param_id':int(i.goods_spec_param_id),'goods_segments_id':i.goods_segments_id}
        TT100002.append(FD100001)

    TT100003 = pd.DataFrame(TT100002)

    try:
        Cz100001 = Cz100001.merge(TT100003, on=['goods_spec_param_id'], how='inner')
    except:
        Cz100001 = Cz100001


    YU100002 = []
    for i in T100001:
        FD100001 = {'translation_id':i.translation_id,'field_text':i.field_text,'language_code':i.language_code}
        YU100002.append(FD100001)

    CX200001 = pd.DataFrame(YU100002)
    CX200001['language_code_index'] = CX200001['language_code'].replace({locale:0, language_code_list:1})
    CX200001 = CX200001.sort_values(['translation_id','language_code_index'], ascending=[True,True])
    CX200001 = CX200001.drop_duplicates(subset=['translation_id'], keep='first')
    try:
        Cz100001[['translation_id','goods_segments_translation_id']] = Cz100001[['translation_id','goods_segments_translation_id']].replace(to_replace =CX200001['translation_id'].to_list(), 
                                value =CX200001['field_text'].to_list())
    except:
        Cz100001

    PO1000007 = Cz100001.to_json(orient='records')

    return json.loads(PO1000007)



class spec_type(BaseModel):
    goods_spec_param_id : List[dict] = Body(...)

@router.get("/category/gui_ge_type/{locale}", status_code = 200)
def order_s_insert_list(locale: str,category_id:int):
    
    from src.shwethe_goods.orm.table.tb_goods_sh_goods_selector.main import tb_goods_sh_goods_selector_engine,tb_goods_sh_goods_selector


    def get_product(category_id):
        with Session(tb_goods_sh_goods_selector_engine) as session:
            # T100001 = pd.read_sql(session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_id == goods_id).limit(1)).statement,con=session.bind)
            heroes = session.exec(select(tb_goods_sh_goods_selector).where(and_(tb_goods_sh_goods_selector.category_id == category_id,tb_goods_sh_goods_selector.parent_id == 0)))
                
            return heroes

    def get_product_2(category_id):
        with Session(tb_goods_sh_goods_selector_engine) as session:
            # T100001 = pd.read_sql(session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_id == goods_id).limit(1)).statement,con=session.bind)
            heroes = session.exec(select(tb_goods_sh_goods_selector).where(and_(tb_goods_sh_goods_selector.category_id == category_id,tb_goods_sh_goods_selector.parent_id != 0)))
                
            return heroes


    def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

    ANS10001 = get_product(category_id)

    df = sqmodel_to_df(ANS10001)

    ANS20001 = get_product_2(category_id)

    df2 = sqmodel_to_df(ANS20001)

    print(df)
    print(df2)
    try:
        FF10001 = df.merge(df2, left_on=['auto_id'], right_on=['parent_id'], how='inner')

        PO1000007 = FF10001.to_json(orient='records')

        return json.loads(PO1000007)
    except:
        return []

@router.get("/filtter_goods_sku/{locale}", status_code = 200)
def order_s_insert_list(category_id:int,locale: str,parent : int = 0,offset:int = 0,limit:int=9 , goods_spec_param_id : str = None):
    
    print(offset)
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(mongodb_data_api)
    from src.Connect.https_connect import mongodb_data_api
    print(limit)
    from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import tb_goods_sku_engine,tb_goods_sku
    from src.shwethe_goods.orm.table.tb_goods_app_translation.main import tb_goods_translate_entry_engine,tb_goods_translate_entry
    from src.shwethe_goods.orm.table.tb_goods_sh_goods_selector.main import tb_goods_sh_goods_selector_engine,tb_goods_sh_goods_selector,tb_goods_sh_goods_selector_base,tb_goods_sh_goods_selector_value
    
    def get_data_tb_goods_spu_sku(array_list,category_id):
        with Session(tb_goods_sku_engine) as session:
            print(array_list)
            print("array_list")
            if array_list != None and array_list != () and array_list != '' :
                heroes = session.exec(select(tb_goods_sku).where(and_(tb_goods_sku.goods_id.in_(array_list))).where(tb_goods_sku.spg_id == category_id).offset(offset).limit(limit).order_by(desc(tb_goods_sku.goods_qty))).all()
                # heroes = session.exec(select(tb_goods_sku).where(and_(tb_goods_sku.goods_param.op("@>") (v) for v in (array_list))).where(tb_goods_sku.spg_id == category_id).offset(offset).limit(limit).order_by(desc(tb_goods_sku.goods_qty))).all()
            else:
                heroes = session.exec(select(tb_goods_sku).where(tb_goods_sku.spg_id == category_id).offset(offset).limit(limit).order_by(desc(tb_goods_sku.goods_qty))).all()

            # if array_list != None:
            #     heroes = session.exec(select(tb_goods_sku).where(and_(tb_goods_sku.goods_param.op("@>") (v) for v in (array_list))).where(tb_goods_sku.spg_id == category_id).offset(offset).limit(limit).order_by(desc(tb_goods_sku.goods_qty))).all()
            # else:
            #     heroes = session.exec(select(tb_goods_sku).where(tb_goods_sku.spg_id == category_id).offset(offset).limit(limit).order_by(desc(tb_goods_sku.goods_qty))).all()

            return heroes

    def get_data_tb_goods_spu_sku_(key,value):
        with Session(tb_goods_sku_engine) as session:
            heroes = session.exec(select(tb_goods_sku).where(tb_goods_sku.goods_param.op('->')(key) == value).offset(offset).limit(limit)).all()
            return heroes

    def get_data_2(spec_parmam):
        with Session(tb_goods_sh_goods_selector_engine) as session:
            heroes = session.exec(select(tb_goods_sh_goods_selector_value).where(tb_goods_sh_goods_selector_value.selector_value.in_((spec_parmam))))

            return heroes

    def get_data_3(spec_parmam):
        with Session(tb_goods_sh_goods_selector_engine) as session:
            print(spec_parmam)
            print("spec_parmam")
            heroes = session.exec(select(tb_goods_sh_goods_selector).where(tb_goods_sh_goods_selector.auto_id.in_((spec_parmam))))

            return heroes

    if goods_spec_param_id == None or goods_spec_param_id == '':
        print("__________________")
        print(goods_spec_param_id)
        T900001 = get_data_tb_goods_spu_sku(goods_spec_param_id,category_id)
    else:
        print("++++++++++++++++++")
        print(goods_spec_param_id)
        data = json.loads(goods_spec_param_id)
        new_array = {}
        data_2={}

        list_data = []
        for new_array_1 in data:
            if  str(data[new_array_1]) != '[]':
                print("+-+-+-+-+-+-+-+-")
                print(data[new_array_1])
                list_data.append(data[new_array_1])

        for key in data:
            # if data[key] != [] or  data[key] != '[]':
            if  str(data[key]) != '[]':
                data_2[key] = [int(val) for val in data[key]]

        print(data_2)

        data = data_2

        def product(*args, repeat=1):
            pools = [tuple(pool) for pool in args] * repeat
            result = [[]]
            for pool in pools:
                result = [x+[y] for x in result for y in pool]
            for prod in result:
                yield tuple(prod)
        V10001 = [dict(zip(data, v)) for v in product(*data.values())]


        def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
            """Convert a SQLModel objects into a pandas DataFrame."""
            records = [i.dict() for i in objs]
            df = pd.DataFrame.from_records(records)
            return df


        # print("V10001")
        # print(list_data[0])
        try:
            JJ10001 = get_data_3(list_data[0])
        except:
            JJ10001 = []
        print(JJ10001)
        print("JJ10001")

        JJ10001 = sqmodel_to_df(JJ10001)

        try:
            data_list = JJ10001['mm_name'].to_list()
        except:
            data_list = []

        JJ10001 = get_data_2(data_list)

        JJ10001 = sqmodel_to_df(JJ10001)
        print(JJ10001)
        print("JJ10001+++++++++++")
        try:
            data_list = JJ10001['goods_id'].to_list()
        except:
            data_list = []
        
        tuples = tuple(data_list)


        T900001 = get_data_tb_goods_spu_sku(tuples,category_id)


    def c200001():
        with Session(tb_goods_translate_entry_engine) as session:
            heroes = session.exec(select(tb_goods_translate_entry).where(tb_goods_translate_entry.language_code == locale)).all()
            return heroes
    T100001 = c200001()
    goods_id = []
    YU100001 = []
    
    for i in T900001:
        FD100001 = {'images':i.images,'goods_id':i.goods_id,'spg_id':i.spg_id,'goods_qty':i.goods_qty,'goods_price':i.goods_price}
        goods_id.append(i.spg_id)
        YU100001.append(FD100001)

    Cz100001 = pd.DataFrame(YU100001)


    def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                     json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mmname','product_d_name': str+'_d_name'})
        # print(GF10001)
        return GF10001

    if not Cz100001.empty:
        merge_data10001 = Cz100001[['goods_id']]
        merge_data10002 = merge_data10001.rename(columns={"goods_id": "product_id"})
        merge_data10003 = get_data_api(merge_data10002,"goods")
        merge_data10004 = Cz100001.merge(merge_data10003, left_on=['goods_id'],right_on=['goods_id'], how='inner')
        Cz100001 = merge_data10004


        YU100002 = []
        for i in T100001:
            FD100001 = {'translation_id':i.translation_id,'field_text':i.field_text,'language_code':i.language_code}
            YU100002.append(FD100001)


        Cz100001['goods_qty_price'] = (Cz100001['goods_qty'] * Cz100001['goods_price'])
        # Cz100001['goods_qty_price_check'] = (Cz100001['goods_qty_price'] > 100000)
        Cz100001['goods_qty'].mask(Cz100001['goods_qty_price'] >=100000 ,'NaN', inplace=True)

        print(Cz100001[['goods_price','goods_qty_price']])



        PO1000007 = Cz100001.to_json(orient='records')
        return json.loads(PO1000007)
    else:
        return []
