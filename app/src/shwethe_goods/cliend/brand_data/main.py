from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header,HTTPException,Query
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np
from sqlmodel import Session,select,or_
router = APIRouter()
type_list = [1,2,3]
language_code_list = 'mm'


@router.get("/brand_list/{locale}", status_code = 200)
def order_s_insert_list(locale: str):

    from src.shwethe_goods.orm.table.tb_brand.main import tb_brand_engine,tb_brand

    def c100001():
        with Session(tb_brand_engine) as session:
            heroes = session.exec(select(tb_brand)).all()
            return heroes
    T100001 = c100001()
    YU100001 = []
    for i in T100001:
        print(i)
        FD100001 = {'brand_id':i.brand_id,'name':i.name,'images':i.images,'letter':i.letter}
        YU100001.append(FD100001)
    CX100001 = pd.DataFrame(YU100001)
    print(CX100001)

    print(CX100001)
    PO1000007 = CX100001.to_json(orient='records')
    
    return json.loads(PO1000007)




@router.get("/brand_list/{locale}/{brand_id_}", status_code = 200)
def order_s_insert_list(locale: str,brand_id_:int):

    from src.shwethe_goods.orm.table.tb_brand.main import tb_brand_engine,tb_brand

    def c100001(brand_id_):
        with Session(tb_brand_engine) as session:
            heroes = session.exec(select(tb_brand).where(tb_brand.brand_id == brand_id_)).all()
            return heroes
    T100001 = c100001(brand_id_)
    YU100001 = []
    for i in T100001:
        print(i)
        FD100001 = {'brand_id':i.brand_id,'name':i.name,'images':i.images,'letter':i.letter}
        YU100001.append(FD100001)
    CX100001 = pd.DataFrame(YU100001)
    print(CX100001)

    print(CX100001)
    PO1000007 = CX100001.to_json(orient='records')
    
    return json.loads(PO1000007)