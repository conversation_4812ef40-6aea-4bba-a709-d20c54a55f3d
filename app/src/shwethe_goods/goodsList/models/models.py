from xmlrpc.client import DateTime
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from typing import List, Optional
from helper import generate_datetime_id

# 资料表名称 pre_order_product 和框架
# class delivery_insert_base(SQLModel):
#     product_id: Optional[int]
    

# class delivery_insert(delivery_insert_base, table=True):
#     auto_id: Optional[int] = Field(default=None, primary_key=True)
#     product_id: Optional[int]
#     product_qty: Optional[float]
#     che_ci: Optional[int]
#     lei_a: Optional[int]
#     lei_b: Optional[int]
#     jia_yi_fang_a: Optional[int]
#     jia_yi_fang_b: Optional[int]
#     shu_riqi_datetime : datetime
#     data_sub : dict = Field(sa_column=Column(JSONB), default={})


class goods_put_post(BaseModel):
    attrInfo : List[dict]
    productInfo:dict
    selectorInfo:List[dict]