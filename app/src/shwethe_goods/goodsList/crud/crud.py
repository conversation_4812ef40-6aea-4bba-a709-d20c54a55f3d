from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,or_,and_
from sqlalchemy import text
from typing import List, Optional,Dict
from src.config.db_info.database import get_session as get_session_insert_data
from src.common.product_name import product_name_package
import json
from src.shwethe_delivery.deliveryGoods.crud.crud import (
    delivery_insert,
)
from src.time_zone import time_zone_function
from datetime import datetime, timedelta
import numpy as np

from src.shwethe_goods.orm.table.tb_goods_sh_goods_attr.main import (
    tb_goods_sh_goods_attr_value,
)

from src.shwethe_goods.orm.table.tb_goods_sh_goods_selector.main import (
    tb_goods_sh_goods_selector_value
)


from src.shwethe_goods.goodsList.models.models import (
    goods_put_post
)

from src.shwethe_goods.orm.table.tb_goods_spu_sku.main import (
    tb_goods_sku
)

from src.common.product_name import product_name_package
from src.common.qty import product_qty_package



import pandas as pd 

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df



def getGoodsListData(offset:int = 1,limit:int=30,category_id:int=0,parme=str,db: Session = Depends(get_session_insert_data)):
    
    engine = db.get_bind()

    try:
        thislist = parme.split()
    except:
        thislist = []

    K = "undefined"

    while(K in thislist):
        thislist.remove(K)
    
    # Remove underscores from elements of thislist
    thislist = [word.replace('_', ' ') for word in thislist]
    thislist = thislist[1::2]

    print(thislist, "1111111111")
    if parme != 'undefined' and thislist != []:
        # 假设 db 是 SQLAlchemy session 对象
        # statement = db.execute(select(tb_goods_sh_goods_selector_value)
        #                     .where(tb_goods_sh_goods_selector_value.selector_value.in_(thislist))).all()
        statement = db.exec(select(tb_goods_sh_goods_selector_value).where(tb_goods_sh_goods_selector_value.selector_value.in_(thislist))).all()


        A300001 = sqmodel_to_df(statement)
        A300001 = A300001.sort_values(['auto_id'], ascending=[False])
        A300001 = A300001.drop_duplicates(subset=['goods_id','selector_id'], keep='first')
        
        print(A300001)
        
        print(thislist)
        
        A300001 = A300001.groupby('goods_id').filter(lambda x: set(thislist).issubset(x['selector_value']))
        
        print(A300001)
        print("----------------------------------------")
        
        
        if A300001.empty:
            return None
        
        


    else:
        A300001 = pd.DataFrame()
        
        
    # 过滤 DataFrame
    # A300001 = A300001[A300001['selector_value'].isin(thislist)]



    # if not A300001.empty:
    #     A300001 = A300001.groupby(['goods_id'])['auto_id'].count().reset_index()
    #     A300001 = A300001.loc[A300001['auto_id'] == A300001['auto_id'].max()].reset_index()
    #     print(A300001['auto_id'].max())
    #     print("A300001['auto_id'].max()")


    with engine.connect() as con:
        if category_id > 0 and not A300001.empty:
            goods_ids = A300001['goods_id'].tolist()
            if len(goods_ids) == 1:
                sql = """
                    SELECT *, COALESCE(goods_qty, 0) AS adjusted_goods_qty
                    FROM tb_goods_sku 
                    WHERE goods_id = %s AND spg_id = %s 
                    ORDER BY adjusted_goods_qty DESC 
                    LIMIT %s OFFSET (%s-1)*%s
                """
                sql = sql % (goods_ids[0], category_id, limit, offset, limit)
            else:
                sql = """
                    SELECT *, COALESCE(goods_qty, 0) AS adjusted_goods_qty
                    FROM tb_goods_sku 
                    WHERE goods_id IN %s AND spg_id = %s 
                    ORDER BY adjusted_goods_qty DESC 
                    LIMIT %s OFFSET (%s-1)*%s
                """
                sql = sql % (tuple(goods_ids), category_id, limit, offset, limit)
            A200001 = pd.read_sql(text(sql), con)

        if category_id > 0 and A300001.empty:
            sql = """
                SELECT *, COALESCE(goods_qty, 0) AS adjusted_goods_qty
                FROM tb_goods_sku 
                WHERE spg_id = %s 
                ORDER BY adjusted_goods_qty DESC 
                LIMIT %s OFFSET (%s-1)*%s
            """
            sql = sql % (category_id, limit, offset, limit)
            A200001 = pd.read_sql(text(sql), con)

        if category_id == 0:
            sql = """
                SELECT *, COALESCE(goods_qty, 0) AS adjusted_goods_qty
                FROM tb_goods_sku 
                ORDER BY adjusted_goods_qty DESC 
                LIMIT %s OFFSET (%s-1)*%s
            """
            sql = sql % (limit, offset, limit)
            A200001 = pd.read_sql(text(sql), con)


    if A200001.empty:
        return []

    A200001['product_id'] = A200001['goods_id']

    Function = product_name_package()

    V140001 = Function.select_product_list_id_with_http(nameStr='product',df=A200001[['product_id']])

    A200001 = A200001.merge(V140001, on=['product_id'], how='inner')

    print(V140001)

    A200002 = json.loads(A200001.to_json(orient='records'))
    
    return A200002


# def getGoodsListDataByGroup(category_id:int=0,parme=str,db: Session = Depends(get_session_insert_data)):
#     print(category_id, parme, "ooooooooooooooooooooooooooo")

#     try:
#         thislist = parme.split()
#     except:
#         thislist = []

#     K = "undefined"

#     while(K in thislist):
#         thislist.remove(K)
    
#     # Remove underscores from elements of thislist
#     thislist = [word.replace('_', ' ') for word in thislist]
#     thislist = thislist[1::2]
#     print(thislist, "1111111111")
    

#     engine = db.get_bind()
#     with engine.connect() as con:
#         query = text(
#             """SELECT * FROM tb_goods_sh_goods_selector 
#             WHERE category_id = :category_id"""
#         )
#         # df = pd.read_sql(query, con, params={"category_id": 372})
#         df = pd.read_sql(query, con, params={"category_id": category_id})
#         df = df.sort_values(by='auto_id', ascending=True)
#         print(df)
#     filtered_df = df[df['parent_id'] != 0]
#     unique_parent_ids = filtered_df['parent_id'].unique()
#     unique_parent_ids_list = unique_parent_ids.tolist()
#     # print(unique_parent_ids_list)


#     with engine.connect() as con:
#         query = text(
#             """SELECT * FROM tb_goods_sh_goods_selector_value 
#             WHERE selector_id IN :selector_id
#             and selector_value IN :selector_value"""
#         )
#         # df2 = pd.read_sql(query, con, params={"selector_id": tuple(unique_parent_ids_list), "selector_value": tuple(["ဘိုထိုင်လက်လောင်း"])})
#         df2 = pd.read_sql(query, con, params={"selector_id": tuple(unique_parent_ids_list), "selector_value": tuple(thislist)})
#         df2 = df2.sort_values(by='auto_id', ascending=True)
#         # Group by goods_id and filter groups that contain both desired selector_values
#         # filtered_df2 = df2.groupby('goods_id').filter(
#         #     lambda x: set(["ဘိုထိုင်လက်လောင်း"]).issubset(set(x['selector_value']))
#         # )
#         filtered_df2 = df2.groupby('goods_id').filter(
#             lambda x: set(thislist).issubset(set(x['selector_value']))
#         )
#         unique_parent_ids200 = filtered_df2['goods_id'].unique()
#         unique_parent_ids_list200 = unique_parent_ids200.tolist()
#         print(unique_parent_ids_list, unique_parent_ids_list200, filtered_df2)

#     with engine.connect() as con:
#         query = text(
#             """SELECT * FROM tb_goods_sh_goods_selector_value 
#             WHERE goods_id IN :goods_id
#             and selector_id IN :selector_id"""
#         )
#         df3 = pd.read_sql(query, con, params={"goods_id": tuple(unique_parent_ids_list200), "selector_id": tuple(unique_parent_ids_list)})
#         df3 = df3.sort_values(by='auto_id', ascending=True)
#         filtered_df300 = df3[df3['selector_value'] != '-']
#         print('df33333333', filtered_df300)

    
#     # Merge df3 with df1 to get the parent tagName
#     merged_df = filtered_df300.merge(df[['auto_id', 'mm_name']], left_on='selector_id', right_on='auto_id', how='left')
#     merged_df['mm_name'] = pd.Categorical(merged_df['mm_name'], categories=merged_df['mm_name'].unique(), ordered=True)
#     result = merged_df.groupby('mm_name')['selector_value'].apply(lambda x: list(set(x))).reset_index()
#     result.columns = ['tagName', 'tags']
#     output = result.to_dict(orient='records')
#     print('df4444444', merged_df)
#     print('df555555555', result)
    

#     # A200001 = json.loads(filtered_df.to_json(orient='records'))
    
#     return output
# def getGoodsListDataByGroup(category_id:int=0,parme=str,db: Session = Depends(get_session_insert_data)):
#     print(category_id, parme, "ooooooooooooooooooooooooooo")

#     try:
#         thislist = parme.split()
#     except:
#         thislist = []

#     K = "undefined"

#     while(K in thislist):
#         thislist.remove(K)
    
#     # Remove underscores from elements of thislist
#     thislist = [word.replace('_', ' ') for word in thislist]
#     thislist = thislist[1::2]
#     print(thislist, "1111111111")
    

#     engine = db.get_bind()
#     with engine.connect() as con:
#         query = text(
#             """SELECT * FROM tb_goods_sh_goods_selector 
#             WHERE category_id = :category_id"""
#         )
#         # df = pd.read_sql(query, con, params={"category_id": 372})
#         df = pd.read_sql(query, con, params={"category_id": category_id})
#         df = df.sort_values(by='auto_id', ascending=True)
#         print(df)
#     filtered_df = df[df['parent_id'] != 0]
#     unique_parent_ids = filtered_df['parent_id'].unique()
#     unique_parent_ids_list = unique_parent_ids.tolist()
#     # print(unique_parent_ids_list)


#     with engine.connect() as con:
#         query = text(
#             """SELECT * FROM tb_goods_sh_goods_selector_value 
#             WHERE selector_id IN :selector_id
#             and selector_value IN :selector_value"""
#         )
#         # df2 = pd.read_sql(query, con, params={"selector_id": tuple(unique_parent_ids_list), "selector_value": tuple(["ဘိုထိုင်လက်လောင်း"])})
#         df2 = pd.read_sql(query, con, params={"selector_id": tuple(unique_parent_ids_list), "selector_value": tuple(thislist)})
#         df2 = df2.sort_values(by='auto_id', ascending=True)
#         # Group by goods_id and filter groups that contain both desired selector_values
#         # filtered_df2 = df2.groupby('goods_id').filter(
#         #     lambda x: set(["ဘိုထိုင်လက်လောင်း"]).issubset(set(x['selector_value']))
#         # )
#         filtered_df2 = df2.groupby('goods_id').filter(
#             lambda x: set(thislist).issubset(set(x['selector_value']))
#         )
#         unique_parent_ids200 = filtered_df2['goods_id'].unique()
#         unique_parent_ids_list200 = unique_parent_ids200.tolist()
#         print(unique_parent_ids_list, unique_parent_ids_list200, filtered_df2)

#     with engine.connect() as con:
#         query = text(
#             """SELECT * FROM tb_goods_sh_goods_selector_value 
#             WHERE goods_id IN :goods_id
#             and selector_id IN :selector_id"""
#         )
#         df3 = pd.read_sql(query, con, params={"goods_id": tuple(unique_parent_ids_list200), "selector_id": tuple(unique_parent_ids_list)})
#         df3 = df3.sort_values(by='auto_id', ascending=True)
#         filtered_df300 = df3[df3['selector_value'] != '-']
#         print('df33333333', filtered_df300)

    
#     # Merge df3 with df1 to get the parent tagName
#     # merged_df = filtered_df300.merge(df[['auto_id', 'mm_name']], left_on='selector_id', right_on='auto_id', how='left')
#     # merged_df['mm_name'] = pd.Categorical(merged_df['mm_name'], categories=merged_df['mm_name'].unique(), ordered=True)
#     # result = merged_df.groupby('mm_name')['selector_value'].apply(lambda x: list(set(x))).reset_index()
#     # result.columns = ['tagName', 'tags']
#     # output = result.to_dict(orient='records')
#     # print('df4444444', filtered_df300)
#     # print('df555555555', result)

#     # # Step 1: Merge to get parent_mm_name
#     # parent_mm_name = df[['auto_id', 'mm_name']].rename(columns={'auto_id': 'parent_id', 'mm_name': 'parent_mm_name'})
#     # df_merged = df.merge(parent_mm_name, on='parent_id', how='left')
#     # # Step 2: Create 'show' column
#     # df_merged['show'] = df_merged['mm_name'].isin(filtered_df300['selector_value'])
#     # # Step 3: Filter and reorder columns to match the desired output
#     # df4 = df_merged[['auto_id', 'parent_id', 'category_id', 'mm_name', 'sort', 'show', 'parent_mm_name']]
#     # # Step 4: Filter out rows where parent_id is 0
#     # df4 = df4[df4['parent_id'] != 0].reset_index(drop=True)


#     # # Step 1: Merge to get parent_mm_name
#     # parent_mm_name = df[['auto_id', 'mm_name']].rename(columns={'auto_id': 'parent_id', 'mm_name': 'parent_mm_name'})
#     # df_merged = df.merge(parent_mm_name, on='parent_id', how='left')
#     # # Step 2: Create 'show' column
#     # df_merged['show'] = df_merged['mm_name'].isin(filtered_df300['selector_value'])
#     # # Step 3: Filter out rows where parent_id is 0
#     # df4 = df_merged[df_merged['parent_id'] != 0].reset_index(drop=True)
#     # # Step 4: Group by parent_mm_name and construct the nested dictionary
#     # result = []
#     # for parent_mm_name, group in df4.groupby('parent_mm_name'):
#     #     tag_dict = {
#     #         "tagName": parent_mm_name,
#     #         "tags": group.apply(lambda row: {"name": row["mm_name"], "show": row["show"]}, axis=1).tolist()
#     #     }
#     #     result.append(tag_dict)
        
#     # print(result)


#     # Step 1: Merge to get parent_mm_name
#     parent_mm_name = df[['auto_id', 'mm_name']].rename(columns={'auto_id': 'parent_id', 'mm_name': 'parent_mm_name'})
#     df_merged = df.merge(parent_mm_name, on='parent_id', how='left')
#     # Step 2: Create 'show' column
#     df_merged['show'] = df_merged['mm_name'].isin(filtered_df300['selector_value'])
#     # Step 3: Filter out rows where parent_id is 0
#     df_filtered = df_merged[df_merged['parent_id'] != 0].reset_index(drop=True)
#     # Step 4: Preserve the order of parent_mm_name from the original dataframe
#     parent_order = df[df['parent_id'] == 0]['mm_name'].tolist()
#     # Step 5: Group by parent_mm_name and construct the nested dictionary, preserving the order
#     result = []
#     for parent_mm_name in parent_order:
#         group = df_filtered[df_filtered['parent_mm_name'] == parent_mm_name]
#         if not group.empty:
#             tag_dict = {
#                 "tagName": parent_mm_name,
#                 "tags": group.apply(lambda row: {"name": row["mm_name"], "show": row["show"]}, axis=1).tolist()
#             }
#             result.append(tag_dict)



#     # A200001 = json.loads(df4.to_json(orient='records'))
    
#     return result

def filterListByGroup(category_id:int=0,parme=str,db: Session = Depends(get_session_insert_data)):
    print(category_id, parme, "ooooooooooooooooooooooooooo")

    try:
        thislist = parme.split()
    except:
        thislist = []

    K = "undefined"

    while(K in thislist):
        thislist.remove(K)
    
    # Remove underscores from elements of thislist
    thislist = [word.replace('_', ' ') for word in thislist]
    thislist = thislist[1::2]
    # print("1111111111111111111", thislist)
    

    engine = db.get_bind()
    with engine.connect() as con:
        query = text(
            """SELECT * FROM tb_goods_sh_goods_selector 
            WHERE category_id = :category_id"""
        )
        df = pd.read_sql(query, con, params={"category_id": category_id})
        df = df.sort_values(by='auto_id', ascending=True)
        print("2222222222222222222", df)
    filtered_df = df[df['parent_id'] != 0]
    unique_parent_ids = filtered_df['parent_id'].unique()
    unique_parent_ids_list = unique_parent_ids.tolist()


    with engine.connect() as con:
        query = text(
            """SELECT * FROM tb_goods_sh_goods_selector_value 
            WHERE selector_id IN :selector_id
            and selector_value IN :selector_value"""
        )
        df2 = pd.read_sql(query, con, params={"selector_id": tuple(unique_parent_ids_list), "selector_value": tuple(thislist)})
        df2 = df2.sort_values(by='auto_id', ascending=True)
        filtered_df2 = df2.groupby('goods_id').filter(
            lambda x: set(thislist).issubset(set(x['selector_value']))
        )
        unique_parent_ids200 = filtered_df2['goods_id'].unique()
        unique_parent_ids_list200 = unique_parent_ids200.tolist()
        # print(unique_parent_ids_list, unique_parent_ids_list200, filtered_df2)
        print("333333333333333333333", df2)

    with engine.connect() as con:
        query = text(
            """SELECT * FROM tb_goods_sh_goods_selector_value 
            WHERE goods_id IN :goods_id
            and selector_id IN :selector_id"""
        )
        df3 = pd.read_sql(query, con, params={"goods_id": tuple(unique_parent_ids_list200), "selector_id": tuple(unique_parent_ids_list)})
        df3 = df3.sort_values(by='auto_id', ascending=True)
        filtered_df300 = df3[df3['selector_value'] != '-']
        # print('df33333333', filtered_df300)


    # Step 1: Merge to get parent_mm_name
    parent_mm_name = df[['auto_id', 'mm_name']].rename(columns={'auto_id': 'parent_id', 'mm_name': 'parent_mm_name'})
    df_merged = df.merge(parent_mm_name, on='parent_id', how='left')
    # Step 2: Create 'show' column
    df_merged['show'] = df_merged['mm_name'].isin(filtered_df300['selector_value'])
    # Step 3: Filter out rows where parent_id is 0
    df_filtered = df_merged[df_merged['parent_id'] != 0].reset_index(drop=True)
    # Step 4: Preserve the order of parent_mm_name from the original dataframe
    parent_order = df[df['parent_id'] == 0]['mm_name'].tolist()
    # Step 5: Group by parent_mm_name and construct the nested dictionary, preserving the order
    result = []
    for parent_mm_name in parent_order:
        group = df_filtered[df_filtered['parent_mm_name'] == parent_mm_name]
        if not group.empty:
            tag_dict = {
                "tagName": parent_mm_name,
                "tags": group.apply(lambda row: {"name": row["mm_name"], "show": row["show"]}, axis=1).tolist()
            }
            result.append(tag_dict)

    
    return result


def getparmeForCategory(category_id=str,db: Session = Depends(get_session_insert_data)):

    if category_id == 'undefined':
        return []

    engine = db.get_bind()
    with engine.connect() as con:
            A200001 = pd.read_sql(text(
            """  select * from tb_goods_sh_goods_selector where category_id in (%s) """%(category_id)),con)

    T10001 = pd.DataFrame()
    for index, row in A200001.iterrows():
        A = str(row['mm_name'])
        BBBB = A200001.loc[A200001['parent_id'] == row['auto_id']]
        mata  = {
            'tagName':A,
            'tags':BBBB['mm_name'].tolist()
        }
        if len(BBBB['mm_name'].tolist()) > 0 :
            T10001 = T10001.append([mata])


    A200002 = json.loads(T10001.to_json(orient='records'))

    
    return A200002


def getGoodsListDataNotInsert(db: Session = Depends(get_session_insert_data)):
    
    engine = db.get_bind()
    with engine.connect() as con:
            A200001 = pd.read_sql(text(
                # """  select * from tb_goods_sku limit numberperpage offset (pagenumber-1)*numberperpage """%(limit,offset,limit)),con)
                """  select * from tb_goods_sku where spg_id in (SELECT t1.category_id
                    FROM tb_goods_category t1
                    LEFT JOIN tb_goods_category t2 ON t1.category_id = t2.parent_id
                    WHERE t2.category_id IS NULL
                    ORDER BY t1.category_id DESC) """),con)

    if A200001.empty:
        return []

    A200001['product_id'] = A200001['goods_id']


    getQtyFunction = product_qty_package()

    V100001 = getQtyFunction.getAllQty()

    A500001 = V100001[~V100001['product_id'].isin (A200001['product_id'].tolist())]


    if A500001.empty:
    
        return []

    # A200001 = A200001[A200001['product_id'].isin (A500001['product_id'])]

    # if A200001.empty:

    #     return []

    # print(V100001.head())

    # print(V100001.info())

    Function = product_name_package()

    V140001 = Function.select_product_list_id_with_http(nameStr='product',df=A500001[['product_id']])

    A200001 = A500001.merge(V140001, on=['product_id'], how='inner')

    print(V140001)

    # A200001 = A200001.head()

    A200002 = json.loads(A200001.to_json(orient='records'))
    
    return A200002

def getCategoryListData(parent_id:int = 0,db: Session = Depends(get_session_insert_data)):
    
    engine = db.get_bind()
    with engine.connect() as con:
        A200001 = pd.read_sql(text(
            # """  select * from tb_goods_sku limit numberperpage offset (pagenumber-1)*numberperpage """%(limit,offset,limit)),con)
            """  select * from tb_goods_category where parent_id = %s """%(parent_id,)),con)
    if A200001.empty:
        return []
    A200001 = A200001.reset_index()
    A200001['index'] = (A200001['index'] + 1)
    A200001['indexCheck'] = ((A200001['index'] % 2)==0)
    A200001["indexCheck"] = A200001["indexCheck"].astype(int)
    A200001['indexCheck'] = (A200001['indexCheck'] + 1)

    A200002 = json.loads(A200001.to_json(orient='records'))
    
    return A200002

def getCategoryListDataSub(db: Session = Depends(get_session_insert_data)):
    
    engine = db.get_bind()
    with engine.connect() as con:
        A200001 = pd.read_sql(text(
            # """  select * from tb_goods_sku limit numberperpage offset (pagenumber-1)*numberperpage """%(limit,offset,limit)),con)
            """  select * from tb_goods_category where parent_id != 0 """),con)

    mata = []
    for index, row in A200001.iterrows():
        category_id = row['category_id']
        parent_id = row['parent_id']
        A200002 = A200001.loc[A200001['parent_id'] == category_id]
        if not A200002.empty:
            data = {'category_id':category_id,'parent_id':parent_id,'title':row['mm_name'],'dataSub':A200002.to_dict('records')}
            mata.append(data)

    # print(mata)

    A200002 = json.loads(json.dumps(mata))
    
    return A200002

from dataclasses import dataclass, field, asdict


@dataclass
class Tree:
    name: str
    id: int
    child_id: int
    children: List['Tree'] = field(default_factory=list)


def getCategoryListDataSubArcoTree(db: Session = Depends(get_session_insert_data)):
    



    def makehiearchy(data):
        result = []
        d = { "0": { "children": result } }
        for id, name,  child_id in zip(data["key"], data["title"], data["child_id"]):

            d[id] = { "title": name, "key": id, "child_id": child_id }

        for id, child_id in zip(data["key"], data["child_id"]):
            parent = d[child_id]
            if "children" not in parent:
                parent["children"] = []
                parent['disabled'] = True
                 
            parent["children"].append(d[id])

        return result


    engine = db.get_bind()
    with engine.connect() as con:
        A200001 = pd.read_sql(text(
            # """  select * from tb_goods_sku limit numberperpage offset (pagenumber-1)*numberperpage """%(limit,offset,limit)),con)
            """  select * from tb_goods_category  """),con)

    df = A200001.rename(columns={"category_id": "auto_id", "mm_name": "mm_name", "parent_id": "parent_id"})    

    # Example run
    # sql = db.exec(select(tree_key).where(tree_key.head_id == head_id)).all()
    # records = [i.dict() for i in sql]   
    # df = pd.DataFrame.from_records(records)
    # df['auto_fen'] = df['auto_id'].astype(str) + ',' + df['fen_ji'].astype(str) + ',' + df['head_id'].astype(str)

    print(df)
    data = {
        'key': df['auto_id'].astype(str).to_list(),
        'title': df['mm_name'].to_list(),
        'child_id': df['parent_id'].astype(str).to_list()
    }    
    hierarchy = makehiearchy(data)

    return hierarchy


# def getCategoryListDataSubProductBYId(productId : int, db: Session = Depends(get_session_insert_data)):

#     engine = db.get_bind()
#     with engine.connect() as con:
#         A200001 = pd.read_sql(text(
#             # """  select * from tb_goods_sku limit numberperpage offset (pagenumber-1)*numberperpage """%(limit,offset,limit)),con)
#             """  select * from tb_goods_sku where goods_id = %s """%(productId,)),con)
#         print(A200001)

#     A200001 = json.loads(A200001.to_json(orient='records'))

#     return A200001

def getCategoryListDataSubProductId(productId : int,category_id : int,db: Session = Depends(get_session_insert_data)):

    print(category_id)
    category_id_ = category_id
    
    engine = db.get_bind()
    with engine.connect() as con:
        A200001 = pd.read_sql(text(
            # """  select * from tb_goods_sku limit numberperpage offset (pagenumber-1)*numberperpage """%(limit,offset,limit)),con)
            """  select * from tb_goods_sku where goods_id = %s """%(productId,)),con)

        if not A200001.empty:
            if category_id ==0:
                category_id_ = A200001['spg_id'][0]
                
    if not A200001.empty:    
        
        Function = product_name_package()
        LKO10001 = Function.select_product_list_id_with_http(nameStr='product',df=A200001[['goods_id']])


    if A200001.empty and category_id == 0:

        return []


    if category_id_ != 0:
        with engine.connect() as con:
            G200001 = pd.read_sql(text(
                # """  select * from tb_goods_sku limit numberperpage offset (pagenumber-1)*numberperpage """%(limit,offset,limit)),con)
                """  select * from tb_goods_sh_goods_gui_ge where category_id = %s """%(category_id_,)),con)
            array_gui_ge_param = {}
            for index, row in G200001.iterrows():
                A100001 = str(row['mm_name'])

                if A200001.empty:
                    array_gui_ge_param[A100001] = ''

                if not A200001.empty:
                    try:
                        # 首先检查gui_ge_param是否为None
                        if A200001['gui_ge_param'][0] is None:
                            A200001['gui_ge_param'][0] = {}
                        
                        print(A200001['gui_ge_param'][0][A100001])
                        if A200001['gui_ge_param'][0][A100001] == '':
                            A200001['gui_ge_param'][0][A100001] = ''

                    except:
                        # 如果出现异常（例如键不存在），确保字典已初始化
                        if A200001['gui_ge_param'][0] is None:
                            A200001['gui_ge_param'][0] = {}
                        A200001['gui_ge_param'][0][A100001] = ''

                    # print(A200001['goods_param'][0]['A100001'])






    if category_id_ != 0:
        with engine.connect() as con:

            B200001 = pd.read_sql(text(
                # """  select * from tb_goods_sku limit numberperpage offset (pagenumber-1)*numberperpage """%(limit,offset,limit)),con)
                """  select * from tb_goods_sh_goods_attr where category_id = %s """%(category_id_,)),con)

            B200002 = pd.read_sql(text(
                # """  select * from tb_goods_sku limit numberperpage offset (pagenumber-1)*numberperpage """%(limit,offset,limit)),con)
                """  select attr_id as auto_id,attr_value as value from tb_goods_sh_goods_attr_value where goods_id = %s """%(productId,)),con)

            if not B200002.empty:
                B200001 = B200001.merge(B200002, left_on=['auto_id'], right_on=['auto_id'], how='left')
                B200001['value'] = B200001['value'].replace(np.nan, '')

            else:

                B200001['value'] = ''


            
            print(B200001)

    if category_id_ != 0:

        with engine.connect() as con:
            C200001 = pd.read_sql(text(
                """  select * from tb_goods_sh_goods_selector where category_id = %s """%(category_id_,)),con)

            C200002 = pd.read_sql(text(
                """  select selector_id as auto_id ,selector_value as value from tb_goods_sh_goods_selector_value where goods_id = %s """%(productId,)),con)


            if not C200002.empty:

                C200001 = C200001.merge(C200002, left_on=['auto_id'], right_on=['auto_id'], how='left')
                C200001['value'] = C200001['value'].replace(np.nan, '')

            else:

                C200001['value'] = ''

    if A200001.empty:

        data = {'productInfo':{'gui_ge_param':array_gui_ge_param,'images': {'image_p':[]}},'selectorInfo':C200001.to_dict('records'),'attrInfo':B200001.to_dict('records')}

        A200002 = json.loads(json.dumps(data))
    
        return A200002

    # 处理数据中的Timestamp类型
    for column in A200001.select_dtypes(include=['datetime64[ns]']).columns:
        A200001[column] = A200001[column].astype(str)

    data = {'productInfo':A200001.to_dict('records')[0],'selectorInfo':C200001.to_dict('records'),'attrInfo':B200001.to_dict('records')}
    
    if not LKO10001.empty:
        data = {'productNameInfo':LKO10001.to_dict('records')[0],'productInfo':A200001.to_dict('records')[0],'selectorInfo':C200001.to_dict('records'),'attrInfo':B200001.to_dict('records')}

    # 自定义JSON序列化处理函数
    def json_serial(obj):
        if isinstance(obj, (datetime, np.datetime64)):
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

    A200002 = json.loads(json.dumps(data, default=json_serial))

    return A200002



def putCategoryListDataSubProductId(hero:goods_put_post,productId : int,category_id : int,db: Session = Depends(get_session_insert_data)):

    print(hero)

    print(hero.productInfo)

    goodsProduct = db.exec(select(tb_goods_sku).where(tb_goods_sku.goods_id == productId)).first()

    if (goodsProduct == None):

    
        i = tb_goods_sku(goods_id=productId,spg_id=category_id,images=hero.productInfo['images'],gui_ge_param=hero.productInfo['gui_ge_param'])
        
        db.add(i)

    else:
        goodsProduct.images = hero.productInfo['images']
        goodsProduct.spg_id = category_id
        goodsProduct.gui_ge_param = hero.productInfo['gui_ge_param']
        db.add(goodsProduct)


    dfselectorInfo = pd.DataFrame(hero.selectorInfo)

    goodsSelector = db.exec(select(tb_goods_sh_goods_selector_value).where(tb_goods_sh_goods_selector_value.goods_id == productId)).all()


    for index, row in dfselectorInfo.iterrows():

        auto_id = row['auto_id']
        value = row['value']

        if value != '':

    
            goodsSelector2 = db.exec(select(tb_goods_sh_goods_selector_value).where(tb_goods_sh_goods_selector_value.goods_id == productId,tb_goods_sh_goods_selector_value.selector_id == auto_id)).first()

            if (goodsSelector2 == None):

                i = tb_goods_sh_goods_selector_value(selector_id=auto_id,goods_id=productId,selector_value=value)

                db.add(i)

            for i in goodsSelector:

                if i.selector_id == auto_id:

                    print(i)

                    i.selector_value = value

                    db.add(i)

                else:
                    goodsSelector2 = db.exec(select(tb_goods_sh_goods_selector_value).where(tb_goods_sh_goods_selector_value.goods_id == productId,tb_goods_sh_goods_selector_value.selector_id == auto_id)).first()

                    if (goodsSelector2 == None):

                        i = tb_goods_sh_goods_selector_value(selector_id=auto_id,goods_id=productId,selector_value=value)

                        db.add(i)

            




    dfattrInfo = pd.DataFrame(hero.attrInfo)

    goodsAttr = db.exec(select(tb_goods_sh_goods_attr_value).where(tb_goods_sh_goods_attr_value.goods_id == productId)).all()

    for index, row in dfattrInfo.iterrows():
    
        auto_id = row['auto_id']
        value = row['value']

        if value != '':


            goodsAttr2 = db.exec(select(tb_goods_sh_goods_attr_value).where(tb_goods_sh_goods_attr_value.goods_id == productId,tb_goods_sh_goods_attr_value.attr_id == auto_id)).first()

            if (goodsAttr2 == None):

                i = tb_goods_sh_goods_attr_value(attr_id=auto_id,goods_id=productId,attr_value=value)

                db.add(i)

            for i in goodsAttr:

                if i.attr_id == auto_id:

                    print(i)

                    i.attr_value = value

                    db.add(i)

                else:
                        goodsAttr2 = db.exec(select(tb_goods_sh_goods_attr_value).where(tb_goods_sh_goods_attr_value.goods_id == productId,tb_goods_sh_goods_attr_value.attr_id == auto_id)).first()

                        if (goodsAttr2 == None):

                            i = tb_goods_sh_goods_attr_value(attr_id=auto_id,goods_id=productId,attr_value=value)

                            db.add(i)











    db.commit()

    # print(dfattrInfo)

    # print(dfselectorInfo)

    # print("dfselectorInfo")

    # print(hero.selectorInfo)

    return "A200002"



# def pushGoodsDelivery(hero:delivery_post , db: Session = Depends(get_session_insert_data)):
    
#     statement = select(delivery_insert).where(and_(delivery_insert.auto_id == hero.auto_id,delivery_insert.data_sub.op('->>')('status_a') == 'wait_sucess'))

#     results = db.exec(statement).first()

#     if results == None:

#         return []

#     V10001 = dict(results.data_sub)

#     V10001['product_qty_a'] = hero.product_qty

#     results.jia_yi_fang_a = hero.jia_yi_fang
#     results.data_sub = V10001

#     db.add(results)
#     db.commit()
#     db.refresh(results)
    
#     return results


# def getgoodsfordeliveryget(fen:int,formline:int,db: Session = Depends(get_session_insert_data)):
    
#     engine = db.get_bind()
#     with engine.connect() as con:

#         A200001 = pd.read_sql(text(
#             " select * from delivery_insert where  jia_yi_fang_a > 0 and shu_riqi_datetime  > current_date and data_sub ->> 'fen_id_b' = '%s' and data_sub ->> 'from_line' = '%s' and data_sub ->> 'from_data' = 'input' and data_sub ->> 'status' = 'wait_sucess'    " % (fen,formline) ),con)
    
#     if A200001.empty:
#         return []

#     A200001 = pd.json_normalize(A200001.to_dict(orient="records"))
#     A200001.columns = A200001.columns.str.replace('data_sub.', 'data_sub_')
#     A200001 = A200001.replace(np.nan, 0)

#     Function = product_name_package()
#     C100001 = Function.select_product_list_id_with_http(nameStr='product',df=A200001[['product_id']])

#     BV10001 = A200001.merge(C100001, on=['product_id'], how='right')

#     BV10001 = BV10001.replace(np.nan, 0)

#     A200002 = json.loads(BV10001.to_json(orient='records'))
    
#     return A200002

# def pushGoodsDeliveryget(hero:delivery_post , db: Session = Depends(get_session_insert_data)):
    
#     statement = select(delivery_insert).where(and_(delivery_insert.auto_id == hero.auto_id,delivery_insert.data_sub.op('->>')('status') == 'wait_sucess'))

#     results = db.exec(statement).first()

#     if results == None:

#         return []

#     V10001 = dict(results.data_sub)

#     V10001['product_qty_b'] = hero.product_qty

#     results.product_qty = hero.product_qty
#     results.jia_yi_fang_b = hero.jia_yi_fang
#     results.data_sub = V10001

#     db.add(results)
#     db.commit()
#     db.refresh(results)
    
#     return results