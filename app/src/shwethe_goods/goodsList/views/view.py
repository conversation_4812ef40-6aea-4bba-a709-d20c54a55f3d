from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
# from src.config.insert_data.database import get_session as get_session_insert_data
from src.config.db_info.database import get_session as get_session_insert_data

from src.shwethe_goods.goodsList.crud.crud import (
    getGoodsListData,
    getCategoryListData,
    getCategoryListDataSub,
    getCategoryListDataSubProductId,
    putCategoryListDataSubProductId,
    getCategoryListDataSubArcoTree,
    getGoodsListDataNotInsert,
    getparmeForCategory,
    filterListByGroup,
    # getCategoryListDataSubProductBYId
)

from src.shwethe_goods.goodsList.models.models import (
    goods_put_post
)
# from src.shwethe_delivery.deliveryGoods.models.models import (
#     delivery_productgoods,
#     delete_delivery_productgoods,
#     delivery_insert_post
# )

router = APIRouter()

@router.get("/goodsList")
def create_a_hero(offset:int = 1,limit:int=20,category_id:int=0,parme:str = None,db: Session = Depends(get_session_insert_data)):
    
    return getGoodsListData(offset = offset,limit=limit,parme=parme,category_id=category_id,db=db)


@router.get("/filterListByGroup")
def create_a_hero(category_id:int=0,parme:str = None,db: Session = Depends(get_session_insert_data)):
    
    return filterListByGroup(parme=parme,category_id=category_id,db=db)


@router.get("/goodsList/category_id/{category_id}")
def create_a_hero(category_id:str,db: Session = Depends(get_session_insert_data)):
    
    return getparmeForCategory(category_id=category_id,db=db)


@router.get("/goodsList/notShow")
def create_a_hero(db: Session = Depends(get_session_insert_data)):
    
    return getGoodsListDataNotInsert(db=db)

# @router.get("/goodsList/{productId}")

@router.get("/goodsList/product")
def create_a_hero(category_id:int = 0,productId:int = 0,db: Session = Depends(get_session_insert_data)):
    
    return getCategoryListDataSubProductId(productId = productId,category_id=category_id,db=db)

# @router.get("/goodsList/productByID")
# def create_a_hero(productId:int = 0,db: Session = Depends(get_session_insert_data)):
    
#     return getCategoryListDataSubProductBYId(productId = productId,db=db)

@router.put("/goodsList/{productId}")
def create_a_hero(hero:goods_put_post,category_id:int = 0,productId:int = 1,db: Session = Depends(get_session_insert_data)):
    
    return putCategoryListDataSubProductId(hero=hero,productId = productId,category_id=category_id,db=db)

@router.get("/categoryList")
def create_a_hero(parent_id:int = 0,db: Session = Depends(get_session_insert_data)):
    
    return getCategoryListData(parent_id = parent_id,db=db)

@router.get("/categoryList/sub")
def create_a_hero(db: Session = Depends(get_session_insert_data)):
    
    return getCategoryListDataSub(db=db)

@router.get("/categoryList/sub/arco/selecttree")
def create_a_hero(db: Session = Depends(get_session_insert_data)):
    
    return getCategoryListDataSubArcoTree(db=db)



# @router.put("/deleverly/getgoodsfordeliveryinput/push")
# def create_a_hero(hero: delivery_post , db: Session = Depends(get_session_insert_data)):
    
#     return pushGoodsDelivery(hero=hero, db=db)

# @router.get("/deleverly/getgoodsfordeliveryinput/get")
# def create_a_hero(fen: int ,formline : int, db: Session = Depends(get_session_insert_data)):
    
#     return getgoodsfordeliveryget(fen=fen,formline=formline, db=db)


# @router.put("/deleverly/getgoodsfordeliveryinput/get")
# def create_a_hero(hero: delivery_post , db: Session = Depends(get_session_insert_data)):
    
#     return pushGoodsDeliveryget(hero=hero, db=db)