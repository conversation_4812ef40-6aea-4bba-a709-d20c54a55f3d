from sqlmodel import Session, SQLModel, create_engine
from sqlalchemy.orm import sessionmaker
from src.config.db_info.config import test_settings as settings

# connect_args = {"check_same_thread": False}
connect_args = {"options": "-c timezone=Asia/Yangon"}
engine = create_engine(settings.DATABASE_URI, echo=True,pool_pre_ping=True ,pool_size=3,
    max_overflow=5, connect_args=connect_args)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def create_db_and_tables():
    # SQLModel.metadata.create_all(engine)
    pass


def get_session():
    with Session(engine) as session:
        yield session
        
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()