from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from src.config.data.settings import Settings
from sqlmodel import SQLModel

# from core.models.data_db.data import jia_yi_fang,product

settings = Settings()

SQLALCHEMY_DATABASE_URI = settings.database_url

engine = create_engine(
    SQLALCHEMY_DATABASE_URI,
    pool_size=10,
    max_overflow=20,
    pool_recycle=1800,
    pool_pre_ping=True,
    connect_args={"options": "-c timezone=Asia/Yangon"}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
