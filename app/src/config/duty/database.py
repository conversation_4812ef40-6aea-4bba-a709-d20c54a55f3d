from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlmodel import SQLModel
from src.config.duty.settings import Settings



settings = Settings()

SQLALCHEMY_DATABASE_URI = settings.database_url

engine = create_engine(
    SQLALCHEMY_DATABASE_URI,
    pool_size=10,
    max_overflow=20,
    pool_recycle=1800,
    pool_pre_ping=True,
    connect_args={"options": "-c timezone=Asia/Yangon"}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# SQLModel.metadata.create_all(engine)
# SQLModel.metadata.create_all(engine, tables=[da_ka_tb.__table__])
