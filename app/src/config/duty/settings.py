from pydantic import BaseSettings
from src.config.connection import database_url

class Settings(BaseSettings):
    app_name: str = "User Service"
    debug: bool = False
    # database_url: str = "postgresql://shwethe:<EMAIL>:5432/employee_db"
    # database_url: str = "postgresql://shwethe:<EMAIL>:5432/employee_db"
    database_url: str = database_url + "/duty"


    class Config:
        env_prefix = "USER_"