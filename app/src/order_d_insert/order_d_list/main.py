from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time

router = APIRouter()

@router.get("/order_d_insert", status_code = 200)
def order_d_insert(fen_dian_id : int):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    def check_data(fen_dian_id):

        sql_insert = """SELECT order_s_insert_id,che_liang,datetime,order_s_insert_bill_id,data_sub  FROM 
        order_s_insert where order_s_insert_bill_id is not null  and  data_sub ->> 'fen_dian_id' = '%s'
         and  data_sub ->> 'status' = 'wait_sucess' and datetime > current_date -30 """ % (fen_dian_id)
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        GG100001 = pd.DataFrame(FF100001)
        GG100001['new_date_column'] = GG100001['datetime'].dt.date
        SD100001=GG100001.groupby(['new_date_column'])['order_s_insert_id'].max().reset_index()
        SD100001 = SD100001.sort_values(['order_s_insert_id'], ascending=[False]).head(2)
        FF100001 = GG100001[GG100001['new_date_column'].isin (SD100001['new_date_column'].to_list())]
        A100002 = FF100001.to_dict('records')
        A100003 = pd.json_normalize(A100002)
        A100004 = A100003.reindex(columns=['data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime'])
        A100005 = A100004.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
        psycopg2_conn_insert_data_s.close()
        return A100005

    # def check_data_2():
    #     sql_insert = """SELECT che_liang,datetime,order_s_insert_bill_id,data_sub  FROM order_s_insert where order_s_insert_bill_id is not null and  data_sub ->> 'status' = 'wait_sucess'  """ 
    #     FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)

    #     return FF100001
    
    GG100001 = check_data(fen_dian_id)
    # GG100001 = GG100001.groupby(['jin_huo_bian','che_liang','fen_dian_id','car_round'])['datetime'].min().reset_index()
    GG100001 = GG100001.groupby(['jin_huo_bian','che_liang','fen_dian_id','car_round'])['datetime'].min().reset_index()
    # GG100001 = pd.DataFrame(FF100001['data_detail'][0])
    # GG100001 = pd.DataFrame(FF100001)
    # GG100001['product_id'] = GG100001['product_id'].astype(int)
    H1000001 = GG100001[['che_liang']]
    H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
    # H3000001 = GG100001[['jia_fang']]
    # H3000001 = H3000001.rename(columns={'jia_fang': 'jia_yi_id'})

    res_1 = json.loads(H1000001.to_json(orient='records'))

    # res_2 = json.loads(H3000001.to_json(orient='records'))


    r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                     json={"data_api": res_1})

    # r2 = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
    #                  json={"data_api": res_2})

    # FF100002 = FF100001.to_json(orient='records')

    GF10001 = pd.DataFrame(json.loads(r.json()))

    # GF20001 = pd.DataFrame(json.loads(r2.json()))

    GG100009 = GF10001.merge(GG100001, left_on=['jia_yi_id'], right_on=['che_liang'], how='inner')
    GG100009 = GG100009.drop(columns=['che_liang'])
    GG100009

    # GG100010 = GF20001.merge(GG100009, left_on=['jia_yi_id'], right_on=['jia_fang'], how='inner')
    # GG100010 = GG100010.drop(columns=['jia_fang'])
    # GG100010
    GG100010 = GG100009.rename(columns={'jia_yi_id_x': 'parner_id','jia_yi_idname_x': 'parner_idname','jia_yi_mm_name_x': 'parner_mmname'
    ,'jia_yi_id': 'car_id','jia_yi_idname': 'car_idname','car_mm_name': 'parner_mmname'})
    # GG100010 = GG100009.rename(columns={'jia_yi_id_x': 'parner_id','jia_yi_idname_x': 'parner_idname','jia_yi_mm_name_x': 'parner_mmname'
    # ,'jia_yi_id_y': 'car_id','jia_yi_idname_y': 'car_idname','car_mm_name_y': 'parner_mmname'})
    # print(GG100010)
    GG100010['datetime'] = GG100010['datetime'].astype(str)
    GG100010 = GG100010.sort_values(['datetime'], ascending=[False])

    GG100010 = GG100010.to_json(orient='records')


    return GG100010
    
class che_liang_(BaseModel):
    car_id : int
    fen_dian_id : int
    jin_huo_bian : int


@router.post("/order_d_insert", status_code=200)
def order_d_insert(req_body : che_liang_ = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    import json
    ganarate_id = generate_datetime_selie()
    print(ganarate_id)
    datetime = str(generate_datetime())

    def check_data(order_s_d_insert_bill_id,jin_huo_bian):

        sql_insert = """
                    select * from order_s_d_insert where data_sub ->> 'fen_dian_id' = '%s' and data_sub ->> 'jin_huo_bian' = '%s'  and data_sub ->> 'status' = 'wait_sucess'
                    """ % (order_s_d_insert_bill_id,jin_huo_bian)
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)

        # HG000013 = pd.read_sql('select * from order_s_d_insert where order_s_insert_bill_id = '+str(order_s_insert_bill_id)+'  ',psycopg2_conn_insert_data_s)        
        
        return FF100001

    def insert_data(valuess2,datetime_,data_sub):
        sql_insert = """ insert into order_s_d_insert(car_id,datetime,data_sub) values (%s,%s,%s)  """
        cur.execute(sql_insert, (valuess2,datetime_,data_sub))
        psycopg2_conn_insert_data_s.close()


    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()
    GF100012 = check_data(str(req_body.fen_dian_id),str(req_body.jin_huo_bian))
    # print()
    if  GF100012.empty:
        insert_data(str(req_body.car_id), str(datetime),json.dumps({"status": "wait_sucess","fen_dian_id":req_body.fen_dian_id,"jin_huo_bian":req_body.jin_huo_bian}))


    return True


@router.get("/order_d_list_all", status_code = 200)
def order_d_list_all(type_user_jia_yi : int):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.https_connect import mongodb_data_api
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    

    A100001 = (''' select car_id,data_detail from order_s_d_insert where data_sub ->> 'status' = 'sucess' and data_detail @>  '[{"type_user_jia_yi": %s}]' ''' % (type_user_jia_yi) )
    A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)      
    A100002 = A100001.to_dict('records')
    A100003 = pd.json_normalize(A100002,'data_detail',['car_id'])
    A100003 = A100003.loc[A100003['type_user_jia_yi'] == type_user_jia_yi]



    def get_data_api(res_1,str):
        # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                     json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mmname'})
        # print(GF10001)
        return GF10001

    def get_data_api_jia_yi(res_1,str):
        # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                     json={"data_api": res_1})
        print(r)
        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'jia_yi_id': str+'_id','jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mmname'})
        print(GF10001)

        return GF10001

    if not A100003.empty:
        merge_data10001 = A100003[['product_id']]
        merge_data10003 = get_data_api(merge_data10001,"product")
        merge_data10004 = merge_data10003.merge(A100003, left_on=['product_id'],right_on=['product_id'], how='outer')
        merge_data10005 = merge_data10004.drop(columns=['product_id'])
    else:
        merge_data10005 = pd.DataFrame()

    if not merge_data10005.empty:
        type_user_jia_yi = merge_data10005[['car_id']]
        type_user_jia_yi_10001 = type_user_jia_yi.rename(columns={'car_id': 'jia_yi_id'})
        type_user_jia_yi_10002 = get_data_api_jia_yi(type_user_jia_yi_10001,"che_liang")
        type_user_jia_yi_10003 = type_user_jia_yi_10002.merge(merge_data10005, left_on=['che_liang_id'],right_on=['car_id'], how='outer')
        type_user_jia_yi_10004 = type_user_jia_yi_10003.drop(columns=['car_id'])
    else:

        type_user_jia_yi_10004 = pd.DataFrame()


    A100004 = type_user_jia_yi_10004.to_json(orient='records')
    psycopg2_conn_insert_data_s.close()
    return json.loads(A100004)
