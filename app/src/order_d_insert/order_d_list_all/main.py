from fastapi import FastAP<PERSON>, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time

router = APIRouter()



@router.get("/order_d_list_all", status_code = 200)
def order_s_insert_list(type_user_jia_yi : int):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    

    A100001 = (''' select car_id,data_detail where data_detail @> ANY (ARRAY ['{"jin_huo_bian": %s}'] ::jsonb[]); ''' % (type_user_jia_yi) )
    A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)        

    

    GG100010 = A100001.to_json(orient='records')

    psycopg2_conn_insert_data_s.close()
    return GG100010