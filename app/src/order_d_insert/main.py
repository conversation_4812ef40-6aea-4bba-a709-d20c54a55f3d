from fastapi import FastAP<PERSON>, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time

router = APIRouter()


# class che_liang_(BaseModel):
#     parner_id : int
#     car_id : int
#     order_s_d_insert_bill_id:str


# @router.post("/order_d_insert", status_code=200)
# def order_d_insert(req_body : che_liang_ = Body(...)):
#     import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
#     import importlib
#     importlib.reload(psycopg2_conn_insert_data_s)
#     from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
#     import json
#     ganarate_id = generate_datetime_selie()
#     print(ganarate_id)
#     datetime = str(generate_datetime())

#     def check_data(order_s_d_insert_bill_id):

#         sql_insert = """
#                     select * from order_s_d_insert where order_s_d_insert_bill_id = '%s'  and data_sub ->> 'status' = 'wait_sucess'
#                     """ % (order_s_d_insert_bill_id)
#         FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)

#         # HG000013 = pd.read_sql('select * from order_s_d_insert where order_s_insert_bill_id = '+str(order_s_insert_bill_id)+'  ',psycopg2_conn_insert_data_s)        
        
#         return FF100001

#     def insert_data(valuess,valuess2,datetime_,ganarate_id,data_sub):
#         sql_insert = """ insert into order_s_d_insert(parner_id,car_id,datetime,order_s_d_insert_bill_id,data_sub) values (%s,%s,%s,%s,%s)  """
#         cur.execute(sql_insert, (valuess,valuess2,datetime_,ganarate_id,data_sub))

#     psycopg2_conn_insert_data_s.autocommit = True
#     cur = psycopg2_conn_insert_data_s.cursor()
#     GF100012 = check_data(str(req_body.order_s_d_insert_bill_id))
#     # print()
#     if  GF100012.empty:
#         insert_data(str(req_body.parner_id),str(req_body.car_id), str(datetime),str(req_body.order_s_d_insert_bill_id),json.dumps({"status": "wait_sucess"}))

#     psycopg2_conn_insert_data_s.close()

#     return True





# @router.get("/order_d_insert", status_code = 200)
# def order_d_insert():
#     import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
#     import src.Connect.https_connect as https_connect
#     import importlib
#     importlib.reload(psycopg2_conn_insert_data_s)
#     importlib.reload(https_connect)
#     from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
#     from src.Connect.https_connect import mongodb_data_api
#     sql_insert = """SELECT che_liang,jia_fang,datetime,order_s_insert_bill_id  FROM order_s_insert where order_s_insert_bill_id is not null and  data_sub ->> 'status' = 'wait_sucess'  """ 
#     FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
#     FF100001
#     # GG100001 = pd.DataFrame(FF100001['data_detail'][0])
#     GG100001 = pd.DataFrame(FF100001)
#     # GG100001['product_id'] = GG100001['product_id'].astype(int)
#     H1000001 = GG100001[['che_liang']]
#     H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
#     H3000001 = GG100001[['jia_fang']]
#     H3000001 = H3000001.rename(columns={'jia_fang': 'jia_yi_id'})

#     res_1 = json.loads(H1000001.to_json(orient='records'))

#     res_2 = json.loads(H3000001.to_json(orient='records'))


#     r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
#                      json={"data_api": res_1})

#     r2 = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
#                      json={"data_api": res_2})


#     FF100002 = FF100001.to_json(orient='records')

#     GF10001 = pd.DataFrame(json.loads(r.json()))

#     GF20001 = pd.DataFrame(json.loads(r2.json()))


#     GG100009 = GF10001.merge(GG100001, left_on=['jia_yi_id'], right_on=['che_liang'], how='inner')
#     GG100009 = GG100009.drop(columns=['che_liang'])
#     GG100009

#     GG100010 = GF20001.merge(GG100009, left_on=['jia_yi_id'], right_on=['jia_fang'], how='inner')
#     GG100010 = GG100010.drop(columns=['jia_fang'])
#     GG100010
#     GG100010 = GG100010.rename(columns={'jia_yi_id_x': 'parner_id','jia_yi_idname_x': 'parner_idname','jia_yi_mm_name_x': 'parner_mmname'
#     ,'jia_yi_id_y': 'car_id','jia_yi_idname_y': 'car_idname','car_mm_name_y': 'parner_mmname'})
#     print(GG100010)
#     GG100010['datetime'] = GG100010['datetime'].astype(str)

#     GG100010 = GG100010.to_json(orient='records')

#     psycopg2_conn_insert_data_s.close()

#     return GG100010


# class order_d_insert_list_json(BaseModel):
#     product_id: int
#     product_qty: float
#     type_user_jia_yi : int



# class order_d_insert_list(BaseModel):
#     order_s_d_insert_bill_id : str
#     data_jsonb : order_d_insert_list_json


# @router.post("/order_d_insert_list", status_code=200)
# def order_d_insert_list(req_body : order_d_insert_list = Body(...)):
#     import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
#     import importlib
#     importlib.reload(psycopg2_conn_insert_data_s)
#     from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

#     datetime = str(generate_datetime())
#     import json

#     req_body_ = req_body.json()
#     FFF = json.loads(req_body_)

#     bill_id = FFF['order_s_d_insert_bill_id']
#     bill_id

#     product_id = FFF['data_jsonb']['product_id']
#     product_id

#     product_qty = FFF['data_jsonb']['product_qty']
#     product_qty




#     def update_data(product_id,bill_id,FFF):
#         sql_insert = """   WITH f AS
#                         (
#                         SELECT ('{'||index-1||'}')::text[] as path
#                         FROM order_s_d_insert 
#                         ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
#                         where contact ->> 'product_id' = '%s' 
#                         and order_s_d_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess' ) 

#                         UPDATE order_s_d_insert
#                         SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
#                         FROM f where order_s_d_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess'   ;

#                         """
#         cur.execute(sql_insert, (product_id, bill_id, FFF, bill_id))

#     def put_data(product_id,bill_id,FFF):

#         sql_insert = """
#                         UPDATE order_s_d_insert
#                         SET data_detail=data_detail  || %s ::jsonb
#                         where order_s_d_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess' ;
#                         """
#         cur.execute(sql_insert, ( FFF, bill_id))


#     sql_insert = """
#                     SELECT *
#                     FROM order_s_d_insert 
#                     ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
#                     where contact ->> 'product_id' = '%s'
#                     and order_s_d_insert_bill_id = '%s' and data_sub ->> 'status' = 'wait_sucess'
#                     """ % (product_id, bill_id)

#     psycopg2_conn_insert_data_s.autocommit = True
#     cur = psycopg2_conn_insert_data_s.cursor()

#     FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
#     FF100001

#     try:
#         FF100001.contact[0]['product_qty'] = float(FF100001.contact[0]['product_qty']) + float(product_qty)
#         print(FF100001.contact[0])
#     except:
#         pass

#     # def check_type_list(auto_id):
#     #     HG100001 = pd.read_sql('select lei_a,lei_b from lei_type_list where auto_id = '+ str(auto_id) +' ',psycopg2_conn_insert_data_s)
        
#     #     return HG100001

#     # T10001 = check_type_list(FFF['data_jsonb']['parner_id'])
#     # print(T10001)

#     # FFF['data_jsonb']['lei_a'] = T10001['lei_a'][0]
#     # FFF['data_jsonb']['lei_b'] = T10001['lei_b'][0]


#     import json
#     PPO10001 = pd.DataFrame([FFF['data_jsonb']])
#     T100009 = PPO10001.to_dict(orient='records')[0]
#     FFF

#     if not FF100001.empty:
#         print("1")
#         print(FF100001.contact[0])
#         FFF2 = json.dumps(FF100001.contact[0])
#         update_data(product_id,bill_id,FFF2)

#     else:
#         print("2")
#         PPO10001['r_id'] = generate_datetime_id()
#         T100003 = PPO10001.to_dict(orient='records')
#         FFF = json.dumps(T100003)
#         put_data(product_id, bill_id, FFF)

#     psycopg2_conn_insert_data_s.close()

#     return "sucess"


# @router.get("/order_d_insert_list", status_code=200)
# def order_d_insert_list(order_s_d_insert_bill_id : str):
#     import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
#     import src.Connect.https_connect as https_connect
#     import importlib
#     importlib.reload(psycopg2_conn_insert_data_s)
#     importlib.reload(https_connect)
#     from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
#     from src.Connect.https_connect import mongodb_data_api
#     sql_insert = """
#                         SELECT * 
#                         FROM order_s_d_insert  where order_s_d_insert_bill_id = '%s'
#                         """ % (order_s_d_insert_bill_id)
#     FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
#     FF100001
#     if not FF100001.empty:
#         GG100001 = pd.DataFrame(FF100001['data_detail'][0])
#     else:
#         GG100001 = pd.DataFrame()

#     if not GG100001.empty:
#         GG100001['product_id'] = GG100001['product_id'].astype(int)
#         H1000001 = GG100001[['product_id']]

#         res = json.loads(H1000001.to_json(orient='records'))
#         # print(res)
#         r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
#                         json={"data_api": res})
#         FF100002 = FF100001.to_json(orient='records')
#         # print(r.json())

#         GF10001 = pd.DataFrame(json.loads(r.json()))
#         print(GF10001)
#         print(GG100001)
#     else:
#         GF10001 = pd.DataFrame()
#     if not GF10001.empty:
#         GG100009 = GF10001.merge(GG100001, left_on=['product_id'], right_on=['product_id'], how='inner')
#         GG100009
#     else:
#         GG100009 = pd.DataFrame()

#     GG100010 = GG100009.to_json(orient='records')

#     psycopg2_conn_insert_data_s.close()

#     return GG100010



class order_d_insert_list_put_list(BaseModel):
    r_id : str
    product_id: int
    before_cheange_qty : float
    product_qty: float



class order_d_insert_list_put(BaseModel):
    order_s_d_insert_bill_id : str
    data_jsonb : order_d_insert_list_put_list

@router.put("/order_d_insert_list", status_code = 200)  
def order_d_insert_list(req_body : order_d_insert_list_put = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    datetime = str(generate_datetime())
    import json

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    order_s_d_insert_bill_id = FFF['order_s_d_insert_bill_id']
    order_s_d_insert_bill_id
    product_id = FFF['data_jsonb']['product_id']
    product_qty = FFF['data_jsonb']['product_qty']
    before_cheange_qty = FFF['data_jsonb']['before_cheange_qty']
    # product_price = FFF['data_jsonb']['product_price']
    r_id = FFF['data_jsonb']['r_id']




    def update_data(r_id,order_s_d_insert_bill_id,FFF,before_cheange_qty):
        sql_insert = """   WITH f AS
                        (
                        SELECT ('{'||index-1||'}')::text[] as path
                        FROM order_s_d_insert 
                        ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                        where contact ->> 'r_id' = %s and contact ->> 'product_qty' = '%s'
                        and order_s_d_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess' ) 

                        UPDATE order_s_d_insert
                        SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                        FROM f where order_s_d_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess'   ;

                        """
        cur.execute(sql_insert, (r_id, before_cheange_qty,order_s_d_insert_bill_id, FFF, order_s_d_insert_bill_id))


    def check_data(r_id,order_s_d_insert_bill_id):
        sql_insert = """
                    SELECT *
                    FROM order_s_d_insert 
                    ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                    where contact ->> 'r_id' = '%s'
                    and order_s_d_insert_bill_id = '%s' and data_sub ->> 'status' = 'wait_sucess'
                    """ % (r_id, order_s_d_insert_bill_id)

        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001

        return  FF100001

    

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    FF100001 = check_data(r_id,order_s_d_insert_bill_id)
    if not FF100001.empty:
        FF100001.contact[0]['product_qty'] = product_qty


        import json
        PPO10001 = pd.DataFrame([FFF['data_jsonb']])
        T100009 = PPO10001.to_dict(orient='records')[0]
        FFF

        # print(FF100001)
        if not FF100001.empty:
            print("1")
            print(FF100001.contact[0])
            FFF2 = json.dumps(FF100001.contact[0])
            update_data(r_id,order_s_d_insert_bill_id,FFF2,before_cheange_qty)

        else:
            print("2")
            PPO10001['r_id'] = generate_datetime_id()
            T100003 = PPO10001.to_dict(orient='records')
            FFF = json.dumps(T100003)
            put_data(product_id, bill_id, FFF)

        psycopg2_conn_insert_data_s.close()

        return "sucess"
    else:
        pass

        return  False




# class order_d_insert_list_delete_list(BaseModel):
#     r_id : str
#     product_id: int
#     before_delete_qty : float



# class order_d_insert_list_delete(BaseModel):
#     order_s_d_insert_bill_id : str
#     data_jsonb : order_d_insert_list_delete_list

# @router.delete("/order_d_insert_list", status_code = 200)  
# def order_d_insert_list(req_body : order_d_insert_list_delete = Body(...)):
#     import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
#     import importlib
#     importlib.reload(psycopg2_conn_insert_data_s)
#     from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

#     datetime = str(generate_datetime())
#     import json

#     req_body_ = req_body.json()
#     FFF = json.loads(req_body_)

#     order_s_d_insert_bill_id = FFF['order_s_d_insert_bill_id']
#     order_s_d_insert_bill_id
#     product_id = FFF['data_jsonb']['product_id']
#     before_delete_qty = FFF['data_jsonb']['before_delete_qty']
#     r_id = FFF['data_jsonb']['r_id']

#     def update_data(r_id,order_s_d_insert_bill_id,FFF,before_delete_qty):
#         sql_insert = """   WITH f AS
#                         (
#                         SELECT ('{'||index-1||'}')::text[] as path
#                         FROM order_s_d_insert 
#                         ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
#                         where contact ->> 'r_id' = %s and contact ->> 'product_qty' = '%s'
#                         and order_s_d_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess' ) 

#                         UPDATE order_s_d_insert
#                         SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
#                         FROM f where order_s_d_insert_bill_id = %s and data_sub ->> 'status' = 'wait_sucess'   ;

#                         """
#         cur.execute(sql_insert, (r_id,before_delete_qty,order_s_d_insert_bill_id, FFF, order_s_d_insert_bill_id))
#     def delete_data(product_id,order_s_d_insert_bill_id):
#         sql_insert = """   UPDATE order_s_d_insert set data_detail = t.js_new from (
#             select jsonb_agg ((data_detail ->> (idx-1)::int)::jsonb) as js_new
#             from order_s_d_insert cross join jsonb_array_elements(data_detail) with 
#             ordinality arr(j,idx) where  j ->> 'product_id' not in ('%s')  and order_s_d_insert_bill_id = %s
#         )t where order_s_d_insert_bill_id = %s;
#                         """
#         cur.execute(sql_insert, (product_id,order_s_d_insert_bill_id,order_s_d_insert_bill_id))


#     def check_data(r_id,order_s_d_insert_bill_id):
#         sql_insert = """
#                     SELECT *
#                     FROM order_s_d_insert 
#                     ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
#                     where contact ->> 'r_id' = '%s'
#                     and order_s_d_insert_bill_id = '%s' and data_sub ->> 'status' = 'wait_sucess'
#                     """ % (r_id, order_s_d_insert_bill_id)

#         FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
#         FF100001  

#         return  FF100001
    
#     def check_data_empty(order_s_d_insert_bill_id):
#         sql_insert = """
#                     SELECT data_detail
#                     FROM order_s_d_insert 
#                     where order_s_d_insert_bill_id = '%s' and data_sub ->> 'status' = 'wait_sucess'
#                     """ % (order_s_d_insert_bill_id)

#         FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
#         FF100001  

#         return  FF100001

#     def update_json(order_s_d_insert_bill_id):
#             sql_insert = """ UPDATE order_s_d_insert SET data_detail= '[]' ::jsonb WHERE order_s_d_insert_bill_id = %s ; """
#             cur.execute(sql_insert, (order_s_d_insert_bill_id,))


#     psycopg2_conn_insert_data_s.autocommit = True
#     cur = psycopg2_conn_insert_data_s.cursor()

#     FF100001 = check_data(r_id,order_s_d_insert_bill_id)
#     print(FF100001.contact)
    
#     try:
#         FF100001.contact[0]['product_id'] = 0 
#         FFF2 = json.dumps(FF100001.contact[0])
#         update_data(r_id,order_s_d_insert_bill_id,FFF2,before_delete_qty)
#     except:
#         pass
#     delete_data(0,order_s_d_insert_bill_id)

#     F10001 = check_data_empty(order_s_d_insert_bill_id)
#     print(F10001)
#     if not F10001.empty:
#         if F10001['data_detail'][0] == None:
#             print('yes')
#             update_json(order_s_d_insert_bill_id)
#         psycopg2_conn_insert_data_s.close()
#         return "sucess"

#     else:
#         psycopg2_conn_insert_data_s.close()
#         return False


