from fastapi import FastAP<PERSON>, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import time

router = APIRouter()

# class order_d_insert_list_json(BaseModel):
#     product_id: int
#     product_qty: float
#     type_user_jia_yi : int

# class order_d_insert_list(BaseModel):
#     jin_huo_bian : int
#     fen_dian_id : int
#     type_user_jia_yi : int
#     data_jsonb : order_d_insert_list_json



# @router.post("/order_d_insert_list", status_code=200)
# def order_d_insert_list(req_body : order_d_insert_list = Body(...)):
#     import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
#     import importlib
#     importlib.reload(psycopg2_conn_insert_data_s)
#     from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

#     datetime = str(generate_datetime())
#     import json

#     req_body_ = req_body.json()
#     FFF = json.loads(req_body_)

#     jin_huo_bian = FFF['jin_huo_bian']
#     jin_huo_bian

#     fen_dian_id = FFF['fen_dian_id']
#     fen_dian_id

#     type_user_jia_yi = FFF['type_user_jia_yi']
#     type_user_jia_yi

#     product_id = FFF['data_jsonb']['product_id']
#     product_id

#     product_qty = FFF['data_jsonb']['product_qty']
#     product_qty




#     def update_data(product_id,jin_huo_bian,FFF2,fen_dian_id,type_user_jia_yi):
#         sql_insert = """   WITH f AS
#                         (
#                         SELECT ('{'||index-1||'}')::text[] as path
#                         FROM order_s_d_insert 
#                         ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
#                         where contact ->> 'product_id' = '%s' and contact ->> 'type_user_jia_yi' = '%s' 
#                         and data_sub ->> 'fen_dian_id' = '%s' and data_sub ->> 'jin_huo_bian' = '%s' 
#                         and data_sub ->> 'status' = 'wait_sucess' ) 

#                         UPDATE order_s_d_insert
#                         SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
#                         FROM f where data_sub ->> 'fen_dian_id' = '%s' and 
#                         data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'status' = 'wait_sucess'   ;

#                         """
#         cur.execute(sql_insert, (product_id,type_user_jia_yi, fen_dian_id,jin_huo_bian, FFF2,fen_dian_id, jin_huo_bian))

#     def put_data(FFF,fen_dian_id,jin_huo_bian):

#         sql_insert = """
#                         UPDATE order_s_d_insert
#                         SET data_detail=data_detail  || %s ::jsonb
#                         where data_sub ->> 'fen_dian_id' = '%s' and data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'status' = 'wait_sucess' ;
#                         """
#         cur.execute(sql_insert, ( FFF, fen_dian_id,jin_huo_bian))


#     def check_data_list(product_id,fen_dian_id,jin_huo_bian,type_user_jia_yi):
#         sql_insert = """
#                         SELECT *
#                         FROM order_s_d_insert 
#                         ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
#                         where contact ->> 'product_id' = '%s' and contact ->> 'type_user_jia_yi' = '%s'
#                         and  data_sub ->> 'fen_dian_id' = '%s' and data_sub ->> 'jin_huo_bian' = '%s' and  data_sub ->> 'status' = 'wait_sucess'
#                         """ % (product_id,type_user_jia_yi, fen_dian_id,jin_huo_bian)

#         psycopg2_conn_insert_data_s.autocommit = True
#         cur = psycopg2_conn_insert_data_s.cursor()

#         FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        
#         return FF100001
#     psycopg2_conn_insert_data_s.autocommit = True
#     cur = psycopg2_conn_insert_data_s.cursor()


#     FF100001 = check_data_list(product_id,fen_dian_id,jin_huo_bian,type_user_jia_yi)

#     try:
#         FF100001.contact[0]['product_qty'] = float(FF100001.contact[0]['product_qty']) + float(product_qty)
#         print(FF100001.contact[0])
#     except:
#         pass

#     # def check_type_list(auto_id):
#     #     HG100001 = pd.read_sql('select lei_a,lei_b from lei_type_list where auto_id = '+ str(auto_id) +' ',psycopg2_conn_insert_data_s)
        
#     #     return HG100001

#     # T10001 = check_type_list(FFF['data_jsonb']['parner_id'])
#     # print(T10001)

#     # FFF['data_jsonb']['lei_a'] = T10001['lei_a'][0]
#     # FFF['data_jsonb']['lei_b'] = T10001['lei_b'][0]


#     import json
#     PPO10001 = pd.DataFrame([FFF['data_jsonb']])
#     T100009 = PPO10001.to_dict(orient='records')[0]
#     FFF

#     if not FF100001.empty:
#         print("1")
#         print(FF100001.contact[0])
#         FFF2 = json.dumps(FF100001.contact[0])
#         # update_data(product_id,bill_id,FFF2)
#         update_data(product_id,jin_huo_bian,FFF2,fen_dian_id,type_user_jia_yi)

#     else:
#         print("2")
#         PPO10001['r_id'] = generate_datetime_id()
#         T100003 = PPO10001.to_dict(orient='records')
#         FFF = json.dumps(T100003)
#         # put_data(product_id, bill_id, FFF)
#         put_data(FFF,fen_dian_id,jin_huo_bian)

#     psycopg2_conn_insert_data_s.close()

#     return "sucess"


class order_d_insert_list_json(BaseModel):
    product_id: int
    product_qty: float
    type_user_jia_yi : int

class order_d_insert_list(BaseModel):
    jin_huo_bian : int
    fen_dian_id : int
    type_user_jia_yi : int
    data_jsonb : order_d_insert_list_json



@router.post("/order_d_insert_list", status_code=200)
def order_d_insert_list(req_body : order_d_insert_list = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    datetime = str(generate_datetime())
    import json

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    jin_huo_bian = FFF['jin_huo_bian']
    jin_huo_bian

    fen_dian_id = FFF['fen_dian_id']
    fen_dian_id

    type_user_jia_yi = FFF['type_user_jia_yi']
    type_user_jia_yi

    product_id = FFF['data_jsonb']['product_id']
    product_id

    product_qty = FFF['data_jsonb']['product_qty']
    product_qty




    def update_data(product_id,jin_huo_bian,FFF2,fen_dian_id):
        sql_insert = """   WITH f AS
                        (
                        SELECT ('{'||index-1||'}')::text[] as path
                        FROM order_s_d_insert 
                        ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                        where contact ->> 'product_id' = '%s'  and data_sub ->> 'fen_dian_id' = '%s' and data_sub ->> 'jin_huo_bian' = '%s' 
                        and data_sub ->> 'status' = 'wait_sucess' ) 

                        UPDATE order_s_d_insert
                        SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                        FROM f where data_sub ->> 'fen_dian_id' = '%s' and 
                        data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'status' = 'wait_sucess'   ;

                        """
        cur.execute(sql_insert, (product_id, fen_dian_id,jin_huo_bian, FFF2,fen_dian_id, jin_huo_bian))

    def put_data(FFF,fen_dian_id,jin_huo_bian):

        sql_insert = """
                        UPDATE order_s_d_insert
                        SET data_detail=data_detail  || %s ::jsonb
                        where data_sub ->> 'fen_dian_id' = '%s' and data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'status' = 'wait_sucess' ;
                        """
        cur.execute(sql_insert, ( FFF, fen_dian_id,jin_huo_bian))


    def check_data_list(product_id,fen_dian_id,jin_huo_bian):
        sql_insert = """
                        SELECT *
                        FROM order_s_d_insert 
                        ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                        where contact ->> 'product_id' = '%s'  and  data_sub ->> 'fen_dian_id' = '%s' and data_sub ->> 'jin_huo_bian' = '%s' and  data_sub ->> 'status' = 'wait_sucess'
                        """ % (product_id, fen_dian_id,jin_huo_bian)

        psycopg2_conn_insert_data_s.autocommit = True
        cur = psycopg2_conn_insert_data_s.cursor()

        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        
        return FF100001
    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()


    FF100001 = check_data_list(product_id,fen_dian_id,jin_huo_bian)

    try:
        FF100001.contact[0]['product_qty'] = float(FF100001.contact[0]['product_qty']) + float(product_qty)
        print(FF100001.contact[0])
    except:
        pass

    # def check_type_list(auto_id):
    #     HG100001 = pd.read_sql('select lei_a,lei_b from lei_type_list where auto_id = '+ str(auto_id) +' ',psycopg2_conn_insert_data_s)
        
    #     return HG100001

    # T10001 = check_type_list(FFF['data_jsonb']['parner_id'])
    # print(T10001)

    # FFF['data_jsonb']['lei_a'] = T10001['lei_a'][0]
    # FFF['data_jsonb']['lei_b'] = T10001['lei_b'][0]


    import json
    PPO10001 = pd.DataFrame([FFF['data_jsonb']])
    T100009 = PPO10001.to_dict(orient='records')[0]
    FFF

    if not FF100001.empty:
        print("1")
        print(FF100001.contact[0])
        FFF2 = json.dumps(FF100001.contact[0])
        # update_data(product_id,bill_id,FFF2)
        update_data(product_id,jin_huo_bian,FFF2,fen_dian_id)

    else:
        print("2")
        PPO10001['r_id'] = generate_datetime_id()
        T100003 = PPO10001.to_dict(orient='records')
        FFF = json.dumps(T100003)
        # put_data(product_id, bill_id, FFF)
        put_data(FFF,fen_dian_id,jin_huo_bian)

    psycopg2_conn_insert_data_s.close()

    return "sucess"

@router.get("/order_d_insert_list", status_code=200)
def order_d_insert_list(jin_huo_bian : int,fen_dian_id : int,type_user_jia_yi : int):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as https_connect
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(https_connect)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api

    def check_data(jin_huo_bian,fen_dian_id):
        sql_insert = """
                        SELECT * 
                        FROM order_s_d_insert  where data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'
                        """ % (jin_huo_bian,fen_dian_id)
        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001

        return  FF100001

    FF100001 = check_data(jin_huo_bian,fen_dian_id)


    if not FF100001.empty:
        GG100001 = pd.DataFrame(FF100001['data_detail'][0])
        try:
            GG100001 = GG100001.loc[GG100001['type_user_jia_yi'] == type_user_jia_yi]
        except:
            GG100001 = pd.DataFrame()
    else:
        GG100001 = pd.DataFrame()

    if not GG100001.empty:
        GG100001['product_id'] = GG100001['product_id'].astype(int)
        H1000001 = GG100001[['product_id']]

        res = json.loads(H1000001.to_json(orient='records'))
        # print(res)
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res})
        FF100002 = FF100001.to_json(orient='records')
        # print(r.json())

        GF10001 = pd.DataFrame(json.loads(r.json()))
        print(GF10001)
        print(GG100001)
    else:
        GF10001 = pd.DataFrame()
    if not GF10001.empty:
        GG100009 = GF10001.merge(GG100001, left_on=['product_id'], right_on=['product_id'], how='inner')
        GG100009
    else:
        GG100009 = pd.DataFrame()

    GG100010 = GG100009.to_json(orient='records')

    psycopg2_conn_insert_data_s.close()

    return GG100010

class order_d_insert_list_put_list(BaseModel):
    r_id : str
    product_id: int
    before_cheange_qty : float
    product_qty: float



class order_d_insert_list_put(BaseModel):
    jin_huo_bian : int
    fen_dian_id : int
    data_jsonb : order_d_insert_list_put_list

@router.put("/order_d_insert_list", status_code = 200)  
def order_d_insert_list(req_body : order_d_insert_list_put = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    datetime = str(generate_datetime())
    import json

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    fen_dian_id = FFF['fen_dian_id']
    jin_huo_bian = FFF['jin_huo_bian']
    product_id = FFF['data_jsonb']['product_id']
    product_qty = FFF['data_jsonb']['product_qty']
    before_cheange_qty = FFF['data_jsonb']['before_cheange_qty']
    # product_price = FFF['data_jsonb']['product_price']
    r_id = FFF['data_jsonb']['r_id']




    def update_data(r_id,jin_huo_bian,FFF,before_delete_qty,fen_dian_id):
        sql_insert = """   WITH f AS
                        (
                        SELECT ('{'||index-1||'}')::text[] as path
                        FROM order_s_d_insert 
                        ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                        where contact ->> 'r_id' = %s and contact ->> 'product_qty' = '%s'
                        and data_sub ->> 'fen_dian_id' = '%s' and data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'status' = 'wait_sucess' ) 

                        UPDATE order_s_d_insert
                        SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                        FROM f where data_sub ->> 'fen_dian_id' = '%s' and  data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'status' = 'wait_sucess'   ;

                        """
        cur.execute(sql_insert, (r_id, before_cheange_qty,fen_dian_id,jin_huo_bian, FFF, fen_dian_id,jin_huo_bian))


    def check_data(r_id,jin_huo_bian,fen_dian_id):
        sql_insert = """
                    SELECT *
                    FROM order_s_d_insert 
                    ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                    where contact ->> 'r_id' = '%s'
                    and data_sub ->> 'fen_dian_id' = '%s' and data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'status' = 'wait_sucess'
                    """ % (r_id, fen_dian_id,jin_huo_bian)

        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001

        return  FF100001

    

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    FF100001 = check_data(r_id,jin_huo_bian,fen_dian_id)
    if not FF100001.empty:
        FF100001.contact[0]['product_qty'] = product_qty


        import json
        PPO10001 = pd.DataFrame([FFF['data_jsonb']])
        T100009 = PPO10001.to_dict(orient='records')[0]
        FFF

        # print(FF100001)
        if not FF100001.empty:
            print("1")
            print(FF100001.contact[0])
            FFF2 = json.dumps(FF100001.contact[0])
            update_data(r_id,jin_huo_bian,FFF2,before_cheange_qty,fen_dian_id)

        else:
            print("2")
            PPO10001['r_id'] = generate_datetime_id()
            T100003 = PPO10001.to_dict(orient='records')
            FFF = json.dumps(T100003)
            put_data(product_id, bill_id, FFF)

        psycopg2_conn_insert_data_s.close()

        return "sucess"
    else:
        pass

        return  False

class order_d_insert_list_delete_list(BaseModel):
    r_id : str
    product_id: int
    before_delete_qty : float

class order_d_insert_list_delete(BaseModel):
    jin_huo_bian : int
    fen_dian_id : int
    data_jsonb : order_d_insert_list_delete_list

@router.delete("/order_d_insert_list", status_code = 200)  
def order_d_insert_list(req_body : order_d_insert_list_delete = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    datetime = str(generate_datetime())
    import json

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    jin_huo_bian = FFF['jin_huo_bian']
    fen_dian_id = FFF['fen_dian_id']
    
    product_id = FFF['data_jsonb']['product_id']
    before_delete_qty = FFF['data_jsonb']['before_delete_qty']
    r_id = FFF['data_jsonb']['r_id']

    def update_data(r_id,FFF,before_delete_qty,jin_huo_bian,fen_dian_id):
        if before_delete_qty > 0:
            sql_insert = """   WITH f AS
                            (
                            SELECT ('{'||index-1||'}')::text[] as path
                            FROM order_s_d_insert 
                            ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                            where contact ->> 'r_id' = %s and contact ->> 'product_qty' = '%s'
                            and data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'
                            and data_sub ->> 'status' = 'wait_sucess' ) 

                            UPDATE order_s_d_insert
                            SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                            FROM f where data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'
                            and data_sub ->> 'status' = 'wait_sucess';

                            """
            cur.execute(sql_insert, (r_id,before_delete_qty,jin_huo_bian,fen_dian_id, FFF, jin_huo_bian,fen_dian_id))

        else:
            sql_insert = """   WITH f AS
                            (
                            SELECT ('{'||index-1||'}')::text[] as path
                            FROM order_s_d_insert 
                            ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                            where contact ->> 'r_id' = %s and contact ->> 'product_qty' = %s
                            and data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'
                            and data_sub ->> 'status' = 'wait_sucess' ) 

                            UPDATE order_s_d_insert
                            SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
                            FROM f where data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'
                            and data_sub ->> 'status' = 'wait_sucess';

                            """
            cur.execute(sql_insert, (r_id,before_delete_qty,jin_huo_bian,fen_dian_id, FFF, jin_huo_bian,fen_dian_id))

        # sql_insert = """   WITH f AS
        #                 (
        #                 SELECT ('{'||index-1||'}')::text[] as path
        #                 FROM order_s_d_insert 
        #                 ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
        #                 where contact ->> 'r_id' = %s 
        #                 and data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'
        #                 and data_sub ->> 'status' = 'wait_sucess' ) 

        #                 UPDATE order_s_d_insert
        #                 SET data_detail = JSONB_SET(data_detail,f.path,%s ::jsonb,false)
        #                 FROM f where data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'
        #                 and data_sub ->> 'status' = 'wait_sucess';

        #                 """


    def delete_data(product_id,jin_huo_bian,fen_dian_id):
        sql_insert = """   UPDATE order_s_d_insert set data_detail = t.js_new from (
            select jsonb_agg ((data_detail ->> (idx-1)::int)::jsonb) as js_new
            from order_s_d_insert cross join jsonb_array_elements(data_detail) with 
            ordinality arr(j,idx) where  j ->> 'product_id' not in ('%s') and  data_sub ->> 'jin_huo_bian' = '%s' 
            and data_sub ->> 'fen_dian_id' = '%s'
            )t where data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s';
                        """
        cur.execute(sql_insert, (product_id,jin_huo_bian,fen_dian_id,jin_huo_bian,fen_dian_id))


    def check_data(r_id,jin_huo_bian,fen_dian_id):
        sql_insert = """
                    SELECT *
                    FROM order_s_d_insert 
                    ,jsonb_array_elements(data_detail) with ordinality ass(contact, index)
                    where contact ->> 'r_id' = '%s'
                    and data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'
                     and data_sub ->> 'status' = 'wait_sucess'
                    """ % (r_id, jin_huo_bian,fen_dian_id)

        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001  

        return  FF100001
    
    def check_data_empty(jin_huo_bian,fen_dian_id):
        sql_insert = """
                    SELECT data_detail
                    FROM order_s_d_insert 
                    where data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'
                    and data_sub ->> 'status' = 'wait_sucess'
                    """ % (jin_huo_bian,fen_dian_id)

        FF100001 = pd.read_sql(sql_insert, psycopg2_conn_insert_data_s)
        FF100001

        return  FF100001

    def update_json(jin_huo_bian,fen_dian_id):
            sql_insert = """ UPDATE order_s_d_insert SET data_detail= '[]' ::jsonb WHERE 
            data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s' ; """
            cur.execute(sql_insert, (jin_huo_bian,fen_dian_id))


    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()

    FF100001 = check_data(r_id,jin_huo_bian,fen_dian_id)
    
    # try:

    FF100001.contact[0]['product_id'] = 0 
    FFF2 = json.dumps(FF100001.contact[0])
    update_data(r_id,FFF2,before_delete_qty,jin_huo_bian,fen_dian_id)

    # except:
        # pass

    delete_data(0,jin_huo_bian,fen_dian_id)
    F10001 = check_data_empty(jin_huo_bian,fen_dian_id)

    print(F10001)

    if not F10001.empty:
        if F10001['data_detail'][0] == None:
            print('yes')
            update_json(jin_huo_bian,fen_dian_id)
        psycopg2_conn_insert_data_s.close()

        return "sucess"

    else:
        psycopg2_conn_insert_data_s.close()

        return False