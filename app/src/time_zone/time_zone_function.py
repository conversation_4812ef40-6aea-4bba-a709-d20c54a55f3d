from src.time_zone.time_zone import timezone
import pytz
from datetime import datetime,timedelta

def my_timezone():
    tz = datetime.now(pytz.timezone(timezone))
    return tz

def get_datetime():
    tz = pytz.timezone(timezone)
    now = datetime.now()
    your_now = now.astimezone(tz)
    your_now

    return your_now


def get_date():
    tz = pytz.timezone(timezone)
    now = datetime.now()
    your_now = now.astimezone(tz).strftime("%Y-%m-%d")
    your_now

    return your_now


def get_date_delete_day(day):
    tz = pytz.timezone(timezone) 
    now = datetime.now() - timedelta(days=day)
    your_now = now.astimezone(tz).strftime("%Y-%m-%d")
    your_now

    return your_now

def get_date_delete_is_datetime(day):
    tz = pytz.timezone(timezone) 
    now = datetime.now() - timedelta(days=day)

    return now