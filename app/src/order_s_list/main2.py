
from fastapi import FastAP<PERSON>, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie,generate_datetime_selie_v1
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np

router = APIRouter()


def order_s_compline_product_left_empty(jin_huo_bian,fen_dian_id):

    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s


    A100001 = (''' select data_detail,order_s_insert_bill_id,data_sub from order_s_insert where data_sub ->> 'row_type' = 'for_product' and  data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'  ''' % (jin_huo_bian,fen_dian_id) )
    A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)

    A100002 = A100001.to_dict('records')
    A100003 = pd.json_normalize(A100002)

    A100004 = A100003.reindex(columns=['data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail','order_s_insert_bill_id','order_s_insert_id'])
    A100005 = A100004.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
    A100005 = A100005.to_dict('records')
    A100003 = pd.json_normalize(A100005,'data_detail',['jin_huo_bian','fen_dian_id','order_s_insert_id'])
    # print("A100003")
    # print(A100003)
    # print(A100003.info())
    A100004 = A100003.groupby(['jin_huo_bian','fen_dian_id','product_id'])['product_qty'].sum().reset_index()
    A100004 = A100004.rename(columns={'product_id': 'product_id_x'})

    # print("A100005")
    # print(A100004.info())
    # A100002 = A100001.to_dict('records')
    # A100003 = pd.json_normalize(A100002)
    # A100004 = A100003.reindex(columns=['data_sub.fen_dian_id','data_sub.car_round','order_s_insert_id','che_liang','data_detail','order_s_insert_bill_id'])
    # A100005 = A100004.rename(columns={'data_sub.car_round': 'car_round' ,'data_sub.fen_dian_id' : 'fen_dian_id' })
    # A100006 = A100003.to_dict('records')




    psycopg2_conn_insert_data_s.close()



    # try:
    #     # A100003 = pd.json_normalize(A100006,'data_detail',['order_s_insert_bill_id'])
    #     A100003 = pd.json_normalize(A100006,'data_detail',['jin_huo_bian','fen_dian_id'])
    # except:
    #     A100003 = pd.DataFrame()
    return A100004


def order_s_compline_product_right_empty(jin_huo_bian,fen_dian_id):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    A100001 = (''' select data_detail,data_sub from order_s_d_insert where data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'  ''' % (jin_huo_bian,fen_dian_id) )
    A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
    # print(A100001['data_sub'][0]['jin_huo_bian'])

    A100002 = A100001.to_dict('records')
    A100003 = pd.json_normalize(A100002)


    A100004 = A100003.reindex(columns=['data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail'])
    A100005 = A100004.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
    A100005 = A100005.to_dict('records')
    A100003 = pd.json_normalize(A100005,'data_detail',['jin_huo_bian','fen_dian_id'])



    A100004 = A100003.groupby(['jin_huo_bian','fen_dian_id','product_id'])['product_qty'].sum().reset_index()
    A100004 = A100004.rename(columns={'product_id': 'product_id_y'})

    # print("A100004")
    # print(A100004.info())
    # A100003 = pd.json_normalize(A100002)
    # A100004 = A100003.reindex(columns=['data_sub.fen_dian_id','data_sub.car_round','order_s_insert_id','che_liang','data_detail','order_s_insert_bill_id'])
    # A100005 = A100004.rename(columns={'data_sub.car_round': 'car_round' ,'data_sub.fen_dian_id' : 'fen_dian_id' })
    # A100006 = A100003.to_dict('records')
    psycopg2_conn_insert_data_s.close()
    # try:
    #     # A100003 = pd.json_normalize(A100002,'data_detail',['order_s_insert_bill_id'])
    #     A100003 = pd.json_normalize(A100006,'data_detail',['jin_huo_bian','fen_dian_id'])
    # except:
    #     A100003 = pd.DataFrame()

    return A100004

def functionorder_s_list(jin_huo_bian,fen_dian_id):
    from src.Connect.https_connect import mongodb_data_api
    # import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    # import importlib
    # importlib.reload(psycopg2_conn_insert_data_s)
    # from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    
    check_data_x = order_s_compline_product_left_empty(jin_huo_bian,fen_dian_id)
    # print("check_data_x")
    # print(check_data_x)
    # print(check_data_x)

    check_data_y = order_s_compline_product_right_empty(jin_huo_bian,fen_dian_id)
    # print("check_data_y")
    # print(check_data_y)
    # print(check_data_y)


    # def check_data_x(INT):
    #     A100001 = (''' select order_s_insert_id,che_liang,data_detail,order_s_insert_bill_id,data_sub from order_s_insert where order_s_insert_bill_id = '%s'  ''' % (INT) )
    #     A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
    #     A100002 = A100001.to_dict('records')
    #     A100003 = pd.json_normalize(A100002)
    #     A100004 = A100003.reindex(columns=['data_sub.fen_dian_id','data_sub.car_round','order_s_insert_id','che_liang','data_detail','order_s_insert_bill_id'])
    #     A100005 = A100004.rename(columns={'data_sub.car_round': 'car_round' ,'data_sub.fen_dian_id' : 'fen_dian_id' })
    #     A100006 = A100005.to_dict('records')
    #     try:
    #         A100003 = pd.json_normalize(A100006,'data_detail',['car_round','order_s_insert_id','che_liang','data_detail','order_s_insert_bill_id'])
    #     except:
    #         A100003 = pd.DataFrame(A100006)
    #     return A100003

    # def check_data_y(INT):

    #     A100001 = ('''   select order_s_d_insert_bill_id,data_detail from order_s_d_insert where order_s_d_insert_bill_id = '%s'  ''' % (INT) )
    #     A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
    #     A100002 = A100001.to_dict('records')
    #     A100003 = pd.json_normalize(A100002,'data_detail',['order_s_d_insert_bill_id'])

    #     return A100003

    def merge_data(check_data_x,check_data_y):
        CC100001 = check_data_x.merge(check_data_y,
                                left_on=['jin_huo_bian','fen_dian_id','product_id_x'],right_on = ['jin_huo_bian','fen_dian_id','product_id_y'], how='outer', indicator=True)
        CC100001 = CC100001[['jin_huo_bian','fen_dian_id','product_id_x','product_id_y','_merge','product_qty_x','product_qty_y']]
        CC100001 = CC100001.replace(np.nan, 0)
        return CC100001

    # print(check_data_x.empty)
    # print(check_data_y.empty)
    if not check_data_x.empty & check_data_y.empty:
        merge_data = merge_data(check_data_x,check_data_y)
        # print(merge_data)

    def get_data_api(res_1,str):
        # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        # print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                     json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'product_id': str,'product_idname': str+'_idname','product_mm_name': str+'_mmname'})
        # print(GF10001)

        return GF10001

    merge_data10001 = merge_data[['product_id_x']]
    merge_data10002 = merge_data10001.rename(columns={'product_id_x': 'product_id'})
    merge_data10003 = get_data_api(merge_data10002,"product_d_id")
    merge_data10004 = merge_data10003.merge(merge_data, left_on=['product_d_id'],right_on=['product_id_x'], how='outer')
    merge_data10005 = merge_data10004.drop(columns=['product_id_x'])



    merge_data20001 = merge_data10005[['product_id_y']]
    merge_data20002 = merge_data20001.rename(columns={'product_id_y': 'product_id'})
    merge_data20003 = get_data_api(merge_data20002,"product_id")
    merge_data20004 = merge_data20003.merge(merge_data10005, left_on=['product_id'],right_on=['product_id_y'], how='outer')
    merge_data20005 = merge_data20004.drop(columns=['product_id_y'])

    # merge_data10004 = merge_data10003.merge(merge_data, on=['product_id_x'], how='inner')
    # merge_data10005 = merge_data10004.drop(columns=['product_id_x'])

    # merge_data20003 = get_data_api(merge_data20002,"product")
    # print(merge_data20005)
    # check_data_y = check_data_y(INT)

    # check_data_x = check_data_x(INT)

    # print(check_data_x)

    # print(check_data_y)
    # if check_data_x.empty and check_data_y.empty:
    #     FF100001 = pd.DataFrame()
    # else:
    #     print(check_data_x)
    #     print(check_data_y)
    #     merge_data = merge_data(check_data_x,check_data_y)
        

    # # print(merge_data)
    # # print(merge_data.info())

    # psycopg2_conn_insert_data_s.close()

    # merge_data20005 = merge_data20005.to_json(orient='records')

    return merge_data20005

@router.get("/order_s_compline", status_code=200)
def order_s_compline_product():
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    
    def check_data_x():
        A100001 = (''' select order_s_insert_id,data_detail,order_s_insert_bill_id,data_sub,che_liang from order_s_insert where  data_sub ->> 'row_type' = 'for_product' and datetime > CURRENT_DATE -1 ''')
        A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
        A100002 = A100001.to_dict('records')
        A100003 = pd.json_normalize(A100002)
        A100004 = A100003.reindex(columns=['data_sub.status','data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail','order_s_insert_bill_id','order_s_insert_id'])
        A100005 = A100004.rename(columns={'data_sub.status': 'status' ,'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
        A100005 = A100005.to_dict('records')
        A100003 = pd.json_normalize(A100005,'data_detail',['status','jin_huo_bian','fen_dian_id','order_s_insert_id','order_s_insert_bill_id','che_liang'])
        A100003 = A100003.rename(columns={'product_id': 'product_id_x','product_qty': 'product_qty_x'})

        return A100003

    def check_data_sp():
        A100001 = (''' select order_s_insert_id,data_detail,order_s_insert_bill_id,data_sub,che_liang from order_s_insert where  data_sub ->> 'row_type' = 'for_car_cost' and datetime > CURRENT_DATE -1 ''')
        A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
        A100002 = A100001.to_dict('records')
        A100003 = pd.json_normalize(A100002)
        A100004 = A100003.reindex(columns=['data_sub.status','data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail','order_s_insert_bill_id','order_s_insert_id'])
        A100005 = A100004.rename(columns={'data_sub.status': 'status' ,'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
        A100005 = A100005.to_dict('records')
        A100003 = pd.json_normalize(A100005,'data_detail',['status','jin_huo_bian','fen_dian_id','order_s_insert_id','order_s_insert_bill_id','che_liang'])
        A100003 = A100003.rename(columns={'product_id': 'product_id_x','product_qty': 'product_qty_x'})

        return A100003


    def mer_data(cc1,cc2):
        BV100001 = cc2[['jin_huo_bian','fen_dian_id']]
        # print(cc1)
        # print(cc2[['jin_huo_bian','fen_dian_id']])
        X1001 = BV100001.merge(cc1, left_on=['jin_huo_bian', 'fen_dian_id'], right_on=['jin_huo_bian', 'fen_dian_id'], how='outer', indicator=True)
        print(X1001)
        print("cc1 cc2")

        return "AAAAA"

    def check_data_y():

        A100001 = ('''   select order_s_d_insert_bill_id,data_detail,data_sub,car_id from order_s_d_insert  where  datetime > CURRENT_DATE -1''')
        A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
        A100002 = A100001.to_dict('records')
        A100003 = pd.json_normalize(A100002)
        A100004 = A100003.reindex(columns=['data_sub.jin_huo_bian','car_id','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail'])
        A100005 = A100004.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round','car_id':'che_liang' })
        A100005 = A100005.to_dict('records')
        A100003 = pd.json_normalize(A100005,'data_detail',['jin_huo_bian','fen_dian_id','che_liang'])
        A100003 = A100003.rename(columns={'product_id': 'product_id_y','product_qty': 'product_qty_y'})

        return A100003

    def merge_data(check_data_x,check_data_y):
        # CC100001 = check_data_x.merge(check_data_y,
        #                         left_on=['jin_huo_bian','fen_dian_id'],right_on = ['jin_huo_bian','fen_dian_id'], how='outer', indicator=True)
        TY100001 = pd.concat([check_data_x,check_data_y])
        # print("TY100001")
        # print(TY100001['che_liang'])
        return TY100001

    def check_data_empty(data):
        data['product_id_x'] = data['product_id_x'].fillna(0)
        data['product_id_y'] = data['product_id_y'].fillna(0)
        data['order_s_insert_bill_id'] = data['order_s_insert_bill_id'].fillna(0)
        data['che_liang'] = data['che_liang'].fillna(0)
        data['product_qty_x'] = data['product_qty_x'].fillna(0)
        data['product_qty_y'] = data['product_qty_y'].fillna(0)
        # data['order_s_d_insert_bill_id'] = data['order_s_d_insert_bill_id'].fillna(0)
        data10001 = data[['product_id_x','product_id_y','jin_huo_bian','fen_dian_id','che_liang','status']].drop_duplicates()
        # print("data10001")
        # print(data10001)


        data10001['product_id_x'] = data10001['product_id_x'].astype(int)
        data10001['product_id_y'] = data10001['product_id_y'].astype(int)
        # data10001['qty_ans'] = (data10001['product_qty_x'] - data10001['product_qty_y'])

        # print("data10001")
        # print(data10001)

        # data['order_s_d_insert_bill_id'] = data['order_s_d_insert_bill_id'].astype(int)
        # val = data._get_numeric_data()
        data10001['product_id_x'][data10001['product_id_x'] >= 1 ] = 1
        data10001['product_id_y'][data10001['product_id_y'] >= 1 ] = 1

        # print(data10001)
        x_df=data10001.groupby(['jin_huo_bian','fen_dian_id','che_liang','status'])['product_id_x'].sum().reset_index()
        y_df=data10001.groupby(['jin_huo_bian','fen_dian_id','che_liang'])['product_id_y'].sum().reset_index()
        # y_df = y_df.rename(columns={'order_s_d_insert_bill_id': 'order_s_insert_bill_id'})

        CC100001 = x_df.merge(y_df,on=['jin_huo_bian','fen_dian_id','che_liang'], how='outer', indicator=True)
        
        CC100001 = CC100001.replace(np.nan, 0)
        # CC100002 = CC100001
        CC100002 = CC100001.loc[CC100001['che_liang'] != 0.0]

        CC100002['ANS'] = (CC100002['product_id_x'] - CC100002['product_id_y'])
        CC100002 = CC100002.drop_duplicates()
        CC100003 = CC100002.drop(columns=['_merge'])
        # CC100003 = CC100003.head(1)
        return  CC100003
    check_data_y = check_data_y()
    check_data_x = check_data_x()
    merge_data = merge_data(check_data_x,check_data_y)

    merge_data100001 = check_data_empty(merge_data)
    # print("merge_data100001")
    # print(merge_data100001['status'])

    GF10001123213 = check_data_sp()
    mer_data(GF10001123213,merge_data100001)

    
    merge_dat400001 = merge_data100001[['jin_huo_bian','fen_dian_id']].to_dict('records')  
    tr100001 = []
    for i in merge_dat400001:
        s1 = i
        s1 = json.dumps(i)
        DF100001 = '{}'.format(s1)
        # print(DF100001)
        tr100001.append(DF100001)

    # print("merge_data100001")
    # print(merge_data100001)
    GD10001 = merge_data100001








    def get_data_api(res_1,str):
        # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        # print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                     json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'jia_yi_id': str,'jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mmname'})
        # print(GF10001)

        return GF10001

    

    H3000001 = GD10001[['che_liang']]
    H3000001 = H3000001.rename(columns={'che_liang': 'jia_yi_id'})
    # print(H3000001)
    H3000001 = get_data_api(H3000001,"che_liang")
    GG100009 = H3000001.merge(GD10001, on=['che_liang'], how='inner')
    GG100009 = GG100009.drop(columns=['che_liang'])
    GG100060 = GG100009
    HG10001 = pd.DataFrame()
    for index, row in GG100060.iterrows():
        jin_huo_bian__ = int(row['jin_huo_bian'])
        fen_dian_id = int(row['fen_dian_id'])
        # print("jin_huo_bian__")
        # print(jin_huo_bian__,fen_dian_id)
        try:
            FD100001 = functionorder_s_list(jin_huo_bian__,fen_dian_id)
            FD100001['jin_huo_bian'] = jin_huo_bian__
            FD100001['fen_dian_id'] = fen_dian_id
            HG10001 = HG10001.append(FD100001)
        except:
            pass

    # print("HG10001")
    # print(HG10001)
    # HG10001['product_qty_x'] = HG10001['product_qty_x'].astype(float)
    # HG10001['product_qty_x'] = HG10001['product_qty_x'].astype(float)
    HG10001['qty_ans'] = (HG10001['product_qty_x'] - HG10001['product_qty_y'])
    # print(HG10001[['product_qty_x','product_qty_y','jin_huo_bian','fen_dian_id','qty_ans']])
    HG10001 = HG10001[['jin_huo_bian','fen_dian_id','qty_ans']]

    HG10001=HG10001.groupby(['jin_huo_bian','fen_dian_id'])['qty_ans'].sum().reset_index()

    # H3000001 = GG100009[['jia_fang']]
    # H3000001 = H3000001.rename(columns={'jia_fang': 'jia_yi_id'})
    # H3000001 = get_data_api(H3000001,"jia_fang")
    # GG100009 = H3000001.merge(GG100009, on=['jia_fang'], how='inner')
    # GG100060 = GG100009.drop(columns=['jia_fang'])
    # GG100060 = GG100060
    # print(GG100060)
    GG100060 = GG100060.merge(HG10001,on=['jin_huo_bian','fen_dian_id'], how='outer', indicator=True)
    # GG100060['product_id_ans'] = (GG100060['product_id_y'] - GG100060['product_id_x'])
    GG100060['qty_ans'] = GG100060['qty_ans'].fillna(-999999)
    GG100060['product_id_y_'] = GG100060['product_id_y']
    GG100060['product_id_y_'] = GG100060['product_id_y_'].astype(float)



    GD10002 = GG100060.to_json(orient='records')


    psycopg2_conn_insert_data_s.close()

    return GD10002