from fastapi import FastAP<PERSON>, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie,generate_datetime_selie_v1
from typing import List, Optional
import json
import requests
import pandas as pd
import time
import numpy as np

router = APIRouter()



def order_s_compline_product_left_empty(jin_huo_bian,fen_dian_id):

    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s


    A100001 = (''' select data_detail,order_s_insert_bill_id,data_sub from order_s_insert where data_sub ->> 'row_type' = 'for_product' and  data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'  ''' % (jin_huo_bian,fen_dian_id) )
    A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)

    A100002 = A100001.to_dict('records')
    A100003 = pd.json_normalize(A100002)

    A100004 = A100003.reindex(columns=['data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail','order_s_insert_bill_id','order_s_insert_id'])
    A100005 = A100004.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
    A100005 = A100005.to_dict('records')
    A100003 = pd.json_normalize(A100005,'data_detail',['jin_huo_bian','fen_dian_id','order_s_insert_id'])
    print("A100003")
    print(A100003)
    print(A100003.info())
    A100004 = A100003.groupby(['jin_huo_bian','fen_dian_id','product_id'])['product_qty'].sum().reset_index()
    A100004 = A100004.rename(columns={'product_id': 'product_id_x'})

    print("A100005")
    print(A100004.info())
    # A100002 = A100001.to_dict('records')
    # A100003 = pd.json_normalize(A100002)
    # A100004 = A100003.reindex(columns=['data_sub.fen_dian_id','data_sub.car_round','order_s_insert_id','che_liang','data_detail','order_s_insert_bill_id'])
    # A100005 = A100004.rename(columns={'data_sub.car_round': 'car_round' ,'data_sub.fen_dian_id' : 'fen_dian_id' })
    # A100006 = A100003.to_dict('records')




    psycopg2_conn_insert_data_s.close()



    # try:
    #     # A100003 = pd.json_normalize(A100006,'data_detail',['order_s_insert_bill_id'])
    #     A100003 = pd.json_normalize(A100006,'data_detail',['jin_huo_bian','fen_dian_id'])
    # except:
    #     A100003 = pd.DataFrame()
    return A100004

def order_s_compline_product_left_empty_v1(jin_huo_bian,fen_dian_id):
    
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s


    A100001 = (''' select data_detail,order_s_insert_bill_id,data_sub from order_s_insert where data_sub ->> 'row_type' = 'for_product' and  data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'  ''' % (jin_huo_bian,fen_dian_id) )
    A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
    A100004 = A100001.reindex(columns=['data_sub.status','data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail','order_s_insert_bill_id','order_s_insert_id'])
    A100005 = A100004.rename(columns={'data_sub.status': 'status' ,'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
    A100005 = A100005.to_dict('records')
    A100003 = pd.json_normalize(A100005,'data_detail',['status','jin_huo_bian','fen_dian_id','order_s_insert_id','order_s_insert_bill_id'])
    A100003 = A100003.rename(columns={'product_id': 'product_id_x'})

    # A100002 = A100001.to_dict('records')
    # A100003 = pd.json_normalize(A100002)
    # A100004 = A100003.reindex(columns=['data_sub.fen_dian_id','data_sub.car_round','order_s_insert_id','che_liang','data_detail','order_s_insert_bill_id'])
    # A100005 = A100004.rename(columns={'data_sub.car_round': 'car_round' ,'data_sub.fen_dian_id' : 'fen_dian_id' })
    # A100006 = A100003.to_dict('records')




    psycopg2_conn_insert_data_s.close()



    # try:
    #     # A100003 = pd.json_normalize(A100006,'data_detail',['order_s_insert_bill_id'])
    #     A100003 = pd.json_normalize(A100006,'data_detail',['jin_huo_bian','fen_dian_id'])
    # except:
    #     A100003 = pd.DataFrame()
    return A100003

def order_s_compline_product_right_empty(jin_huo_bian,fen_dian_id):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s

    A100001 = (''' select data_detail,data_sub from order_s_d_insert where data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s'  ''' % (jin_huo_bian,fen_dian_id) )
    A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
    # print(A100001['data_sub'][0]['jin_huo_bian'])

    A100002 = A100001.to_dict('records')
    A100003 = pd.json_normalize(A100002)


    A100004 = A100003.reindex(columns=['data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail'])
    A100005 = A100004.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
    A100005 = A100005.to_dict('records')
    A100003 = pd.json_normalize(A100005,'data_detail',['jin_huo_bian','fen_dian_id'])



    A100004 = A100003.groupby(['jin_huo_bian','fen_dian_id','product_id'])['product_qty'].sum().reset_index()
    A100004 = A100004.rename(columns={'product_id': 'product_id_y'})

    print("A100004")
    print(A100004.info())
    # A100003 = pd.json_normalize(A100002)
    # A100004 = A100003.reindex(columns=['data_sub.fen_dian_id','data_sub.car_round','order_s_insert_id','che_liang','data_detail','order_s_insert_bill_id'])
    # A100005 = A100004.rename(columns={'data_sub.car_round': 'car_round' ,'data_sub.fen_dian_id' : 'fen_dian_id' })
    # A100006 = A100003.to_dict('records')
    psycopg2_conn_insert_data_s.close()
    # try:
    #     # A100003 = pd.json_normalize(A100002,'data_detail',['order_s_insert_bill_id'])
    #     A100003 = pd.json_normalize(A100006,'data_detail',['jin_huo_bian','fen_dian_id'])
    # except:
    #     A100003 = pd.DataFrame()

    return A100004

@router.get("/order_s_compline_product", status_code=200)
def order_s_list(jin_huo_bian: int ,fen_dian_id: int , left: int = 10, right: int = 10):
    from src.Connect.https_connect import mongodb_data_api
    # import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    # import importlib
    # importlib.reload(psycopg2_conn_insert_data_s)
    # from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    
    check_data_x = order_s_compline_product_left_empty(jin_huo_bian,fen_dian_id)
    print("check_data_x")
    print(check_data_x)
    # print(check_data_x)

    check_data_y = order_s_compline_product_right_empty(jin_huo_bian,fen_dian_id)
    print("check_data_y")
    print(check_data_y)
    # print(check_data_y)


    # def check_data_x(INT):
    #     A100001 = (''' select order_s_insert_id,che_liang,data_detail,order_s_insert_bill_id,data_sub from order_s_insert where order_s_insert_bill_id = '%s'  ''' % (INT) )
    #     A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
    #     A100002 = A100001.to_dict('records')
    #     A100003 = pd.json_normalize(A100002)
    #     A100004 = A100003.reindex(columns=['data_sub.fen_dian_id','data_sub.car_round','order_s_insert_id','che_liang','data_detail','order_s_insert_bill_id'])
    #     A100005 = A100004.rename(columns={'data_sub.car_round': 'car_round' ,'data_sub.fen_dian_id' : 'fen_dian_id' })
    #     A100006 = A100005.to_dict('records')
    #     try:
    #         A100003 = pd.json_normalize(A100006,'data_detail',['car_round','order_s_insert_id','che_liang','data_detail','order_s_insert_bill_id'])
    #     except:
    #         A100003 = pd.DataFrame(A100006)
    #     return A100003

    # def check_data_y(INT):

    #     A100001 = ('''   select order_s_d_insert_bill_id,data_detail from order_s_d_insert where order_s_d_insert_bill_id = '%s'  ''' % (INT) )
    #     A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
    #     A100002 = A100001.to_dict('records')
    #     A100003 = pd.json_normalize(A100002,'data_detail',['order_s_d_insert_bill_id'])

    #     return A100003

    def merge_data(check_data_x,check_data_y):
        CC100001 = check_data_x.merge(check_data_y,
                                left_on=['jin_huo_bian','fen_dian_id','product_id_x'],right_on = ['jin_huo_bian','fen_dian_id','product_id_y'], how='outer', indicator=True)
        CC100001 = CC100001[['jin_huo_bian','fen_dian_id','product_id_x','product_id_y','_merge','product_qty_x','product_qty_y']]
        CC100001 = CC100001.replace(np.nan, 0)
        return CC100001

    print(check_data_x.empty)
    print(check_data_y.empty)
    if not check_data_x.empty & check_data_y.empty:
        merge_data = merge_data(check_data_x,check_data_y)
        print(merge_data)

    def get_data_api(res_1,str):
        # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                     json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'product_id': str,'product_idname': str+'_idname','product_mm_name': str+'_mmname'})
        # print(GF10001)

        return GF10001

    merge_data10001 = merge_data[['product_id_x']]
    merge_data10002 = merge_data10001.rename(columns={'product_id_x': 'product_id'})
    merge_data10003 = get_data_api(merge_data10002,"product_d_id")
    merge_data10004 = merge_data10003.merge(merge_data, left_on=['product_d_id'],right_on=['product_id_x'], how='outer')
    merge_data10005 = merge_data10004.drop(columns=['product_id_x'])



    merge_data20001 = merge_data10005[['product_id_y']]
    merge_data20002 = merge_data20001.rename(columns={'product_id_y': 'product_id'})
    merge_data20003 = get_data_api(merge_data20002,"product_id")
    merge_data20004 = merge_data20003.merge(merge_data10005, left_on=['product_id'],right_on=['product_id_y'], how='outer')
    merge_data20005 = merge_data20004.drop(columns=['product_id_y'])

    # merge_data10004 = merge_data10003.merge(merge_data, on=['product_id_x'], how='inner')
    # merge_data10005 = merge_data10004.drop(columns=['product_id_x'])

    # merge_data20003 = get_data_api(merge_data20002,"product")
    print(merge_data20005)
    # check_data_y = check_data_y(INT)

    # check_data_x = check_data_x(INT)

    # print(check_data_x)

    # print(check_data_y)
    # if check_data_x.empty and check_data_y.empty:
    #     FF100001 = pd.DataFrame()
    # else:
    #     print(check_data_x)
    #     print(check_data_y)
    #     merge_data = merge_data(check_data_x,check_data_y)
        

    # # print(merge_data)
    # # print(merge_data.info())

    # psycopg2_conn_insert_data_s.close()

    merge_data20005 = merge_data20005.to_json(orient='records')

    return json.loads(merge_data20005)








def functionorder_s_list(jin_huo_bian,fen_dian_id):
    from src.Connect.https_connect import mongodb_data_api
    # import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    # import importlib
    # importlib.reload(psycopg2_conn_insert_data_s)
    # from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    
    check_data_x = order_s_compline_product_left_empty(jin_huo_bian,fen_dian_id)
    print("check_data_x")
    print(check_data_x)
    # print(check_data_x)

    check_data_y = order_s_compline_product_right_empty(jin_huo_bian,fen_dian_id)
    print("check_data_y")
    print(check_data_y)
    # print(check_data_y)


    # def check_data_x(INT):
    #     A100001 = (''' select order_s_insert_id,che_liang,data_detail,order_s_insert_bill_id,data_sub from order_s_insert where order_s_insert_bill_id = '%s'  ''' % (INT) )
    #     A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
    #     A100002 = A100001.to_dict('records')
    #     A100003 = pd.json_normalize(A100002)
    #     A100004 = A100003.reindex(columns=['data_sub.fen_dian_id','data_sub.car_round','order_s_insert_id','che_liang','data_detail','order_s_insert_bill_id'])
    #     A100005 = A100004.rename(columns={'data_sub.car_round': 'car_round' ,'data_sub.fen_dian_id' : 'fen_dian_id' })
    #     A100006 = A100005.to_dict('records')
    #     try:
    #         A100003 = pd.json_normalize(A100006,'data_detail',['car_round','order_s_insert_id','che_liang','data_detail','order_s_insert_bill_id'])
    #     except:
    #         A100003 = pd.DataFrame(A100006)
    #     return A100003

    # def check_data_y(INT):

    #     A100001 = ('''   select order_s_d_insert_bill_id,data_detail from order_s_d_insert where order_s_d_insert_bill_id = '%s'  ''' % (INT) )
    #     A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
    #     A100002 = A100001.to_dict('records')
    #     A100003 = pd.json_normalize(A100002,'data_detail',['order_s_d_insert_bill_id'])

    #     return A100003

    def merge_data(check_data_x,check_data_y):
        CC100001 = check_data_x.merge(check_data_y,
                                left_on=['jin_huo_bian','fen_dian_id','product_id_x'],right_on = ['jin_huo_bian','fen_dian_id','product_id_y'], how='outer', indicator=True)
        CC100001 = CC100001[['jin_huo_bian','fen_dian_id','product_id_x','product_id_y','_merge','product_qty_x','product_qty_y']]
        CC100001 = CC100001.replace(np.nan, 0)
        return CC100001

    print(check_data_x.empty)
    print(check_data_y.empty)
    if not check_data_x.empty & check_data_y.empty:
        merge_data = merge_data(check_data_x,check_data_y)
        print(merge_data)

    def get_data_api(res_1,str):
        # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                     json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'product_id': str,'product_idname': str+'_idname','product_mm_name': str+'_mmname'})
        # print(GF10001)

        return GF10001

    merge_data10001 = merge_data[['product_id_x']]
    merge_data10002 = merge_data10001.rename(columns={'product_id_x': 'product_id'})
    merge_data10003 = get_data_api(merge_data10002,"product_d_id")
    merge_data10004 = merge_data10003.merge(merge_data, left_on=['product_d_id'],right_on=['product_id_x'], how='outer')
    merge_data10005 = merge_data10004.drop(columns=['product_id_x'])



    merge_data20001 = merge_data10005[['product_id_y']]
    merge_data20002 = merge_data20001.rename(columns={'product_id_y': 'product_id'})
    merge_data20003 = get_data_api(merge_data20002,"product_id")
    merge_data20004 = merge_data20003.merge(merge_data10005, left_on=['product_id'],right_on=['product_id_y'], how='outer')
    merge_data20005 = merge_data20004.drop(columns=['product_id_y'])

    # merge_data10004 = merge_data10003.merge(merge_data, on=['product_id_x'], how='inner')
    # merge_data10005 = merge_data10004.drop(columns=['product_id_x'])

    # merge_data20003 = get_data_api(merge_data20002,"product")
    print(merge_data20005)
    # check_data_y = check_data_y(INT)

    # check_data_x = check_data_x(INT)

    # print(check_data_x)

    # print(check_data_y)
    # if check_data_x.empty and check_data_y.empty:
    #     FF100001 = pd.DataFrame()
    # else:
    #     print(check_data_x)
    #     print(check_data_y)
    #     merge_data = merge_data(check_data_x,check_data_y)
        

    # # print(merge_data)
    # # print(merge_data.info())

    # psycopg2_conn_insert_data_s.close()

    # merge_data20005 = merge_data20005.to_json(orient='records')

    return merge_data20005



@router.get("/order_s_compline", status_code=200)
def order_s_compline_product():
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    
    def check_data_x():
        A100001 = (''' select order_s_insert_id,data_detail,order_s_insert_bill_id,data_sub,che_liang from order_s_insert where  data_sub ->> 'row_type' = 'for_product' and datetime > CURRENT_DATE -2 ''')
        A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
        A100002 = A100001.to_dict('records')
        A100003 = pd.json_normalize(A100002)
        A100004 = A100003.reindex(columns=['data_sub.status','data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail','order_s_insert_bill_id','order_s_insert_id'])
        A100005 = A100004.rename(columns={'data_sub.status': 'status' ,'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
        A100005 = A100005.to_dict('records')
        A100003 = pd.json_normalize(A100005,'data_detail',['status','jin_huo_bian','fen_dian_id','order_s_insert_id','order_s_insert_bill_id','che_liang'])
        A100003 = A100003.rename(columns={'product_id': 'product_id_x','product_qty': 'product_qty_x'})
        return A100003

    def check_data_y():

        A100001 = ('''   select order_s_d_insert_bill_id,data_detail,data_sub,car_id from order_s_d_insert  where  datetime > CURRENT_DATE -2''')
        A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
        A100002 = A100001.to_dict('records')
        A100003 = pd.json_normalize(A100002)
        A100004 = A100003.reindex(columns=['data_sub.jin_huo_bian','car_id','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail'])
        A100005 = A100004.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round','car_id':'che_liang' })
        A100005 = A100005.to_dict('records')
        A100003 = pd.json_normalize(A100005,'data_detail',['jin_huo_bian','fen_dian_id','che_liang'])
        A100003 = A100003.rename(columns={'product_id': 'product_id_y','product_qty': 'product_qty_y'})

        return A100003

    def merge_data(check_data_x,check_data_y):
        # CC100001 = check_data_x.merge(check_data_y,
        #                         left_on=['jin_huo_bian','fen_dian_id'],right_on = ['jin_huo_bian','fen_dian_id'], how='outer', indicator=True)
        TY100001 = pd.concat([check_data_x,check_data_y])
        print("TY100001")
        print(TY100001['che_liang'])
        return TY100001

    def check_data_empty(data):
        data['product_id_x'] = data['product_id_x'].fillna(0)
        data['product_id_y'] = data['product_id_y'].fillna(0)
        data['order_s_insert_bill_id'] = data['order_s_insert_bill_id'].fillna(0)
        data['che_liang'] = data['che_liang'].fillna(0)
        data['product_qty_x'] = data['product_qty_x'].fillna(0)
        data['product_qty_y'] = data['product_qty_y'].fillna(0)
        # data['order_s_d_insert_bill_id'] = data['order_s_d_insert_bill_id'].fillna(0)
        data10001 = data[['product_id_x','product_id_y','jin_huo_bian','fen_dian_id','che_liang','status']].drop_duplicates()
        # print("data10001")
        # print(data10001)


        data10001['product_id_x'] = data10001['product_id_x'].astype(int)
        data10001['product_id_y'] = data10001['product_id_y'].astype(int)
        # data10001['qty_ans'] = (data10001['product_qty_x'] - data10001['product_qty_y'])

        print("data10001")
        print(data10001)

        # data['order_s_d_insert_bill_id'] = data['order_s_d_insert_bill_id'].astype(int)
        # val = data._get_numeric_data()
        data10001['product_id_x'][data10001['product_id_x'] >= 1 ] = 1
        data10001['product_id_y'][data10001['product_id_y'] >= 1 ] = 1

        # print(data10001)
        x_df=data10001.groupby(['jin_huo_bian','fen_dian_id','che_liang','status'])['product_id_x'].sum().reset_index()
        y_df=data10001.groupby(['jin_huo_bian','fen_dian_id','che_liang'])['product_id_y'].sum().reset_index()
        # y_df = y_df.rename(columns={'order_s_d_insert_bill_id': 'order_s_insert_bill_id'})

        CC100001 = x_df.merge(y_df,on=['jin_huo_bian','fen_dian_id','che_liang'], how='outer', indicator=True)
        
        CC100001 = CC100001.replace(np.nan, 0)
        # CC100002 = CC100001
        CC100002 = CC100001.loc[CC100001['che_liang'] != 0.0]

        CC100002['ANS'] = (CC100002['product_id_x'] - CC100002['product_id_y'])
        CC100002 = CC100002.drop_duplicates()
        CC100003 = CC100002.drop(columns=['_merge'])
        # CC100003 = CC100003.head(1)
        return  CC100003
    check_data_y = check_data_y()
    check_data_x = check_data_x()
    merge_data = merge_data(check_data_x,check_data_y)
    # print(merge_data['order_s_insert_bill_id'])
    merge_data100001 = check_data_empty(merge_data)
    print("merge_data100001")
    print(merge_data100001['status'])
    # print(merge_data100001['order_s_insert_bill_id'])
    # def check_data(pd_df_tuple):
    # def check_data(pd_df_tuple):
    #     print(pd_df_tuple)
    #     # print(pd_df_tuple)
    #     # A100001 = (''' select jia_fang,che_liang,order_s_insert_id,data_detail,order_s_insert_bill_id,data_sub from order_s_insert where data_sub @> ANY (ARRAY '%s'::jsonb[]); ''' % (pd_df_tuple) )
    #     # A100001 = (''' select jia_fang,che_liang,order_s_insert_id,data_detail,order_s_insert_bill_id,data_sub from order_s_insert where data_sub @> ANY (ARRAY ['{"jin_huo_bian": 72257, "fen_dian_id": 1}','{"jin_huo_bian": 72256, "fen_dian_id": 1}'] ::jsonb[]); ''' )
    #     A100001 = (''' select jia_fang,che_liang,order_s_insert_id,data_detail,order_s_insert_bill_id,data_sub from order_s_insert where data_sub @> ANY (ARRAY %s ::jsonb[]); ''' % (pd_df_tuple) ) 
        
    #     # if len(pd_df_tuple) > 1:
    #     #     A100001 = (''' select jia_fang,che_liang,order_s_insert_id,data_detail,order_s_insert_bill_id,data_sub from order_s_insert where order_s_insert_bill_id in %s ''' % (pd_df_tuple,) )
    #     # else:
    #     #     A100001 = (''' select jia_fang,che_liang,order_s_insert_id,data_detail,order_s_insert_bill_id,data_sub from order_s_insert where order_s_insert_bill_id = '%s' ''' % (pd_df_tuple) )
    #     A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
    #     A100002 = A100001.to_dict('records')  

    #     A100003 = pd.json_normalize(A100002)
    #     # A100004 = A100003.reindex(columns=['data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail'])
    #     A100004 = A100003.reindex(columns=['data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','datetime','data_detail'])
    #     A100005 = A100004.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
    #     A100005 = A100005.to_dict('records')
    #     A100006 = pd.json_normalize(A100005,'data_detail',['jin_huo_bian','fen_dian_id','che_liang'])

    #     # A100003 = pd.json_normalize(A100002)

    #     # A100004 = A100003.reindex(columns=['jia_fang','data_sub.fen_dian_id','order_s_insert_id','order_s_insert_bill_id','che_liang'])
    #     # A100005 = A100004.rename(columns={'data_sub.fen_dian_id' : 'fen_dian_id' })

    #     return A100006

    # def check_merch_ans(x_df,y_df):

    #     CC100001 = x_df.merge(y_df,
    #                             on=['jin_huo_bian','fen_dian_id'], how='inner', indicator=True)
        
    #     CC100001 = CC100001.drop(columns=['_merge'])
    #     CC100001 = CC100001.replace(np.nan, 0)

    #     return CC100001
    # print("VC100001")
    # print(merge_data100001)
    # tuples = [tuple(x) for x in merge_data100001[['jin_huo_bian','fen_dian_id']].to_numpy()]
    merge_dat400001 = merge_data100001[['jin_huo_bian','fen_dian_id']].to_dict('records')  
    tr100001 = []
    for i in merge_dat400001:
        s1 = i
        s1 = json.dumps(i)
        DF100001 = '{}'.format(s1)
        print(DF100001)
        tr100001.append(DF100001)

    
    # merge_dat400001 = merge_data100001[['jin_huo_bian','fen_dian_id']].to_json(orient='records')
    # print(merge_data100001(tuple(tuples)))
    # VC100001 = check_data(tr100001)
    # print("VC100001")
    # print(VC100001)
    # print(merge_data100001['jin_huo_bian'],merge_data100001['fen_dian_id'])
    # VC100001 = check_data(tuple(merge_data100001['order_s_insert_bill_id'].tolist()))
    # GD10001 = check_merch_ans(VC100001,merge_data100001)
    print("merge_data100001")
    print(merge_data100001)
    GD10001 = merge_data100001

    def get_data_api(res_1,str):
        # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                     json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'jia_yi_id': str,'jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mmname'})
        print(GF10001)

        return GF10001

    # TR100001 = order_s_compline_product()
    # print(TR100001)
    # GD10001 = GD10001.drop_duplicates()

    H3000001 = GD10001[['che_liang']]
    H3000001 = H3000001.rename(columns={'che_liang': 'jia_yi_id'})
    # print(H3000001)
    H3000001 = get_data_api(H3000001,"che_liang")
    GG100009 = H3000001.merge(GD10001, on=['che_liang'], how='inner')
    GG100009 = GG100009.drop(columns=['che_liang'])
    GG100060 = GG100009
    HG10001 = pd.DataFrame()
    for index, row in GG100060.iterrows():
        jin_huo_bian__ = int(row['jin_huo_bian'])
        fen_dian_id = int(row['fen_dian_id'])
        print("jin_huo_bian__")
        print(jin_huo_bian__,fen_dian_id)
        try:
            FD100001 = functionorder_s_list(jin_huo_bian__,fen_dian_id)
            FD100001['jin_huo_bian'] = jin_huo_bian__
            FD100001['fen_dian_id'] = fen_dian_id
            HG10001 = HG10001.append(FD100001)
        except:
            pass

    print("HG10001")
    print(HG10001)
    # HG10001['product_qty_x'] = HG10001['product_qty_x'].astype(float)
    # HG10001['product_qty_x'] = HG10001['product_qty_x'].astype(float)
    HG10001['qty_ans'] = (HG10001['product_qty_x'] - HG10001['product_qty_y'])
    print(HG10001[['product_qty_x','product_qty_y','jin_huo_bian','fen_dian_id','qty_ans']])
    HG10001 = HG10001[['jin_huo_bian','fen_dian_id','qty_ans']]

    HG10001=HG10001.groupby(['jin_huo_bian','fen_dian_id'])['qty_ans'].sum().reset_index()

    # H3000001 = GG100009[['jia_fang']]
    # H3000001 = H3000001.rename(columns={'jia_fang': 'jia_yi_id'})
    # H3000001 = get_data_api(H3000001,"jia_fang")
    # GG100009 = H3000001.merge(GG100009, on=['jia_fang'], how='inner')
    # GG100060 = GG100009.drop(columns=['jia_fang'])
    # GG100060 = GG100060
    # print(GG100060)
    GG100060 = GG100060.merge(HG10001,on=['jin_huo_bian','fen_dian_id'], how='outer', indicator=True)
    # GG100060['product_id_ans'] = (GG100060['product_id_y'] - GG100060['product_id_x'])
    GG100060['qty_ans'] = GG100060['qty_ans'].fillna(-999999)
    GG100060['product_id_y_'] = GG100060['product_id_y']
    GG100060['product_id_y_'] = GG100060['product_id_y_'].astype(float)



    GD10002 = GG100060.to_json(orient='records')


    psycopg2_conn_insert_data_s.close()

    return GD10002

@router.get("/order_s_compline_bill_id", status_code=200)
def order_s_compline_product(jin_huo_bian:int,fen_dian_id:int):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    
    def check_data_x(jin_huo_bian,fen_dian_id):
        A100001 = (''' select order_s_insert_id,data_detail,order_s_insert_bill_id,data_sub,che_liang,jia_fang from order_s_insert where data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'fen_dian_id' = '%s' ''') % (jin_huo_bian,fen_dian_id)
        A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
        A100002 = A100001.to_dict('records')
        A100003 = pd.json_normalize(A100002)
        A100004 = A100003.reindex(columns=['data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail','order_s_insert_bill_id','order_s_insert_id'])
        A100005 = A100004.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
        A100005 = A100005.to_dict('records')
        A100003 = pd.json_normalize(A100005,'data_detail',['jia_fang','jin_huo_bian','fen_dian_id','order_s_insert_id','order_s_insert_bill_id','che_liang'])
        # A100003 = A100003.rename(columns={'product_id': 'product_id_x','product_qty': 'product_qty_x'})
        return A100003
    def get_app_name_fiile(ids,check_len):
        if check_len == 1 :
            AS10001 = """  select order_s_insert_bill_id,order_in_s_bill_id,data_detail from bill_id_s_insert where order_in_s_bill_id =  ('%s')  """  % ids
        else:
            AS10001 = """  select order_s_insert_bill_id,order_in_s_bill_id,data_detail from bill_id_s_insert where order_in_s_bill_id in  %s  """  % str(ids)
        AS10002 = pd.read_sql(AS10001,psycopg2_conn_insert_data_s)
        AS10002 = AS10002.to_dict('records')
        A100003 = pd.json_normalize(AS10002,'data_detail',['order_in_s_bill_id','order_s_insert_bill_id'])
        A100005=A100003.groupby(['order_in_s_bill_id'])['r_id'].max().reset_index()
        A100003 = A100003[A100003['r_id'].isin (A100005['r_id'].to_list())]
        # AS10002=AS10002.groupby(['shwethe_app_name_id','file_path','shwethe_file_name'])['version_app'].max().reset_index().head(1)
        # print(AS10002)
        return A100003
    
    YT100001 = check_data_x(jin_huo_bian,fen_dian_id)
    print(YT100001)
    YT100001['price_ans'] = (YT100001['product_qty'] * YT100001['product_price'])
    try:
        YT100001=YT100001.groupby(['s_bill_id','bi_zhi','jia_fang'])['price_ans'].sum().reset_index()
        GFH10001 = pd.DataFrame([{'bi_zhi' : 138,'bi_zhi_idname' : 'bath'},{'bi_zhi' : 139,'bi_zhi_idname' : 'kyat'}])
        print(GFH10001)
        YT100001 = GFH10001.merge(YT100001, left_on=['bi_zhi'],right_on=['bi_zhi'], how='inner')
    except:
        YT100001=YT100001.groupby(['s_bill_id','jia_fang'])['price_ans'].sum().reset_index()


    def get_data_api(res_1,str):
        # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                     json={"data_api": res_1})
        print(r.json())
        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'jia_yi_id': str+'_id','jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mmname'})
        print(GF10001)

        return GF10001

    


    print(YT100001)
    GF10002 = YT100001[['jia_fang']]
    GF10003 = GF10002.rename(columns={'jia_fang': 'jia_yi_id'})
    GF10004 = get_data_api(GF10003,"jia_yi")
    YT100001 = GF10004.merge(YT100001, left_on=['jia_yi_id'],right_on=['jia_fang'], how='outer')





    shwethe_app_name_id_list = tuple(YT100001['s_bill_id'].to_list())
    check_len = len(tuple(map( str , shwethe_app_name_id_list ) ))
    print(check_len)
    shwethe_app_name_id_list_1 = tuple(map( str , shwethe_app_name_id_list ) )
    try:
        HG10001 = get_app_name_fiile(shwethe_app_name_id_list_1,check_len)

        CC100001 = YT100001.merge(HG10001,
                                    left_on=['s_bill_id'],right_on = ['order_in_s_bill_id'], how='outer', indicator=True)
        CC100001 = CC100001.replace(np.nan, 0)

        CC100001['ANS'] = (CC100001['order_s_bill_id_total'] - CC100001['price_ans'])

        print(HG10001)
    except:
        CC100001 = YT100001 






    CC100002 = CC100001.to_json(orient='records')
    # GD10002 = YT100001.to_json(orient='records')


    psycopg2_conn_insert_data_s.close()

    return json.loads(CC100002)

class Foo_post(BaseModel):
    order_s_bill_id_total:float


class che_liang_(BaseModel):
    order_in_s_bill_id : str
    data_jsonb : Foo_post

@router.post("/order_s_compline_bill_id", status_code=200)
def order_s_compline_bill_id(req_body : che_liang_ = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)


    order_in_s_bill_id = FFF['order_in_s_bill_id']
    order_in_s_bill_id

    def post_data(order_in_s_bill_id,FFF):
            sql_insert = """
                            UPDATE bill_id_s_insert
                            SET data_detail=data_detail  || %s ::jsonb
                            where order_in_s_bill_id = %s ;
                            """
            cur.execute(sql_insert, ( FFF, order_in_s_bill_id))

    psycopg2_conn_insert_data_s.autocommit = True
    cur = psycopg2_conn_insert_data_s.cursor()
    PPO10001 = pd.DataFrame([FFF['data_jsonb']])
    PPO10001['r_id'] = str(generate_datetime_selie_v1())
    T100003 = PPO10001.to_dict(orient='records')
    FFF = json.dumps(T100003)
    post_data(order_in_s_bill_id, FFF)

    psycopg2_conn_insert_data_s.close()

    return "sucess"





@router.get("/order_s_compline_bill_id_by_id", status_code=200)
def order_s_compline_bill_id(s_bill_id:str,order_s_insert_bill_id:str):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    
    # {
    # "r_id": "20211019125413f367cd",
    # "lei_a": 42,
    # "lei_b": 22,
    # "s_bill_id": "s0000000028",
    # "product_id": 18976,
    # "lei_type_id": 1,
    # "product_qty": 50,
    # "product_price": 10,
    # "order_s_insert_bill_id": "20211019125342"
    # }


    def get_data(s_bill_id,order_s_insert_bill_id):
            sql_insert = """select  order_s_insert_bill_id,data_detail   from order_s_insert where order_s_insert_bill_id = '%s'
                            """ % order_s_insert_bill_id
            FD10001 = pd.read_sql(sql_insert,psycopg2_conn_insert_data_s)
            FD10002 = FD10001.to_dict('records')
            FD10003 = pd.json_normalize(FD10002,'data_detail',['order_s_insert_bill_id'])
            FD10004 = FD10003.reindex(columns=['product_id','product_qty','product_price','s_bill_id'])
            FD10005 = FD10004.loc[FD10004['s_bill_id'] == s_bill_id]
            return FD10005
    TR10001 = get_data(s_bill_id,order_s_insert_bill_id)
    print(TR10001)
    def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
        res_1 = json.loads(res_1.to_json(orient='records'))
        print(res_1)
        r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})

        GF10001 = pd.DataFrame(json.loads(r.json()))

        GF10001 = GF10001.rename(columns={'jia_yi_id': str,'jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mmname'})
        return GF10001
    TR10002 = TR10001[['product_id']]
    # H3000001 = H3000001.rename(columns={'che_liang': 'jia_yi_id'})
    # print(H3000001)
    H3000001 = get_data_api(TR10002,"product_id")
    print(H3000001)
    # get_data_api()

    CC100001 = H3000001.merge(TR10001,
                                    left_on=['product_id'],right_on = ['product_id'], how='inner')
    CC100001 = CC100001.replace(np.nan, 0)
    CC100001['ANS'] = (CC100001['product_qty'] * CC100001['product_price'])
    TR10002 = CC100001.to_json(orient='records')
    psycopg2_conn_insert_data_s.close()

    return json.loads(TR10002)


def order_s_compline_product_function(jin_huo_bian):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api
    
    def check_data_x(jin_huo_bian):
        A100001 = (''' select order_s_insert_id,data_detail,order_s_insert_bill_id,data_sub,che_liang from order_s_insert where data_sub ->> 'jin_huo_bian' = '%s' and data_sub ->> 'row_type' = 'for_product'  ''' % jin_huo_bian)
        A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
        A100002 = A100001.to_dict('records')
        A100003 = pd.json_normalize(A100002)
        A100004 = A100003.reindex(columns=['data_sub.status','data_sub.jin_huo_bian','che_liang','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail','order_s_insert_bill_id','order_s_insert_id'])
        A100005 = A100004.rename(columns={'data_sub.status': 'status' ,'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round' })
        A100005 = A100005.to_dict('records')
        A100003 = pd.json_normalize(A100005,'data_detail',['status','jin_huo_bian','fen_dian_id','order_s_insert_id','order_s_insert_bill_id','che_liang'])
        A100003 = A100003.rename(columns={'product_id': 'product_id_x','product_qty': 'product_qty_x'})
        return A100003

    def check_data_y(jin_huo_bian):

        A100001 = ('''   select order_s_d_insert_bill_id,data_detail,data_sub,car_id from order_s_d_insert where  data_sub ->> 'jin_huo_bian' = '%s' ''' % jin_huo_bian)
        A100001 = pd.read_sql(A100001, psycopg2_conn_insert_data_s)
        A100002 = A100001.to_dict('records')
        A100003 = pd.json_normalize(A100002)
        A100004 = A100003.reindex(columns=['data_sub.jin_huo_bian','car_id','data_sub.fen_dian_id','data_sub.car_round','jia_fang','datetime','data_detail'])
        A100005 = A100004.rename(columns={'data_sub.jin_huo_bian': 'jin_huo_bian' ,'data_sub.fen_dian_id' : 'fen_dian_id','data_sub.car_round':'car_round','car_id':'che_liang' })
        A100005 = A100005.to_dict('records')
        A100003 = pd.json_normalize(A100005,'data_detail',['jin_huo_bian','fen_dian_id','che_liang'])
        A100003 = A100003.rename(columns={'product_id': 'product_id_y','product_qty': 'product_qty_y'})

        return A100003

    def merge_data(check_data_x,check_data_y):
        # CC100001 = check_data_x.merge(check_data_y,
        #                         left_on=['jin_huo_bian','fen_dian_id'],right_on = ['jin_huo_bian','fen_dian_id'], how='outer', indicator=True)
        TY100001 = pd.concat([check_data_x,check_data_y])
        print("TY100001")
        print(TY100001['che_liang'])
        return TY100001

    def check_data_empty(data):
        try:
            data['product_id_x'] = data['product_id_x'].fillna(0)
            data['product_id_y'] = data['product_id_y'].fillna(0)
            data['product_qty_y'] = data['product_qty_y'].fillna(0)
            data['product_qty_x'] = data['product_qty_x'].fillna(0)
        except:
            data['product_id_x'] = 2131231
            data['product_qty_x'] = -123
            data['product_id_y'] = 123
            data['product_qty_y'] = -11123
        data['order_s_insert_bill_id'] = data['order_s_insert_bill_id'].fillna(0)
        data['che_liang'] = data['che_liang'].fillna(0)
        # data['order_s_d_insert_bill_id'] = data['order_s_d_insert_bill_id'].fillna(0)
        data10001 = data[['product_id_x','product_id_y','jin_huo_bian','fen_dian_id','order_s_insert_bill_id','che_liang','status']]
        # print("data10001")
        # print(data10001)


        data10001['product_id_x'] = data10001['product_id_x'].astype(int)
        data10001['product_id_y'] = data10001['product_id_y'].astype(int)
        # data10001['qty_ans'] = (data10001['product_qty_x'] - data10001['product_qty_y'])

        print("data10001")
        print(data10001)

        # data['order_s_d_insert_bill_id'] = data['order_s_d_insert_bill_id'].astype(int)
        # val = data._get_numeric_data()
        data10001['product_id_x'][data10001['product_id_x'] >= 1 ] = 1
        data10001['product_id_y'][data10001['product_id_y'] >= 1 ] = 1

        # print(data10001)
        x_df=data10001.groupby(['jin_huo_bian','fen_dian_id','che_liang','status'])['product_id_x'].sum().reset_index()
        y_df=data10001.groupby(['jin_huo_bian','fen_dian_id','che_liang'])['product_id_y'].sum().reset_index()
        # y_df = y_df.rename(columns={'order_s_d_insert_bill_id': 'order_s_insert_bill_id'})

        CC100001 = x_df.merge(y_df,on=['jin_huo_bian','fen_dian_id','che_liang'], how='outer', indicator=True)
        
        CC100001 = CC100001.replace(np.nan, 0)
        # CC100002 = CC100001
        CC100002 = CC100001.loc[CC100001['che_liang'] != 0.0]

        CC100002['ANS'] = (CC100002['product_id_x'] - CC100002['product_id_y'])
        CC100002 = CC100002.drop_duplicates()
        CC100003 = CC100002.drop(columns=['_merge'])
        # CC100003 = CC100003.head(1)
        return  CC100003
    check_data_y = check_data_y(jin_huo_bian)
    check_data_x = check_data_x(jin_huo_bian)
    merge_data = merge_data(check_data_x,check_data_y)
    merge_data100001 = check_data_empty(merge_data)
    print("merge_data100001")
    print(merge_data100001)

    HG10001 = pd.DataFrame()
    for index, row in merge_data100001.iterrows():
        jin_huo_bian__ = int(row['jin_huo_bian'])
        fen_dian_id = int(row['fen_dian_id'])
        print("jin_huo_bian__")
        print(jin_huo_bian__,fen_dian_id)
        try:
            FD100001 = functionorder_s_list(jin_huo_bian__,fen_dian_id)
            FD100001['jin_huo_bian'] = jin_huo_bian__
            FD100001['fen_dian_id'] = fen_dian_id
            HG10001 = HG10001.append(FD100001)
        except:
            pass
    print("HG10001")
    psycopg2_conn_insert_data_s.close()

    print(HG10001)
    if HG10001.empty:
        return pd.DataFrame()
    else:
        HG10001['qty_ans'] = (HG10001['product_qty_x'] - HG10001['product_qty_y'])
        HG10001 = HG10001.groupby(['jin_huo_bian','fen_dian_id'])['qty_ans'].sum().reset_index()
        return HG10001

class order_s_compline_summit_(BaseModel):
    jin_huo_bian : int
    status : str

@router.put("/order_s_compline_summit", status_code=200)
def order_s_compline_summit(req_body : order_s_compline_summit_ = Body(...)):
    import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_insert_data_s)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
    from src.Connect.https_connect import mongodb_data_api

    try:
        def summit_sucess(jin_huo_bian,jsonb,status):
            sql_insert = """ UPDATE order_s_insert  
            SET data_sub=data_sub  || %s ::jsonb  
            where  data_sub ->> 'jin_huo_bian' = %s and data_sub ->> 'status' = %s and datetime > NOW() - INTERVAL %s """
            cur.execute(sql_insert, (jsonb,jin_huo_bian,status,'1 day'))
            sql_insert2 = """ UPDATE order_s_d_insert  
            SET data_sub=data_sub  || %s ::jsonb  
            where  data_sub ->> 'jin_huo_bian' = %s and data_sub ->> 'status' = %s and datetime > NOW() - INTERVAL %s  """
            cur.execute(sql_insert2, (jsonb,jin_huo_bian,status,'1 day'))
            return

        psycopg2_conn_insert_data_s.autocommit = True
        cur = psycopg2_conn_insert_data_s.cursor()
        summit_sucess(str(req_body.jin_huo_bian),json.dumps({'status':'sucessing'}),'wait_sucess')
        FD10001 = order_s_compline_product_function(req_body.jin_huo_bian)

        if req_body.status == 'wait_sucess':
            summit_sucess(str(req_body.jin_huo_bian),json.dumps({'status':str(req_body.status)}),'sucess')
            return "sucess"
        else:
            if not FD10001.empty :
                if FD10001['qty_ans'][0] == 0 :
                    summit_sucess(str(req_body.jin_huo_bian),json.dumps({'status':str(req_body.status)}),'sucessing')
                else:
                    summit_sucess(str(req_body.jin_huo_bian),json.dumps({'status':str(req_body.status)}),'sucessing')
            else:
                summit_sucess(str(req_body.jin_huo_bian),json.dumps({'status':str(req_body.status)}),'sucessing')
            return "sucess"



    finally:
        psycopg2_conn_insert_data_s.close()