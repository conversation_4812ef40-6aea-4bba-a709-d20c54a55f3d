from xmlrpc.client import DateTime

from click import option
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id
# from importlib import reload 	
# get_itme_id = reload(src.ithem_id.get_itme_id)


# import utcnow as utcnow
# from api.public.team.models import Team


# class HeroBase(SQLModel):
#     name: str
#     secret_name: str
#     age: Optional[int] = None

#     # team_id: Optional[int] = Field(default=None, foreign_key="team.id")

#     class Config:
#         schema_extra = {
#             "example": {
#                 "id": 1,
#                 "name": "Super Man",
#                 "secret_name": "<PERSON> Kent",
#                 "age": 27,
#                 "team_id": 1,
#             }
#         }


# class Hero(HeroBase, table=True):
#     id: Optional[int] = Field(default=None, primary_key=True)

#     # team: Optional[Team] = Relationship(back_populates="heroes")


# class HeroCreate(HeroBase):
#     pass


# class HeroRead(HeroBase):
#     id: int


# class HeroUpdate(HeroBase):
#     name: Optional[str] = None
#     secret_name: Optional[str] = None
#     age: Optional[int] = None
#     team_id: Optional[int] = None

#     class Config:
#         schema_extra = {
#             "example": {
#                 "name": "Super Man",
#                 "secret_name": "Clark Kent",
#                 "age": 27,
#                 "team_id": 1,
#             }
#         }



# 资料表名称 pre_order_product 和框架
class parner_name_list_base(SQLModel):
    parner_id: Optional[int]
    

class parner_name_list(parner_name_list_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    parner_id: Optional[int]

class parner_name_list_read(BaseModel):
    
    auto_id : Optional[int]
    parner_id : Optional[int]
    parner_idname : Optional[str]
    parner_mmname : Optional[str]


class product_name_list_read(BaseModel):

    product_id : Optional[int] 
    product_idname : Optional[str] 
    product_d_name : Optional[str] 
    product_mm_name : Optional[str] 


class jia_yi_name_list_read(BaseModel):
    
    jia_yi_id : Optional[int] 
    jia_yi_idname : Optional[str] 
    jia_yi_mm_name : Optional[str] 



# 资料表名称 pre_order_product 和框架
class fen_dian_base(SQLModel):
    fen_dian_id: Optional[int]
    fen_dian_name: Optional[str]



class fen_dian(fen_dian_base, table=True):
    
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    fen_dian_id: Optional[int]
    fen_dian_name : Optional[str]



# class put_order_product(order_head_in_base):
#     jiaYiId: Optional[int]
#     orderId: Optional[str]
#     lei : Optional[int]
#     keBian : Optional[int]
#     statusCodeId : Optional[int]

# class pre_order_product_get_base(order_head_in_base):
#     jiaYiId: Optional[int] = None
#     datetime: datetime 
#     orderId: Optional[str]
#     lei : Optional[int]
#     keBian : Optional[int]
#     statusCodeId : Optional[int]
#     dataSubName: dict

    

# # 规定读资料
# class pre_order_product_model_read(BaseModel):
#     autoId: Optional[int]
#     jiaYiId: Optional[int] = None
#     datetime: datetime
#     orderId: Optional[str]
    

# # 输入资料
# class create_pre_order_product_model(BaseModel):
#     jiaYiId: Optional[int] = None
#     datetime: Optional[str] 
#     orderId: Optional[str] 

#     class Config:
#         schema_extra = {
#             "example": {
#                 "jia_yi_id": 1150,
#                 "datetime": "2022-04-22 08:10:33.137",
#             }
#         }
# class dataJson(BaseModel):
#     keBianIdname: str
#     keBianMmName: str = None
#     jiaYiIdname: str 
#     jiaYiMmName: str = None

# # 更新资料
# class create_put_order_product_model(BaseModel):
#     orderId: str
#     statusCodeId: int
#     lei: int
#     jiaYiId: int
#     keBian: int
#     dataSubName:dict
#     dataSub:Optional[dict] = {}

    # class Config:
    #     schema_extra = {
    #         "example": {
    #             "jia_yi_id": 1150,
    #             "datetime": "2022-04-22 08:10:33.137",
    #         }
    #     }

# 资料表名称 pre_order_product_sub 和框架

# class pre_order_product_sub_base(SQLModel):
#     productId: Optional[int]
#     productQty: Optional[float] 
#     productPrice: Optional[float] 
#     productMaxQty: Optional[float] 
#     orderId: Optional[str]
#     dataSubName: dict
#     # orderProductSubId : Optional[str]
#     # datetime : datetime


# class pre_order_product_sub(pre_order_product_sub_base, table=True):
#     autoId: Optional[int] = Field(default=None, primary_key=True)
#     productId: Optional[int]
#     productQty: Optional[float] 
#     productPrice: Optional[float] 
#     productMaxQty: Optional[float] 
#     orderId: Optional[str]
#     dataSubName: dict = Field(sa_column=Column(JSONB), default={})
#     orderProductSubId : Optional[str]
#     datetime : Optional[datetime] 



# # 规定读资料
# class pre_order_product_sub_model_read(pre_order_product_sub_base):
    
#     productId: Optional[int]
#     productQty: Optional[float] 
#     productPrice: Optional[float] 
#     productMaxQty: Optional[float] = 0
#     orderId: Optional[str]
#     orderProductSubId : Optional[str]
#     datetime : Optional[datetime] 



# class dataSubJson(BaseModel):
#     productIdname: str
#     productMmName: str
#     productDName: str = None

# # 输入资料
# class create_pre_order_product_sub_model(pre_order_product_sub_base):

#     productId: int
#     productQty: float
#     productPrice: float
#     productMaxQty: Optional[float] = 0
#     orderId: str
#     dataSubName : dataSubJson
#     orderProductSubId : Optional[str]
#     datetime : Optional[datetime] 

#     # class Config:
#     #     schema_extra = {
#     #         "example": {
#     #             "productId": 0,
#     #             "productQty": 0,
#     #             "productPrice": 0,
#     #             "productMaxQty": 0,
#     #             "orderId": "string",
#     #             "dataSub": {
#     #                 "ProductIdname": 0,
#     #                 "ProductMmName": 0,
#     #                 "ProductDName": 0
#     #             }
#     #         }
#     #     }

# class product_sub_model(pre_order_product_sub_base):
#     productId: int
#     productQty: float
#     productPrice: float
#     productMaxQty: float
#     orderId: str


# class product_sub_model_get(pre_order_product_sub_base):
#     orderId: Optional[int]