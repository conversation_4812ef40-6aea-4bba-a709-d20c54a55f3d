from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel
from typing import List, Optional

from datetime import datetime
from src.shwethe_in.database import get_session
from helper import generate_datetime_id
from src.shwethe_in.search.models.models import parner_name_list,fen_dian
import json
import requests
import pandas as pd

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df



def read_parner_list(db: Session = Depends(get_session)):

    heroes = db.exec(select(parner_name_list)).all()
    df = sqmodel_to_df(heroes)
    df['jia_yi_id'] = df['parner_id']
    

    from src.common.jia_yi_name import jia_yi_name_package

    T10001 = jia_yi_name_package()
    T10002 = T10001.select_data_list_id_with_http(df,'parner')

    X1001 = df.merge(T10002, on=['jia_yi_id'], how='inner')


    X1001 = X1001.to_json(orient='records')
    

    return json.loads(X1001)


def read_fen_dian_list(db: Session = Depends(get_session)):
    
    heroes = db.exec(select(fen_dian)).all()
    df = sqmodel_to_df(heroes)
    # df['jia_yi_id'] = df['parner_id']
    

    # from src.common.jia_yi_name import jia_yi_name_package

    # T10001 = jia_yi_name_package()
    # T10002 = T10001.select_data_list_with_http(df,'parner')

    # X1001 = df.merge(T10002, on=['jia_yi_id'], how='inner')


    df = df.to_json(orient='records')
    

    return json.loads(df)


def read_product_list(text:str,db: Session = Depends(get_session)):
    
    # heroes = db.exec(select(parner_name_list)).all()
    # df = sqmodel_to_df(heroes)
    # df['jia_yi_id'] = df['parner_id']
    

    from src.common.product_name import product_name_package

    T10001 = product_name_package()
    T10002 = T10001.select_product_list_with_http('product',{'text':text})

    # X1001 = df.merge(T10002, on=['jia_yi_id'], how='inner')


    X1001 = T10002.to_json(orient='records')
    

    return json.loads(X1001)


def read_jia_yi_name_list(text:str,db: Session = Depends(get_session)):
    
    # heroes = db.exec(select(parner_name_list)).all()
    # df = sqmodel_to_df(heroes)
    # df['jia_yi_id'] = df['parner_id']
    

    from src.common.jia_yi_name import jia_yi_name_package

    T10001 = jia_yi_name_package()
    T10002 = T10001.select_data_list_with_http('jia_yi',{'text':text})

    # X1001 = df.merge(T10002, on=['jia_yi_id'], how='inner')


    X1001 = T10002.to_json(orient='records')
    

    return json.loads(X1001)



def read_product_list_for_input_product(text:str,db: Session = Depends(get_session)):
    
    # heroes = db.exec(select(parner_name_list)).all()
    # df = sqmodel_to_df(heroes)
    # df['jia_yi_id'] = df['parner_id']
    

    from src.common.product_name import product_name_package

    T10001 = product_name_package()
    T10002 = T10001.select_product_list_with_http('product',{'text':text})

    url = 'http://192.168.1.85' + '/order_list'

    # body_raw = {"data_api": [{"jia_yi_id": 22904}]}
    # payload = {'text': 'value1'}

    df2 = requests.get(url=url)
    df2 = df2.json()
    df2 = pd.DataFrame(df2)

    # print(df2.info())

    # print(T10002.info())


    T20002 = T10002[T10002.set_index(['product_id']).index.isin([1108,1109,73478,73488,10919,1892,62034,72430,73520])]
    T10002 = T10002[T10002.set_index(['product_id']).index.isin(df2.set_index(['id_x']).index)]
    T10002 = pd.concat([T20002,T10002])


    # print(df2)

    # X1001 = df.merge(T10002, on=['jia_yi_id'], how='inner')


    X1001 = T10002.to_json(orient='records')
    

    return json.loads(X1001)


# def create_hero(hero: HeroCreate, db: Session = Depends(get_session)):
#     hero_to_db = Hero.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
#     return hero_to_db





# def read_hero(hero_id: int, db: Session = Depends(get_session)):
#     hero = db.get(Hero, hero_id)
#     if not hero:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {hero_id}",
#         )
#     return hero


# def update_hero(hero_id: int, hero: HeroUpdate, db: Session = Depends(get_session)):
#     hero_to_update = db.get(Hero, hero_id)
#     if not hero_to_update:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {hero_id}",
#         )

#     team_data = hero.dict(exclude_unset=True)
#     for key, value in team_data.items():
#         setattr(hero_to_update, key, value)

#     db.add(hero_to_update)
#     db.commit()
#     db.refresh(hero_to_update)
#     return hero_to_update


# def delete_hero(hero_id: int, db: Session = Depends(get_session)):
#     hero = db.get(Hero, hero_id)
#     if not hero:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {hero_id}",
#         )

#     db.delete(hero)
#     db.commit()
#     return {"ok": True}





# def create_pre_order_product(hero: pre_order_product_model_read, db: Session = Depends(get_session)):

#     hero.datetime = str(datetime.now())
#     hero.orderId = generate_datetime_id()
#     hero_to_db = pre_order_product.from_orm(hero)
    
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)

#     return hero_to_db


# def create_put_order_sub_product(hero: create_put_order_product_model, db: Session = Depends(get_session)):
#     # print(hero)
#     # hero.datetime = str(datetime.now())
#     # hero.orderProductSubId = generate_datetime_id()

#     statement = select(pre_order_product).where(pre_order_product.orderId == hero.orderId)
#     results = db.exec(statement).one()

#     print("results:", results)
#     print("hero:", hero)


#     results.lei = hero.lei
#     results.keBian = hero.keBian
#     results.statusCodeId = hero.statusCodeId
#     results.jiaYiId = hero.jiaYiId
#     results.dataSubName = hero.dataSubName
#     results.dataSub = hero.dataSub

#     db.add(results)
#     db.commit()
#     db.refresh(results)
    
#     return results


# def create_pre_order_sub_product(hero: pre_order_product_sub_model_read, db: Session = Depends(get_session)):
#     # print(hero)
#     hero.datetime = str(datetime.now())
#     hero.orderProductSubId = generate_datetime_id()
#     hero_to_db = pre_order_product_sub.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
    
#     return hero_to_db




# def get_pre_order_sub_product(orderId:str, db: Session = Depends(get_session)):
#     print(orderId)
#     # hero_to_db = pre_order_product_sub.from_orm(hero)

#     # db.add(hero_to_db)
#     # db.commit()
#     # db.refresh(hero_to_db)

#     # def read_hero(hero_id: int, db: Session = Depends(get_session)):
#     #     hero = db.get(Hero, hero_id)
#     # if not hero:
#     #     raise HTTPException(
#     #         status_code=status.HTTP_404_NOT_FOUND,
#     #         detail=f"Hero not found with id: {hero_id}",
#     #     )
#     # return hero
#     # heroes = db.exec(select(Hero).offset(offset).limit(limit)).all()
#     statement = select(pre_order_product_sub).where(pre_order_product_sub.orderId == orderId)
#     results = db.exec(statement).all()

#     for hero in results:
#         print(hero)
#     print(results)
#     print("results")
#     # hero = db.get(pre_order_product_sub_base, orderId)
#     if not results:
#         # raise HTTPException(
#         #     status_code=status.HTTP_404_NOT_FOUND,
#         #     detail=f"Hero not found with id: {orderId}",
#         # )
#         return []
        
#     return results
    


# def create_get_order_sub_product(orderId:str, hero: pre_order_product_sub_model_read, db: Session = Depends(get_session)):
#     # print(hero)
#     hero_to_db = pre_order_product_sub.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
    
#     return hero_to_db



# def getDataSubProduct(db: Session = Depends(get_session)):

#     heroes = db.exec(select(pre_order_product).offset(offset).limit(limit)).all()
    
#     return heroes


# def get_pre_order_list(db: Session = Depends(get_session)):
   
#     heroes = db.exec(select(pre_order_product)).all()

#     df = sqmodel_to_df(heroes)

#     df = df.sort_values(['datetime'], ascending=[False])

#     # df10001 = df.to_dict('records')

#     # df10002 = pd.json_normalize(df10001)

#     # df10003 = df10002.rename(columns={'dataSubName.jiaYiIdname': 'jiaYiIdname','dataSubName.jiaYiMmName': 'jiaYiMmName'
#     # ,'dataSubName.keBianIdname': 'keBianIdname','dataSubName.keBianMmName': 'keBianMmName'})
    

#     df10004 = df.to_json(orient='records')

    
#     return json.loads(df10004)