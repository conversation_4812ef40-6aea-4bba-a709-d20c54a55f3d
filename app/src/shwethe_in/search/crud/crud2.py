from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel
from typing import List, Optional

from datetime import datetime
from src.shwethe_in.database import get_session
from helper import generate_datetime_id
from src.shwethe_in.search.models.models import parner_name_list,fen_dian
import json
import requests
import pandas as pd
import numpy as np

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

def read_product_list_for_input_product_v2(text:str,parner_id:int,db: Session = Depends(get_session)):
    
    from src.common.product_name import product_name_package

    T10001 = product_name_package()
    T10002 = T10001.select_product_list_with_http('product',{'text':text})

    # url = 'http://192.168.1.85' + '/order_list'
    url = "http://pv-api.shwethe.com/shwethe_api_in/api/v1/follow/follow_goods"
    
    

    df2 = requests.get(url=url)
    df2 = df2.json()
    df2 = pd.DataFrame(df2)
    
    if df2.empty:
        
        return []

    T20002 = T10002[T10002.set_index(['product_id']).index.isin([1108,1109,73478,73488,10919,1892,62034,72430,73520])]
    
    
    T10002 = T10002[T10002.set_index(['product_id']).index.isin(df2.set_index(['id_x']).index)]
    
    
    
    
    
    T10002 = pd.concat([T20002,T10002])
    
    T10002 = T10002.merge(df2, left_on=['product_id'], right_on=['id_x'], how='inner')
    
    T10002 = T10002.loc[T10002['parner'] == parner_id]
    
    
    T10002 = pd.concat([T20002,T10002])
    
    T10002['1'] = T10002['1'].replace(np.nan, 99999)
    T10002['2'] = T10002['2'].replace(np.nan, 99999)
    
    print(T10002)
    
    if T10002.empty:
            
        return []
    
    X1001 = T10002.to_json(orient='records')
    
    return json.loads(X1001)