from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_in.database import get_session
from src.shwethe_in.search.crud.crud import (
    read_parner_list,
    read_fen_dian_list,
    read_product_list,
    read_jia_yi_name_list,
    read_product_list_for_input_product
)

from src.shwethe_in.search.models.models import jia_yi_name_list_read,product_name_list_read,parner_name_list_read,fen_dian

router = APIRouter()


@router.get("/parner_list", response_model=List[parner_name_list_read])
def create_a_hero(db: Session = Depends(get_session)):

    return read_parner_list(db=db)


@router.get("/fen_dian_list", response_model=List[fen_dian])
def create_a_hero(db: Session = Depends(get_session)):

    return read_fen_dian_list(db=db)



@router.get("/product_list", response_model=List[product_name_list_read])
def create_a_hero(text:str,db: Session = Depends(get_session)):

    return read_product_list(text=text,db=db)


@router.get("/jia_yi_name_list", response_model=List[jia_yi_name_list_read])
def create_a_hero(text:str,db: Session = Depends(get_session)):

    return read_jia_yi_name_list(text=text,db=db)


@router.get("/product_list_for_input_product", response_model=List[product_name_list_read])
def create_a_hero(text:str,db: Session = Depends(get_session)):

    return read_product_list_for_input_product(text=text,db=db)


@router.get("/test123456", response_model=str)
def create_a_hero(text:str,db: Session = Depends(get_session)):

    from weasyprint import HTML
    string2 = """ 
    <!DOCTYPE html>
    <html>
    <head>
    <meta charset="utf-8">
        <link href='https://fonts.googleapis.com/css?family=Libre Barcode 39' rel='stylesheet'>
    <style>
    div.aa {
        font-family: 'Libre Barcode 39';font-size: 30px;
    }
    @page {
            size: 3.14in 1.57in;
            margin: 0in 0.44in 0.2in 0.44in;
        }

    div.a {
    width: auto;
    border: 1px solid black;
    }

    div.vvv {
    width: 20px;
    height: 20px;
    border: 1px solid black;  
    }

    div.c {
    width: 50%;
    border: 1px solid black;  
    }
    @font-face {
            font-family: 'Noto Sans Thai';
            src: url('https://s3.amazonaws.com/vo-random/ShareX/2019/02/NotoSansThai-Regular.ttf') format('truetype');
        }

    @font-face {
        font-family: 'Unicode';
        src: url('https://cdn.rawgit.com/LeonarAung/MyanmarFont/6cf1262f/mon3.woff') format('woff'), url('https://cdn.rawgit.com/LeonarAung/MyanmarFont/6cf1262f/mon3.ttf') format('ttf');
    }

    img {
    width: 100%;
    height: auto;
    # border-style: dashed;
    }


    </style>
    </head>
    <body>


    <div class="">啊实打实的</div>
    <div class="">gv5000</div>
    <div class="">မြန်မာဘာသာစကား</div>
    <br>
    <img alt='Barcode Generator TEC-IT'
        src='https://barcode.tec-it.com/barcode.ashx?data=GV50004&code=Code128&translate-esc=on&imagetype=Png' style="width: 200px; height: 50px; object-fit: contain;"/>



    </body>
    </html> """

    # hs = open("asciiCharHTMLTable.html", 'w')
    # hs.write(string2)

    # import time

    # time.sleep( 2 )


    HTML(string=string2).write_pdf("google.pdf")
    # HTML("asciiCharHTMLTable.html", encoding="utf-8").write_pdf("google.pdf")

    return" read_product_list_for_input_product(text=text,db=db)"

# @router.post("", response_model=pre_order_product_model_read)
# def create_a_hero(hero: create_pre_order_product_model, db: Session = Depends(get_session)):

#     return create_pre_order_product(hero=hero, db=db)


# @router.put("", response_model=pre_order_product_model_read)
# def create_a_hero(hero: create_put_order_product_model, db: Session = Depends(get_session)):
#     return create_put_order_sub_product(hero=hero, db=db)


# @router.post("/sub_pre_order", response_model=pre_order_product_sub_model_read)
# def create_a_hero(hero: create_pre_order_product_sub_model, db: Session = Depends(get_session)):
    
#     return create_pre_order_sub_product(hero=hero, db=db)


# @router.get("/sub_pre_order/{orderId}", response_model=List[product_sub_model])
# def create_a_hero(orderId: str , db: Session = Depends(get_session)):
    
#     return get_pre_order_sub_product(orderId=orderId, db=db)



# @router.get("/pre_order_list", response_model=List[pre_order_product_get_base])
# def pre_order_list(
#             db: Session = Depends(get_session),
#         ):
    
#     return get_pre_order_list(db=db)








# @router.post("", response_model=HeroRead)
# def create_a_hero(hero: HeroCreate, db: Session = Depends(get_session)):
#     return create_hero(hero=hero, db=db)




# @router.get("", response_model=List[HeroRead])
# def get_heroes(
#     offset: int = 0,
#     limit: int = Query(default=100, lte=100),
#     db: Session = Depends(get_session),
# ):
#     return read_heroes(offset=offset, limit=limit, db=db)


# @router.get("/{hero_id}", response_model=HeroRead)
# def get_a_hero(hero_id: int, db: Session = Depends(get_session)):
#     return read_hero(hero_id=hero_id, db=db)


# @router.patch("/{hero_id}", response_model=HeroRead)
# def update_a_hero(hero_id: int, hero: HeroUpdate, db: Session = Depends(get_session)):
#     return update_hero(hero_id=hero_id, hero=hero, db=db)


# @router.delete("/{hero_id}")
# def delete_a_hero(hero_id: int, db: Session = Depends(get_session)):
#     return delete_hero(hero_id=hero_id, db=db)


