
from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_in.database import get_session
from src.shwethe_in.search.crud.crud2 import (
    read_product_list_for_input_product_v2
)

from src.shwethe_in.search.models.models import jia_yi_name_list_read,product_name_list_read,parner_name_list_read,fen_dian

router = APIRouter()

@router.get("/product_list_for_input_product")
def create_a_hero(text:str,parnerId:int,db: Session = Depends(get_session)):
    return read_product_list_for_input_product_v2(text=text,parner_id=parnerId,db=db)