from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_in.database import get_session
from src.shwethe_in.status_list.crud.crud import (
    put_status_list,
    read_status_list,
    post_list_car_in,
    get_list_car_in,
    fun_warehourse_in_get
)

from src.shwethe_in.product_in.crud.crud import (
    getDataListBillbyjinHuoBian,
    getDataListProductCompilebyjinHuoBian
)

from src.shwethe_in.product_in.models.models import compileProduct
from src.shwethe_in.status_list.models.models import order_head_in_put,list_car_in_parner_get,order_body_in,order_head_in,order_body_in_post

router = APIRouter()


@router.get("", response_model=List[order_head_in])
def create_a_hero(db: Session = Depends(get_session)):
    return read_status_list(db=db)

@router.put("", response_model=order_head_in)
def create_a_hero2(hero:order_head_in_put,action:str=None,db : Session = Depends(get_session)):
    return put_status_list(action=action,hero=hero,db=db)

@router.post("/list_car_in_parner", response_model=order_head_in)
def create_a_hero(hero: order_body_in_post ,  db: Session = Depends(get_session)):
    return post_list_car_in(hero=hero,db=db)

@router.get("/list_car_in_parner", response_model=List[order_body_in])
def list_car_in_parner(jin_huo_bian:int ,db: Session = Depends(get_session)):
    return get_list_car_in(jin_huo_bian = jin_huo_bian , db=db)

@router.get("/warehourseList", response_model=List[order_head_in])
def create_a_hero(fen:int ,  db: Session = Depends(get_session)):
    return fun_warehourse_in_get(fen=fen,db=db)

@router.get("/list_car_in_parner/{jin_huo_bian}/bill_list", response_model=List[list_car_in_parner_get])
def list_car_in_parner(jin_huo_bian:int ,db: Session = Depends(get_session)):
    return getDataListBillbyjinHuoBian(jin_huo_bian = jin_huo_bian , db=db)

@router.get("/list_car_in_parner/{jin_huo_bian}/product_list", response_model=List[compileProduct])
def list_car_in_parner(jin_huo_bian:int ,db: Session = Depends(get_session)):
    return getDataListProductCompilebyjinHuoBian(jin_huo_bian = jin_huo_bian , db=db)

@router.get("/list_car_in_parner/{jin_huo_bian}/direct_list", response_model=List[compileProduct])
def list_car_in_parner(jin_huo_bian:int ,db: Session = Depends(get_session)):
    return getDataListProductCompilebyjinHuoBian(jin_huo_bian = jin_huo_bian , db=db)



