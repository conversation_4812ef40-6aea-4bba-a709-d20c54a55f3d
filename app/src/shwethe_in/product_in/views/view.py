from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_in.database import get_session
from src.shwethe_in.product_in.crud.crud import (
   check_product_parner_have,
   get_bill,
   fun_product_in_post,
   fun_product_in_get,
   fun_product_in_delete,
   check_direct_parner_have,
   fun_direct_in_post,
   fun_direct_in_get,
   fun_direct_in_delete,
   warehourse_in_post,
   warehourse_in_get,
   warehourse_in_delete,
   post_bill,
   goods_info_parme_post,
   post_car_ithem_round,
   post_car_ithem,
   get_car_item_round,
   get_car_item
   
)

from src.shwethe_in.product_in.models.models import (
    goods_info_parme_post_base_model,
    product_in_get,bill_id_s_insert_post,
    warehouse_product_in,warehouse_product_in_post
    ,direct_in_post,direct_in,test,bill_id_s_insert,
    product_in_post,product_in,
    car_ithem_round_post,
    car_ithem_post,
)

router = APIRouter()



@router.get("/product_option")
def product_option(product_id:int,parner:int,db: Session = Depends(get_session)):

    return check_product_parner_have(product_id=product_id,parner=parner,db=db)

@router.get("/bill", response_model=List[bill_id_s_insert])
def bill(jin_huo_bian:int,db: Session = Depends(get_session)):

    return get_bill(jin_huo_bian=jin_huo_bian,db=db)

@router.post("/bill", response_model=List[bill_id_s_insert])
def bill(hero:bill_id_s_insert_post,db: Session = Depends(get_session)):

    return post_bill(hero=hero,db=db)

@router.post("/product_in", response_model=List[product_in])
def bill(hero : product_in_post , db: Session = Depends(get_session)):
    
    return fun_product_in_post(hero=hero,db=db)

@router.get("/product_in", response_model=List[product_in_get])
def bill(order_che_ci_body_in_id : int , db: Session = Depends(get_session)):
    
    return fun_product_in_get(order_che_ci_body_in_id=order_che_ci_body_in_id ,db=db)

@router.delete("/product_in", response_model=int)
def bill(order_che_ci_body_in_id : int , db: Session = Depends(get_session)):
    
    return fun_product_in_delete(auto_id=order_che_ci_body_in_id ,db=db)


@router.get("/direct_option", response_model=test)
def product_option(product_id:int,parner:int,db: Session = Depends(get_session)):

    return check_direct_parner_have(product_id=product_id,parner=parner,db=db)

@router.post("/direct_in", response_model=List[direct_in])
def bill(hero : direct_in_post , db: Session = Depends(get_session)):
    
    return fun_direct_in_post(hero=hero,db=db)

@router.get("/direct_in", response_model=List[direct_in])
def bill(order_che_ci_body_in_id : int , db: Session = Depends(get_session)):
    
    return fun_direct_in_get(order_che_ci_body_in_id=order_che_ci_body_in_id ,db=db)

@router.delete("/direct_in", response_model=int)
def bill(order_che_ci_body_in_id : int , db: Session = Depends(get_session)):
    
    return fun_direct_in_delete(auto_id=order_che_ci_body_in_id ,db=db)


@router.post("/warehourse_in", response_model=List[warehouse_product_in])
def warehourse_in(hero : warehouse_product_in_post , db: Session = Depends(get_session)):
    
    return warehourse_in_post(hero=hero,db=db)


@router.get("/warehourse_in", response_model=List[warehouse_product_in])
def warehourse_in(jin_huo_bian : int , jia_yi_fang : int , db: Session = Depends(get_session)):
    
    return warehourse_in_get(jin_huo_bian=jin_huo_bian,jia_yi_fang=jia_yi_fang,db=db)

@router.delete("/warehourse_in", response_model=int)
def warehourse_in(auto_id : int ,  db: Session = Depends(get_session)):
    
    return warehourse_in_delete(auto_id=auto_id ,db=db)


@router.delete("/insert_order", response_model=int)
def warehourse_in(auto_id : int ,  db: Session = Depends(get_session)):
    
    from src.common.function.function import delete_insert_order
    
    return delete_insert_order(auto_id=auto_id)


@router.post("/goods_info_parme")
def warehourse_in(hero : goods_info_parme_post_base_model , db: Session = Depends(get_session)):
    
    return goods_info_parme_post(hero=hero,db=db)



@router.post("/car_ithem_round")
def car_ithem_round(hero:car_ithem_round_post,db: Session = Depends(get_session)):
    
    return post_car_ithem_round(hero=hero,db=db)

@router.get("/car_ithem_round/{day}")
def read_car_items(day: int, db: Session = Depends(get_session)):
    return get_car_item_round(day, db)

@router.post("/car_ithem")
def car_ithem(hero:car_ithem_post,db: Session = Depends(get_session)):
    
    return post_car_ithem(hero=hero,db=db)

@router.get("/car_ithem")
def car_ithem(uuid:str,db: Session = Depends(get_session)):
    
    return get_car_item(uuid=uuid,db=db)

# @router.post("", response_model=pre_order_product_model_read)
# def create_a_hero(hero: create_pre_order_product_model, db: Session = Depends(get_session)):

#     return create_pre_order_product(hero=hero, db=db)


# @router.put("", response_model=pre_order_product_model_read)
# def create_a_hero(hero: create_put_order_product_model, db: Session = Depends(get_session)):
#     return create_put_order_sub_product(hero=hero, db=db)


# @router.post("/sub_pre_order", response_model=pre_order_product_sub_model_read)
# def create_a_hero(hero: create_pre_order_product_sub_model, db: Session = Depends(get_session)):
    
#     return create_pre_order_sub_product(hero=hero, db=db)


# @router.get("/sub_pre_order/{orderId}", response_model=List[product_sub_model])
# def create_a_hero(orderId: str , db: Session = Depends(get_session)):
    
#     return get_pre_order_sub_product(orderId=orderId, db=db)



# @router.get("/pre_order_list", response_model=List[pre_order_product_get_base])
# def pre_order_list(
#             db: Session = Depends(get_session),
#         ):
    
#     return get_pre_order_list(db=db)








# @router.post("", response_model=HeroRead)
# def create_a_hero(hero: HeroCreate, db: Session = Depends(get_session)):
#     return create_hero(hero=hero, db=db)




# @router.get("", response_model=List[HeroRead])
# def get_heroes(
#     offset: int = 0,
#     limit: int = Query(default=100, lte=100),
#     db: Session = Depends(get_session),
# ):
#     return read_heroes(offset=offset, limit=limit, db=db)


# @router.get("/{hero_id}", response_model=HeroRead)
# def get_a_hero(hero_id: int, db: Session = Depends(get_session)):
#     return read_hero(hero_id=hero_id, db=db)


# @router.patch("/{hero_id}", response_model=HeroRead)
# def update_a_hero(hero_id: int, hero: HeroUpdate, db: Session = Depends(get_session)):
#     return update_hero(hero_id=hero_id, hero=hero, db=db)


# @router.delete("/{hero_id}")
# def delete_a_hero(hero_id: int, db: Session = Depends(get_session)):
#     return delete_hero(hero_id=hero_id, db=db)


