from cgitb import text
from typing import List
from xmlrpc.client import DateTime

from click import option
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy import TEXT
from src.time_zone.time_zone_function import get_datetime
from sqlalchemy.sql.expression import table, text
from datetime import datetime
from helper import generate_datetime_id
# from importlib import reload 	
# get_itme_id = reload(src.ithem_id.get_itme_id)





# 资料表名称 pre_order_product 和框架
class lei_type_list_base(SQLModel):
    lei_a: Optional[int]
    lei_b: Optional[int]
    lei_name: Optional[int]
    data_sub: Optional[int]
    code_id : Optional[int]
    

class lei_type_list(lei_type_list_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    lei_a: Optional[int]
    lei_b: Optional[int]
    lei_name: Optional[int]
    data_sub: Optional[int]
    code_id : Optional[int]

# class lei_type_list(BaseModel):
    
#     auto_id : Optional[int]
#     parner_id : Optional[int]
#     parner_idname : Optional[str]
#     parner_mmname : Optional[str]


class test(BaseModel):

    product_id : Optional[int] 
    payment_type : List[Optional[dict]] 
    price : float
    # auto_id : Optional[int] 
    # lei_name : Optional[str] 
    # lei_a : Optional[str] 
    # lei_b : Optional[str] 
    # data_sub : Optional[dict] 
    # code_id : Optional[int]


# 资料表名称 pre_order_product 和框架
class bill_id_s_insert_base(SQLModel):
    order_in_s_bill_id: Optional[str]
    order_s_insert_bill_id: Optional[str]
    data_detail : Optional[dict] 
    data_sub : Optional[dict] 
    

class bill_id_s_insert(bill_id_s_insert_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    order_in_s_bill_id: Optional[str] = Field(sa_column=Column(TEXT(convert_unicode=True), server_default=text("'s'::text || lpad(nextval('bill_id_s_insert_id_id'::regclass)::text, 10, '0'::text)")))
    order_s_insert_bill_id: Optional[str] 
    data_detail : dict = Field(sa_column=Column(JSONB), default=[])
    data_sub : dict = Field(sa_column=Column(JSONB), default={})


class bill_id_s_insert_post(BaseModel):
    jin_huo_bian: int


class bill_id_s_post(BaseModel):
    
    order_s_insert_bill_id: Optional[str]
    data_sub : dict = Field(sa_column=Column(JSONB), default={})


class product_in_base(SQLModel):
    bill_id: Optional[str]
    product_id: Optional[int]
    product_qty: Optional[float]
    product_price_x: Optional[float]
    product_price_y: Optional[float]
    shu_ri_qi: Optional[datetime]
    bi_zhi: Optional[int]
    lei_type_list_id: Optional[int]
    ke_bian: Optional[int]
    order_che_ci_body_in_id: Optional[int]
    order_id: Optional[str] 
    data_sub_name : Optional[dict]
    

class product_in(product_in_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    bill_id: Optional[str]
    product_id: Optional[int]
    product_qty: Optional[float]
    product_price_x: Optional[float]
    product_price_y: Optional[float]
    shu_ri_qi: Optional[datetime]
    bi_zhi: Optional[int]
    lei_type_list_id: Optional[int]
    ke_bian: Optional[int]
    order_che_ci_body_in_id: Optional[int]
    order_id: Optional[str] 
    data_sub_name : dict = Field(sa_column=Column(JSONB), default={})


class product_in_get(BaseModel):
    
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    bill_id: Optional[str]
    product_id: Optional[int]
    product_qty: Optional[float]
    product_price_x: Optional[float]
    product_price_y: Optional[float]
    shu_ri_qi: Optional[datetime]
    bi_zhi: Optional[int]
    lei_type_list_id: Optional[int]
    ke_bian: Optional[int]
    order_che_ci_body_in_id: Optional[int]
    product_price_sum: Optional[float]
    data_sub_name : dict = Field(sa_column=Column(JSONB), default={})


class product_in_post(BaseModel):
    order_id: Optional[str] = 0
    bill_id: str
    product_id: int
    product_qty: float
    product_price_x: float
    product_price_y: Optional[float] = 0
    shu_ri_qi: Optional[datetime]
    bi_zhi : int
    lei_type_list_id: int
    jia_yi_fang : Optional[int]
    ke_bian: int = 0
    order_che_ci_body_in_id: int
    data_sub_name : dict



class direct_in_base(SQLModel):
    bill_id: Optional[str]
    product_id: Optional[int]
    product_qty: Optional[float]
    product_price_x: Optional[float]
    product_price_y: Optional[float]
    shu_ri_qi: Optional[datetime]
    jia_yi_fang : Optional[int]
    bi_zhi: Optional[int]
    lei_type_list_id: Optional[int]
    ke_bian: Optional[int]
    order_che_ci_body_in_id: Optional[int]
    data_sub_name : Optional[dict]
    

class direct_in(direct_in_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    bill_id: Optional[str]
    product_id: Optional[int]
    product_qty: Optional[float]
    product_price_x: Optional[float]
    product_price_y: Optional[float]
    jia_yi_fang : Optional[int]
    shu_ri_qi: Optional[datetime]
    bi_zhi: Optional[int]
    lei_type_list_id: Optional[int]
    ke_bian: Optional[int]
    order_che_ci_body_in_id: Optional[int]
    data_sub_name : dict = Field(sa_column=Column(JSONB), default={})



class direct_in_post(BaseModel):
    
    bill_id: str
    product_id: int
    product_qty: float
    product_price_x: float
    product_price_y: Optional[float] = 0
    shu_ri_qi: Optional[datetime]
    jia_yi_fang : int
    bi_zhi : int
    lei_type_list_id: int
    ke_bian: int = 0
    order_che_ci_body_in_id: int
    data_sub_name : dict



class direct_in_base(SQLModel):
    bill_id: Optional[str]
    product_id: Optional[int]
    product_qty: Optional[float]
    product_price_x: Optional[float]
    product_price_y: Optional[float]
    shu_ri_qi: Optional[datetime]
    bi_zhi: Optional[int]
    lei_type_list_id: Optional[int]
    ke_bian: Optional[int]
    order_che_ci_body_in_id: Optional[int]
    data_sub_name : Optional[dict]










class warehouse_product_in_base(SQLModel):
    jia_yi_fang: Optional[int]
    product_id: int
    product_qty: Optional[float]
    jin_huo_bian: Optional[int]
    fen_dian_id: Optional[int]
    shu_ri_qi:Optional[datetime]
    data_sub_name : dict = Field(sa_column=Column(JSONB), default={})
    data_sub: dict = Field(sa_column=Column(JSONB), default={})
    

class warehouse_product_in(warehouse_product_in_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    jia_yi_fang: Optional[int]
    product_id: int
    product_qty: Optional[float]
    jin_huo_bian: Optional[int]
    fen_dian_id: Optional[int]
    shu_ri_qi:Optional[datetime]
    data_sub_name : dict = Field(sa_column=Column(JSONB), default={})
    data_sub: dict = Field(sa_column=Column(JSONB), default={})


class warehouse_product_in_post(BaseModel):
    
    jia_yi_fang: int
    product_id: int
    product_qty: float
    jin_huo_bian: int
    fen_dian_id: int
    shu_ri_qi:Optional[datetime]
    data_sub_name : Optional[dict]  
    data_sub: Optional[dict] 


class compileProduct(BaseModel):
    product_id   : Optional[int]
    product_idname  : Optional[str]
    product_mm_name  : Optional[str]
    product_d_name   : Optional[str]
    product_qty_x   : Optional[int]                 
    product_qty_y   : Optional[int]   
    product_check_qty   : Optional[bool]    
    
    
    
    
    
    
    
    
    
    
class goods_info_parme_base(SQLModel):
    product_id: Optional[int]
    key: str
    value: Optional[str]
    shu_ri_qi:Optional[datetime]
    

class goods_info_parme(goods_info_parme_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    product_id: Optional[int]
    key: str
    value: Optional[str]
    shu_ri_qi:Optional[datetime]
    
    
class goods_info_parme_post_base_model(BaseModel):
    product_id: Optional[int]
    key: str
    value: str
    
    
class car_ithem_round_base(SQLModel):
    car_round_uuid: Optional[str]
    create_datetime:Optional[datetime]
    explain : Optional[str]
    
    

class car_ithem_round(car_ithem_round_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    car_round_uuid: Optional[str]
    create_datetime:Optional[datetime]
    explain : Optional[str]
    
    
class car_ithem_round_post(BaseModel):
    car_round_uuid: str
    create_datetime:Optional[datetime]
    explain : Optional[str]
    
    
    
    
class car_ithem_base(SQLModel):
    product_id: Optional[int]
    product_qty:Optional[float]
    added_at:Optional[datetime]
    uuid:Optional[str]
    car_round_uuid:Optional[str]
    

class car_ithem(car_ithem_base, table=True):

    auto_id: Optional[int] = Field(default=None, primary_key=True)
    product_id: Optional[int]
    product_qty:Optional[float]
    added_at:Optional[datetime]
    uuid:Optional[str]
    car_round_uuid:Optional[str]
    
    
class car_ithem_post(BaseModel):
    product_id: int
    product_qty:float
    added_at:Optional[datetime]
    uuid:Optional[str]
    car_round_uuid:Optional[str]
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    


# 资料表名称 pre_order_product 和框架
# class fen_dian_base(SQLModel):
#     fen_dian_id: Optional[int]
#     fen_dian_name: Optional[str]



# class fen_dian(fen_dian_base, table=True):
    
#     auto_id: Optional[int] = Field(default=None, primary_key=True)
#     fen_dian_id: Optional[int]
#     fen_dian_name : Optional[str]



# class put_order_product(order_head_in_base):
#     jiaYiId: Optional[int]
#     orderId: Optional[str]
#     lei : Optional[int]
#     keBian : Optional[int]
#     statusCodeId : Optional[int]

# class pre_order_product_get_base(order_head_in_base):
#     jiaYiId: Optional[int] = None
#     datetime: datetime 
#     orderId: Optional[str]
#     lei : Optional[int]
#     keBian : Optional[int]
#     statusCodeId : Optional[int]
#     dataSubName: dict

    

# # 规定读资料
# class pre_order_product_model_read(BaseModel):
#     autoId: Optional[int]
#     jiaYiId: Optional[int] = None
#     datetime: datetime
#     orderId: Optional[str]
    

# # 输入资料
# class create_pre_order_product_model(BaseModel):
#     jiaYiId: Optional[int] = None
#     datetime: Optional[str] 
#     orderId: Optional[str] 

#     class Config:
#         schema_extra = {
#             "example": {
#                 "jia_yi_id": 1150,
#                 "datetime": "2022-04-22 08:10:33.137",
#             }
#         }
# class dataJson(BaseModel):
#     keBianIdname: str
#     keBianMmName: str = None
#     jiaYiIdname: str 
#     jiaYiMmName: str = None

# # 更新资料
# class create_put_order_product_model(BaseModel):
#     orderId: str
#     statusCodeId: int
#     lei: int
#     jiaYiId: int
#     keBian: int
#     dataSubName:dict
#     dataSub:Optional[dict] = {}

    # class Config:
    #     schema_extra = {
    #         "example": {
    #             "jia_yi_id": 1150,
    #             "datetime": "2022-04-22 08:10:33.137",
    #         }
    #     }

# 资料表名称 pre_order_product_sub 和框架

# class pre_order_product_sub_base(SQLModel):
#     productId: Optional[int]
#     productQty: Optional[float] 
#     productPrice: Optional[float] 
#     productMaxQty: Optional[float] 
#     orderId: Optional[str]
#     dataSubName: dict
#     # orderProductSubId : Optional[str]
#     # datetime : datetime


# class pre_order_product_sub(pre_order_product_sub_base, table=True):
#     autoId: Optional[int] = Field(default=None, primary_key=True)
#     productId: Optional[int]
#     productQty: Optional[float] 
#     productPrice: Optional[float] 
#     productMaxQty: Optional[float] 
#     orderId: Optional[str]
#     dataSubName: dict = Field(sa_column=Column(JSONB), default={})
#     orderProductSubId : Optional[str]
#     datetime : Optional[datetime] 



# # 规定读资料
# class pre_order_product_sub_model_read(pre_order_product_sub_base):
    
#     productId: Optional[int]
#     productQty: Optional[float] 
#     productPrice: Optional[float] 
#     productMaxQty: Optional[float] = 0
#     orderId: Optional[str]
#     orderProductSubId : Optional[str]
#     datetime : Optional[datetime] 



# class dataSubJson(BaseModel):
#     productIdname: str
#     productMmName: str
#     productDName: str = None

# # 输入资料
# class create_pre_order_product_sub_model(pre_order_product_sub_base):

#     productId: int
#     productQty: float
#     productPrice: float
#     productMaxQty: Optional[float] = 0
#     orderId: str
#     dataSubName : dataSubJson
#     orderProductSubId : Optional[str]
#     datetime : Optional[datetime] 

#     # class Config:
#     #     schema_extra = {
#     #         "example": {
#     #             "productId": 0,
#     #             "productQty": 0,
#     #             "productPrice": 0,
#     #             "productMaxQty": 0,
#     #             "orderId": "string",
#     #             "dataSub": {
#     #                 "ProductIdname": 0,
#     #                 "ProductMmName": 0,
#     #                 "ProductDName": 0
#     #             }
#     #         }
#     #     }

# class product_sub_model(pre_order_product_sub_base):
#     productId: int
#     productQty: float
#     productPrice: float
#     productMaxQty: float
#     orderId: str


# class product_sub_model_get(pre_order_product_sub_base):
#     orderId: Optional[int]