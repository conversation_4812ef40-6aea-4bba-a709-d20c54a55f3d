from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel
from typing import List, Optional

from datetime import datetime, timedelta
from src.shwethe_in.database import get_session
from src.time_zone.time_zone_function import get_datetime
from helper import generate_datetime_id
from src.shwethe_in.product_in.models.models import car_ithem,car_ithem_post,car_ithem_round,car_ithem_round_post,goods_info_parme,goods_info_parme_post_base_model,bill_id_s_insert_post,bill_id_s_insert,bill_id_s_post,warehouse_product_in,warehouse_product_in_post,direct_in_post,direct_in,lei_type_list,bill_id_s_insert,product_in_post,product_in
from src.shwethe_in.status_list.models.models import order_body_in
from src.shwethe_in.status_list.crud.crud import check_status_list
from src.common.function.function import get_shwethe_in_goods
import json
import pandas as pd
import requests
import numpy as np
from sqlmodel import func,Session,select,or_,and_,SQLModel

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def check_parner_have_qty(product_id:int ,parner : int ):

    from src.Connect.https_connect import mongodb_data_api
    from src.common.function.function import get_shwethe_in_goods,get_shwethe_in_goods_parner
    
    # B10001 = get_shwethe_in_goods(product_id=product_id)
    B10001 = get_shwethe_in_goods_parner(product_id=product_id)
    
    print(B10001)

    params = {'product_id':product_id}

    r = requests.get(mongodb_data_api + '/api/v1/data/zuotian_maimai/zuotian_maimai_qty_by',
                     params=params)

    GF10001 = pd.DataFrame(r.json())
    r = requests.get(mongodb_data_api + '/api/v1/data/o_ku_zou/o_ku_zuo_by',
                     params=params)

    GF20001 = pd.DataFrame(r.json())

    JH10001 = pd.concat([GF10001,GF20001,B10001])



    if JH10001.empty:

        return pd.DataFrame

    JH10001 = JH10001.groupby(['product_id','jia_fang'])['product_qty'].sum().reset_index()

    JH10001 = JH10001.loc[JH10001['jia_fang'] == parner]

    print(parner)
    print(JH10001)
    print("JH10001")

    if JH10001.empty:
    
        return pd.DataFrame


    JH10001 = JH10001.loc[JH10001['product_qty'] > 0.5 ]

    if JH10001.empty:
        
        return pd.DataFrame

    # JH10004 = JH10001.to_json(orient='records')

    return JH10001


def check_product_parner_have(product_id:int ,parner : int , db: Session = Depends(get_session)):

    V10001 = check_parner_have_qty(product_id=product_id ,parner = parner)


    V10001 

    print(V10001)
    print("V10001")

    if not V10001.empty:
        heroes = db.exec(select(lei_type_list).where(lei_type_list.auto_id == 2)).all()

        df = sqmodel_to_df(heroes)
        print(df)

        print("heroesTrue")

    if  V10001.empty:
        heroes = db.exec(select(lei_type_list).where(and_(lei_type_list.auto_id != 2,lei_type_list.data_sub.op('@>')(json.dumps({'row_type' : 'for_product'}))))).all()

        df = sqmodel_to_df(heroes)
        print(df)
        print("heroesFalse")
        
    if V10001.empty :
        V10001 = pd.DataFrame()


    mata = {
        'product_id': product_id,
        'payment_type' : df.to_dict(orient='records'),
        'price' : 0,
        'product_qty': V10001.to_dict(orient='records'),
    }
    print(mata)

    JH10004 = df.to_json(orient='records')

    return mata


def check_direct_parner_have(product_id:int ,parner : int , db: Session = Depends(get_session)):
    
    V10001 = check_parner_have_qty(product_id=product_id ,parner = parner)

    print(V10001)
    if V10001:
        heroes = db.exec(select(lei_type_list).where(lei_type_list.auto_id != 2)).all()

        df = sqmodel_to_df(heroes)
        print(df)

        print("heroesTrue")

    if not V10001:

        # array_list = [{'row_type' : 'for_product'},{'row_type' : 'for_product'}]


        # heroes = db.exec(select(lei_type_list).where(lei_type_list.data_sub.op('->')('row_type').in_(['for_product','for_product']))).all()

        heroes = db.exec(select(lei_type_list).where(lei_type_list.data_sub.op('->>')("row_type" ).in_ (["for_product","for_car_cost"])  )).all()

        # heroes = db.exec(select(lei_type_list).where(and_(lei_type_list.auto_id != 2,lei_type_list.data_sub.op('@>')(json.dumps({'row_type' : 'for_product'}))))).all()

        df = sqmodel_to_df(heroes)
        print(df)
        print("heroesFalse")


    mata = {
        'product_id': product_id,
        'payment_type' : df.to_dict(orient='records'),
        'price' : 0
    }
    print(mata)

    JH10004 = df.to_json(orient='records')

    return mata


def get_bill(jin_huo_bian:int , db: Session = Depends(get_session)):

    heroes = db.exec(select(bill_id_s_insert).where(and_(bill_id_s_insert.data_sub.op('@>')(json.dumps({'jin_huo_bian' : jin_huo_bian}))))).all()

    df = sqmodel_to_df(heroes)
    print(df)
    print("heroesFalse")

    JH10004 = df.to_json(orient='records')

    return json.loads(JH10004)


def post_bill(hero:bill_id_s_insert_post , db: Session = Depends(get_session)):
    


    # if df.empty:

    hero_1 = bill_id_s_insert(order_s_insert_bill_id="Deadpond", data_sub={'jin_huo_bian' : hero.jin_huo_bian})
    
    # heroes.shu_ri_qi = str(get_datetime())

    db.add(hero_1)
    db.commit()
    db.refresh(hero_1)

    heroes = db.exec(select(bill_id_s_insert).where(and_(bill_id_s_insert.data_sub.op('@>')(json.dumps({'jin_huo_bian' : hero.jin_huo_bian}))))).all()

    df = sqmodel_to_df(heroes)

    JH10004 = df.to_json(orient='records')

    return json.loads(JH10004)


def fun_product_in_post(hero : product_in_post , db: Session = Depends(get_session)):

    heroes = db.exec(select(order_body_in).where(and_(order_body_in.auto_id == hero.order_che_ci_body_in_id))).one()
    
    checkState = check_status_list(heroes.jin_huo_bian,db)

    heroes = db.exec(select(product_in).where(and_(product_in.product_id == hero.product_id,
    product_in.order_che_ci_body_in_id == hero.order_che_ci_body_in_id,product_in.bill_id == hero.bill_id,product_in.product_price_x == hero.product_price_x))).all()
    df = sqmodel_to_df(heroes)


    if not checkState:

        if df.empty:
            hero.shu_ri_qi = str(get_datetime())
            hero_to_db = product_in.from_orm(hero)
            db.add(hero_to_db)
            db.commit()
            db.refresh(hero_to_db)

    heroes = db.exec(select(product_in).where(and_(product_in.order_che_ci_body_in_id == hero.order_che_ci_body_in_id))).all()
    df = sqmodel_to_df(heroes)
    JH10004 = df.to_json(orient='records')

    return json.loads(JH10004)



def fun_product_in_get(order_che_ci_body_in_id : int , db: Session = Depends(get_session)):

    
    heroes = db.exec(select(product_in).where(and_(product_in.order_che_ci_body_in_id == order_che_ci_body_in_id))).all()
    df = sqmodel_to_df(heroes)

    try:
        df['product_price_sum'] = (df['product_price_x'] * df['product_qty'])
    except:
        pass


    JH10004 = df.to_json(orient='records')

    return json.loads(JH10004)


def fun_product_in_delete(auto_id : int , db: Session = Depends(get_session)):
    
    
    # heroes = db.exec(select(product_in).where(and_(product_in.order_che_ci_body_in_id == order_che_ci_body_in_id))).all()
    # df = sqmodel_to_df(heroes)
    # JH10004 = df.to_json(orient='records')

    heroes2 = db.exec(select(product_in).where(and_(product_in.auto_id == auto_id))).one()

    heroes = db.exec(select(order_body_in).where(and_(order_body_in.auto_id == heroes2.order_che_ci_body_in_id))).one()

    checkState = check_status_list(heroes.jin_huo_bian,db)

    if not checkState:

        statement = select(product_in).where(product_in.auto_id == auto_id)
        results = db.exec(statement)
        hero = results.one()
        print("Hero: ", hero)
        db.delete(hero)
        db.commit()

        print("Deleted hero:", hero)

    return 200





def fun_direct_in_post(hero : direct_in_post , db: Session = Depends(get_session)):

    heroes5 = db.exec(select(order_body_in).where(and_(order_body_in.auto_id == hero.order_che_ci_body_in_id))).one()

    print(hero)

    print(heroes5)
    print("heroes5")
    
    checkState = check_status_list(heroes5.jin_huo_bian , db)

    
    heroes = db.exec(select(direct_in).where(and_(direct_in.product_id == hero.product_id,direct_in.product_price_x == hero.product_price_x,direct_in.order_che_ci_body_in_id == hero.order_che_ci_body_in_id))).all()
    df = sqmodel_to_df(heroes)
    print("heroesFalse")

    if not checkState:

        if df.empty:
            hero.shu_ri_qi = str(get_datetime())
            hero_to_db = direct_in.from_orm(hero)
            db.add(hero_to_db)
            db.commit()
            db.refresh(hero_to_db)

    heroes = db.exec(select(direct_in).where(and_(direct_in.order_che_ci_body_in_id == heroes5.auto_id))).all()
    df = sqmodel_to_df(heroes)
    JH10004 = df.to_json(orient='records')

    return json.loads(JH10004)



def fun_direct_in_get(order_che_ci_body_in_id : int , db: Session = Depends(get_session)):

    
    heroes = db.exec(select(direct_in).where(and_(direct_in.order_che_ci_body_in_id == order_che_ci_body_in_id))).all()
    df = sqmodel_to_df(heroes)
    JH10004 = df.to_json(orient='records')

    return json.loads(JH10004)


def fun_direct_in_delete(auto_id : int , db: Session = Depends(get_session)):
    
    heroes2 = db.exec(select(direct_in).where(and_(direct_in.auto_id == auto_id))).one()

    heroes = db.exec(select(order_body_in).where(and_(order_body_in.auto_id == heroes2.order_che_ci_body_in_id))).one()

    checkState = check_status_list(heroes.jin_huo_bian,db)

    if not checkState:
        statement = select(direct_in).where(direct_in.auto_id == auto_id)
        results = db.exec(statement)
        hero = results.one()
        print("Hero: ", hero)

        db.delete(hero)
        db.commit()

        print("Deleted hero:", hero)

    return 200


def warehourse_in_post(hero : warehouse_product_in_post , db: Session = Depends(get_session)):

    checkState = check_status_list(hero.jin_huo_bian,db)

    A100001 = get_datetime() - timedelta(days=1)
    A100001 = datetime(A100001.year, A100001.month, A100001.day)
    

    heroes = db.exec(select(warehouse_product_in).where(and_(warehouse_product_in.product_id == hero.product_id,
    warehouse_product_in.jin_huo_bian == hero.jin_huo_bian,
    warehouse_product_in.jia_yi_fang == hero.jia_yi_fang,
    warehouse_product_in.shu_ri_qi > A100001))).all()
    
    df = sqmodel_to_df(heroes)

    print(df)
    print("df----------")


    if not checkState:

        if df.empty:
            hero.shu_ri_qi = str(get_datetime())
            hero_to_db = warehouse_product_in.from_orm(hero)
            db.add(hero_to_db)
            db.commit()
            db.refresh(hero_to_db)

    heroes = db.exec(select(warehouse_product_in).where(and_(warehouse_product_in.jin_huo_bian == hero.jin_huo_bian,
    warehouse_product_in.jia_yi_fang == hero.jia_yi_fang))).all()
    df = sqmodel_to_df(heroes)
    JH10004 = df.to_json(orient='records')

    return json.loads(JH10004)

def warehourse_in_get(jin_huo_bian : int , jia_yi_fang:int , db: Session = Depends(get_session)):
    

    heroes = db.exec(select(warehouse_product_in).where(and_(warehouse_product_in.jia_yi_fang == jia_yi_fang,
    warehouse_product_in.jin_huo_bian == jin_huo_bian))).all()

    df = sqmodel_to_df(heroes)
    JH10004 = df.to_json(orient='records')

    return json.loads(JH10004)


def warehourse_in_delete(auto_id : int , db: Session = Depends(get_session)):

    A100001 = get_datetime() - timedelta(days=1)
    A100001 = datetime(A100001.year, A100001.month, A100001.day)


    heroes = db.exec(select(warehouse_product_in).where(and_(warehouse_product_in.auto_id == auto_id
    ,warehouse_product_in.shu_ri_qi > A100001))).one()
    
    checkState = check_status_list(heroes.jin_huo_bian,db)

    if not checkState:
        statement = select(warehouse_product_in).where(warehouse_product_in.auto_id == auto_id)
        results = db.exec(statement)
        hero = results.one()
        print("Hero: ", hero)

        db.delete(hero)
        db.commit()

        print("Deleted hero:", hero)

    return 200



def getDataListBillbyjinHuoBian(jin_huo_bian : int , db: Session = Depends(get_session)):


    def getHead(jin_huo_bian):
        
        statement = select(order_body_in).where(and_(order_body_in.jin_huo_bian == jin_huo_bian))
        results = db.exec(statement)
        hero = results.all()

        hero = sqmodel_to_df(hero)

        A100002 = hero.to_dict('records')
        A100003 = pd.json_normalize(A100002)

        return A100003
    
    def getDataA(df):
    
        statement = select(product_in).where(and_(product_in.order_che_ci_body_in_id.in_(df['auto_id'].values.tolist())))
        results = db.exec(statement)
        hero = results.all()

        hero = sqmodel_to_df(hero)

        hero = hero.to_dict('records')
        hero = pd.json_normalize(hero)

        return hero


    def getDataB(df):


        statement = select(direct_in).where(direct_in.order_che_ci_body_in_id.in_(df['auto_id'].values.tolist()))
        results = db.exec(statement)
        hero = results.all()

        hero = sqmodel_to_df(hero)
        hero = hero.to_dict('records')
        hero = pd.json_normalize(hero)

        return hero



    T100001 = getHead(jin_huo_bian)

    if T100001.empty:
        return []

    try:
        A100001 = getDataA(T100001)
        print(A100001['order_che_ci_body_in_id'])
        print(T100001['auto_id'])
        print(A100001.info())
        A100001 = A100001.merge(T100001, left_on=['order_che_ci_body_in_id'], right_on=['auto_id'], how='inner')
        A100001['ans'] = (A100001['product_price_x'] * A100001['product_qty'])
        A100002 = A100001.groupby(['bill_id','data_sub_name.jia_yi_fang_idname','data_sub_name.jia_yi_fang_mmname','data_sub_name.bi_zhi_name'])['ans'].sum().reset_index()
        A100002
    except:
        A100002 = pd.DataFrame()

    try:
        B100001 = getDataB(T100001)
        print(B100001.info())
        B100001 = B100001.merge(T100001, left_on=['order_che_ci_body_in_id'], right_on=['auto_id'], how='inner')
        B100001['ans'] = (B100001['product_price_x'] * B100001['product_qty'])
        B100002 = B100001.groupby(['bill_id','data_sub_name.jia_yi_fang_idname','data_sub_name.jia_yi_fang_mmname'
        ,'data_sub_name.bi_zhi_name'])['ans'].sum().reset_index()
        B100002
    except:
        B100002 = pd.DataFrame()

    NB10001 = pd.concat([A100002,B100002])

    if NB10001.empty:
        return []

    NB10002 = NB10001.groupby(['bill_id','data_sub_name.jia_yi_fang_idname','data_sub_name.jia_yi_fang_mmname','data_sub_name.bi_zhi_name'])['ans'].sum().reset_index()
    NB10002

    NB10002 = NB10002.rename(columns={'data_sub_name.jia_yi_fang_idname': 'jia_yi_fang_idname',
    'data_sub_name.jia_yi_fang_mmname': 'jia_yi_fang_mmname','data_sub_name.bi_zhi_name': 'bi_zhi_name'})

    print(NB10002)

    print(NB10002.info())

    NB10003 = NB10002.to_json(orient='records')

    return json.loads(NB10003)


def getDataListProductCompilebyjinHuoBian(jin_huo_bian : int , db: Session = Depends(get_session)):
    

    def getHead(jin_huo_bian):
        
        statement = select(order_body_in).where(and_(order_body_in.jin_huo_bian == jin_huo_bian))
        results = db.exec(statement)
        hero = results.all()
        hero = sqmodel_to_df(hero)
        A100002 = hero.to_dict('records')
        A100003 = pd.json_normalize(A100002)


        return A100003
    
    def getDataA(df):


    
        statement = select(product_in).where(and_(product_in.order_che_ci_body_in_id.in_(df['auto_id'].values.tolist())))
        results = db.exec(statement)
        hero = results.all()
        hero = sqmodel_to_df(hero)
        A100002 = hero.to_dict('records')
        A100003 = pd.json_normalize(A100002)

        A100003 = A100003.replace(np.nan, '')


        return A100003


    def getDataB(jin_huo_bian):

        # print("123456789df")
        # print(jin_huo_bian)

        statement = select(warehouse_product_in).where(warehouse_product_in.jin_huo_bian == jin_huo_bian)
        results = db.exec(statement)
        hero = results.all()

        hero = sqmodel_to_df(hero)

        A100002 = hero.to_dict('records')
        A100003 = pd.json_normalize(A100002)

        A100003 = A100003.replace(np.nan, '')


        # print(A100003)


        return A100003


    def getDataC(df):

        # print(df['auto_id'].values.tolist())
    
        statement = select(direct_in).where(and_(direct_in.order_che_ci_body_in_id.in_(df['auto_id'].values.tolist())))
        results = db.exec(statement)
        hero = results.all()
        hero = sqmodel_to_df(hero)
        A100002 = hero.to_dict('records')
        A100003 = pd.json_normalize(A100002)

        A100003 = A100003[['product_id','data_sub_name.product_idname','data_sub_name.product_mm_name','data_sub_name.product_d_name','product_qty']]

        # A100003['product_qty_x'] = A100003['product_qty']
        # A100003['product_qty_y'] = A100003['product_qty']

        A100003 = A100003.replace(np.nan, '')

        # print(hero)


        return A100003


    T100001 = getHead(jin_huo_bian)

    # print(T100001['auto_id'])
    # print("T100001")

    if T100001.empty:
        return []

    # print(T100001.info())

    try:
        A100002 = getDataA(T100001)

        # print(A100002)

    except:
        A100002 = pd.DataFrame()



    try:
        B100002 = getDataB(jin_huo_bian)
        # print(B100002)

    except:
        B100002 = pd.DataFrame()

    try:

        A100003 = getDataC(T100001)

        # print(A100002)

    except:
        A100003 = pd.DataFrame()




    # print(A100002.info())

    def groupbyData(data):
        if not data.empty:

            data['date_column'] = pd.to_datetime(data['shu_ri_qi']).dt.date
            CC10001 = data.groupby(['date_column','product_id','data_sub_name.product_idname','data_sub_name.product_mm_name','data_sub_name.product_d_name'])['product_qty'].sum().reset_index()
            CC10001
        else:
            CC10001 = pd.DataFrame()

        return CC10001



    CC10001 = groupbyData(A100002)

    CP10001 = groupbyData(B100002)

    if not CP10001.empty:

        # AB20001 = CP10001[['product_id','data_sub_name.product_idname','data_sub_name.product_mm_name','data_sub_name.product_d_name']]
        AB20001 = CP10001[['product_id']]

    else:
        AB20001 = pd.DataFrame()
    if not CP10001.empty:
        
        AB10001 = CC10001[['product_id']]
        # AB10001 = CC10001[['product_id','data_sub_name.product_idname','data_sub_name.product_mm_name','data_sub_name.product_d_name']]

    else:
        AB10001 = pd.DataFrame()

    

    T100001 = pd.concat([CP10001,CC10001,A100003])

    if T100001.empty:
        return []

    T100001 = T100001.sort_values(['product_id'], ascending=[False])

    T100001 = T100001.drop_duplicates(subset=['product_id'], keep='first')
    # print(A100003.info())
    

    # CC10001 = A100002.groupby(['product_id','data_sub_name.product_idname','data_sub_name.product_mm_name','data_sub_name.product_d_name'])['product_qty'].sum().reset_index()
    # CC10001

    # CP10001 = B100002.groupby(['product_id','data_sub_name.product_idname','data_sub_name.product_mm_name','data_sub_name.product_d_name'])['product_qty'].sum().reset_index()
    # CP10001


    if CC10001.empty and CP10001.empty and A100003.empty:

        return []

    if not CC10001.empty or not CP10001.empty:



        SD10001 = CC10001.merge(CP10001, left_on=['product_id','date_column'], right_on=['product_id','date_column'], how='outer')
    else:
        SD10001 = pd.DataFrame()


    # print(SD10001)
    if not A100003.empty:
        A100003 = A100003[['product_id','product_qty','product_qty']]
        A100003.columns = ['product_id','product_qty_x','product_qty_y']
    else:
        A100003 = pd.DataFrame()

    SD10001 = pd.concat([SD10001,A100003])

    SD10001 = SD10001.merge(T100001, left_on=['product_id'], right_on=['product_id'], how='outer')

    # print(SD10001.info())

    # SD10001 = SD10001.rename(columns={'data_sub_name.product_idname_x': 'product_idname_x',
    # 'data_sub_name.product_mm_name_x': 'product_mm_name_x',
    # 'data_sub_name.product_d_name_x': 'product_d_name_x',
    # 'data_sub_name.product_qty_x': 'product_qty_x',
    # 'data_sub_name.product_idname_y': 'product_idname_y',
    # 'data_sub_name.product_mm_name_y': 'product_mm_name_y',
    # 'data_sub_name.product_d_name_y': 'product_d_name_y',
    # 'data_sub_name.product_qty_y': 'product_qty_y'})

    SD10001 = SD10001.rename(columns={
    'data_sub_name.product_idname': 'product_idname',
    'data_sub_name.product_mm_name': 'product_mm_name',
    'data_sub_name.product_d_name': 'product_d_name',
    'data_sub_name.product_qty_x': 'product_qty_x',
    # 'data_sub_name.product_idname': 'product_idname_y',
    # 'data_sub_name.product_mm_name': 'product_mm_name_y',
    # 'data_sub_name.product_d_name': 'product_d_name_y',
    'data_sub_name.product_qty_y': 'product_qty_y'
    })

    print(SD10001.info())
    SD10001 = SD10001.replace(np.nan, 0)
    SD10001['product_check_qty'] = (SD10001['product_qty_x'] - SD10001['product_qty_y']).abs()
    SD10001['product_check_qty'] = (SD10001['product_check_qty'] > 0 )
    SD10001 = SD10001.sort_values(['product_check_qty'], ascending=[False])

    SD10001 = SD10001.to_json(orient='records')

    return json.loads(SD10001)



def goods_info_parme_post(hero : goods_info_parme_post_base_model , db: Session = Depends(get_session)):
    # insert to sql goods_info_parme
    hero2 = goods_info_parme(product_id=hero.product_id,key=hero.key,value=hero.value,shu_ri_qi=datetime.now())
    db.add(hero2)
    db.commit()
    db.refresh(hero2)
    return hero2



# def getDataListProductCompilebyjinHuoBian(jin_huo_bian : int , db: Session = Depends(get_session)):
    

#     def getHead(jin_huo_bian):
        
#         statement = select(order_body_in).where(and_(order_body_in.jin_huo_bian == jin_huo_bian))
#         results = db.exec(statement)
#         hero = results.all()
#         hero = sqmodel_to_df(hero)
#         A100002 = hero.to_dict('records')
#         A100003 = pd.json_normalize(A100002)

#         return A100003
    
#     def getDataA(df):
    
#         statement = select(direct_in).where(and_(direct_in.order_che_ci_body_in_id.in_(df['auto_id'].values.tolist())))
#         results = db.exec(statement)
#         hero = results.all()
#         hero = sqmodel_to_df(hero)
#         A100002 = hero.to_dict('records')
#         A100003 = pd.json_normalize(A100002)

#         return A100003


#     # def getDataB(jin_huo_bian):

#     #     statement = select(warehouse_product_in).where(warehouse_product_in.jin_huo_bian == jin_huo_bian)
#     #     results = db.exec(statement)
#     #     hero = results.all()

#     #     hero = sqmodel_to_df(hero)

#     #     A100002 = hero.to_dict('records')
#     #     A100003 = pd.json_normalize(A100002)

#     #     return A100003

#     T100001 = getHead(jin_huo_bian)

#     # print(T100001['auto_id'])
#     # print("T100001")

#     if T100001.empty:
#         return []

#     # print(T100001.info())

#     try:
#         A100002 = getDataA(T100001)

#         print(A100002)

#     except:
#         A100002 = pd.DataFrame()

#     # try:
#     #     B100002 = getDataB(jin_huo_bian)
#     #     print(B100002)

#     # except:
#     #     B100002 = pd.DataFrame()




#     print(A100002.info())

#     def groupbyData(data):
#         if not data.empty:
#             CC10001 = data.groupby(['product_id','data_sub_name.product_idname','data_sub_name.product_mm_name','data_sub_name.product_d_name'])['product_qty'].sum().reset_index()
#             CC10001
#         else:
#             CC10001 = pd.DataFrame()

#         return CC10001

#     CC10001 = groupbyData(A100002)

#     # CP10001 = groupbyData(B100002)

#     # CC10001 = A100002.groupby(['product_id','data_sub_name.product_idname','data_sub_name.product_mm_name','data_sub_name.product_d_name'])['product_qty'].sum().reset_index()
#     # CC10001

#     # CP10001 = B100002.groupby(['product_id','data_sub_name.product_idname','data_sub_name.product_mm_name','data_sub_name.product_d_name'])['product_qty'].sum().reset_index()
#     # CP10001


#     if CC10001.empty or CP10001.empty:

#         return []

#     SD10001 = CC10001.merge(CP10001, left_on=['product_id'], right_on=['product_id'], how='outer')

#     SD10001 = SD10001.rename(columns={'data_sub_name.product_idname_x': 'product_idname_x',
#     'data_sub_name.product_mm_name_x': 'product_mm_name_x',
#     'data_sub_name.product_d_name_x': 'product_d_name_x',
#     'data_sub_name.product_qty_x': 'product_qty_x',
#     'data_sub_name.product_idname_y': 'product_idname_y',
#     'data_sub_name.product_mm_name_y': 'product_mm_name_y',
#     'data_sub_name.product_d_name_y': 'product_d_name_y',
#     'data_sub_name.product_qty_y': 'product_qty_y'})

#     SD10001 = SD10001.to_json(orient='records')

#     return json.loads(SD10001)






# def create_hero(hero: HeroCreate, db: Session = Depends(get_session)):
#     hero_to_db = Hero.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
#     return hero_to_db





# def read_hero(hero_id: int, db: Session = Depends(get_session)):
#     hero = db.get(Hero, hero_id)
#     if not hero:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {hero_id}",
#         )
#     return hero


# def update_hero(hero_id: int, hero: HeroUpdate, db: Session = Depends(get_session)):
#     hero_to_update = db.get(Hero, hero_id)
#     if not hero_to_update:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {hero_id}",
#         )

#     team_data = hero.dict(exclude_unset=True)
#     for key, value in team_data.items():
#         setattr(hero_to_update, key, value)

#     db.add(hero_to_update)
#     db.commit()
#     db.refresh(hero_to_update)
#     return hero_to_update


# def delete_hero(hero_id: int, db: Session = Depends(get_session)):
#     hero = db.get(Hero, hero_id)
#     if not hero:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=f"Hero not found with id: {hero_id}",
#         )

#     db.delete(hero)
#     db.commit()
#     return {"ok": True}





# def create_pre_order_product(hero: pre_order_product_model_read, db: Session = Depends(get_session)):

#     hero.datetime = str(datetime.now())
#     hero.orderId = generate_datetime_id()
#     hero_to_db = pre_order_product.from_orm(hero)
    
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)

#     return hero_to_db


# def create_put_order_sub_product(hero: create_put_order_product_model, db: Session = Depends(get_session)):
#     # print(hero)
#     # hero.datetime = str(datetime.now())
#     # hero.orderProductSubId = generate_datetime_id()

#     statement = select(pre_order_product).where(pre_order_product.orderId == hero.orderId)
#     results = db.exec(statement).one()

#     print("results:", results)
#     print("hero:", hero)


#     results.lei = hero.lei
#     results.keBian = hero.keBian
#     results.statusCodeId = hero.statusCodeId
#     results.jiaYiId = hero.jiaYiId
#     results.dataSubName = hero.dataSubName
#     results.dataSub = hero.dataSub

#     db.add(results)
#     db.commit()
#     db.refresh(results)
    
#     return results


# def create_pre_order_sub_product(hero: pre_order_product_sub_model_read, db: Session = Depends(get_session)):
#     # print(hero)
#     hero.datetime = str(datetime.now())
#     hero.orderProductSubId = generate_datetime_id()
#     hero_to_db = pre_order_product_sub.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
    
#     return hero_to_db




# def get_pre_order_sub_product(orderId:str, db: Session = Depends(get_session)):
#     print(orderId)
#     # hero_to_db = pre_order_product_sub.from_orm(hero)

#     # db.add(hero_to_db)
#     # db.commit()
#     # db.refresh(hero_to_db)

#     # def read_hero(hero_id: int, db: Session = Depends(get_session)):
#     #     hero = db.get(Hero, hero_id)
#     # if not hero:
#     #     raise HTTPException(
#     #         status_code=status.HTTP_404_NOT_FOUND,
#     #         detail=f"Hero not found with id: {hero_id}",
#     #     )
#     # return hero
#     # heroes = db.exec(select(Hero).offset(offset).limit(limit)).all()
#     statement = select(pre_order_product_sub).where(pre_order_product_sub.orderId == orderId)
#     results = db.exec(statement).all()

#     for hero in results:
#         print(hero)
#     print(results)
#     print("results")
#     # hero = db.get(pre_order_product_sub_base, orderId)
#     if not results:
#         # raise HTTPException(
#         #     status_code=status.HTTP_404_NOT_FOUND,
#         #     detail=f"Hero not found with id: {orderId}",
#         # )
#         return []
        
#     return results
    


# def create_get_order_sub_product(orderId:str, hero: pre_order_product_sub_model_read, db: Session = Depends(get_session)):
#     # print(hero)
#     hero_to_db = pre_order_product_sub.from_orm(hero)
#     db.add(hero_to_db)
#     db.commit()
#     db.refresh(hero_to_db)
    
#     return hero_to_db



# def getDataSubProduct(db: Session = Depends(get_session)):

#     heroes = db.exec(select(pre_order_product).offset(offset).limit(limit)).all()
    
#     return heroes


# def get_pre_order_list(db: Session = Depends(get_session)):
   
#     heroes = db.exec(select(pre_order_product)).all()

#     df = sqmodel_to_df(heroes)

#     df = df.sort_values(['datetime'], ascending=[False])

#     # df10001 = df.to_dict('records')

#     # df10002 = pd.json_normalize(df10001)

#     # df10003 = df10002.rename(columns={'dataSubName.jiaYiIdname': 'jiaYiIdname','dataSubName.jiaYiMmName': 'jiaYiMmName'
#     # ,'dataSubName.keBianIdname': 'keBianIdname','dataSubName.keBianMmName': 'keBianMmName'})
    

#     df10004 = df.to_json(orient='records')

    
#     return json.loads(df10004)
from uuid import uuid4


def post_car_ithem_round(hero=car_ithem_round_post,db: Session = Depends(get_session)):
    # insert to car_ithem_round table
    
    hero.car_round_uuid = str(uuid4())
    hero.create_datetime = datetime.now()
    
    hero2 = car_ithem_round(**hero.dict())
    db.add(hero2)
    db.commit()
    db.refresh(hero2)
    return hero2


def get_car_item_round(days: int, db: Session = Depends(get_session)):
    # Get current date and time
    now = datetime.now()

    # Calculate the start date based on the number of days
    start_date = (now - timedelta(days=days)).date()
    end_date = now.date()
    
    # Fetch the car_item_round records within the given date range
    car_items = db.query(car_ithem_round).filter(
        func.date(car_ithem_round.create_datetime).between(start_date, end_date)
    ).all()
    
    if not car_items:
        # If no records are found, raise a 404 Not Found error
        raise HTTPException(status_code=404, detail="No car items found for the given range")
    
    # Return the fetched records
    return car_items

def post_car_ithem(hero=car_ithem_post, db: Session = Depends(get_session)):
    # Check if there is already data in the table with the same unique identifier (e.g., car_id)
    existing_car_item = db.query(car_ithem).filter(car_ithem.uuid == hero.uuid,car_ithem.car_round_uuid == hero.car_round_uuid).first()
    if existing_car_item:
        raise HTTPException(status_code=400, detail="Car item with this ID already exists. Insert not allowed.")

    # Generate a new UUID
    hero.added_at = datetime.now()

    # Create a new CarItem instance using the provided data
    hero2 = car_ithem(**hero.dict())

    # Add the new instance to the session and commit it
    db.add(hero2)
    db.commit()
    db.refresh(hero2)
    
    return hero2

from src.common.function.function import merge_dataframe_on_keyword_product


# def is_uuid(value):
#     """Check if a value is a valid UUID."""
#     try:
#         return bool(re.match(r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$', str(value)))
#     except:
#         return False

def get_car_item(uuid: str, db: Session = Depends(get_session)):
    # Fetch the car_item_round records with the given uuid
    car_items = db.query(car_ithem).filter(car_ithem.car_round_uuid == uuid).all()
    
    if not car_items:
        # If no records are found, raise a 404 Not Found error
        raise HTTPException(status_code=404, detail="No car items found for the given uuid")
    
    A10001 = sqmodel_to_df(car_items)
    A10001 = merge_dataframe_on_keyword_product(A10001, 'product_id',images=True,weight_data=True)
    A10001 = A10001.sort_values(['mm_name'], ascending=[False])
    A10001 = A10001.replace({np.nan: 0})
    # Ensure all data except UUID fields is UTF-8 encoded
    # for col in A10001.columns:
    #     if not is_uuid(A10001[col].iloc[0]):  # Check if the first value in the column is a UUID
    #         A10001[col] = A10001[col].apply(lambda x: x.encode('utf-8', errors='ignore').decode('utf-8', errors='ignore') if isinstance(x, str) else x)
    
    print(A10001)
    print(A10001.info())
    
    A10001_json = A10001.to_dict(orient='records')
    
    # Return the fetched records as JSON
    return A10001_json