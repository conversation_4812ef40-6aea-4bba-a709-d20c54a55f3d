from xmlrpc.client import DateTime
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id


class address_info_base(SQLModel):
    fen: Optional[int]
    phone: dict 
    addresses : Optional[int]

# 创建资料表 order_product

class address_info(address_info_base, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    fen: Optional[int]
    phone: dict = Field(sa_column=Column(JSONB), default={})
    addresses : Optional[int]
    

# 规定读资料
class order_product_model_read(address_info_base):
    phone: dict 
    addresses : str


