from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,or_,and_
from typing import List, Optional
import numpy as np
from datetime import datetime
from src.shwethe_api_n_v2.database import get_session
from helper import generate_datetime_id

from src.shwethe_api_n_v2.adresses.models.models import address_info


import json
import pandas as pd 

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df




def getDataAdresses(fen:int, db: Session = Depends(get_session)):


    heroes = db.exec(select(address_info).where(address_info.fen.in_([fen]))).all()

    df1 = sqmodel_to_df(heroes)

    # if df1.empty:
    
    #     return []

    df2 = df1.to_json(orient='records')


    return json.loads(df2)
