from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_in.database import get_session

from src.shwethe_in.adresses.crud.crud import (
    getDataAdresses
)
from src.shwethe_in.adresses.models.models import order_product_model_read

router = APIRouter()

@router.get("/{fen}", response_model=List[order_product_model_read])
def create_a_hero(fen: int , db: Session = Depends(get_session)):
    
    return getDataAdresses(fen=fen, db=db)
