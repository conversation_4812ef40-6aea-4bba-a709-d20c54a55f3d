from xmlrpc.client import DateTime
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id


class order_product_base(SQLModel):
    cheCi: Optional[int]
    orderProductId: Optional[str]
    jiaYiFang : Optional[int]
    shuRiqiDatetime: Optional[datetime] 
    riqiDatetime: Optional[datetime] 
    keBian : Optional[int]
    statusCodeId : Optional[int]
    dataSubName: dict 

# 创建资料表 order_product

# class order_product(order_product_base, table=True):
#     autoId: Optional[int] = Field(default=None, primary_key=True)
#     che_ci: Optional[int]
#     orderProductRId: Optional[str]
#     orderProductSubId : Optional[str]
#     jiaYiFang : Optional[int]
#     shuRiqiDatetime: Optional[datetime] 
#     riqiDatetime: Optional[datetime] 
#     keBian : Optional[int]
#     statusCodeId : Optional[int]
#     dataSubName: dict = Field(sa_column=Column(JSONB), default={})

class order_product(order_product_base, table=True):
    autoId: Optional[int] = Field(default=None, primary_key=True)
    cheCi: Optional[int]
    orderProductId: Optional[str]
    jiaYiFang : Optional[int]
    shuRiqiDatetime: Optional[datetime] 
    riqiDatetime: Optional[datetime] 
    keBian : Optional[int]
    statusCodeId : Optional[int]
    dataSubName: dict = Field(sa_column=Column(JSONB), default={})

# 规定读资料
class order_product_model_read(order_product_base):
    che_ci: Optional[int]
    orderProductId: Optional[str]
    jiaYiFang : Optional[int]
    shuRiqiDatetime: datetime
    riqiDatetime: datetime
    keBian : Optional[int]
    statusCodeId : Optional[int]
    dataSubName: dict 


# 输入资料
class order_product_model(BaseModel):
    jiaYiFang: Optional[int] = None
    cheCi: Optional[int]
    orderProductId: Optional[str]
    shuRiqiDatetime: Optional[datetime]
    riqiDatetime: Optional[datetime]
    keBian : Optional[int]
    statusCodeId : Optional[int]
    dataSubName: dict

    class Config:
        schema_extra = {
            "example": {
                "jiaYiFang": 1150,
                "datetime": "2022-04-22 08:10:33.137",
                "dataSubName":{}
            }
        }


# -》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》

class order_product_sub_base(SQLModel):
    orderProductRid: Optional[int]
    orderProductSubId: Optional[str]
    productId : Optional[int]
    productQty: Optional[datetime] 
    productPrice: Optional[float] 
    biZhi : Optional[int]
    fenDianId : Optional[int]
    fromLine : Optional[str]
    dataSubName: dict 
    shuRiqiDatetime:Optional[datetime] 

# 创建资料表 order_product

# class order_product(order_product_base, table=True):
#     autoId: Optional[int] = Field(default=None, primary_key=True)
#     che_ci: Optional[int]
#     orderProductRId: Optional[str]
#     orderProductSubId : Optional[str]
#     jiaYiFang : Optional[int]
#     shuRiqiDatetime: Optional[datetime] 
#     riqiDatetime: Optional[datetime] 
#     keBian : Optional[int]
#     statusCodeId : Optional[int]
#     dataSubName: dict = Field(sa_column=Column(JSONB), default={})

class order_product_sub(order_product_sub_base, table=True):
    autoId: Optional[int] = Field(default=None, primary_key=True)
    orderProductRid: Optional[str]
    orderProductSubId: Optional[str]
    productId : Optional[int]
    productQty: Optional[float] 
    productPrice: Optional[float] 
    shuRiqiDatetime:Optional[datetime] 
    biZhi : Optional[int]
    fenDianId : Optional[int]
    fromLine : Optional[str]
    dataSubName: dict = Field(sa_column=Column(JSONB), default={})

# 规定读资料
class order_product_sub_model_read(order_product_base):
    orderProductRid: Optional[str]
    orderProductSubId: Optional[str]
    productId : Optional[int]
    productQty: Optional[float] 
    productPrice: Optional[float] 
    shuRiqiDatetime:Optional[datetime] 
    biZhi : Optional[int]
    fenDianId : Optional[int]
    fromLine : Optional[str]
    dataSubName: dict 


# 输入资料
class create_order_product_sub_model(BaseModel):
    orderProductRid: Optional[str]
    orderProductSubId: Optional[str]
    productId : Optional[int]
    productQty: Optional[float] 
    shuRiqiDatetime:Optional[datetime] 
    productPrice: Optional[float] 
    biZhi : Optional[int]
    fenDianId : Optional[int]
    fromLine : Optional[str]
    dataSubName: dict 

    class Config:
        schema_extra = {
            "example": {
                "productId": 1150,
                "orderProductRid":"test",
                "dataSubName":{}
            }
        }








# -》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》
class main_order_product_sub_model(BaseModel):
    productId: Optional[int]
    productQty: Optional[float]
    productPrice: Optional[float]
    productMaxQty: Optional[float]
    orderId: Optional[str]
    orderProductSubId: Optional[str]
    dataSubName_x:Optional[dict]
    dataSubName_y:Optional[dict]
    productWantQty:Optional[float]
    productQtyAns:Optional[float]
    
    