from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_in.database import get_session

from src.shwethe_in.main_order.crud.crud import (
    getProductForUser,
    create_order_product,
    create_order_sub_product,
    get_order_sub_product,
    status_list_list
)
from src.shwethe_in.pre_order.models.models import pre_order_product_get_base,product_sub_model
from src.shwethe_in.main_order.models.models import create_order_product_sub_model,order_product_sub_model_read,order_product_model,main_order_product_sub_model,order_product_model_read

router = APIRouter()

@router.get("/get_Product/{userId}", response_model=List[main_order_product_sub_model])
def create_a_hero(userId: int , db: Session = Depends(get_session)):
    
    return getProductForUser(userId=userId, db=db)


@router.post("", response_model=order_product_model_read)
def postProduct(hero: order_product_model , db: Session = Depends(get_session)):
    
    return create_order_product(hero=hero, db=db)


@router.post("/sub_order", response_model=order_product_sub_model_read)
def sub_order(hero: create_order_product_sub_model, db: Session = Depends(get_session)):
    
    return create_order_sub_product(hero=hero, db=db)


@router.get("/sub_order", response_model=List[order_product_sub_model_read])
def get_sub_order(orderProductRid: str, db: Session = Depends(get_session)):
    
    return get_order_sub_product(orderProductRid=orderProductRid, db=db)

@router.get("/status_list", response_model=List[order_product_sub_model_read])
def status_list(db: Session = Depends(get_session)):
    
    return status_list_list(db=db)