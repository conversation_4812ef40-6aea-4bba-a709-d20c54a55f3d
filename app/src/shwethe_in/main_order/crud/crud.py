from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,or_,and_
from typing import List, Optional
import numpy as np
from datetime import datetime
from src.shwethe_api_n_v2.database import get_session
from helper import generate_datetime_id

from src.shwethe_api_n_v2.pre_order.models.models import pre_order_product_sub,pre_order_product
from src.shwethe_api_n_v2.main_order.models.models import order_product_sub,order_product_model_read,order_product,order_product_sub_model_read
# from src.shwethe_api_n_v2.main_order.models.models import pre_order_product,pre_order_product_model_read,Hero, HeroCreate, HeroUpdate


import json
import pandas as pd 

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df




def getsubProductOrderId(df, db: Session = Depends(get_session)):

    print(df['orderProductSubId'])

    heroes = db.exec(select(order_product_sub).where(order_product_sub.orderProductSubId.in_(df['orderProductSubId'].values.tolist()))).all()

    df1 = sqmodel_to_df(heroes)

    # if df1.empty:
    
    #     return []

    return df1

def getProductForUser(userId:int, db: Session = Depends(get_session)):
    # 获取客户的orderID
    heroes = db.exec(select(pre_order_product).where(and_(pre_order_product.jiaYiId == int(userId),pre_order_product.statusCodeId == 200))).all()
    
    df1 = sqmodel_to_df(heroes)

    # print(df1['orderId'].values.tolist())

    if df1.empty:

        return []

    heroes = db.exec(select(pre_order_product_sub).where(pre_order_product_sub.orderId.in_(df1['orderId'].values.tolist()))).all()

    df2 = sqmodel_to_df(heroes)


    df3 = df1.merge(df2, on=['orderId'], how='inner')


    if df3.empty:

        return []

    FD10001 = getsubProductOrderId(df3,db)

    # df4 = df3.rename(columns={"dataSubName_x": "dataSubName_x", "dataSubName_y": "dataSubName"})
    if not FD10001.empty:

        FD10002 = FD10001[['orderProductSubId','productQty','productId']]
        FD10003 = FD10002.replace(np.nan, 0)

        FD10004 = FD10003.rename(columns={"productQty": "productWantQty"})

        FD10004 = FD10004.groupby(['orderProductSubId','productId'])['productWantQty'].sum().reset_index()

        GG = df3.merge(FD10004, on=['orderProductSubId', 'productId'], how='left', indicator=True)

        GG = GG.replace(np.nan, 0)

        GG['productQtyAns'] = (GG['productQty'] - GG['productWantQty'])


        GG['productQty'] = (GG['productQty'] - GG['productWantQty'])

        GG = GG.loc[GG['productQtyAns'] > 0 ]

        df3 = GG




    # print(df3.info())

    df5 = df3.to_json(orient='records')


    return json.loads(df5)



def create_order_product(hero: order_product_model_read, db: Session = Depends(get_session)):
    
    hero.shuRiqiDatetime = str(datetime.now())
    hero.riqiDatetime = str(datetime.now())
    hero.orderProductId = generate_datetime_id()
    hero_to_db = order_product.from_orm(hero)
    
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)

    return hero_to_db



def check_product_is_exit(orderProductRid : str,orderProductSubId : str,fromLine : str,fenDianId: str , db: Session = Depends(get_session)):

    heroes = db.exec(select(order_product_sub).where(and_(order_product_sub.orderProductSubId == orderProductSubId,order_product_sub.fenDianId == fenDianId,
    order_product_sub.orderProductRid == orderProductRid,order_product_sub.fromLine == fromLine))).all()
    df = sqmodel_to_df(heroes)
    return df

def create_order_sub_product(hero: order_product_sub_model_read, db: Session = Depends(get_session)):
    # print(hero)
    hero.shuRiqiDatetime = str(datetime.now())

    VC10001 = check_product_is_exit(hero.orderProductRid,hero.orderProductSubId,hero.fromLine,hero.fenDianId,db)

    if not VC10001.empty:
        print(VC10001)

        statement = select(order_product_sub).where(and_(order_product_sub.orderProductSubId == hero.orderProductSubId,order_product_sub.fromLine == hero.fromLine
        ,order_product_sub.orderProductRid == hero.orderProductRid,order_product_sub.fenDianId == hero.fenDianId))

        results = db.exec(statement).one()

        results.productQty = VC10001['productQty'][0] + hero.productQty

        db.add(results)
        db.commit()
        db.refresh(results)
        print("pass")
        
        return results
        
    # hero.orderProductSubId = generate_datetime_id()
    hero_to_db = order_product_sub.from_orm(hero)
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    
    return hero_to_db


def get_order_sub_product(orderProductRid: str, db: Session = Depends(get_session)):


    heroes = db.exec(select(order_product_sub).where(order_product_sub.orderProductRid == orderProductRid)).all()

    df2 = sqmodel_to_df(heroes)

    print(df2)

    if df2.empty:
    
        return []

    df5 = df2.to_json(orient='records')

    return json.loads(df5)


def status_list_list(db: Session = Depends(get_session)):
    
    heroes = db.exec(select(order_product)).all()

    df2 = sqmodel_to_df(heroes)

    if df2.empty:
    
        return []

    df5 = df2.to_json(orient='records')

    return json.loads(df5)