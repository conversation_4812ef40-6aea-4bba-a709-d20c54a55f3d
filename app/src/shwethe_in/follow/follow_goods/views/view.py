from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_in.database import get_session
from fastapi.responses import FileResponse


from src.shwethe_in.follow.follow_goods.crud.crud import (
    getsubProductOrderId,
    putTrackDatetime,
    getsubProductOrderIdParnerId,
    insert_follow_goods_fun
)
from src.shwethe_in.follow.follow_goods.models.models import (
    put_logEntries,
    insert_order_goods_follow
)


router = APIRouter()

@router.get("")
def create_a_hero(db: Session = Depends(get_session)):
    
    return getsubProductOrderId(db=db)


@router.get("/{parner_id}/file",response_class=FileResponse)
def create_a_hero(parner_id:int,parnerName:str,db: Session = Depends(get_session)):
    
    return getsubProductOrderIdParnerId(parner_id=parner_id,parnerName=parnerName,db=db)


@router.put("/track_datetime")
def create_a_hero(hero:put_logEntries,db: Session = Depends(get_session)):
    
    return putTrackDatetime(hero=hero,db=db)


@router.post("/insert_follow_goods")
def create_a_hero(hero:insert_order_goods_follow,db: Session = Depends(get_session)):
    
    return insert_follow_goods_fun(data=hero)