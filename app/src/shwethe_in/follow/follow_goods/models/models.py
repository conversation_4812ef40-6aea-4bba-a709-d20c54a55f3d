from xmlrpc.client import DateTime
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id
import time
import random
from typing import List, Optional



class log_entries_base(SQLModel):
    payload: dict = Field(sa_column=Column(JSONB), default={})


class log_entries(log_entries_base, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    payload: dict = Field(sa_column=Column(JSONB), default={})

# 规定读资料
class put_logEntries(BaseModel):
    payload: Optional[dict] = Field(sa_column=Column(JSONB), default={})
    
    
def generate_id() -> str:
    x = time.strftime("%Y%m%d%H%M")
    B = random.randint(1, 100000000000000000)
    return f"{x}{B}"
    
class insert_order_goods_follow(BaseModel):
    datetime: str = Field(default_factory=lambda: datetime.now().isoformat())
    date: str = Field(default_factory=lambda: datetime.now().date().isoformat())
    idname: Optional[int] = None
    sname: Optional[int] = None
    price: Optional[float] = None
    id: str = Field(default_factory=generate_id)
    data: List[dict] = {}
    user_id: int = Field(default=1)