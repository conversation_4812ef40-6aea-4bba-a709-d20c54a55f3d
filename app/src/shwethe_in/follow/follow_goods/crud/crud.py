from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,or_,and_
from typing import List, Optional
import numpy as np
from datetime import datetime
from src.shwethe_in.database import get_session
from helper import generate_datetime_id
from fastapi.responses import FileResponse



from src.common.function.function import insert_order_follow_ithem

from src.shwethe_in.http_services.follow_order_list import (
follow_goods_for_in
)

from src.shwethe_in.follow.follow_goods.models.models import (
        put_logEntries,
        log_entries
)


import json
import pandas as pd 

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def getsubProductOrderId(db: Session = Depends(get_session)):

        A100001 = follow_goods_for_in()
        # print(A100001)
        # print("A100001")

        heroes = db.exec(select(log_entries).where(log_entries.payload.op("->>")("followId").in_(A100001['iid'].tolist()) )).all()

        heroes10001 = sqmodel_to_df(heroes)


        heroes10002 = pd.json_normalize(heroes10001.to_dict(orient="records"))

        if heroes10002.empty:

                A100002 = A100001.to_json(orient='records')

                return json.loads(A100002)


        def dropData(staringForLoc,df):
                df = df.loc[df['payload.action'] == staringForLoc]
                df = df.sort_values(['auto_id'], ascending=[False])
                df = df.drop_duplicates(subset=['payload.followId'], keep='first')
                return df



        A10001 = dropData('product_price_changed',heroes10002) 

        A20001 = dropData('track_datetime_shwethe_in',heroes10002)
        
        A30001 = dropData('status_input',heroes10002)

        Y10001 = dropData('product_qty_changed',heroes10002)



        if not A20001.empty:
                A20001 = A20001.rename(columns={'payload.datetimedata': 'trackDatetime','payload.followId': 'iid'})
                A20001 = A20001[['trackDatetime','iid']]

        if not A10001.empty:
                A10001 = A10001.rename(columns={'payload.price': 'price','payload.followId': 'iid'})
                A10001 = A10001[['price','iid']]
                
        if not A30001.empty:
                A30001 = A30001.rename(columns={'payload.status': 'status','payload.followId': 'iid'})
                A30001 = A30001[['status','iid']]
                
        if A10001.empty:
            A10001 = pd.DataFrame([{'price':0,'iid':""}])


        def merData(df1,df2,leftstr,rightstr,howmer):
                X1001 = df1.merge(df2, left_on=[leftstr], right_on=[rightstr], how=howmer)

                return X1001

        if not A10001.empty:
                V10001 = merData(A100001 , A10001,'iid','iid','left')

        if not A20001.empty:
                V10001 = merData(V10001 , A20001,'iid','iid','left')
                
        if not A30001.empty:
                V10001 = merData(V10001 , A30001,'iid','iid','left')
                V10001['status'] = V10001['status'].replace(np.nan, 'ยังไม่ได้สั่ง')

        V10001['price_x'] = V10001['price_x'].astype(float)

        for index, row in Y10001.iterrows():
                V10001.loc[V10001['iid'] == row['payload.followId'], '1'] = row['payload.1']
                V10001.loc[V10001['iid'] == row['payload.followId'], '2'] = row['payload.2']
                
        V10001 = V10001.replace(np.nan, '')

        V10001.to_csv('out.csv')

        A100002 = V10001.to_json(orient='records')

        


        return json.loads(A100002)
    

def logUpdate(put_logEntriesJson , db: Session = Depends(get_session)):
        try:
                hero_to_db = log_entries.from_orm(put_logEntriesJson)
                db.add(hero_to_db)
                db.commit()
                db.refresh(hero_to_db)
                return True
        except:
                return False


def putTrackDatetime(hero : put_logEntries,db: Session = Depends(get_session)):

    print(hero)
    print("hero---------------->")

    A10001 = logUpdate(hero,db)
    
    return A10001




def getsubProductOrderIdParnerId(parner_id:int,parnerName:str,db: Session = Depends(get_session)):

        A100001 = follow_goods_for_in()

        heroes = db.exec(select(log_entries).where(log_entries.payload.op("->>")("followId").in_(A100001['iid'].tolist()) )).all()

        heroes10001 = sqmodel_to_df(heroes)

        # print(heroes10001)
        # print("heroes10001")

        heroes10002 = pd.json_normalize(heroes10001.to_dict(orient="records"))

        if heroes10002.empty:

                A100002 = A100001.to_json(orient='records')

                return json.loads(A100002)


        def dropData(staringForLoc,df):
                df = df.loc[df['payload.action'] == staringForLoc]
                df = df.sort_values(['auto_id'], ascending=[False])
                df = df.drop_duplicates(subset=['payload.followId'], keep='first')
                return df

        A10001 = dropData('product_price_changed',heroes10002) 

        A20001 = dropData('track_datetime_shwethe_in',heroes10002)

        Y10001 = dropData('product_qty_changed',heroes10002)




        # heroes10002 = heroes10002.at[6,'NAME']='Safa'

        if not A20001.empty:
                A20001 = A20001.rename(columns={'payload.datetimedata': 'trackDatetime','payload.followId': 'iid'})
                A20001 = A20001[['trackDatetime','iid']]

        if not A10001.empty:
                A10001 = A10001.rename(columns={'payload.price': 'price','payload.followId': 'iid'})
                A10001 = A10001[['price','iid']]


        def merData(df1,df2,leftstr,rightstr,howmer):
                X1001 = df1.merge(df2, left_on=[leftstr], right_on=[rightstr], how=howmer)

                return X1001

        if not A10001.empty:
                print(A100001.info())
                print(A20001.info())
                V10001 = merData(A100001 , A10001,'iid','iid','left')

        if not A20001.empty:
                print(A100001.info())
                print(A20001.info())
                V10001 = merData(V10001 , A20001,'iid','iid','left')

        V10001['price_x'] = V10001['price_x'].astype(float)

        for index, row in Y10001.iterrows():
                V10001.loc[V10001['iid'] == row['payload.followId'], '1'] = row['payload.1']
                V10001.loc[V10001['iid'] == row['payload.followId'], '2'] = row['payload.2']
                # print(row['payload.followId'])

        # print(V10001)
        V10001 = V10001.loc[V10001['parner'] == parner_id].reset_index()

        V10001['time'] = pd.to_datetime(V10001['time'])
        V10001['time'] = V10001['time'].dt.date



        # print(V10001['datetime'])

        # V10001 = V10001['datetime'].dt.strftime("%Y-%m-%d")
        # V10001['datetime'] = V10001['datetime'].astype(str)
        print(V10001.info())

        # V10001 = V10001[['idname_x','mm_name_x','th_name','1','2','qty_x','price_x','time','trackDatetime']]
        V10001['trackDatetime'] = V10001[['trackDatetime']].replace(np.nan, '')

        V10001.loc[V10001['trackDatetime'] == '', 'สถานะ'] = 'เช็คของ'
        V10001.loc[V10001['trackDatetime'] != '', 'สถานะ'] = 'สั่งของ'
        V10001['ราคาเปลี่ยน(ถ้ามี)*'] = ''
        # df.loc[np.isnan(df["Age"]), 'Age'] = rand1

        # V10001.loc[V10001['trackDatetime'] != np.nan, 'สถานะ'] = 'สั่งของ'

        print(V10001)
        print("V10001----------------")


        V10001 = V10001.sort_values(['time'], ascending=[False])

        V10001 = V10001[['idname_x','mm_name_x','th_name','d_name','1','2','qty_x','price_x','time','trackDatetime','สถานะ','ราคาเปลี่ยน(ถ้ามี)*']]

        V10001.columns = ['รหัสสินค้า','รายการสินค้า(M)','รายการสินค้า(T)','ขนาด/ยี่ห่อ','ร้านใน','ร้านนอก','จำนวนรวม','ราคาต่อหน่วย','วันที่/เวลา','วันที่ถึง','สถานะ','ราคาเปลี่ยน(ถ้ามี)*']


        V10001 = V10001.replace(np.nan, '')

        V10001.to_excel(parnerName+'out.xlsx' )

        from openpyxl import Workbook
        from openpyxl import load_workbook

        test_spreadsheet = parnerName+"out.xlsx"
        wb = load_workbook(test_spreadsheet)
        wb.security.workbookPassword = str(parner_id)
        wb.security.lockStructure = True

        ws = wb.worksheets[0]
        for col in ['F', 'G','H','I']:
                ws.column_dimensions[col].hidden= True
                # ws.column_dimensions['ราคาต่อหน่วย'].hidden= True
        ws.protection
        ws.protection.set_password(str(parner_id))

        wb.save(parnerName+'out.xlsx')

        # import subprocess
        # subprocess.run(['unoconv','-p' ,'p4ssw0rd', '-f', 'xlsx', 'out.csv'])
        # os.system('unoconv -p p4ssw0rd -f csv out.xlsx')

        # from cryptography.fernet import Fernet

        
        # key generation
        # key = Fernet.generate_key()
        # print(key)
        
        # string the key in a file
        # with open('filekey.key', 'wb') as filekey:
        #         filekey.write(key)

        # Encrypt by specifying the encryption type
        # wb.setEncryptionOptions(EncryptionType.XOR, 40)

        # Specify Strong Encryption type (RC4,Microsoft Strong Cryptographic Provider)
        # wb.setEncryptionOptions(EncryptionType.STRONG_CRYPTOGRAPHIC_PROVIDER, 128)

        # Save Excel file

        # test_spreadsheet = "out.xlsx"
        # wb = load_workbook(test_spreadsheet)
        # ws = wb.worksheets[0]
        # # ws.protection
        # # ws.protection.set_password('test123')
        # # wb.security.workbookPassword = 'test'
        # hashed_password = 'test'
        # wb.security.set_workbook_password(hashed_password, already_hashed=True)

        # wb.security.revisionsPassword = 'test'
        # wb.security.lockStructure = True
        # wb.save('out1.xlsx')
        # wb.close()

        # def createpass(file):
                
        #         # opening the key
        #         with open('filekey.key', 'rb') as filekey:
        #                 key = filekey.read()
                
        #         # using the generated key
        #         fernet = Fernet(key)
                
        #         # opening the original file to encrypt
        #         with open(file, 'rb') as file:
        #                 original = file.read()
                
        #         # encrypting the file
        #         encrypted = fernet.encrypt(original)
                
        #         # opening the file in write mode and
        #         # writing the encrypted data
        #         with open(file, 'wb') as encrypted_file:
        #                 encrypted_file.write(encrypted)

        # createpass('out.xlsx')


        

        # A100002 = V10001.to_json(orient='records')

        

        # A100002 = A100001.to_json(orient='records')

        # return FileResponse('out.csv')

        from datetime import date

        today = date.today()

        # dd/mm/YY
        # d1 = today.strftime("%d/%m/%Y")
        d1 = today.strftime("%Y/%m/%d")


        return FileResponse(
               path=parnerName+'out.xlsx',
        filename=parnerName+'-'+d1+'.xlsx',
        media_type='application/octet-stream')



def insert_follow_goods_fun(data):
        insert_order_follow_ithem(data)
        return True