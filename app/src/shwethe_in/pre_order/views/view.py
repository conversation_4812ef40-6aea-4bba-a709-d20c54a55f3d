from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.shwethe_in.database import get_session
from src.shwethe_in.pre_order.crud.crud import (
    create_hero,
    delete_hero,
    read_hero,
    read_heroes,
    update_hero,
    create_pre_order_product,
    create_pre_order_sub_product,
    get_pre_order_sub_product,
    create_put_order_sub_product,
    get_pre_order_list
)

from src.shwethe_in.pre_order.models.models import pre_order_product_model_read,create_pre_order_product_model,HeroCreate, HeroRead, HeroUpdate
from src.shwethe_in.pre_order.models.models import pre_order_product_get_base,pre_order_product_base,create_put_order_product_model,create_pre_order_product_sub_model,product_sub_model,pre_order_product_sub_model_read

router = APIRouter()




@router.post("", response_model=pre_order_product_model_read)
def create_a_hero(hero: create_pre_order_product_model, db: Session = Depends(get_session)):

    return create_pre_order_product(hero=hero, db=db)


@router.put("", response_model=pre_order_product_model_read)
def create_a_hero(hero: create_put_order_product_model, db: Session = Depends(get_session)):
    return create_put_order_sub_product(hero=hero, db=db)


@router.post("/sub_pre_order", response_model=pre_order_product_sub_model_read)
def create_a_hero(hero: create_pre_order_product_sub_model, db: Session = Depends(get_session)):
    
    return create_pre_order_sub_product(hero=hero, db=db)


@router.get("/sub_pre_order/{orderId}", response_model=List[product_sub_model])
def create_a_hero(orderId: str , db: Session = Depends(get_session)):
    
    return get_pre_order_sub_product(orderId=orderId, db=db)



@router.get("/pre_order_list", response_model=List[pre_order_product_get_base])
def pre_order_list(
            db: Session = Depends(get_session),
        ):
    
    return get_pre_order_list(db=db)








# @router.post("", response_model=HeroRead)
# def create_a_hero(hero: HeroCreate, db: Session = Depends(get_session)):
#     return create_hero(hero=hero, db=db)




# @router.get("", response_model=List[HeroRead])
# def get_heroes(
#     offset: int = 0,
#     limit: int = Query(default=100, lte=100),
#     db: Session = Depends(get_session),
# ):
#     return read_heroes(offset=offset, limit=limit, db=db)


# @router.get("/{hero_id}", response_model=HeroRead)
# def get_a_hero(hero_id: int, db: Session = Depends(get_session)):
#     return read_hero(hero_id=hero_id, db=db)


# @router.patch("/{hero_id}", response_model=HeroRead)
# def update_a_hero(hero_id: int, hero: HeroUpdate, db: Session = Depends(get_session)):
#     return update_hero(hero_id=hero_id, hero=hero, db=db)


# @router.delete("/{hero_id}")
# def delete_a_hero(hero_id: int, db: Session = Depends(get_session)):
#     return delete_hero(hero_id=hero_id, db=db)


