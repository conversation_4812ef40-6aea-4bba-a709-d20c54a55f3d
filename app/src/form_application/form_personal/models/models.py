from typing import Dict
from xmlrpc.client import DateTime

from click import option
from pydantic.types import Optional, List
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel, Column, JSON
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id





class appTreeKey_base(SQLModel):
    mm_name: Optional[str]
    parent_id: Optional[int] 
    head_id: Optional[int]
    fen_ji: Optional[int]
    type: Optional[int]
    input_or_text: Optional[str]
    hide: Optional[bool]
    
class appTreeKey(appTreeKey_base, table=True):
    __tablename__ = "appTreeKey"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    mm_name: Optional[str]
    parent_id: Optional[int]
    head_id: Optional[int]
    fen_ji: Optional[int]
    type: Optional[int]
    input_or_text: Optional[str]
    hide: Optional[bool]

class appTreeKey_read(BaseModel):
    auto_id: Optional[int]
    mm_name: Optional[str]
    parent_id: Optional[int] 
    head_id: Optional[int]
    fen_ji: Optional[int]
    type: Optional[int]
    input_or_text: Optional[str]
    hide: Optional[bool]

class appTreeKey_post(BaseModel):
    mm_name: Optional[str]
    parent_id: Optional[int] = 0
    head_id: Optional[int]
    fen_ji: Optional[int]
    type: Optional[int]
    input_or_text: Optional[str]
    hide: Optional[bool]

# --------------------------------

class app_person_base(SQLModel):
    name: Optional[str]
    datetime: Optional[datetime]
    fen_dian_id: Optional[int]
    
class app_person(app_person_base, table=True):
    __tablename__ = "app_person"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    name: Optional[str]
    datetime: Optional[datetime]
    fen_dian_id: Optional[int]

class app_person_read(BaseModel):
    auto_id: Optional[int]
    name: Optional[str]
    datetime: Optional[datetime]
    fen_dian_id: Optional[int]

class app_person_post(BaseModel):
    name: Optional[str]
    datetime: Optional[datetime]
    fen_dian_id: Optional[int]

# --------------------------------

class appPersonInfo_base(SQLModel):
    person_autoId: Optional[int]
    key: Optional[int]
    value: Optional[str]
    parent_id: Optional[int]
    datetime: Optional[datetime]
    reletion_id: Optional[str]
    
class appPersonInfo(appPersonInfo_base, table=True):
    __tablename__ = "appPersonInfo"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    person_autoId: Optional[int]
    key: Optional[int]
    value: Optional[str]
    parent_id: Optional[int]
    datetime: Optional[datetime]
    reletion_id: Optional[str]

class appPersonInfo_read(BaseModel):
    auto_id: Optional[int]
    person_autoId: Optional[int]
    key: Optional[int]
    value: Optional[str]
    parent_id: Optional[int]
    datetime: Optional[datetime]
    reletion_id: Optional[str]

class appPersonInfo_post(BaseModel):
    person_autoId: Optional[int]
    key: Optional[int]
    value: Optional[str]
    parent_id: Optional[int]
    datetime: Optional[datetime]
    reletion_id: Optional[str]

# --------------------------------

class appTag_base(SQLModel):
    jia_yi: Optional[str]
    
class appTag(appTag_base, table=True):
    __tablename__ = "appTag"
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    jia_yi: Optional[str]

class appTag_read(BaseModel):
    auto_id: Optional[int]
    jia_yi: Optional[str]

class appTag_post(BaseModel):
    jia_yi: Optional[str]