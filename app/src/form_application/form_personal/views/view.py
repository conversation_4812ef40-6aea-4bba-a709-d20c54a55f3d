# from typing import Dict, Optional
from pydantic.types import Optional, List, Dict, Tuple
from builtins import int
from src.form_application.form_personal.models.models import appTreeKey_post
from src.form_application.form_personal.crud.crud import getTable100, changeWorkStatus100, getPersonalInfo100, getLanguage100, getEducation100, getMissUserTable100, filterAge100, filterLang100, formInsert100, getDesiredJob100, addDesiredJob100, getKnowStore100, addKnowStore100, getContact100, addContact100, MgetLanguage100, addLanguage100
from fastapi import APIRouter, Depends, Query, Body
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.form_application.database import get_session,create_db_and_tables



router = APIRouter()


@router.get("/getTable")
def getTable(db: Session = Depends(get_session)):
    return getTable100(db=db)

@router.get("/getMissUserTable")
def getMissUserTable(db: Session = Depends(get_session)):
    return getMissUserTable100(db=db)

@router.get("/getPersonalInfo/{auto_id}")
def getPersonalInfo(auto_id: int, db: Session = Depends(get_session)):
    return getPersonalInfo100(auto_id=auto_id, db=db) 

@router.get("/getLanguage/{person_autoId}")
def getLanguage(person_autoId: int, db: Session = Depends(get_session)):
    return getLanguage100(person_autoId=person_autoId, db=db) 

@router.get("/getEducation/{person_autoId}")
def getEducation(person_autoId: int, db: Session = Depends(get_session)):
    return getEducation100(person_autoId=person_autoId, db=db) 

@router.get("/filterAge")
def filterAge(older_age: int, younger_age: int, db: Session = Depends(get_session)):
    return filterAge100(older_age, younger_age,  db=db)

@router.get("/filterLang")
def filterLang(languages: List[str] = Query(None), db: Session = Depends(get_session)):
    # if languages is None:
    #     languages = []
    return filterLang100(languages, db=db)

@router.post("/formInsert")
def formInsert(person: dict = Body(...), db: Session = Depends(get_session)):
    return formInsert100(person,db=db)

@router.get("/getDesiredJob")
def getDesiredJob(db: Session = Depends(get_session)):
    return getDesiredJob100(db=db)

@router.post("/addDesiredJob")
def addDesiredJob(hero : appTreeKey_post , db: Session = Depends(get_session)):
    return addDesiredJob100(hero=hero,db=db)

@router.get("/getKnowStore")
def getKnowStore(db: Session = Depends(get_session)):
    return getKnowStore100(db=db)

@router.post("/addKnowStore")
def addKnowStore(hero : appTreeKey_post , db: Session = Depends(get_session)):
    return addKnowStore100(hero=hero,db=db)

@router.get("/getContact")
def getContact(db: Session = Depends(get_session)):
    return getContact100(db=db)

@router.post("/addContact")
def addContact(hero : appTreeKey_post , db: Session = Depends(get_session)):
    return addContact100(hero=hero,db=db)

@router.get("/MgetLanguage")
def MgetLanguage(db: Session = Depends(get_session)):
    return MgetLanguage100(db=db)

@router.post("/addLanguage")
def addLanguage(hero : appTreeKey_post , db: Session = Depends(get_session)):
    return addLanguage100(hero=hero,db=db)

@router.post("/changeWorkStatus")
def changeWorkStatus(auto_id: int, status_work: str, db: Session = Depends(get_session)):
    return changeWorkStatus100(auto_id=auto_id, status_work=status_work, db=db)

# @router.get("/vvvvvv")
# def vvvvvv(db: Session = Depends(get_session)):
#     return vvvvvv100(db=db)

# @router.get("/checkDriver/{checkDriver}")
# def checkDriver(checkDriver : str , db: Session = Depends(get_session)):
#     return checkDriver100(checkDriver=checkDriver,db=db)

# @router.get("/getTreeParent")
# def getTreeParent(db: Session = Depends(get_session)):
#     return getTreeParent100(db=db)

# @router.post("/addPersonalName")
# def addPersonalName(hero : personal_post , db: Session = Depends(get_session)):
#     return addPersonalName100(hero=hero,db=db)

# @router.get("/getPersonalNameColumn/{parent_id}")
# def getPersonalNameColumn(parent_id : int, db: Session = Depends(get_session)):
#     return getPersonalNameColumn100(parent_id=parent_id, db=db)

# @router.get("/getPersonalName/{parent_id}")
# def getPersonalName(parent_id : int, db: Session = Depends(get_session)):
#     return getPersonalName100(parent_id=parent_id, db=db)

# @router.get("/getPersonalNameShow")
# def getPersonalNameShow(db: Session = Depends(get_session)):
#     return getPersonalNameShow100(db=db)

# @router.post("/addPersonalInfo")
# def addPersonalInfo(data: List[dict], db: Session = Depends(get_session)):
#     return addPersonalInfo100(data=data,db=db)

# # @router.post("/addPersonalInfo")
# # def addPersonalInfo(hero : personal_info_post , db: Session = Depends(get_session)):
# #     return addPersonalInfo100(hero=hero,db=db)

# @router.get("/getPersonalNameById/{auto_id}")
# def getPersonalNameById(auto_id: int, db: Session = Depends(get_session)):
#     return getPersonalNameById100(auto_id=auto_id, db=db) 

# @router.get("/getPersonalInfo/{auto_id}")
# def getPersonalInfo(auto_id: int, db: Session = Depends(get_session)):
#     return getPersonalInfo100(auto_id=auto_id, db=db) 

# @router.get("/getPersonalTreeTag")
# def getPersonalTreeTag(db: Session = Depends(get_session)):
#     return getPersonalTreeTag100(db=db)

# @router.get("/getPersonalStatus")
# def getPersonalStatus(db: Session = Depends(get_session)):
#     return getPersonalStatus100(db=db)

# @router.get("/getTreekeyParent")
# def getTreekeyParent(db: Session = Depends(get_session)):
#     return getTreekeyParent100(db=db) 

# @router.get("/selectTree/{head_id}")
# def selectTree(head_id: int, db: Session = Depends(get_session)):
#     return selectTree100(head_id=head_id, db=db)

# @router.get("/getTag")
# def getTag(db: Session = Depends(get_session)):
#     return getTag100(db=db)

# @router.post("/addTreekey")
# def addTreekey(hero : tree_key_post , db: Session = Depends(get_session)):
#     return addTreekey100(hero=hero,db=db)

# @router.delete("/delPersonInfo/{auto_id}")
# def delPersonInfo(auto_id: int, db: Session = Depends(get_session)):
#     return delPersonInfo100(auto_id=auto_id, db=db)

# @router.get("/getPersonAccess/{checkDriver}")
# def getPersonAccess(checkDriver: int, db: Session = Depends(get_session)):
#     return getPersonAccess100(checkDriver=checkDriver, db=db)

# @router.get("/MgetPersonalName/{fen_dian_id}")
# def MgetPersonalName(fen_dian_id : int, db: Session = Depends(get_session)):
#     return MgetPersonalName100(fen_dian_id=fen_dian_id, db=db)
    
# @router.get("/MgetActive/{fen_dian_id}")
# def MgetActive(fen_dian_id : int, db: Session = Depends(get_session)):
#     return MgetActive100(fen_dian_id=fen_dian_id, db=db)

# @router.post("/MaddPersonalName")
# def MaddPersonalName(hero : personal_post , db: Session = Depends(get_session)):
#     return MaddPersonalName100(hero=hero,db=db)

# @router.post("/MaddPersonData")
# def MaddPersonData(hero : personal_info_post , db: Session = Depends(get_session)):
#     return MaddPersonData100(hero=hero,db=db)

# @router.get("/getMPersonData/{auto_id}")
# def getMPersonData(auto_id: int, db: Session = Depends(get_session)):
#     return getMPersonData100(auto_id=auto_id, db=db) 
    
# @router.get("/getAllEmployee")
# def getAllEmployee(db: Session = Depends(get_session)):
#     return getAllEmployee100(db=db) 