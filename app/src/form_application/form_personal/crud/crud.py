from builtins import int
from locale import D_FMT
from lib2to3.pgen2.pgen import DFAState
from multiprocessing.reduction import DupFd
from unittest import result
from src.form_application.form_personal.models.models import app_person, appPersonInfo, appTree<PERSON>ey, appTree<PERSON>ey_post
from src.Connect.https_connect import mongodb_data_api
from fastapi import Depends, HTTPException, status, Body
from sqlmodel import Session, select,SQLModel
from typing import Dict, List, Optional
from pydantic.types import Optional, List, Dict, Tuple

from datetime import datetime
from src.form_application.database import get_session
from helper import generate_datetime_id
import json
import pandas as pd
import requests
from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT
import numpy as np
import datetime as DT


def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


# def getTable100(db: Session = Depends(get_session)):
    
#     try:
#         heroesPersonal = db.exec(select(app_person).order_by(app_person.datetime.desc())).all()
#         records = [i.dict() for i in heroesPersonal]   
#         df = pd.DataFrame.from_records(records).fillna(0)

#         heroesPersonal_info = db.exec(select(appPersonInfo)).all()
#         records = [i.dict() for i in heroesPersonal_info]   
#         df2 = pd.DataFrame.from_records(records)
#         df2 = df2.sort_values(['person_autoId', 'key' ,'datetime']).drop_duplicates(['person_autoId', 'key'], keep='last')

#         vvv = [3, 4, 5, 6, 16, 23, 24, 19, 17]
#         heroeStree_key = db.exec(select(appTreeKey).where(appTreeKey.auto_id.in_(vvv)).where(appTreeKey.input_or_text == 'input')).all()
#         records = [i.dict() for i in heroeStree_key]   
#         df3 = pd.DataFrame.from_records(records)

#         mergeDF = df2.merge(df3, left_on='key', right_on='auto_id')
#         mergeDF = mergeDF.set_index(['person_autoId', 'mm_name']).value.unstack().reset_index().fillna('')
#         mergeDF = df.merge(mergeDF, left_on='auto_id', right_on='person_autoId')

#         heroesPersonal_info = db.exec(select(appPersonInfo).where(appPersonInfo.key == 16)).all()
#         records = [i.dict() for i in heroesPersonal_info]   
#         df4 = pd.DataFrame.from_records(records)
#         result = df4.pivot_table(index=["person_autoId", "key", "parent_id"], values=["value"], aggfunc=lambda x: list(x)).reset_index()

#         ddd = pd.merge(mergeDF, result, on='person_autoId')
#         ddd = ddd.drop(columns=['key', 'parent_id', 'name_x', 'language']).rename(columns={'value': 'language', 'name_y': 'name'})

#         mergeDF = ddd.to_dict("records")

#     except:
#         mergeDF = []
#     return mergeDF


# def getTable100(db: Session = Depends(get_session)):
    
#     import datetime as DT
#     today = DT.date.today()
#     week_ago = today - DT.timedelta(days=90)
#     print(week_ago)
    
#     try:
#         # heroesPersonal = db.exec(select(app_person).order_by(app_person.datetime.desc())).all()
#         heroesPersonal = db.exec(select(app_person).where(app_person.datetime > week_ago).order_by(app_person.datetime.desc())).all()  
#         records = [i.dict() for i in heroesPersonal]   
#         df = pd.DataFrame.from_records(records).fillna(0)

#         heroesPersonal_info = db.exec(select(appPersonInfo).where(appPersonInfo.datetime > week_ago).order_by(appPersonInfo.datetime.desc())).all()  
#         # heroesPersonal_info = db.exec(select(appPersonInfo)).all()
#         records = [i.dict() for i in heroesPersonal_info]   
#         df2 = pd.DataFrame.from_records(records)
#         df2 = df2.sort_values(['person_autoId', 'key' ,'datetime']).drop_duplicates(['person_autoId', 'key'], keep='last')

#         vvv = [3, 4, 5, 6, 16, 23, 24, 19, 17, 21]
#         heroeStree_key = db.exec(select(appTreeKey).where(appTreeKey.auto_id.in_(vvv)).where(appTreeKey.input_or_text == 'input')).all()
#         records = [i.dict() for i in heroeStree_key]   
#         df3 = pd.DataFrame.from_records(records)

#         mergeDF = df2.merge(df3, left_on='key', right_on='auto_id')
#         mergeDF = mergeDF.set_index(['person_autoId', 'mm_name']).value.unstack().reset_index().fillna('')
#         mergeDF = df.merge(mergeDF, left_on='auto_id', right_on='person_autoId')

#         heroesPersonal_info = db.exec(select(appPersonInfo).where(appPersonInfo.key == 16)).all()
#         records = [i.dict() for i in heroesPersonal_info]   
#         df4 = pd.DataFrame.from_records(records)
#         result = df4.pivot_table(index=["person_autoId", "key", "parent_id"], values=["value"], aggfunc=lambda x: list(x)).reset_index()

#         ddd = pd.merge(mergeDF, result, on='person_autoId')
#         ddd = ddd.drop(columns=['key', 'parent_id', 'name_x', 'language']).rename(columns={'value': 'language', 'name_y': 'name'})

#         mergeDF = ddd.to_dict("records")

#     except:
#         mergeDF = []
#     return mergeDF
def getTable100(db: Session = Depends(get_session)):
    
    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=90)
    print(week_ago)
    
    try:
        # heroesPersonal = db.exec(select(app_person).order_by(app_person.datetime.desc())).all()
        heroesPersonal = db.exec(select(app_person).where(app_person.datetime > week_ago).order_by(app_person.datetime.desc())).all()  
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records).fillna(0)

        heroesPersonal_info = db.exec(select(appPersonInfo).where(appPersonInfo.datetime > week_ago).order_by(appPersonInfo.datetime.desc())).all()  
        # heroesPersonal_info = db.exec(select(appPersonInfo)).all()
        records = [i.dict() for i in heroesPersonal_info]   
        df2 = pd.DataFrame.from_records(records)
        df2 = df2.sort_values(['person_autoId', 'key' ,'datetime']).drop_duplicates(['person_autoId', 'key'], keep='last')

        vvv = [3, 4, 5, 6, 16, 23, 24, 19, 17, 21, 140]
        # heroeStree_key = db.exec(select(appTreeKey).where(appTreeKey.auto_id.in_(vvv)).where(appTreeKey.input_or_text == 'input')).all()
        heroeStree_key = db.exec(select(appTreeKey).where(appTreeKey.auto_id.in_(vvv))).all()
        records = [i.dict() for i in heroeStree_key]   
        df3 = pd.DataFrame.from_records(records)

        mergeDF = df2.merge(df3, left_on='key', right_on='auto_id')
        mergeDF = mergeDF.set_index(['person_autoId', 'mm_name']).value.unstack().reset_index().fillna('')
        mergeDF = df.merge(mergeDF, left_on='auto_id', right_on='person_autoId')

        heroesPersonal_info = db.exec(select(appPersonInfo).where(appPersonInfo.key == 16)).all()
        records = [i.dict() for i in heroesPersonal_info]   
        df4 = pd.DataFrame.from_records(records)
        result = df4.pivot_table(index=["person_autoId", "key", "parent_id"], values=["value"], aggfunc=lambda x: list(x)).reset_index()

        merge1 = pd.merge(mergeDF, result, on='person_autoId')
        merge1 = merge1.drop(columns=['key', 'parent_id', 'name_x', 'language']).rename(columns={'value': 'language', 'name_y': 'name'})

        heroesPersonal_info = db.exec(select(appPersonInfo).where(appPersonInfo.key == 21)).all()
        records = [i.dict() for i in heroesPersonal_info]   
        df5 = pd.DataFrame.from_records(records)
        result = df5.pivot_table(index=["person_autoId", "key", "parent_id"], values=["value"], aggfunc=lambda x: list(x)).reset_index()
        merge2 = pd.merge(merge1, result, on='person_autoId')
        merge2 = merge2.drop(columns=['key', 'parent_id']).rename(columns={'value': 'desired_job'})

        mergeDF = merge2.to_dict("records")

    except:
        mergeDF = []
    return mergeDF


# def getMissUserTable100(db: Session = Depends(get_session)):
    
#     try:
#         ConstTable100 = getTable100(db=db)
#         df = pd.DataFrame(ConstTable100)
#         if df.empty:
#             print("Dataframe is empty")
   
#             heroesPersonal = db.exec(select(app_person)).all()
#             records = [i.dict() for i in heroesPersonal]   
#             df2 = pd.DataFrame.from_records(records)
#             print(df2)

#             difference = df2
#             print(difference)

#         else:
#             print("have data", df)

#             selected_column = df[["name"]]
#             print(selected_column)

#             heroesPersonal = db.exec(select(app_person).order_by(app_person.datetime.desc())).all()
#             records = [i.dict() for i in heroesPersonal]   
#             df2 = pd.DataFrame.from_records(records)
#             print(df2)

#             difference = pd.merge(selected_column, df2, on='name', how='outer', indicator=True)
#             difference = difference[difference._merge == 'right_only']

#         mergeDF = difference.to_dict("records")
#     except:
#         mergeDF = []
#     return mergeDF


def getMissUserTable100(db: Session = Depends(get_session)):
    
    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=90)
    print(week_ago)

    try:
        ConstTable100 = getTable100(db=db)
        df = pd.DataFrame(ConstTable100)
        if df.empty:
            print("Dataframe is empty")
   
            # heroesPersonal = db.exec(select(app_person)).all()
            heroesPersonal = db.exec(select(app_person).where(app_person.datetime > week_ago)).all()  
            records = [i.dict() for i in heroesPersonal]   
            df2 = pd.DataFrame.from_records(records)
            print(df2)

            difference = df2
            print(difference)

        else:
            print("have data", df)

            selected_column = df[["name"]]
            print(selected_column)
            
            # heroesPersonal = db.exec(select(app_person).order_by(app_person.datetime.desc())).all()
            heroesPersonal = db.exec(select(app_person).where(app_person.datetime > week_ago).order_by(app_person.datetime.desc())).all()  
            records = [i.dict() for i in heroesPersonal]   
            df2 = pd.DataFrame.from_records(records)
            print(df2)

            difference = pd.merge(selected_column, df2, on='name', how='outer', indicator=True)
            difference = difference[difference._merge == 'right_only']

        mergeDF = difference.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


# def getPersonalInfo100(auto_id: int, db: Session = Depends(get_session)):
#     try:
#         df = db.exec(select(
#             app_person.auto_id, app_person.name, app_person.datetime,
#             appPersonInfo.auto_id, appPersonInfo.person_autoId, appPersonInfo.key, appPersonInfo.value, appPersonInfo.parent_id, appPersonInfo.datetime, appPersonInfo.reletion_id
#             )
#         .where(app_person.auto_id == auto_id)
#         .where(appPersonInfo.person_autoId == auto_id)
#         .where(appPersonInfo.reletion_id == 'None')
#         ).all()
#         df1 = pd.DataFrame(df, columns =['auto_id', 'name', 'datetime', 'auto_id_1', 'person_autoId', 'key', 'value', 'parent_id', 'datetime_1', 'reletion_id'])
#         # print(df1)  

#         df2 = db.exec(select(appTreeKey)
#         .where(appTreeKey.fen_ji != 3)
#         .where(appTreeKey.auto_id != 11, appTreeKey.parent_id != 11)
#         .where(appTreeKey.input_or_text == 'input')
#         ).all()
#         df2 = sqmodel_to_df(df2)
#         # print(df2, "ccccccccccccccccccccccccc")
 
#         df21 = db.exec(select(appTreeKey)
#         .where(appTreeKey.auto_id.in_(df2['parent_id']))
#         ).all()
#         df21 = sqmodel_to_df(df21)
#         # print(df21, "ccccccccccccccccccccccccc")

#         mergeDF = df2.merge(df21, how='outer', left_on='parent_id', right_on='auto_id').merge(df1, how='outer', left_on='auto_id_x', right_on='key').fillna(0)
#         mergeDF = [{"title_info": k, "content": [{"title_2":k1, "list": []} if v1['auto_id_1'].all() == 0 else {"title_2":k1, "list": v1[["auto_id_1", "value", "hide_x"]].to_dict('records')} for k1, v1 in v.groupby("mm_name_x")]} for k,v in mergeDF.groupby(["mm_name_y"])]
        
#     except: 
#         mergeDF = []
#     return mergeDF


def getPersonalInfo100(auto_id: int, db: Session = Depends(get_session)):
    try:
        df = db.exec(select(
            app_person.auto_id, app_person.name, app_person.datetime,
            appPersonInfo.auto_id, appPersonInfo.person_autoId, appPersonInfo.key, appPersonInfo.value, appPersonInfo.parent_id, appPersonInfo.datetime, appPersonInfo.reletion_id
            )
        .where(app_person.auto_id == auto_id)
        .where(appPersonInfo.person_autoId == auto_id)
        .where(appPersonInfo.reletion_id == 'None')
        ).all()
        df1 = pd.DataFrame(df, columns =['auto_id', 'name', 'datetime', 'auto_id_1', 'person_autoId', 'key', 'value', 'parent_id', 'datetime_1', 'reletion_id'])
        df1['datetime_1'] = pd.to_datetime(df1['datetime_1']).dt.date
        today = DT.date.today() - DT.timedelta(days=30)
        df1['todayMinus'] = today
        df1['dateCheckSee'] = df1.apply(lambda x: True if x['datetime_1'] > x['todayMinus'] else False, axis=1)
        df1['today'] = pd.to_datetime("today").strftime("%Y-%m-%d")
        df1['dateCheckToday'] = df1.apply(lambda x: True if str(x['datetime_1']) == str(x['today']) else False, axis=1)
        # print(df1)  

        df2 = db.exec(select(appTreeKey)
        .where(appTreeKey.fen_ji != 3)
        .where(appTreeKey.auto_id != 11, appTreeKey.parent_id != 11)
        .where(appTreeKey.input_or_text == 'input')
        ).all()
        df2 = sqmodel_to_df(df2)
        # print(df2, "ccccccccccccccccccccccccc")
 
        df21 = db.exec(select(appTreeKey)
        .where(appTreeKey.auto_id.in_(df2['parent_id']))
        ).all()
        df21 = sqmodel_to_df(df21)
        # print(df21, "ccccccccccccccccccccccccc")

        mergeDF = df2.merge(df21, how='outer', left_on='parent_id', right_on='auto_id').merge(df1, how='outer', left_on='auto_id_x', right_on='key').fillna(0)
        mergeDF = [{"title_info": k, "content": [{"title_2":k1, "list": []} if v1['auto_id_1'].all() == 0 else {"title_2":k1, "list": v1[["auto_id_1", "value", "dateCheckSee", "dateCheckToday", "hide_x"]].to_dict('records')} for k1, v1 in v.groupby("mm_name_x")]} for k,v in mergeDF.groupby(["mm_name_y"])]
        
    except: 
        mergeDF = []
    return mergeDF


def getLanguage100(person_autoId: int, db: Session = Depends(get_session)):
    try:
        df1 = db.exec(select(appPersonInfo)
        .where(appPersonInfo.person_autoId == person_autoId)
        .where(appPersonInfo.reletion_id.like('%lang%'))
        ).all()
        records = [i.dict() for i in df1]   
        df11 = pd.DataFrame.from_records(records)
        df12 = df11['key'].to_list()
        # print(df12)

        df2 = db.exec(select(appTreeKey)
        .where(appTreeKey.auto_id.in_(df12))
        ).all()
        records = [i.dict() for i in df2]   
        df21 = pd.DataFrame.from_records(records)

        mergeDF = df11.merge(df21, how='outer', left_on='key', right_on='auto_id').fillna(0)
        pivot_table = mergeDF.pivot(index='reletion_id', columns='mm_name', values='value').reset_index()
        pivot_table = pivot_table.to_dict("records")   
    except: 
        pivot_table = []
    return pivot_table


def getEducation100(person_autoId: int, db: Session = Depends(get_session)):
    try:
        df1 = db.exec(select(appPersonInfo)
        .where(appPersonInfo.person_autoId == person_autoId)
        .where(appPersonInfo.reletion_id.like('%education%'))
        ).all()
        records = [i.dict() for i in df1]   
        df11 = pd.DataFrame.from_records(records)
        df12 = df11['key'].to_list()
        # print(df11)

        df2 = db.exec(select(appTreeKey)
        .where(appTreeKey.auto_id.in_(df12))
        ).all()
        records = [i.dict() for i in df2]   
        df21 = pd.DataFrame.from_records(records)

        mergeDF = df11.merge(df21, how='outer', left_on='key', right_on='auto_id').fillna(0)
        pivot_table = mergeDF.pivot(index='reletion_id', columns='mm_name', values='value').reset_index()
        pivot_table = pivot_table.to_dict("records")
    except: 
        pivot_table = []
    return pivot_table


def filterAge100(older_age: int, younger_age: int, db: Session = Depends(get_session)):
    try:
        ConstTable100 = getTable100(db=db)
        mergeDF = pd.DataFrame(ConstTable100)

        mergeDF["age"] = mergeDF["age"].astype(int)
        if older_age is not None:
            mergeDF = mergeDF[mergeDF["age"] >= older_age]
        if younger_age is not None:
            mergeDF = mergeDF[mergeDF["age"] <= younger_age]
        
        mergeDF = mergeDF.to_dict("records")
    except:
        mergeDF = []
    return mergeDF


def filterLang100(languages: List[str], db: Session = Depends(get_session)):
    try:
        ConstTable100 = getTable100(db=db)
        mergeDF = pd.DataFrame(ConstTable100)

        # select_lang = ["thai"]
        filtervv = mergeDF['language'].apply(lambda x: all(i in x for i in languages))
        filtered_df = mergeDF[filtervv]

        pivot_table = filtered_df.to_dict("records")   
    except: 
        pivot_table = []
    return pivot_table


def formInsert100(person: dict = Body(...), db: Session = Depends(get_session)):

    def fetchData():
        df = db.exec(select(app_person)
        .where(app_person.name == person.get("name"))
        ).all()
        records = [i.dict() for i in df]   
        df = pd.DataFrame.from_records(records)
        print(df.to_dict("records"))
        return df.to_dict("records")

    if(fetchData() == []):
        print("no have")
        hero_to_db = app_person.from_orm(person)
        hero_to_db.name = person.get("name")
        hero_to_db.datetime = str(datetime.now())
        hero_to_db.fen_dian_id = person.get("fen_dian_id")
        db.add(hero_to_db)
        db.commit()
        print("hero_to_db", hero_to_db)

        df = db.exec(select(appTreeKey)
        .where(appTreeKey.parent_id == 2)
        ).all()
        records = [i.dict() for i in df]   
        df = pd.DataFrame.from_records(records)
        print("df.to_dict", df.to_dict("records"))

        result = [{'key': entry['auto_id'], 'value': person[entry['mm_name']], 'parent_id': entry['parent_id']} for entry in df.to_dict("records") if entry['mm_name'] in person]
        print("result", result)

        for entry in result:
            hero = appPersonInfo(person_autoId=hero_to_db.auto_id, key=entry['key'], value=entry['value'], parent_id=entry['parent_id'], datetime=str(datetime.now()), reletion_id="None")
            db.add(hero)
            db.commit()

        vvv = fetchData()
        res = vvv

    else:
        print("havehavehavehavehavehavehavehavehave")
        print(person.get("sector"))

        res = fetchData()

        if(person.get("sector") == 'person'):
            print("person person person")
            # DELETE
            result = db.query(appPersonInfo).filter(appPersonInfo.person_autoId == res[0]['auto_id'], appPersonInfo.parent_id == 2).delete()
            db.commit()
            # NEW INSERT
            df = db.exec(select(appTreeKey)
            .where(appTreeKey.parent_id == 2)
            ).all()
            records = [i.dict() for i in df]   
            df = pd.DataFrame.from_records(records)
            print(df.to_dict("records"))

            result = [{'key': entry['auto_id'], 'value': person[entry['mm_name']], 'parent_id': entry['parent_id']} for entry in df.to_dict("records") if entry['mm_name'] in person]
            print(result)
            
            for entry in result:
                hero = appPersonInfo(person_autoId=res[0]['auto_id'], key=entry['key'], value=entry['value'], parent_id=entry['parent_id'], datetime=str(datetime.now()), reletion_id="None")
                db.add(hero)
                db.commit()
        
        if(person.get("sector") == 'experian'):
            print("experian experian experian", person)

            # DELETE
            result = db.query(appPersonInfo).filter(appPersonInfo.person_autoId == res[0]['auto_id'], appPersonInfo.parent_id == 18).delete()
            db.commit()
            # NEW INSERT
            df = db.exec(select(appTreeKey)
            .where(appTreeKey.parent_id == 18)
            ).all()
            records = [i.dict() for i in df]   
            df = pd.DataFrame.from_records(records)
            print("df.to_dict", df.to_dict("records"))

            result = [{'key': item['auto_id'], 'value': subitem['name'], 'parent_id': item['parent_id']} for item in df.to_dict("records") for subitem in person['experian']]
            print("result", result)

            filtered_list = [item for item in result if item['value'] != '']
            print("filtered_list", filtered_list)

            for entry in filtered_list:
                hero = appPersonInfo(person_autoId=person.get("auto_id"), key=entry['key'], value=entry['value'], parent_id=entry['parent_id'], datetime=str(datetime.now()), reletion_id="None")
                db.add(hero)
                db.commit()

        if(person.get("sector") == 'education'):
            print("education education education", person)

            # DELETE
            result = db.query(appPersonInfo).filter(appPersonInfo.person_autoId == res[0]['auto_id'], appPersonInfo.parent_id == 11).delete()
            db.commit()
            # NEW INSERT
            df = db.exec(select(appTreeKey)
            .where(appTreeKey.parent_id == 11)
            ).all()
            records = [i.dict() for i in df]   
            df = pd.DataFrame.from_records(records)
            print("df.to_dict", df.to_dict("records"))

            result = []
            counter = 1
            for education in person['education']:
                if all(v == "" for v in education.values()):
                    continue
                for field in df.to_dict("records"):
                    result.append({
                        'key': str(field['auto_id']),
                        'value': education[field['mm_name']],
                        'parent_id': str(field['parent_id']),
                        'reletion_id': f"{person['auto_id']}_education_{counter}"
                    })
                counter += 1
            print("result", result)

            for entry in result:
                hero = appPersonInfo(person_autoId=person.get("auto_id"), key=entry['key'], value=entry['value'], parent_id=entry['parent_id'], datetime=str(datetime.now()), reletion_id=entry['reletion_id'])
                db.add(hero)
                db.commit()

        if(person.get("sector") == 'desiredJob'):
            print("desiredJob desiredJob desiredJob", person)

            # DELETE
            result = db.query(appPersonInfo).filter(appPersonInfo.person_autoId == res[0]['auto_id'], appPersonInfo.parent_id == 20).delete()
            db.commit()
            # # NEW INSERT
            df = db.exec(select(appTreeKey)
            .where(appTreeKey.parent_id == 20)
            ).all()
            records = [i.dict() for i in df]   
            df = pd.DataFrame.from_records(records)
            print("df.to_dict", df.to_dict("records"))

            result = []
            for value in person['desiredJob']:
                item = {
                    'key': str(df.to_dict("records")[0]['auto_id']),
                    'value': value,
                    'parent_id': str(df.to_dict("records")[0]['parent_id'])
                }
                result.append(item)
            print(result)

            for entry in result:
                hero = appPersonInfo(person_autoId=person.get("auto_id"), key=entry['key'], value=entry['value'], parent_id=entry['parent_id'], datetime=str(datetime.now()), reletion_id="None")
                db.add(hero)
                db.commit()
        
        if(person.get("sector") == 'other'):
            print("other other other", person)

            # DELETE
            result = db.query(appPersonInfo).filter(appPersonInfo.person_autoId == res[0]['auto_id'], appPersonInfo.parent_id == 22).delete()
            db.commit()
            # # NEW INSERT
            df = db.exec(select(appTreeKey)
            .where(appTreeKey.parent_id == 22)
            ).all()
            records = [i.dict() for i in df]   
            df = pd.DataFrame.from_records(records)
            print("df.to_dict", df.to_dict("records"))

            result = []
            for item in df.to_dict("records"):
                key = item['auto_id']
                value = person[item['mm_name']]
                parent_id = item['parent_id']
                
                if isinstance(value, list):
                    for v in value:
                        result.append({'key': key, 'value': v, 'parent_id': parent_id})
                else:
                    result.append({'key': key, 'value': value, 'parent_id': parent_id})
            print(result)

            for entry in result:
                hero = appPersonInfo(person_autoId=person.get("auto_id"), key=entry['key'], value=entry['value'], parent_id=entry['parent_id'], datetime=str(datetime.now()), reletion_id="None")
                db.add(hero)
                db.commit()

        if(person.get("sector") == 'contact'):
            print("contact contact contact", person)

            # DELETE
            result = db.query(appPersonInfo).filter(appPersonInfo.person_autoId == res[0]['auto_id'], appPersonInfo.parent_id == 8).delete()
            db.commit()
            # # NEW INSERT
            df = db.exec(select(appTreeKey)
            .where(appTreeKey.parent_id == 8)
            ).all()
            records = [i.dict() for i in df]   
            df = pd.DataFrame.from_records(records)
            print("df.to_dict", df.to_dict("records"))

            result = []
            for mm_name, data in person['form'].items():
                for mm in df.to_dict("records"):
                    if mm_name == mm['mm_name']:
                        parent_id = mm['parent_id']
                        auto_id = mm['auto_id']
                        for item in data:
                            if item["name"] != "":
                                result.append({'key': auto_id, 'value': item['name'], 'parent_id': parent_id})
            print(result)

            for entry in result:
                hero = appPersonInfo(person_autoId=person.get("auto_id"), key=entry['key'], value=entry['value'], parent_id=entry['parent_id'], datetime=str(datetime.now()), reletion_id="None")
                db.add(hero)
                db.commit()
        
        if(person.get("sector") == 'skill'):
            print("skill skill skill", person)

            # DELETE
            result = db.query(appPersonInfo).filter(appPersonInfo.person_autoId == res[0]['auto_id'], appPersonInfo.parent_id.in_([15, 16])).delete()
            db.commit()
            # # NEW INSERT
            df = db.exec(select(appTreeKey)
            .where(appTreeKey.parent_id.in_([15, 16]))
            ).all()
            records = [i.dict() for i in df]   
            df = pd.DataFrame.from_records(records)
            print("df.to_dict", df.to_dict("records"))

            result = []
            counter = 1
            for obj1 in person['language']:
                for obj2 in df.to_dict("records"):
                    if obj2['mm_name'] in obj1:
                        result.append({'key': obj2['auto_id'], 'value': obj1[obj2['mm_name']], 'parent_id': obj2['parent_id'], 'reletion_id': f"{person['auto_id']}_lang_{counter}"})
                counter += 1

            for obj1 in person['other']:
                for obj2 in df.to_dict("records"):
                    if obj2['mm_name'] in obj1 and obj1['other'] != '':
                        result.append({'key': obj2['auto_id'], 'value': obj1['other'], 'parent_id': obj2['parent_id'], 'reletion_id': 'None'})
            print(result)

            for entry in result:
                hero = appPersonInfo(person_autoId=person.get("auto_id"), key=entry['key'], value=entry['value'], parent_id=entry['parent_id'], datetime=str(datetime.now()), reletion_id=entry['reletion_id'])
                db.add(hero)
                db.commit()
        
           
    print("No sector")
    
    return res


def getDesiredJob100(db: Session = Depends(get_session)):
    
    try:
        heroesPersonal = db.exec(select(appTreeKey).where(appTreeKey.parent_id == 34).order_by(appTreeKey.auto_id)).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records)
        df = df.rename(columns={"mm_name": "name"})
        df = df.to_dict("records")
    except:
        df = []
    return df


def addDesiredJob100(hero: appTreeKey_post, db: Session = Depends(get_session)):
    
    hero_to_db = appTreeKey.from_orm(hero)
    hero_to_db.parent_id = 34
    hero_to_db.head_id = 34
    hero_to_db.fen_ji = 1
    hero_to_db.type = 4
    hero_to_db.input_or_text = "select"
    hero_to_db.hide = False
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    
    return hero_to_db


def getKnowStore100(db: Session = Depends(get_session)):
    
    try:
        heroesPersonal = db.exec(select(appTreeKey).where(appTreeKey.parent_id == 47).order_by(appTreeKey.auto_id)).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records)
        df = df.rename(columns={"mm_name": "name"})
        df = df.to_dict("records")
    except:
        df = []
    return df


def addKnowStore100(hero: appTreeKey_post, db: Session = Depends(get_session)):
    
    hero_to_db = appTreeKey.from_orm(hero)
    hero_to_db.parent_id = 47
    hero_to_db.head_id = 47
    hero_to_db.fen_ji = 1
    hero_to_db.type = 4
    hero_to_db.input_or_text = "select"
    hero_to_db.hide = False
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    
    return hero_to_db


def getContact100(db: Session = Depends(get_session)):
    
    try:
        heroesPersonal = db.exec(select(appTreeKey).where(appTreeKey.parent_id == 8).order_by(appTreeKey.auto_id)).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records)
        df = df.rename(columns={"mm_name": "name"})
        df = df.to_dict("records")
    except:
        df = []
    return df


def addContact100(hero: appTreeKey_post, db: Session = Depends(get_session)):
    
    hero_to_db = appTreeKey.from_orm(hero)
    hero_to_db.parent_id = 8
    hero_to_db.head_id = 1
    hero_to_db.fen_ji = 2
    hero_to_db.type = 1
    hero_to_db.input_or_text = "input"
    hero_to_db.hide = False
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    
    return hero_to_db


def MgetLanguage100(db: Session = Depends(get_session)):
    try:
        heroesPersonal = db.exec(select(appTreeKey).where(appTreeKey.parent_id == 92).order_by(appTreeKey.auto_id)).all()
        records = [i.dict() for i in heroesPersonal]   
        df = pd.DataFrame.from_records(records)
        df = df.rename(columns={"mm_name": "name"})
        df = df.to_dict("records")
    except:
        df = []
    return df


def addLanguage100(hero: appTreeKey_post, db: Session = Depends(get_session)):
    
    hero_to_db = appTreeKey.from_orm(hero)
    hero_to_db.parent_id = 92
    hero_to_db.head_id = 92
    hero_to_db.fen_ji = 1
    hero_to_db.type = 4
    hero_to_db.input_or_text = "select"
    hero_to_db.hide = False
    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    
    return hero_to_db


def changeWorkStatus100(auto_id: int, status_work: str, db: Session = Depends(get_session)):
    
    person_info = appPersonInfo(person_autoId=auto_id, key=140, value=status_work, parent_id=140, datetime=str(datetime.now()), reletion_id="None")
    
    db.add(person_info)
    db.commit()
    db.refresh(person_info)

    return person_info



