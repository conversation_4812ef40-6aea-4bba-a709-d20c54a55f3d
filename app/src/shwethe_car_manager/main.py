from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header,HTTPException,Query
from pydantic import BaseModel
from helper import generate_id, generate_datetime,generate_datetime_id,generate_datetime_selie
from typing import List, Optional
import json
import requests
import pandas as pd
import pytz
import datetime
from sqlalchemy import desc
import time
import numpy as np
from sqlmodel import Session,select,or_,and_,SQLModel
router = APIRouter()



@router.get("/check_delevery/{fen}", status_code = 200)
def order_s_insert_list(fen: int):

    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api
    A100001 = datetime.datetime.now()
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()
        def get_data_api(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/product_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'product_id': str+'_id','product_idname': str+'_idname','product_mm_name': str+'_mm_name'})
            
            return GF10001

        def get_data(fen):
            sql_insert = """
            select * from delivery_insert where data_sub ->> 'fen_id_a' = '%s' and data_sub ->> 'from_data' = 'ai'  and shu_riqi_datetime > current_date 
            """ % (fen)
            FF100001 = pd.read_sql(sql_insert, psycopg2_conn_shwethe_delivery)
            FF100002 = FF100001.to_dict('records')
            FD100003 = pd.json_normalize(FF100002)
            return FD100003
            
        try:
            GG100001 = get_data(fen)
            H1000001 = GG100001[['product_id']]
            H1000001 = H1000001.rename(columns={'che_liang': 'jia_yi_id'})
            FG10001 = get_data_api(H1000001,"product")
            GG100009 = FG10001.merge(GG100001, on=['product_id'] , how='inner')
            GG100009['shu_riqi_datetime'] = GG100009['shu_riqi_datetime'].astype(str)
            GG100009 = GG100009.sort_values(['product_qty'], ascending=[False])
            GG100011 = GG100009.to_json(orient='records')
            return json.loads(GG100011)
        except:
            return []

    finally:
        psycopg2_conn_shwethe_delivery.close()


@router.get("/car_list/{fen}", status_code = 200)
def order_s_insert_list(fen: int):

    from src.set_up.config import time_out_time
    import src.Connect.postgresql_connect_delivery as psycopg2_conn_shwethe_delivery
    import src.Connect.https_connect as mongodb_data_api
    import importlib
    importlib.reload(psycopg2_conn_shwethe_delivery)
    importlib.reload(mongodb_data_api)
    from src.Connect.postgresql_connect_delivery import psycopg2_conn_shwethe_delivery
    from src.Connect.https_connect import mongodb_data_api,mongodb_data_api
    A100001 = datetime.datetime.now()
    try:
        psycopg2_conn_shwethe_delivery.autocommit = True
        cur = psycopg2_conn_shwethe_delivery.cursor()

        def get_data_api_2(res_1,str):
            # res_1 = json.loads(H1000001.to_json(orient='records'))
            res_1 = json.loads(res_1.to_json(orient='records'))
            r = requests.get(mongodb_data_api + '/api/v1/search/jia_yi_name_list',
                        json={"data_api": res_1})
            GF10001 = pd.DataFrame(json.loads(r.json()))
            GF10001 = GF10001.rename(columns={'jia_yi_id': str+'_id','jia_yi_idname': str+'_idname','jia_yi_mm_name': str+'_mm_name'})
            
            return GF10001

        def get_data_api():
            r = requests.get(mongodb_data_api + '/api/v2/user_info/user_info_list/0',timeout = time_out_time)
            GF10001 = pd.DataFrame(r.json())
            GF10001 = GF10001.loc[GF10001['car_id'] > 0]
            GF10001 = GF10001.drop(columns=['ri_qi'])
            return GF10001
        D100001 = get_data_api()
        H1000001 = D100001[['car_id']]

        

        H1000001 = H1000001.rename(columns={'car_id': 'jia_yi_id'})
        FG10001 = get_data_api_2(H1000001,"car")
        D100001 = FG10001.merge(D100001, left_on=['car_id'], right_on=['car_id'], how='inner')
        A100002 = D100001.to_json(orient='records')

        return json.loads(A100002)

    finally:
        psycopg2_conn_shwethe_delivery.close()

@router.get("/car_list_by/{fen}/{car_id}", status_code = 200)
def order_s_insert_list(fen: int,car_id:int):
    from src.shwethe_car_manager.orm.table.tb_car_round.main import tb_car_round_engine,tb_car_round

    # current_time = datetime.datetime.utcnow()
    # ten_weeks_ago = current_time - datetime.timedelta(days=0)

    from src.time_zone.time_zone_function import get_date
    current_time = get_date()
    today = str(get_date())
    print(today)
    print("today")


    def get_data(car_id):
        with Session(tb_car_round_engine) as session:
            heroes = session.exec(select(tb_car_round).where(and_(tb_car_round.che_liang.in_([car_id]),tb_car_round.shu_riqi_datetime >= today)).order_by(desc(tb_car_round.auto_id))).all()
            return heroes

    T10001 = get_data(car_id)


    def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

    T10001 = sqmodel_to_df(T10001)

    T10002 = T10001.to_json(orient='records')

    return json.loads(T10002)



class car_list_by(BaseModel):
    jia_yi_fang_id:int
    data_json : dict = Body(...)


@router.post("/car_list_by/{fen}/{car_id}", status_code = 200)
def car_list_by(fen: int,car_id:int,req_body : car_list_by = Body(...)):
    from src.shwethe_car_manager.orm.table.tb_car_round.main import tb_car_round_engine,tb_car_round
    from src.time_zone.time_zone_function import get_datetime

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    # current_time = datetime.datetime.utcnow()
    # current_time = datetime.datetime.now()
    current_time = get_datetime()

    def insert_data(jia_yi_fang_id,car_id,json_data):
        with Session(tb_car_round_engine) as session:
            tb_goods_sku_ = tb_car_round(
                che_liang = car_id,
                product_id=9037,
                product_qty=0,
                jia_yi_name_a=jia_yi_fang_id,
                jia_yi_name_b=jia_yi_fang_id,
                lei_a=36,
                lei_b=43,
                shu_riqi_datetime=str(current_time),
                riqi_datetime=str(current_time),
                data_sub=json_data
                )
            session.add(tb_goods_sku_)
            session.commit()
            session.refresh(tb_goods_sku_)
            print("Created hero:", tb_goods_sku_)
            return "Created hero"

    insert_data(FFF['jia_yi_fang_id'],car_id,FFF['data_json'])

    return "aaaa"


class car_list_by_put(BaseModel):
    auto_id : int
    product_qty : float


@router.put("/car_list_by/{fen}/{car_id}", status_code = 200)
def car_list_by(fen: int,car_id:int,req_body : car_list_by_put = Body(...)):
    from src.shwethe_car_manager.orm.table.tb_car_round.main import tb_car_round_engine,tb_car_round

    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    def put_data(auto_id,product_qty):
        with Session(tb_car_round_engine) as session:
            statement = select(tb_car_round).where(and_(tb_car_round.auto_id == int(auto_id)))
            results = session.exec(statement)
            hero = results.one()
            hero.product_qty = product_qty
            session.add(hero)
            session.commit()

            return "Created hero"

    print(FFF['auto_id'],FFF['product_qty'])
    put_data(FFF['auto_id'],FFF['product_qty'])

    return "aaaa"


class car_list_by_sub_post(BaseModel):
    auto_id : int
    che_ci : int

@router.post("/car_list_by_sub", status_code = 200)
def car_list_by_sub(req_body : car_list_by_sub_post = Body(...)):

    from src.shwethe_car_manager.orm.table.tb_car_round.main import tb_car_round_engine,tb_sub_car_round
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)
    from src.time_zone.time_zone_function import get_datetime
    current_time = get_datetime()
    def get_data(auto_id):
        with Session(tb_car_round_engine) as session:
            heroes = session.exec(select(tb_sub_car_round).where(tb_sub_car_round.che_ci.in_([auto_id])).order_by(desc(tb_sub_car_round.auto_id))).all()
            return heroes


    def post_data(auto_id,che_ci,current_time):
        with Session(tb_car_round_engine) as session:
            tb_goods_sku_ = tb_sub_car_round(
                a_id = auto_id,
                che_ci = che_ci,
                shu_riqi_datetime = str(current_time),
                riqi_datetime = str(current_time),
                )
            session.add(tb_goods_sku_)
            session.commit()
            session.refresh(tb_goods_sku_)
            return "Created hero"

    T10001 = get_data(FFF['che_ci'])

    

    def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

    T10001 = sqmodel_to_df(T10001)

    if T10001.empty:
        post_data(FFF['auto_id'],FFF['che_ci'],current_time)

        return "aaaa"
    else:

        return "aaaa"


class car_list_by_sub_delete(BaseModel):
    auto_id : int
    che_ci : int

@router.delete("/car_list_by_sub", status_code = 200)
def car_list_by_sub_delete(req_body : car_list_by_sub_delete = Body(...)):

    from src.shwethe_car_manager.orm.table.tb_car_round.main import tb_car_round_engine,tb_sub_car_round
    req_body_ = req_body.json()
    FFF = json.loads(req_body_)

    def get_data(auto_id):
        with Session(tb_car_round_engine) as session:
            heroes = session.exec(select(tb_sub_car_round).where(and_(tb_sub_car_round.che_ci.in_([auto_id]))).order_by(desc(tb_sub_car_round.auto_id))).all()
            return heroes


    def delete_data(auto_id,che_ci):
        with Session(tb_car_round_engine) as session:
            print(auto_id,che_ci)   
            tb_goods_sku_ = session.exec(select(tb_sub_car_round).where(and_(tb_sub_car_round.a_id == int(auto_id),tb_sub_car_round.che_ci == int(che_ci))))
            hero = tb_goods_sku_.one()
            session.delete(hero)
            session.commit()
            # session.refresh(hero)
            return "Created hero"

    T10001 = get_data(FFF['che_ci'])

    def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

    T10001 = sqmodel_to_df(T10001)

    print(T10001)
    print(FFF)

    if not T10001.empty:
        # print(FFF['auto_id'],FFF['che_ci'])
        delete_data(FFF['auto_id'],FFF['che_ci'])

        return "aaaa"
    else:

        return "aaaa"


@router.get("/car_list_by_sub/{auto_id}", status_code = 200)
def car_list_by_sub(auto_id:int ):


    from src.shwethe_car_manager.orm.table.tb_car_round.main import tb_car_round_engine,tb_sub_car_round
    def get_data(auto_id):
        with Session(tb_car_round_engine) as session:
            heroes = session.exec(select(tb_sub_car_round).where(tb_sub_car_round.a_id.in_([auto_id])).order_by(desc(tb_sub_car_round.auto_id))).all()
            return heroes

    T10001 = get_data(auto_id)

    def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

    T10001 = sqmodel_to_df(T10001)

    T10002 = T10001.to_json(orient='records')

    return json.loads(T10002)



@router.get("/car_list_static/{fen}", status_code = 200)
def car_list_static(type_data:str,fen:int=0):

    from src.shwethe_car_manager.orm.table.tb_car_round.main import tb_car_round_engine,tb_sub_car_round,tb_car_round

    from src.time_zone.time_zone_function import get_datetime
    current_time = get_datetime()
    ten_weeks_ago = current_time - datetime.timedelta(days=0)

    datetime_ = ten_weeks_ago.strftime('%Y-%m-%d')
    print("current_time")
    print(datetime_)

    def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
            """Convert a SQLModel objects into a pandas DataFrame."""
            records = [i.dict() for i in objs]
            df = pd.DataFrame.from_records(records)
            return df

    def get_data(ten_weeks_ago):
        with Session(tb_car_round_engine) as session:
            # .astimezone('Asia/Yangon')
            heroes = session.exec(select(tb_car_round).where(tb_car_round.shu_riqi_datetime >= ten_weeks_ago).order_by(desc(tb_car_round.auto_id))).all()
            return heroes

    T10001 = get_data(datetime_)
    T10001 = sqmodel_to_df(T10001)
    # T10001['riqi_datetime'] = T10001['riqi_datetime'].dt.tz_localize('UTC').dt.tz_convert('Asia/Yangon')
    if T10001.empty:
        return []
    else:
        T10001['shu_riqi_datetime'] = T10001['shu_riqi_datetime'].astype(str)
        T10001['riqi_datetime'] = T10001['riqi_datetime'].astype(str)
        T10001['row_count'] = T10001['product_qty']
        T10001 = T10001.to_dict('records')
        T10001 = pd.json_normalize(T10001)
        T10001 = T10001.sort_values(['shu_riqi_datetime'], ascending=[False])
        T10002 = T10001.groupby(['jia_yi_name_a','che_liang'])['row_count'].sum().reset_index()
        F10001 = T10001.drop_duplicates(subset=['jia_yi_name_a','che_liang'], keep='first')
        F10001 = F10001[['jia_yi_name_a','che_liang','data_sub.car_idname','data_sub.car_mm_name','data_sub.product_idname','data_sub.product_mm_name','data_sub.car_user_idname','data_sub.car_user_mm_name']]
        YY100001 = T10002.merge(F10001, on=['jia_yi_name_a', 'che_liang'],  how='inner')
        T10002 = YY100001.to_json(orient='records')
        return json.loads(T10002)

@router.get("/car_list_with_info/{che_ci}", status_code = 200)
def car_list_by_sub(che_ci : int ):

    from src.shwethe_car_manager.orm.table.tb_car_round.main import tb_car_round_engine,tb_sub_car_round,tb_car_round
    def get_data(auto_id):
        with Session(tb_car_round_engine) as session:
            try:
                heroes = session.exec(select(tb_sub_car_round).where(tb_sub_car_round.a_id.in_(auto_id)).order_by(desc(tb_sub_car_round.auto_id))).all()
            except:
                heroes = session.exec(select(tb_sub_car_round).where(tb_sub_car_round.a_id.in_([auto_id])).order_by(desc(tb_sub_car_round.auto_id))).all()
                
            return heroes

    def get_data_head(che_ci):
        with Session(tb_car_round_engine) as session:
            heroes = session.exec(select(tb_car_round).where(tb_car_round.che_liang.in_([che_ci])).order_by(desc(tb_car_round.auto_id))).all()
            return heroes

    T10001 = get_data_head(che_ci)

    def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df

    T10001 = sqmodel_to_df(T10001)


    TY10001 = get_data(T10001['auto_id'].to_list())
    TY10002 = sqmodel_to_df(TY10001)

    print(TY10002)

    T10002 = TY10002.to_json(orient='records')

    return json.loads(T10002)

