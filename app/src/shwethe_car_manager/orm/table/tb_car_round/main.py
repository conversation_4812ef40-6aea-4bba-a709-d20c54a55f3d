from datetime import datetime
from typing import List, Optional

from sqlmodel import Field, SQLModel, create_engine,Relationship,Column
from sqlalchemy.dialects.postgresql import JSONB


from src.Connect.postgresql_sqlalchemy_car_manager import db_db_info_engine as tb_car_round_engine


class tb_car_round_base(SQLModel):
    auto_id: int

class tb_car_round(tb_car_round_base, table=True):
    auto_id : Optional[int] = Field(default=None, primary_key=True)
    che_liang : str
    product_id : str
    product_qty : float
    jia_yi_name_a : str
    jia_yi_name_b : str
    lei_a : str
    lei_b : str
    shu_riqi_datetime : datetime
    riqi_datetime : datetime
    data_sub: dict = Field(sa_column=Column(JSONB), default={})
    # heroes: List["tb_sub_car_round"] = Relationship(back_populates="team")


class tb_sub_car_round_base(SQLModel):
    auto_id: int

class tb_sub_car_round(tb_sub_car_round_base, table=True):
    auto_id: Optional[int] = Field(default=None, primary_key=True)
    che_ci: int
    shu_riqi_datetime: datetime
    riqi_datetime: datetime
    # a_id: Optional[int] = Field(default=None, foreign_key="tb_car_round.auto_id")
    a_id: int
    # team: Optional[tb_car_round] = Relationship(back_populates="heroes")


# tb_goods_translate_entry_engine = create_engine(db_db_info, echo=True,   
#         max_overflow=2,  # 超过连接池大小外最多创建的连接
#         pool_size=1,  # 连接池大小
#         pool_timeout=30,  # 池中没有线程最多等待的时间，否则报错
#         pool_recycle=60,  # 多久之后对线程池中的线程进行一次连接的回收（重置）
#         )

# SQLModel.metadata.create_all(tb_car_round_engine)