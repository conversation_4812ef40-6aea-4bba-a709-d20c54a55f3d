from fastapi import Depends, HTTPException, status
from sqlmodel import Session, select,SQLModel,or_,and_
from sqlalchemy import text
from typing import List, Optional
from datetime import datetime, timedelta
from src.config.insert_data.database import get_session as get_session_insert_data
from src.common.product_name import product_name_package
import json
from src.shwethe_exchange.models.models import (
    post_exchange_insert,
    exchange_insert,
    put_Product_for_d,
    put_Product_for_images
)
from src.time_zone import time_zone_function
import numpy as np
import pandas as pd 
from src.time_zone.time_zone_function import get_datetime

def sqmodel_to_df(objs: List[SQLModel]) -> pd.DataFrame:
        """Convert a SQLModel objects into a pandas DataFrame."""
        records = [i.dict() for i in objs]
        df = pd.DataFrame.from_records(records)
        return df


def postProduct(hero:post_exchange_insert,db: Session = Depends(get_session_insert_data)):
    
    from src.common.http.mongodbApi.main import get_d_to_gu
    
    # if hero.qtyn >= 0 :
        
    #     A10001 = get_d_to_gu(hero.type_b,hero.product_id)
        
    #     print(A10001)
        
    #     if A10001.empty:
            
    #         raise HTTPException(status_code=400, detail="not found data")
    
    hero_to_db = exchange_insert.from_orm(hero)
    hero_to_db.datetime = get_datetime()

    db.add(hero_to_db)
    db.commit()
    db.refresh(hero_to_db)
    
    return hero_to_db


def getProductList(fen:int,db: Session = Depends(get_session_insert_data)):

    A100001 = get_datetime() - timedelta(days=0)
    A100001 = datetime(A100001.year, A100001.month, A100001.day)
    
    statement = select(exchange_insert).where(and_(
    exchange_insert.data_detail.op('->>')('fen_dian') == str(fen),exchange_insert.datetime > A100001
    ))

    results = db.exec(statement).all()

    df3 = sqmodel_to_df(results)

    results = json.loads(df3.to_json(orient='records'))
    
    return results


def putProductForD(hero:put_Product_for_d,db: Session = Depends(get_session_insert_data)):

    statement = select(exchange_insert).where(and_(
    exchange_insert.id == str(hero.id)
    ))

    results = db.exec(statement).first()

    if results == None:
    
        return []


    V10001 = dict(results.data_detail)
    V10001['status'] = 'accept'

    results.data_detail = V10001
    results.type_a = hero.type_a
    
    # hero_to_db = exchange_insert.from_orm(hero)
    # hero_to_db.datetime = get_datetime()

    db.add(results)
    db.commit()
    db.refresh(results)
    
    return results


def putProductForG(hero:put_Product_for_d,db: Session = Depends(get_session_insert_data)):
    
    statement = select(exchange_insert).where(and_(

    exchange_insert.id == str(hero.id)

    ))

    results = db.exec(statement).first()

    if results == None:
    
        return []


    V10001 = dict(results.data_detail)
    V10001['status'] = 'wait'

    # results.data_detail = V10001
    # results.type_a = hero.type_a
    # hero_to_db = exchange_insert.from_orm(hero)
    # hero_to_db.datetime = get_datetime()

    db.add(results)
    db.commit()
    db.refresh(results)
    
    return results



def putProductForGimages(hero: put_Product_for_images, db: Session = Depends(get_session_insert_data)):
    
    # 获取指定ID的记录
    record = db.query(exchange_insert).filter_by(id=hero.id).first()

    if not record:
        raise HTTPException(status_code=404, detail="Record not found")

    # 初始化record的images字段为字典，如果其当前值为None
    if record.images is None:
        record.images = {}
    
    # 更新或插入键值对
    updated_images = record.images.copy()
    updated_images.update(hero.images)
    record.images = updated_images

    db.add(record)
    
    # 提交更改到数据库
    db.commit()
    db.refresh(record)

    return record