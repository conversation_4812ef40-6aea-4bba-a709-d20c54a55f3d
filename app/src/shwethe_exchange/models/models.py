from xmlrpc.client import DateTime
from pydantic.types import Optional
from pydantic import BaseModel
from sqlmodel import Field, Relationship, SQLModel,Column
from sqlalchemy.dialects.postgresql import JSONB
from src.time_zone.time_zone_function import get_datetime
from datetime import datetime
from helper import generate_datetime_id


# 资料表名称 pre_order_product 和框架
class exchange_insert_base(SQLModel):

    product_id: Optional[int]
    price: Optional[float]
    datetime: Optional[int]
    type_a : Optional[int]
    type_b : Optional[int]
    lei_a : Optional[int]
    lei_b : Optional[int]
    line : Optional[int]
    qtyp : Optional[float]
    qtyn : Optional[float]
    qty : Optional[float]
    uid : Optional[int]
    data_detail : dict = Field(sa_column=Column(JSONB), default={})

class exchange_insert(exchange_insert_base, table=True):

    id: Optional[int] = Field(default=None, primary_key=True)
    product_id: Optional[int]
    price: Optional[float]
    datetime: Optional[int]
    type_a : Optional[int]
    type_b : Optional[int]
    lei_a : Optional[int]
    lei_b : Optional[int]
    line : Optional[int]
    qtyp : Optional[float]
    qtyn : Optional[float]
    qty : Optional[float]
    uid : Optional[int]
    data_detail : dict = Field(sa_column=Column(JSONB), default={})
    data_sub_name : dict = Field(sa_column=Column(JSONB), default={})
    images : dict = Field(sa_column=Column(JSONB), default={})
    

class post_exchange_insert(BaseModel):
    
    product_id: Optional[int]
    type_a : Optional[int] = 0
    type_b : Optional[int] = 0
    lei_a : Optional[int] = 22
    lei_b : Optional[int] = 22
    line : Optional[int] = 420 
    price : Optional[float] = 0 
    qtyp : Optional[float]
    qtyn : Optional[float]
    qty : Optional[float]
    uid : Optional[int]
    data_detail : dict = Field(sa_column=Column(JSONB), default={})
    data_sub_name : dict = Field(sa_column=Column(JSONB), default={})


class put_Product_for_d(BaseModel):
    id : int 
    type_a:int
    data_detail : dict 
    
class put_Product_for_images(BaseModel):
    id : int 
    images : dict 

class put_Product_for_g(BaseModel):
    id : int 


