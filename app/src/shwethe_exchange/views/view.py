from fastapi import APIRouter, Depends, Query
from pydantic.types import List
from sqlmodel import Session

from helper import generate_datetime_id
from src.time_zone.time_zone_function import get_datetime
from src.config.insert_data.database import get_session as get_session_insert_data

from src.shwethe_exchange.crud.crud import (
    postProduct,
    getProductList,
    putProductForD,
    putProductForG,
    putProductForGimages
)
from src.shwethe_exchange.models.models import (
    post_exchange_insert,
    put_Product_for_d,
    put_Product_for_g,
    put_Product_for_images
)

router = APIRouter()

@router.post("/exchange/goods")
def postExchangeGoods(hero: post_exchange_insert , db: Session = Depends(get_session_insert_data)):
    
    return postProduct(hero=hero, db=db)


@router.get("/exchange/goods/List")
def postExchangeGoods(fen:int, db: Session = Depends(get_session_insert_data)):
    
    return getProductList(fen=fen, db=db)

@router.put("/exchange/goods/List/ForD")
def postExchangeGoods(hero:put_Product_for_d, db: Session = Depends(get_session_insert_data)):
    
    return putProductForD(hero=hero, db=db)

@router.put("/exchange/goods/List/ForG")
def postExchangeGoods(hero:put_Product_for_g, db: Session = Depends(get_session_insert_data)):
    
    return putProductForG(hero=hero, db=db)

@router.put("/exchange/goods/List/ForG/images")
def postExchangeGoodsimages(hero:put_Product_for_images, db: Session = Depends(get_session_insert_data)):
    
    return putProductForGimages(hero=hero, db=db)