
from src.shwethe_car_active.src.views import view as shwethe_car_active_info

from fastapi import FastAPI, Depends, <PERSON>er, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    shwethe_car_active_info.router,
    prefix="/shwethe_car_active/api/v1/form",
    tags=["shwethe_car_active"],
    responses={404: {"message": "Not found"}},
)


