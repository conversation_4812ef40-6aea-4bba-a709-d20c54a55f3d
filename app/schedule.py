# import asyncio
# from datetime import datetime

# async def print_hello():
#     while True:
#         print(f"AAA Hello! Current time: {datetime.now()}")
#         await asyncio.sleep(10)  # Sleep for 10 seconds

# def init_scheduler(app):
#     @app.on_event("startup")
#     async def startup_event():
#         # Start the background task
#         asyncio.create_task(print_hello())


import asyncio
from datetime import datetime
from src.shwethe_miniapp_carItemChange.src.crud.crud import updateRealtimeQty100
from src.shwethe_miniapp_carItemChange.database import get_session

async def print_hello():
    while True:
        print(f"Hello! Current time: {datetime.now()}")
        await asyncio.sleep(10)  # Sleep for 10 seconds

async def update_realtime_qty():
    while True:
        try:
            # Get a database session
            db = next(get_session())
            # Run the update function
            result = updateRealtimeQty100(db=db)
            print(f"Updated realtime qty at {datetime.now()}: {result}")
        except Exception as e:
            print(f"Error updating realtime qty: {e}")
        finally:
            # Close the database session
            db.close()
        await asyncio.sleep(1800)  # Sleep for 30 minutes (30 * 60 = 1800 seconds)

def init_scheduler(app):
    @app.on_event("startup")
    async def startup_event():
        # Start both background tasks
        # asyncio.create_task(print_hello())
        asyncio.create_task(update_realtime_qty())