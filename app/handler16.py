
from src.shwethe_miniapp_departScan.src.views import view as shwethe_miniapp_departScan_info

from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    shwethe_miniapp_departScan_info.router,
    prefix="/shwethe_miniapp_departScan/api/v1/form",
    tags=["shwethe_miniapp_departScan"],
    responses={404: {"message": "Not found"}},
)


