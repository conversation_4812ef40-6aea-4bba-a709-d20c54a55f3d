FROM python:3.9-slim

RUN mkdir /app
WORKDIR /app

RUN pip install --upgrade pip

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    libpq-dev \
    build-essential \
    pkg-config \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# FreeTDS configuration removed for simplified build

ADD requirements.txt /app
RUN pip3 install -r requirements.txt
ADD . /app
CMD uvicorn main:app --reload --host 0.0.0.0 --port 8000
# CMD gunicorn --bind 0.0.0.0:8000 -w 3 --threads 3 -k  uvicorn.workers.UvicornWorker  main:app --reload