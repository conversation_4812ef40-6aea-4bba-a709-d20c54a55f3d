# FROM python:3.7.2-slim
# FROM python:3.7.16-slim-bullseye
FROM python:3.7.9-slim

RUN mkdir /app
WORKDIR /app

# 使用阿里云镜像源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list

RUN pip install --upgrade pip

# 添加重试机制并合并apt-get命令
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libpq-dev \
    gcc \
    ffmpeg \
    unixodbc \
    unixodbc-dev \
    freetds-dev \
    freetds-bin \
    tdsodbc \
    locales \
    build-essential \
    libpangocairo-1.0-0 && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

RUN echo "[FreeTDS]\n\
Description = FreeTDS Driver\n\
Driver = /usr/lib/x86_64-linux-gnu/odbc/libtdsodbc.so\n\
Setup = /usr/lib/x86_64-linux-gnu/odbc/libtdsS.so" >> /etc/odbcinst.ini

ADD requirements.txt /app
RUN pip3 install -r requirements.txt
ADD . /app
CMD uvicorn main:app --reload --host 0.0.0.0 --port 8000
# CMD gunicorn --bind 0.0.0.0:8000 -w 3 --threads 3 -k  uvicorn.workers.UvicornWorker  main:app --reload