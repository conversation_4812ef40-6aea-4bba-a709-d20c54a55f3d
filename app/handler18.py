
from src.shwethe_miniapp_report.src.views import view as shwethe_miniapp_report_info

from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    shwethe_miniapp_report_info.router,
    prefix="/shwethe_miniapp_report/api/v1/form",
    tags=["shwethe_miniapp_report"],
    responses={404: {"message": "Not found"}},
)


