
from src.shwethe_company_document.form_company.views import view as shwethe_company_document_form_company

from fastapi import FastAPI, Depends, <PERSON>er, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    shwethe_company_document_form_company.router,
    prefix="/shwethe_company_document/api/v1/company",
    tags=["shwethe_company_document"],
    responses={404: {"message": "Not found"}},
)



# from src.shwethe_company_document.form_company.views import view as shwethe_company_document_form_company
# from src.shwethe_company_document.status_list.views import view as shwethe_company_document_status_list

# from fastapi import FastAPI, Depends, Header, HTTPException, Response
# from fastapi.middleware.cors import CORSMiddleware
# from handler import app



# app.include_router(
#     shwethe_company_document_form_company.router,
#     prefix="/shwethe_company_document/api/v1/company",
#     tags=["shwethe_company_document"],
#     responses={404: {"message": "Not found"}},
# )

# app.include_router(
#     shwethe_company_document_status_list.router,
#     prefix="/shwethe_company_document/api/v2/company",
#     tags=["shwethe_company_document"],
#     responses={404: {"message": "Not found"}},
# )
