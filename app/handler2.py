
from src.shwethe_cai_chan.cliend.cai_chan_category import main as cai_chan_category
from src.shwethe_cai_chan.cliend.list_data import main as list_data
from src.search import main as search




#/home/<USER>/shwethe_order_in_fast_api/app/src/shwethe_cai_chan/cliend/cai_chan_category/main.py


from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app

app.include_router(
    cai_chan_category.router,
    prefix="/shwethe_cai_chan/api/v1/cliend/cai_chan_category",
    tags=["cliend_cai_chan_category"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    search.router,
    prefix="/shwethe_cai_chan/api/v1/cliend/search",
    tags=["search"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    list_data.router,
    prefix="/shwethe_cai_chan/api/v1/cliend/list_data",
    tags=["list_data"],
    responses={404: {"message": "Not found"}},
)



