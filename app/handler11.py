
from src.shwethe_in.status_list.views import view as shwethe_car_garage_router
from src.shwethe_in.search.views import view as shwethe_in_search
from src.shwethe_in.search.views import view2 as shwethe_in_search_v2

from src.shwethe_in.product_in.views import view as shwethe_in_product_in
from src.shwethe_in.follow.follow_goods.views import view as shwethe_in_follow_goods

from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    shwethe_car_garage_router.router,
    prefix="/shwethe_api_in/api/v1/status_list",
    tags=["shwethe_api_in"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    shwethe_in_search.router,
    prefix="/shwethe_api_in/api/v1/search",
    tags=["shwethe_api_in"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    shwethe_in_search_v2.router,
    prefix="/shwethe_api_in/api/v2/search",
    tags=["shwethe_api_in_2"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    shwethe_in_product_in.router,
    prefix="/shwethe_api_in/api/v1/product_in",
    tags=["shwethe_api_in"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    shwethe_in_follow_goods.router,
    prefix="/shwethe_api_in/api/v1/follow/follow_goods",
    tags=["shwethe_api_in_follow"],
    responses={404: {"message": "Not found"}},
)
