
from src.shwethe_g.manager_work_uid import main as manager_work_uid
from src.shwethe_g.barcode.views import view as barcode_view
from src.shwethe_g.gui_ding_product_qty.views import view as gui_ding_product_qty_view
from src.shwethe_g.g_work.views import view as g_work



from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    manager_work_uid.router,
    prefix="/shwethe_g/api/v1/manager_work",
    tags=["shwethe_g"],
    responses={404: {"message": "Not found"}},
)



app.include_router(
    barcode_view.router,
    prefix="/shwethe_g/api/v1/barcode_view",
    tags=["barcode_view"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    gui_ding_product_qty_view.router,
    prefix="/shwethe_g/api/v1/gui_ding_product",
    tags=["gui_ding_product"],
    responses={404: {"message": "Not found"}},
)

app.include_router(
    g_work.router,
    prefix="/shwethe_g/api/v1/g_work",
    tags=["g_work"],
    responses={404: {"message": "Not found"}},
)







