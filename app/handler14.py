
from src.shwethe_exchange.views import view as shwethe_exchange_view

from fastapi import FastAPI, <PERSON>pen<PERSON>, <PERSON><PERSON>, HTTPEx<PERSON>, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app



app.include_router(
    shwethe_exchange_view.router,
    prefix="/shwethe_exchange/api/v1/exchange",
    tags=["shwethe_exchange"],
    responses={404: {"message": "Not found"}},
)


