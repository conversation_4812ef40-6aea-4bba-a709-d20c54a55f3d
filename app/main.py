from typing import Optional

from fastapi import FastAPI

# app = FastAPI()
from handler import app
from handler2 import app
from handler3 import app
from handler4 import app
from handler5 import app
from handler6 import app
from handler7 import app
from handler8 import app
from handler9 import app
from handler10 import app
from handler11 import app
from handler12 import app
from handler13 import app
from handler14 import app
from handler15 import app
from handler16 import app
from handler17 import app
from handler18 import app
from handler19 import app
from handler20 import app
from handler21 import app
from handler22 import app
from handler23 import app
from handler_duty import app
from handler_data import app
from handler_employee import app
from handler_car_rent import app
from handlerSchedule import app

import uvicorn

# @app.get("/")
# def read_root():
#     return {"Hello": "World"}
#
#
# @app.get("/items/{item_id}")
# def read_item(item_id: int, q: Optional[str] = None):
#     return {"item_id": item_id, "q": q}
# import uvicorn

# async def app(scope, receive, send):
    

if __name__ == "__main__":
    uvicorn.run("main:app", host='0.0.0.0', port=8000, workers=4, debug=True)